#!/bin/bash

# R&D指标系统数据库安全部署脚本
# 使用方法: ./deploy_database_safely.sh [database_name] [username]

set -e  # 遇到错误立即退出

# 配置变量
DB_NAME=${1:-"your_database_name"}
DB_USER=${2:-"your_username"}
BACKUP_DIR="./db_backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="${BACKUP_DIR}/backup_before_rd_metrics_${TIMESTAMP}.sql"

echo "🚀 开始R&D指标系统数据库部署..."
echo "数据库: $DB_NAME"
echo "用户: $DB_USER"
echo "时间: $(date)"

# 1. 创建备份目录
mkdir -p $BACKUP_DIR

# 2. 备份现有数据库
echo "📦 正在备份现有数据库..."
mysqldump -u $DB_USER -p --single-transaction --routines --triggers $DB_NAME > $BACKUP_FILE

if [ $? -eq 0 ]; then
    echo "✅ 数据库备份成功: $BACKUP_FILE"
else
    echo "❌ 数据库备份失败，终止部署"
    exit 1
fi

# 3. 检查表是否已存在
echo "🔍 检查R&D指标表是否已存在..."
EXISTING_TABLES=$(mysql -u $DB_USER -p -D $DB_NAME -e "SHOW TABLES LIKE 'rd_%';" | wc -l)

if [ $EXISTING_TABLES -gt 1 ]; then
    echo "⚠️  警告: 发现已存在的R&D指标表"
    echo "是否继续？这将跳过已存在的表创建 (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "❌ 用户取消部署"
        exit 1
    fi
fi

# 4. 执行数据库结构创建
echo "🏗️  正在创建R&D指标表结构..."
mysql -u $DB_USER -p $DB_NAME < app01/rd_metrics/docs/rd_metrics_mysql_schema.sql

if [ $? -eq 0 ]; then
    echo "✅ 数据库表创建成功"
else
    echo "❌ 数据库表创建失败"
    echo "🔄 正在恢复备份..."
    mysql -u $DB_USER -p $DB_NAME < $BACKUP_FILE
    echo "💾 数据库已恢复到部署前状态"
    exit 1
fi

# 5. 验证表创建
echo "🔍 验证表创建结果..."
CREATED_TABLES=$(mysql -u $DB_USER -p -D $DB_NAME -e "SHOW TABLES LIKE 'rd_%';" | tail -n +2)
echo "已创建的表:"
echo "$CREATED_TABLES"

# 6. 检查表结构
echo "📋 检查关键表结构..."
mysql -u $DB_USER -p -D $DB_NAME -e "DESCRIBE rd_team;" > /dev/null 2>&1
mysql -u $DB_USER -p -D $DB_NAME -e "DESCRIBE rd_jira_metrics;" > /dev/null 2>&1
mysql -u $DB_USER -p -D $DB_NAME -e "DESCRIBE rd_metrics_permission;" > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 表结构验证通过"
else
    echo "❌ 表结构验证失败"
    exit 1
fi

# 7. 插入初始权限数据
echo "👤 正在插入初始权限数据..."
mysql -u $DB_USER -p -D $DB_NAME << EOF
INSERT IGNORE INTO rd_metrics_permission (
    user_email, 
    permission_level, 
    can_view_individual_metrics, 
    can_view_team_comparison, 
    can_export_data, 
    can_configure_teams, 
    granted_by, 
    is_test_data
) VALUES 
('<EMAIL>', 'admin', 1, 1, 1, 1, '<EMAIL>', 0);
EOF

echo "✅ 初始权限数据插入完成"

# 8. 创建回滚脚本
ROLLBACK_SCRIPT="${BACKUP_DIR}/rollback_rd_metrics_${TIMESTAMP}.sh"
cat > $ROLLBACK_SCRIPT << EOF
#!/bin/bash
# R&D指标系统回滚脚本
# 生成时间: $(date)

echo "🔄 正在回滚R&D指标系统..."

# 删除R&D指标表
mysql -u $DB_USER -p -D $DB_NAME << SQL
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS rd_metrics_permission_accessible_teams;
DROP TABLE IF EXISTS rd_metrics_permission;
DROP TABLE IF EXISTS rd_git_metrics;
DROP TABLE IF EXISTS rd_jira_metrics;
DROP TABLE IF EXISTS rd_metrics_snapshot;
DROP TABLE IF EXISTS rd_team_member;
DROP TABLE IF EXISTS rd_team;
SET FOREIGN_KEY_CHECKS = 1;
SQL

# 恢复备份
mysql -u $DB_USER -p $DB_NAME < $BACKUP_FILE

echo "✅ 回滚完成"
EOF

chmod +x $ROLLBACK_SCRIPT

echo ""
echo "🎉 R&D指标系统数据库部署完成！"
echo ""
echo "📊 部署摘要:"
echo "  - 备份文件: $BACKUP_FILE"
echo "  - 回滚脚本: $ROLLBACK_SCRIPT"
echo "  - 创建表数: $(echo "$CREATED_TABLES" | wc -l)"
echo ""
echo "🔧 下一步操作:"
echo "  1. 重启Django应用"
echo "  2. 访问 /rd-metrics/ 验证功能"
echo "  3. 如有问题，执行回滚: $ROLLBACK_SCRIPT"
echo ""
echo "⚠️  重要提醒:"
echo "  - 备份文件请妥善保存"
echo "  - 建议在低峰期进行部署"
echo "  - 部署后请及时测试功能"
