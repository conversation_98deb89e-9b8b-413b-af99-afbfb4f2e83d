#!/usr/bin/env python3
"""
测试SPCT Bug提醒API的简单脚本
"""

import requests
import json

def test_debug_status():
    """测试获取调试状态"""
    print("=" * 50)
    print("测试获取调试状态")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:8000/api/spct-bug-debug/")
        if response.status_code == 200:
            data = response.json()
            print("✅ 成功获取调试状态:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_enable_debug():
    """测试开启调试模式"""
    print("\n" + "=" * 50)
    print("测试开启调试模式")
    print("=" * 50)
    
    try:
        payload = {"debug_mode": True}
        response = requests.post(
            "http://localhost:8000/api/spct-bug-debug/",
            headers={"Content-Type": "application/json"},
            data=json.dumps(payload)
        )
        if response.status_code == 200:
            data = response.json()
            print("✅ 成功开启调试模式:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_trigger_bug_reminder():
    """测试触发Bug提醒"""
    print("\n" + "=" * 50)
    print("测试触发Bug提醒")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:8000/api/spct-test-bugs/")
        if response.status_code == 200:
            data = response.json()
            print("✅ 成功触发Bug提醒:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def main():
    """主函数"""
    print("🧪 SPCT Bug提醒API测试")
    
    # 1. 获取当前状态
    test_debug_status()
    
    # 2. 确保调试模式开启
    test_enable_debug()
    
    # 3. 触发Bug提醒
    test_trigger_bug_reminder()
    
    print("\n🎉 测试完成!")
    print("💡 请检查Django服务器日志以查看详细的调试信息")

if __name__ == '__main__':
    main()