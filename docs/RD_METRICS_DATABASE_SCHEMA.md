# R&D Efficiency Metrics Database Schema Design

## Overview
This document outlines the database schema design for the R&D efficiency metrics dashboard, building upon the existing statistics system.

## New Models

### 1. Team Management Models

#### RDTeam
```python
class RDTeam(models.Model):
    """R&D Team Configuration"""
    team_id = models.CharField(max_length=50, unique=True)
    team_name = models.CharField(max_length=100)
    department = models.CharField(max_length=100)
    team_leader_email = models.EmailField()
    parent_team_id = models.CharField(max_length=50, null=True, blank=True)  # For hierarchy
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Configuration
    jira_projects = models.JSONField(default=list)  # ['SPCB', 'SPCT']
    git_repositories = models.JSONField(default=list)  # From services_id.json
    
    class Meta:
        db_table = 'rd_team'
        indexes = [
            models.Index(fields=['team_id', 'is_active']),
            models.Index(fields=['department']),
        ]
```

#### RDTeamMember
```python
class RDTeamMember(models.Model):
    """R&D Team Member Configuration"""
    team = models.ForeignKey(RDTeam, on_delete=models.CASCADE, related_name='members')
    member_email = models.EmailField()
    member_name = models.CharField(max_length=100)
    role = models.CharField(max_length=50, choices=[
        ('developer', 'Developer'),
        ('tester', 'Tester'),
        ('pm', 'Product Manager'),
        ('lead', 'Tech Lead'),
        ('manager', 'Manager'),
    ])
    join_date = models.DateField()
    is_active = models.BooleanField(default=True)
    
    # JIRA and Git identifiers
    jira_account_id = models.CharField(max_length=100, null=True, blank=True)
    git_username = models.CharField(max_length=100, null=True, blank=True)
    
    class Meta:
        db_table = 'rd_team_member'
        unique_together = ['team', 'member_email']
        indexes = [
            models.Index(fields=['member_email', 'is_active']),
            models.Index(fields=['team', 'role']),
        ]
```

### 2. Metrics Data Models

#### RDMetricsSnapshot
```python
class RDMetricsSnapshot(models.Model):
    """R&D Metrics Snapshot - Daily/Weekly/Monthly aggregated data"""
    snapshot_id = models.UUIDField(default=uuid.uuid4, unique=True)
    team = models.ForeignKey(RDTeam, on_delete=models.CASCADE)
    
    # Time period
    period_type = models.CharField(max_length=20, choices=[
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
    ])
    period_start = models.DateField()
    period_end = models.DateField()
    
    # Metrics data (JSON format for flexibility)
    metrics_data = models.JSONField(default=dict)
    
    # Metadata
    calculated_at = models.DateTimeField(auto_now_add=True)
    data_sources = models.JSONField(default=dict)  # Track which data sources were used
    
    class Meta:
        db_table = 'rd_metrics_snapshot'
        unique_together = ['team', 'period_type', 'period_start']
        indexes = [
            models.Index(fields=['team', 'period_type', 'period_start']),
            models.Index(fields=['calculated_at']),
        ]
```

#### RDJiraMetrics
```python
class RDJiraMetrics(models.Model):
    """JIRA-specific metrics data"""
    team = models.ForeignKey(RDTeam, on_delete=models.CASCADE)
    project_key = models.CharField(max_length=20)  # SPCB, SPCT
    
    # Time period
    date = models.DateField()
    
    # Issue metrics
    total_issues = models.IntegerField(default=0)
    created_issues = models.IntegerField(default=0)
    resolved_issues = models.IntegerField(default=0)
    reopened_issues = models.IntegerField(default=0)
    
    # Bug metrics
    total_bugs = models.IntegerField(default=0)
    new_bugs = models.IntegerField(default=0)
    fixed_bugs = models.IntegerField(default=0)
    bug_fix_time_avg = models.FloatField(null=True)  # Average days
    
    # Story/Task metrics
    story_points_committed = models.FloatField(default=0)
    story_points_completed = models.FloatField(default=0)
    velocity = models.FloatField(default=0)
    
    # Cycle time metrics
    avg_cycle_time = models.FloatField(null=True)  # Days
    avg_lead_time = models.FloatField(null=True)   # Days
    
    # Quality metrics
    defect_density = models.FloatField(null=True)
    rework_rate = models.FloatField(null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'rd_jira_metrics'
        unique_together = ['team', 'project_key', 'date']
        indexes = [
            models.Index(fields=['team', 'date']),
            models.Index(fields=['project_key', 'date']),
        ]
```

#### RDGitMetrics
```python
class RDGitMetrics(models.Model):
    """Git-specific metrics data"""
    team = models.ForeignKey(RDTeam, on_delete=models.CASCADE)
    repository_name = models.CharField(max_length=200)
    repository_id = models.IntegerField()  # From services_id.json
    
    # Time period
    date = models.DateField()
    
    # Commit metrics
    total_commits = models.IntegerField(default=0)
    total_authors = models.IntegerField(default=0)
    lines_added = models.IntegerField(default=0)
    lines_deleted = models.IntegerField(default=0)
    
    # Merge request metrics
    merge_requests_created = models.IntegerField(default=0)
    merge_requests_merged = models.IntegerField(default=0)
    avg_mr_review_time = models.FloatField(null=True)  # Hours
    
    # Code quality metrics
    code_churn_rate = models.FloatField(null=True)
    hotspot_files_count = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'rd_git_metrics'
        unique_together = ['team', 'repository_id', 'date']
        indexes = [
            models.Index(fields=['team', 'date']),
            models.Index(fields=['repository_id', 'date']),
        ]
```

### 3. Access Control Models

#### RDMetricsPermission
```python
class RDMetricsPermission(models.Model):
    """R&D Metrics Access Control"""
    user_email = models.EmailField()
    
    # Permission levels
    permission_level = models.CharField(max_length=20, choices=[
        ('viewer', 'Viewer'),
        ('team_lead', 'Team Lead'),
        ('manager', 'Manager'),
        ('admin', 'Admin'),
    ])
    
    # Team access (null = all teams for managers/admins)
    accessible_teams = models.ManyToManyField(RDTeam, blank=True)
    
    # Feature permissions
    can_view_individual_metrics = models.BooleanField(default=True)
    can_view_team_comparison = models.BooleanField(default=False)
    can_export_data = models.BooleanField(default=False)
    can_configure_teams = models.BooleanField(default=False)
    
    is_active = models.BooleanField(default=True)
    granted_by = models.EmailField()
    granted_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'rd_metrics_permission'
        unique_together = ['user_email']
        indexes = [
            models.Index(fields=['user_email', 'is_active']),
            models.Index(fields=['permission_level']),
        ]
```

## Integration with Existing System

### Extending Existing Models
- Leverage existing `UserJiraToken` for JIRA authentication
- Use existing statistics middleware for data collection
- Extend existing `CommandExecutionRecord` for tracking R&D metrics queries

### Data Collection Strategy
1. **Real-time Collection**: Use existing middleware for user interactions
2. **Batch Processing**: Daily cron jobs for JIRA/Git data aggregation
3. **Historical Data**: Maintain snapshots for trend analysis

## Performance Considerations

### Indexing Strategy
- Composite indexes on team + date for time-series queries
- Separate indexes for different query patterns
- Partial indexes for active records only

### Data Retention Policy
- Raw metrics: 2 years
- Daily snapshots: 5 years  
- Weekly/Monthly snapshots: Permanent
- Automated cleanup jobs

### Caching Strategy
- Redis cache for frequently accessed metrics
- Pre-calculated aggregations for common queries
- Cache invalidation on data updates

## Migration Strategy

### Phase 1: Schema Creation
- Create new tables without affecting existing system
- Add foreign key constraints to existing user tables

### Phase 2: Data Population
- Backfill historical data from JIRA/Git APIs
- Validate data integrity and completeness

### Phase 3: Integration
- Connect new models to existing authentication system
- Enable real-time data collection

## Testing Data Management

All test data will be clearly marked with:
- `is_test_data` boolean field in relevant models
- `test_` prefix for test team names
- Separate cleanup procedures for test data removal
