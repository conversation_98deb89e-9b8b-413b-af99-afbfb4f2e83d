# R&D效率指标系统测试指南

## 🧪 测试概述

本指南提供了R&D效率指标系统的完整测试流程，包括功能测试、性能测试和用户验收测试。

## 🚀 快速测试部署

### 1. 环境准备

```bash
# 1. 确保数据库连接正常
python manage.py check

# 2. 执行数据库迁移
python manage.py migrate

# 3. 创建测试数据
python manage.py setup_rd_test_data --all

# 4. 启动开发服务器
python manage.py runserver
```

### 2. 验证基础功能

访问以下URL验证系统正常运行：

- 仪表板首页：`http://localhost:8000/rd-metrics/`
- 团队管理：`http://localhost:8000/rd-metrics/teams/`
- 权限管理：`http://localhost:8000/rd-metrics/permissions/`
- 指标说明：`http://localhost:8000/rd-metrics/help/`
- 管理后台：`http://localhost:8000/admin/`

## 📋 功能测试清单

### 1. 团队管理功能

#### ✅ 团队列表页面
- [ ] 显示所有测试团队
- [ ] 团队信息完整（名称、部门、负责人、成员数）
- [ ] 分页功能正常
- [ ] 搜索和筛选功能

#### ✅ 团队创建/编辑
- [ ] 新建团队表单验证
- [ ] 必填字段检查
- [ ] JIRA项目配置
- [ ] Git仓库配置
- [ ] 保存成功提示

#### ✅ 团队成员管理
- [ ] 添加成员功能
- [ ] 编辑成员信息
- [ ] 移除成员功能
- [ ] 角色分配正确

### 2. 权限管理功能

#### ✅ 权限列表
- [ ] 显示所有用户权限
- [ ] 权限级别显示正确
- [ ] 可访问团队数量统计

#### ✅ 权限授予
- [ ] 新用户权限创建
- [ ] 权限级别设置
- [ ] 团队访问权限配置
- [ ] 功能权限开关

#### ✅ 权限验证
- [ ] 不同权限级别访问控制
- [ ] 团队数据访问限制
- [ ] 功能按钮显示/隐藏

### 3. 仪表板功能

#### ✅ 数据展示
- [ ] 团队选择器正常工作
- [ ] 时间范围选择有效
- [ ] 指标卡片正确显示
- [ ] 数值计算准确

#### ✅ 交互功能
- [ ] 刷新数据按钮
- [ ] 导出数据功能
- [ ] 团队对比功能
- [ ] 响应式布局

#### ✅ 图表展示
- [ ] 趋势图表渲染
- [ ] 对比图表显示
- [ ] 图表交互功能
- [ ] 数据更新实时性

### 4. API接口测试

#### ✅ 团队API
```bash
# 获取团队列表
curl "http://localhost:8000/rd-metrics/api/teams/?user_email=<EMAIL>"

# 获取团队指标
curl "http://localhost:8000/rd-metrics/api/teams/test_chatbot_core/metrics/?days=30&user_email=<EMAIL>"
```

#### ✅ 对比API
```bash
# 团队对比
curl "http://localhost:8000/rd-metrics/api/teams/comparison/?team_ids=test_chatbot_core,test_chatbot_web&days=30&user_email=<EMAIL>"
```

#### ✅ 导出API
```bash
# 数据导出
curl "http://localhost:8000/rd-metrics/api/metrics/export/?team_ids=test_chatbot_core&days=30&format=json&user_email=<EMAIL>"
```

## 🔧 数据收集测试

### 1. 手动数据收集

```bash
# 测试单个团队数据收集
python manage.py collect_rd_metrics --team-id test_chatbot_core --type all --days 7 --verbose

# 测试所有团队数据收集
python manage.py collect_rd_metrics --all-teams --type jira --days 3 --verbose

# 测试快照创建
python manage.py collect_rd_metrics --create-snapshots weekly --verbose
```

### 2. 验证数据完整性

```python
# 在Django shell中验证数据
python manage.py shell

from app01.models import *
from datetime import date, timedelta

# 检查测试团队
teams = RDTeam.objects.filter(is_test_data=True)
print(f"测试团队数量: {teams.count()}")

# 检查JIRA指标数据
jira_metrics = RDJiraMetrics.objects.filter(is_test_data=True)
print(f"JIRA指标记录: {jira_metrics.count()}")

# 检查Git指标数据
git_metrics = RDGitMetrics.objects.filter(is_test_data=True)
print(f"Git指标记录: {git_metrics.count()}")

# 检查快照数据
snapshots = RDMetricsSnapshot.objects.filter(is_test_data=True)
print(f"快照记录: {snapshots.count()}")
```

## 📊 性能测试

### 1. 页面加载性能

使用浏览器开发者工具测试：

- [ ] 仪表板首页加载时间 < 3秒
- [ ] 团队切换响应时间 < 1秒
- [ ] 图表渲染时间 < 2秒
- [ ] API响应时间 < 500ms

### 2. 数据库查询性能

```python
# 在Django shell中测试查询性能
from django.test.utils import override_settings
from django.db import connection
from django.conf import settings

# 启用查询日志
settings.LOGGING['loggers']['django.db.backends']['level'] = 'DEBUG'

# 执行测试查询
from app01.rd_metrics.metrics_engine import RDMetricsCalculator
from app01.models import RDTeam
from datetime import date, timedelta

team = RDTeam.objects.filter(is_test_data=True).first()
calculator = RDMetricsCalculator(team)
end_date = date.today()
start_date = end_date - timedelta(days=30)

# 测试指标计算性能
import time
start_time = time.time()
metrics = calculator.calculate_comprehensive_metrics(start_date, end_date)
end_time = time.time()

print(f"指标计算耗时: {end_time - start_time:.2f}秒")
print(f"数据库查询次数: {len(connection.queries)}")
```

### 3. 并发测试

```bash
# 使用Apache Bench进行简单并发测试
ab -n 100 -c 10 "http://localhost:8000/rd-metrics/api/teams/?user_email=<EMAIL>"

# 或使用Python脚本进行并发测试
python -c "
import concurrent.futures
import requests
import time

def test_request():
    response = requests.get('http://localhost:8000/rd-metrics/api/teams/?user_email=<EMAIL>')
    return response.status_code

start_time = time.time()
with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
    futures = [executor.submit(test_request) for _ in range(50)]
    results = [future.result() for future in futures]

end_time = time.time()
success_count = sum(1 for r in results if r == 200)
print(f'成功请求: {success_count}/50, 总耗时: {end_time - start_time:.2f}秒')
"
```

## 🎭 用户验收测试

### 1. 角色测试场景

#### 管理员角色 (<EMAIL>)
- [ ] 访问所有功能页面
- [ ] 创建和编辑团队
- [ ] 管理用户权限
- [ ] 查看所有团队数据
- [ ] 导出数据
- [ ] 触发数据收集

#### 经理角色 (<EMAIL>)
- [ ] 查看仪表板
- [ ] 进行团队对比
- [ ] 导出数据
- [ ] 无法管理权限
- [ ] 无法配置团队

#### 团队负责人 (<EMAIL>)
- [ ] 查看自己团队数据
- [ ] 管理团队成员
- [ ] 无法查看其他团队
- [ ] 无法进行团队对比

#### 普通用户 (<EMAIL>)
- [ ] 只能查看基础指标
- [ ] 无法管理团队
- [ ] 无法导出数据
- [ ] 无法查看对比

### 2. 业务流程测试

#### 新团队创建流程
1. [ ] 管理员创建新团队
2. [ ] 添加团队成员
3. [ ] 配置JIRA项目和Git仓库
4. [ ] 授予团队负责人权限
5. [ ] 收集团队数据
6. [ ] 验证仪表板显示

#### 数据分析流程
1. [ ] 选择团队和时间范围
2. [ ] 查看关键指标
3. [ ] 分析趋势变化
4. [ ] 进行团队对比
5. [ ] 导出分析报告

## 🐛 常见问题排查

### 1. 页面无法访问

**问题**：访问仪表板返回403错误
**排查步骤**：
```bash
# 检查用户权限
python manage.py shell
from app01.models import RDMetricsPermission
perms = RDMetricsPermission.objects.filter(user_email='<EMAIL>')
print(perms.exists())
```

### 2. 数据不显示

**问题**：仪表板显示"暂无数据"
**排查步骤**：
```bash
# 检查测试数据
python manage.py shell
from app01.models import RDJiraMetrics, RDGitMetrics
print(f"JIRA数据: {RDJiraMetrics.objects.filter(is_test_data=True).count()}")
print(f"Git数据: {RDGitMetrics.objects.filter(is_test_data=True).count()}")
```

### 3. API返回错误

**问题**：API请求返回500错误
**排查步骤**：
```bash
# 查看Django日志
tail -f logs/django.log

# 检查数据库连接
python manage.py dbshell
```

## 🧹 测试清理

### 测试完成后清理

```bash
# 清理所有测试数据
python manage.py setup_rd_test_data --cleanup-test-data

# 重置数据库（如需要）
python manage.py flush --noinput

# 重新创建测试数据（如需要）
python manage.py setup_rd_test_data --all
```

## 📝 测试报告模板

### 测试结果记录

```
测试日期: ____
测试人员: ____
测试环境: ____

功能测试结果:
- 团队管理: ✅/❌
- 权限管理: ✅/❌
- 仪表板: ✅/❌
- API接口: ✅/❌

性能测试结果:
- 页面加载: ____秒
- API响应: ____ms
- 并发处理: ____/____

发现问题:
1. ____
2. ____

建议改进:
1. ____
2. ____
```

## 🎯 测试通过标准

系统测试通过需满足以下条件：

- [ ] 所有核心功能正常工作
- [ ] 权限控制有效
- [ ] 页面加载时间 < 3秒
- [ ] API响应时间 < 1秒
- [ ] 支持10个并发用户
- [ ] 数据计算准确
- [ ] 无严重安全漏洞
- [ ] 用户体验良好

通过以上测试后，系统即可投入生产使用。
