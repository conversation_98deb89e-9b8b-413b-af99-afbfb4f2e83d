# SPCT/SPCB测试环境Bug提醒功能部署指南

## 📋 功能概述

SPCT/SPCB测试环境Bug提醒功能是一个自动化的定时任务，用于监控和提醒SPCT/SPCB项目中测试环境的bug处理情况。

### 主要特性

- **智能筛选**: 只处理有SeaTalk群组的Epic相关的bug
- **分级提醒**: 根据bug优先级和未解决时间进行不同级别的提醒
- **人员映射**: 自动查找assignee对应的Team Leader
- **减少查询**: 优化的JIRA查询策略，减少API调用次数

## 🎯 提醒规则

1. **P0/highest/high优先级bug**: 1天未解决 → @assignee + TL
2. **P1/medium优先级bug**: 超过3天未解决 → @assignee + TL  
3. **测试超期bug**: 每天@assignee的TL（待实现）

## 🔧 技术实现

### 核心函数

```python
# 主要函数位置: app01/seatalk_group_manager.py

@track_cronjob_execution('check_spct_test_bugs')
def check_spct_test_bugs() -> None:
    """SPCT/SPCB测试环境bug提醒定时任务"""
    
def get_epic_groups_from_db() -> Dict[str, str]:
    """从数据库获取SPCB/SPCT项目的群组信息"""
    
def filter_bugs_by_priority_and_time(bugs: List[Dict], current_time: datetime) -> Dict[str, List[Dict]]:
    """根据优先级和时间过滤需要提醒的bug"""
    
def get_assignee_and_tl(assignee_email: str) -> Dict[str, str]:
    """根据assignee邮箱获取对应的TL信息"""
```

### 实现流程

1. **获取群组信息**: 从数据库查询SPCB/SPCT群组列表
2. **查询有效Epic**: 使用指定JQL筛选符合条件的Epic
3. **获取Epic交集**: 只处理既有群组又符合条件的Epic
4. **查询相关Bug**: 查询这些Epic的blocking bugs
5. **过滤和分组**: 按优先级和时间规则过滤bug
6. **发送提醒**: 向对应群组发送提醒消息

## 🚀 部署步骤

### 1. 代码部署

代码已经集成到现有系统中，包含以下文件修改：

- `app01/seatalk_group_manager.py`: 核心功能实现
- `app01/views.py`: Web接口包装器
- `app01/urls.py`: URL路由配置
- `djangoProject/settings.py`: Cron任务配置

### 2. 定时任务配置

已添加到CRONJOBS配置中：

```python
# 每天9、12、15、18点执行，SPCT/SPCB测试环境Bug提醒
('0 9,12,15,18 * * *', 'app01.views.cronjob_spct_test_bugs', 
 f'>> {os.path.join(LOGS_DIR, "cronjob_spct_test_bugs.log")} 2>&1'),
```

### 3. 手动测试接口

```bash
# 手动触发测试
curl -X GET "http://your-domain/api/spct-test-bugs/"
```

## 🐛 调试模式

### 调试模式特性

为了安全地测试Bug提醒功能，系统提供了调试模式：

- **调试模式开启**: 所有消息发送到指定的调试群（默认: SPCPM新功能调试群）
- **调试模式关闭**: 消息发送到对应的正式群组
- **调试信息**: 调试模式下会显示原始目标群组等额外信息

### 调试模式管理

#### 1. 通过脚本管理

```bash
# 查看当前调试模式状态
python3 manage_spct_bug_debug.py status

# 开启调试模式（发送到调试群）
python3 manage_spct_bug_debug.py enable

# 关闭调试模式（发送到正式群）
python3 manage_spct_bug_debug.py disable

# 显示API测试命令
python3 manage_spct_bug_debug.py api
```

#### 2. 通过API管理

```bash
# 获取调试模式状态
curl -X GET "http://your-domain/api/spct-bug-debug/"

# 开启调试模式
curl -X POST "http://your-domain/api/spct-bug-debug/" \
  -H "Content-Type: application/json" \
  -d '{"debug_mode": true}'

# 关闭调试模式
curl -X POST "http://your-domain/api/spct-bug-debug/" \
  -H "Content-Type: application/json" \
  -d '{"debug_mode": false}'
```

### 调试流程建议

1. **开启调试模式**: 确保消息发送到调试群
2. **手动触发测试**: 通过API或脚本触发Bug提醒
3. **验证消息内容**: 检查调试群中的消息格式和内容
4. **调整配置**: 根据需要修改TEAM_DATA或其他配置
5. **关闭调试模式**: 确认无误后切换到正式模式

## 🧪 测试验证

### 自动测试

运行提供的测试脚本：

```bash
python3 test_spct_bug_reminder.py
```

### 手动测试步骤

1. **验证群组数据**: 确保数据库中有SPCB/SPCT群组数据
2. **验证JIRA连接**: 确认JIRA token有效且有权限
3. **验证SeaTalk**: 确认SeaTalk API可以正常发送消息
4. **验证Team数据**: 确认TEAM_DATA中有正确的人员映射
5. **测试调试模式**: 使用调试模式验证消息发送功能

## 📊 监控和日志

### 日志文件

- **执行日志**: `logs/cronjob_spct_test_bugs.log`
- **详细调试**: 在代码中使用`ic()`函数输出调试信息

### 监控指标

- 任务执行成功率
- 处理的Epic数量
- 发送的提醒消息数量
- JIRA查询耗时

## ⚙️ 配置参数

### TEAM_DATA配置

在`app01/seatalk_group_manager.py`中维护团队和TL的映射关系：

```python
TEAM_DATA = {
    "ChatSS": {
        "leaders": ["<EMAIL>"],
        "members": [...],
        "project": "SPCT"
    },
    # 更多团队配置...
}
```

### JQL查询条件

Epic筛选条件：
```sql
project in (SPCB, SPCT) AND 
issuetype = Epic AND 
resolution = Unresolved AND 
"Project Type" = "Feature Project" AND 
status not in (Done, Closed, Waiting, Icebox) AND 
createdDate >= 2025-1-1 AND 
"Planned Integration End Date" >= startOfMonth(-1)
```

Bug筛选条件：
```sql
project in (SPCB, SPCT) AND 
issuetype = Bug AND 
priority IN (P0, P1, highest, high, medium) AND 
status not in (Done, Closed) AND
"Epic Link" in (epic_keys)
```

## 🔒 安全和权限

### 所需权限

- **JIRA权限**: 能够查询SPCB/SPCT项目的Epic和Bug
- **SeaTalk权限**: 能够向群组发送消息
- **数据库权限**: 能够读取SeatalkGroup表

### 敏感信息

- JIRA Token: 配置在`app01/config.py`中
- SeaTalk Access Token: 动态获取

## 🚨 故障排除

### 常见问题

1. **没有找到群组**
   - 检查数据库中SeatalkGroup表是否有SPCB/SPCT数据
   - 验证群组名称格式是否正确

2. **JIRA查询失败**
   - 检查JIRA token是否有效
   - 验证JQL语句是否正确
   - 检查网络连接

3. **消息发送失败**
   - 检查SeaTalk token是否有效
   - 验证群组ID是否存在
   - 检查消息格式是否正确

### 调试方法

1. **查看日志**: 检查cron执行日志
2. **手动测试**: 使用测试脚本验证各个组件
3. **分步调试**: 单独测试各个函数

## 📈 后续优化

### 计划改进

1. **测试完成时间检查**: 实现超过测试完成时间的bug提醒
2. **消息模板优化**: 提供更丰富的消息格式
3. **统计报告**: 添加bug处理统计和趋势分析
4. **配置界面**: 提供Web界面管理团队和提醒规则

### 扩展性

- 支持更多项目类型
- 支持自定义提醒规则
- 支持多种通知渠道
- 支持更复杂的人员映射逻辑

## 📞 联系方式

如有问题，请联系：
- 开发负责人: <EMAIL>
- 默认TL: <EMAIL>