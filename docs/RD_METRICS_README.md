# R&D效率指标仪表板系统

## 📋 系统概述

R&D效率指标仪表板是一个综合性的研发效率监控和分析系统，旨在帮助团队和管理者了解研发过程中的关键指标，提升团队效率和产品质量。

### 🎯 主要功能

- **团队管理**：支持多层级团队结构，灵活的成员管理
- **指标监控**：实时收集和展示JIRA、Git等数据源的关键指标
- **数据可视化**：直观的仪表板界面，支持趋势分析和团队对比
- **权限控制**：基于邮箱的细粒度权限管理
- **数据导出**：支持多种格式的数据导出功能

### 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API       │    │   数据源        │
│                 │    │                 │    │                 │
│ - 仪表板        │◄──►│ - Django Views  │◄──►│ - JIRA API      │
│ - 团队管理      │    │ - REST API      │    │ - Git API       │
│ - 权限管理      │    │ - 数据处理      │    │ - 数据库        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   数据存储      │
                       │                 │
                       │ - PostgreSQL    │
                       │ - Redis缓存     │
                       │ - 文件存储      │
                       └─────────────────┘
```

## 🚀 快速开始

### 1. 环境准备

确保已安装以下依赖：
- Python 3.8+
- Django 4.0+
- PostgreSQL 12+
- Redis (可选，用于缓存)

### 2. 数据库迁移

```bash
# 创建数据库迁移
python manage.py makemigrations

# 执行迁移
python manage.py migrate
```

### 3. 创建测试数据

```bash
# 创建测试团队和权限
python manage.py setup_rd_test_data --all

# 或分步创建
python manage.py setup_rd_test_data --create-teams
python manage.py setup_rd_test_data --create-permissions
python manage.py setup_rd_test_data --create-sample-data
```

### 4. 启动服务

```bash
python manage.py runserver
```

访问 `http://localhost:8000/rd-metrics/` 查看仪表板

## 📊 核心指标说明

### 开发速度指标
- **团队速度**：完成的故事点总数
- **吞吐量**：每天解决的问题数量
- **交付效率**：承诺故事点的完成率

### 质量指标
- **Bug修复率**：Bug修复的比例
- **缺陷密度**：每个故事点的Bug数量
- **返工率**：需要返工的问题比例

### 周期时间指标
- **平均周期时间**：从开始开发到完成的时间
- **平均前置时间**：从需求提出到交付的时间

### 团队效能指标
- **人均生产力**：人均完成的故事点数
- **代码贡献度**：人均提交次数和代码行数
- **协作效率**：MR合并率和审查时间

## 🔧 系统配置

### 1. 团队配置

通过管理界面或API配置团队信息：

```python
# 创建团队
team = RDTeam.objects.create(
    team_id='TEAM001',
    team_name='核心开发团队',
    department='技术部',
    team_leader_email='<EMAIL>',
    jira_projects=['PROJ1', 'PROJ2'],
    git_repositories=['repo1', 'repo2']
)

# 添加团队成员
RDTeamMember.objects.create(
    team=team,
    member_email='<EMAIL>',
    member_name='开发者',
    role='developer',
    join_date=date.today()
)
```

### 2. 权限配置

```python
# 创建权限
RDMetricsPermission.objects.create(
    user_email='<EMAIL>',
    permission_level='manager',
    can_view_team_comparison=True,
    can_export_data=True,
    granted_by='<EMAIL>'
)
```

### 3. 数据收集配置

```bash
# 收集单个团队数据
python manage.py collect_rd_metrics --team-id TEAM001 --type all --days 30

# 收集所有团队数据
python manage.py collect_rd_metrics --all-teams --type jira --days 7

# 创建定期快照
python manage.py collect_rd_metrics --create-snapshots weekly
```

## 📈 数据收集和处理

### 数据源

1. **JIRA数据**
   - 项目：SPCB、SPCT
   - 问题类型：Story、Task、Bug、Epic
   - 状态跟踪：创建、进行中、已解决、重新打开
   - 自定义字段：故事点、优先级等

2. **Git数据**
   - 基于 `services_id.json` 配置的仓库
   - 提交信息：次数、代码行数、作者
   - 合并请求：创建、审查、合并时间
   - 分支管理：主分支、功能分支合并

### 数据处理流程

```
数据收集 → 数据清洗 → 指标计算 → 数据存储 → 可视化展示
    ↓           ↓           ↓           ↓           ↓
JIRA/Git    验证格式    计算衍生指标   PostgreSQL   仪表板
   API      异常检测    趋势分析      Redis缓存    图表展示
```

### 定时任务

建议设置以下定时任务：

```bash
# 每日数据收集 (凌晨2点)
0 2 * * * python manage.py collect_rd_metrics --all-teams --type all --days 1

# 每周快照创建 (周一凌晨3点)
0 3 * * 1 python manage.py collect_rd_metrics --create-snapshots weekly

# 每月快照创建 (每月1号凌晨4点)
0 4 1 * * python manage.py collect_rd_metrics --create-snapshots monthly

# 数据清理 (每月15号凌晨5点，保留2年数据)
0 5 15 * * python manage.py collect_rd_metrics --cleanup --keep-days 730
```

## 🔐 权限管理

### 权限级别

1. **Viewer（查看者）**
   - 查看个人/团队指标
   - 基础数据访问

2. **Team Lead（团队负责人）**
   - Viewer权限
   - 配置团队信息
   - 管理团队成员

3. **Manager（经理）**
   - Team Lead权限
   - 查看团队对比
   - 导出数据

4. **Admin（管理员）**
   - 所有权限
   - 用户权限管理
   - 系统配置

### 权限控制实现

```python
def check_rd_metrics_permission(user_email, required_level='viewer'):
    """检查用户权限"""
    try:
        permission = RDMetricsPermission.objects.get(
            user_email=user_email,
            is_active=True
        )
        return permission.has_permission(required_level)
    except RDMetricsPermission.DoesNotExist:
        return False
```

## 🎨 前端界面

### 主要页面

1. **仪表板首页** (`/rd-metrics/`)
   - 团队选择和时间范围控制
   - 关键指标卡片展示
   - 趋势图表和对比分析

2. **团队管理** (`/rd-metrics/teams/`)
   - 团队列表和详情
   - 成员管理
   - 配置编辑

3. **权限管理** (`/rd-metrics/permissions/`)
   - 用户权限列表
   - 权限授予和撤销

4. **指标说明** (`/rd-metrics/help/`)
   - 详细的指标定义和计算方法
   - 使用指南

### 响应式设计

- 支持桌面端和移动端访问
- 自适应布局和交互
- 触摸友好的操作界面

## 🔍 API接口

### 主要API端点

```
GET  /rd-metrics/api/teams/                     # 获取团队列表
GET  /rd-metrics/api/teams/{team_id}/metrics/   # 获取团队指标
GET  /rd-metrics/api/teams/comparison/          # 团队对比数据
GET  /rd-metrics/api/metrics/export/            # 数据导出
POST /rd-metrics/api/data/collect/              # 触发数据收集
```

### API使用示例

```javascript
// 获取团队指标
fetch('/rd-metrics/api/teams/TEAM001/metrics/?days=30')
  .then(response => response.json())
  .then(data => {
    console.log('团队指标:', data);
  });

// 团队对比
fetch('/rd-metrics/api/teams/comparison/?team_ids=TEAM001,TEAM002&days=30')
  .then(response => response.json())
  .then(data => {
    console.log('对比结果:', data);
  });
```

## 🧪 测试

### 单元测试

```bash
# 运行所有测试
python manage.py test app01.rd_metrics

# 运行特定测试
python manage.py test app01.rd_metrics.tests.test_models
python manage.py test app01.rd_metrics.tests.test_views
python manage.py test app01.rd_metrics.tests.test_services
```

### 测试数据管理

```bash
# 创建测试数据
python manage.py setup_rd_test_data --all

# 清理测试数据
python manage.py setup_rd_test_data --cleanup-test-data
```

## 📝 维护和监控

### 日志监控

系统使用Python logging模块记录关键操作：

```python
import logging
logger = logging.getLogger(__name__)

# 数据收集日志
logger.info(f"开始收集团队 {team_id} 的指标数据")
logger.error(f"数据收集失败: {error_message}")
```

### 性能优化

1. **数据库优化**
   - 合理的索引设计
   - 查询优化
   - 分页处理

2. **缓存策略**
   - Redis缓存热点数据
   - 页面级缓存
   - API响应缓存

3. **异步处理**
   - 后台任务队列
   - 数据收集异步化
   - 大数据量处理优化

### 故障排查

常见问题和解决方案：

1. **JIRA连接失败**
   - 检查token有效性
   - 验证网络连接
   - 确认API权限

2. **数据不一致**
   - 检查数据源配置
   - 验证计算逻辑
   - 重新收集数据

3. **权限问题**
   - 确认用户邮箱正确
   - 检查权限配置
   - 验证团队访问权限

## 🔄 版本更新

### 数据库迁移

```bash
# 创建迁移文件
python manage.py makemigrations app01 --name update_rd_metrics

# 执行迁移
python manage.py migrate
```

### 配置更新

更新系统配置时注意：
- 备份现有数据
- 测试新功能
- 逐步部署更新

## 📞 技术支持

如有问题或建议，请联系：
- 技术负责人：<EMAIL>
- 系统文档：[内部Wiki链接]
- 问题反馈：[JIRA项目链接]
