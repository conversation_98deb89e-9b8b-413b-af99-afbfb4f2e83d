# 🧠 智能通知任务管理功能

## 概述

智能通知任务管理功能是对现有定时任务系统的重大升级，提供了基于 assignee 的智能通知分发、消息合并、智能内容生成等高级功能。

## 🌟 核心特性

### 1. 智能分组通知
- **按 assignee 自动分组**: 自动将 JIRA 查询结果按照 assignee 分组
- **个性化消息**: 为每个 assignee 生成专门的通知消息
- **支持未分配项目**: 自动处理没有 assignee 的 ticket

### 2. 智能消息合并
- **同人消息合并**: 将同一个 assignee 的多个 ticket 合并到一条消息中
- **减少通知干扰**: 避免频繁的单独通知
- **可配置合并规则**: 支持开启/关闭消息合并功能

### 3. 智能内容生成
- **多种消息模板**: 支持每日提醒、Bug 警告、子任务提醒等不同模板
- **自动模板选择**: 根据任务名称和描述自动选择合适的消息模板
- **丰富的格式化**: 包含优先级图标、链接、时间戳等信息

### 4. 灵活的通知方式
- **私聊通知**: 直接发送给相关人员
- **群聊通知**: 发送到指定的群组
- **混合通知**: 同时发送私聊和群聊
- **自动选择**: 根据配置自动判断通知方式

## 🚀 快速开始

### 基础使用

1. **创建智能通知任务**:
   ```
   schedule create "每日Bug提醒" "assignee = currentUser() AND status != Closed" "每天 10:00" smart
   ```

2. **查看任务列表**:
   ```
   schedule list
   ```

3. **管理任务**:
   ```
   schedule pause 123
   schedule resume 123
   schedule delete 123
   ```

### 高级配置

1. **带消息合并的智能任务**:
   ```
   schedule create "子任务智能通知" "issuetype = Subtask AND project = SPCB" "工作日 09:00" assignee merge
   ```

2. **支持群聊的智能任务**:
   ```
   schedule create "团队项目提醒" "project in (SPCB, SPCT)" "每天 14:00" smart group
   ```

## 📋 配置选项

### 通知目标类型 (target_type)
- `private`: 仅私聊通知
- `group`: 仅群聊通知
- `both`: 同时发送私聊和群聊
- `auto`: 自动判断（默认）

### 消息模板 (message_template)
- `daily_reminder`: 每日提醒模板
- `bug_alert`: Bug 警告模板
- `subtask_reminder`: 子任务提醒模板
- `general`: 通用模板（默认）

### 其他配置
- `merge_messages`: 是否合并消息（默认 true）
- `smart_content`: 是否启用智能内容生成（默认 true）
- `group_mapping`: 项目到群组的映射配置

## 🎯 使用场景

### 1. 每日工作提醒
```
schedule create "每日工作提醒" "assignee = currentUser() AND status in ('In Progress', 'To Do')" "每天 09:00" smart
```

### 2. Bug 追踪提醒
```
schedule create "高优先级Bug提醒" "issuetype = Bug AND priority in ('Highest', 'High') AND assignee is not EMPTY" "每天 10:00" smart merge
```

### 3. 项目进度跟踪
```
schedule create "项目进度跟踪" "project = SPCB AND fixVersion = '2024.1' AND status != Closed" "工作日 17:00" smart both
```

### 4. 团队协作提醒
```
schedule create "团队协作提醒" "project in (SPCB, SPCT) AND status = 'Code Review'" "每天 14:00" smart group
```

## 🔧 技术实现

### 核心组件

1. **SmartMessageGenerator**: 智能消息生成器
   - 支持多种消息模板
   - 自动检测消息类型
   - 格式化 ticket 列表

2. **JiraResultProcessor**: JIRA 结果处理器
   - 按 assignee 分组 ticket
   - 提取 assignee 信息
   - 确定通知目标

3. **AdvancedTaskManager**: 高级任务管理器
   - 创建智能通知任务
   - 执行智能通知逻辑
   - 发送通知消息

### 数据流程

1. **任务创建**: 用户通过聊天机器人创建智能通知任务
2. **任务调度**: 系统按照设定的时间执行任务
3. **JIRA 查询**: 执行 JQL 查询获取相关 ticket
4. **结果处理**: 按 assignee 分组并处理结果
5. **消息生成**: 为每个 assignee 生成个性化消息
6. **通知发送**: 根据配置发送私聊或群聊通知

## 📊 消息示例

### Bug 警告消息
```
🐛 每日Bug提醒 - John Doe

发现 2 个需要处理的Bug：
• 🔴 [SPCB-1234](https://jira.shopee.io/browse/SPCB-1234) - Fix login authentication bug (In Progress)
• 🟠 [SPCB-5678](https://jira.shopee.io/browse/SPCB-5678) - Optimize database query performance (Open)

⚠️ 请及时处理
```

### 子任务提醒消息
```
📋 子任务智能通知 - Mary Smith

您有以下 3 个子任务需要关注：
• 🟡 [SPCB-9999](https://jira.shopee.io/browse/SPCB-9999) - Update user interface components (To Do)
• 🟢 [SPCB-1010](https://jira.shopee.io/browse/SPCB-1010) - Write unit tests for API (In Progress)
• ⚪ [SPCB-1111](https://jira.shopee.io/browse/SPCB-1111) - Code review for feature X (Code Review)

📌 请合理安排工作时间
```

## 🛠️ 开发指南

### 使用管理工具

智能通知功能提供了专门的管理工具 `tools/smart_notification_manager.py`：

```bash
# 列出所有智能通知任务
python3 tools/smart_notification_manager.py list

# 测试指定的智能通知任务
python3 tools/smart_notification_manager.py test --task-id 123

# 交互式创建智能通知任务
python3 tools/smart_notification_manager.py create

# 管理白名单权限
python3 tools/smart_notification_manager.py whitelist --action add --user-id 1373330284
python3 tools/smart_notification_manager.py whitelist --action list
python3 tools/smart_notification_manager.py whitelist --action remove --user-id 1373330284
```

### 异步兼容性

所有智能通知功能都已实现异步兼容：

```python
# 创建智能通知任务
config = {
    'type': 'assignee_based',
    'target_type': 'private',
    'merge_messages': True,
    'smart_content': True,
    'message_template': 'daily_reminder'
}

result = await advanced_task_manager.create_smart_notification_task(
    user_id="test_user",
    user_email="<EMAIL>",
    task_name="测试任务",
    jql_query="project = TEST",
    schedule_time="10:00",
    notification_config=config
)
```

### 开发环境调试

在开发环境中，可以设置环境变量跳过权限检查：

```bash
export DJANGO_DEBUG=true
```

这样可以在没有数据库连接或白名单配置的情况下测试功能。

## 🔒 权限要求

- 需要在 `AdvancedTaskFeatureWhitelist` 中添加 `smart_notification` 权限
- 群聊通知需要机器人有相应群组的发送权限

## 📝 最佳实践

1. **精确的 JQL 查询**: 使用具体的查询条件来避免不必要的通知
2. **合理的执行时间**: 避免在非工作时间发送通知
3. **测试先行**: 先使用普通任务测试 JQL 查询，再创建智能任务
4. **定期维护**: 定期检查和清理无用的任务
5. **权限管理**: 严格控制智能通知功能的使用权限

## 🚨 注意事项

- 确保 JQL 查询返回的结果包含 assignee 信息
- 智能通知功能会增加系统负载，请合理使用
- 建议在测试环境中先验证功能后再在生产环境使用
- 群聊通知可能会影响群聊成员，请谨慎使用

## 🔧 技术实现细节

### 数据库操作异步化

为了解决异步上下文中的数据库操作问题，所有数据库操作都使用 `sync_to_async` 进行包装：

```python
from asgiref.sync import sync_to_async

# 包装数据库查询
get_whitelist = sync_to_async(
    lambda: AdvancedTaskFeatureWhitelist.objects.get(
        user_id=user_id, 
        is_active=True
    )
)

# 包装模型方法
has_feature = sync_to_async(whitelist.has_feature)
```

### 智能消息生成算法

消息生成器使用以下算法自动选择合适的模板：

1. **关键词匹配**: 检查任务名称和描述中的关键词
2. **优先级判断**: 根据 ticket 的优先级选择合适的图标
3. **内容格式化**: 自动截断长标题，添加链接和时间戳

### 错误处理机制

系统实现了多层错误处理：

1. **权限检查失败**: 在开发环境自动跳过，生产环境返回错误信息
2. **消息生成失败**: 自动降级到简单消息格式
3. **通知发送失败**: 记录警告日志，继续处理其他通知

## 🛠️ 故障排除

### 常见问题

1. **权限检查失败**
   - 确保用户在白名单中
   - 检查数据库连接是否正常
   - 开发环境可设置 `DJANGO_DEBUG=true` 跳过权限检查

2. **消息发送失败**
   - 检查 employee_code 是否正确
   - 确认私聊机器人权限
   - 验证群聊机器人权限

3. **JIRA 查询无结果**
   - 验证 JQL 语法是否正确
   - 检查查询权限
   - 确认 assignee 字段是否存在

### 调试工具

使用管理工具进行调试：

```bash
# 测试特定任务
python3 tools/smart_notification_manager.py test --task-id 123

# 查看详细日志
tail -f logs/smart_notification.log
```

## 📈 性能优化

### 批量处理

智能通知功能支持批量处理多个 assignee，减少数据库查询次数：

```python
# 一次性处理所有分组
grouped_tickets = self.result_processor.group_tickets_by_assignee(jira_results)
merged_messages = await self.message_generator.generate_merged_messages(
    grouped_tickets, task_name, task_description
)
```

### 缓存机制

- 消息模板缓存：避免重复解析模板
- Assignee 信息缓存：减少重复的用户信息查询
- 群组映射缓存：优化群聊通知性能

## 🚀 扩展开发

### 添加新的消息模板

在 `SmartMessageGenerator.MESSAGE_TEMPLATES` 中添加新模板：

```python
MESSAGE_TEMPLATES = {
    'custom_template': {
        'title': '📌 {task_name} - 自定义提醒',
        'content': '您有 {count} 个自定义项目：\n{ticket_list}',
        'footer': '🔗 查看更多详情'
    }
}
```

### 扩展通知方式

在 `AdvancedTaskManager` 中添加新的通知方法：

```python
async def _send_custom_notification(self, assignee_email: str, message: str):
    """发送自定义通知"""
    # 实现自定义通知逻辑
    pass
```

### 集成外部系统

可以扩展智能通知功能与其他系统集成：

- **Slack 通知**: 添加 Slack webhook 支持
- **邮件通知**: 集成邮件发送功能
- **微信通知**: 支持企业微信机器人

## 🚀 部署说明

### 启动定时任务调度器

智能通知功能需要定时任务调度器在后台运行。详细的启动和管理说明请参考：

📖 **[定时任务调度器使用说明](../app01/ai_module/TASK_SCHEDULER_README.md)**

快速启动：
```bash
# 启动调度器
./task_scheduler.sh start

# 查看状态
./task_scheduler.sh status
```

### 环境变量配置

在生产环境中建议设置以下环境变量：

```bash
# Django 设置
export DJANGO_SETTINGS_MODULE=djangoProject.settings

# 禁用调试输出（可选）
export IC_DISABLE=1

# 数据库配置
export DB_HOST=your_db_host
export DB_PORT=your_db_port
export DB_NAME=your_db_name
export DB_USER=your_db_user
export DB_PASSWORD=your_db_password
```

---

更多技术细节请参考源代码：
- `app01/ai_module/advanced_task_manager.py`
- `app01/ai_module/task_scheduler.py`
- `tools/smart_notification_manager.py` 