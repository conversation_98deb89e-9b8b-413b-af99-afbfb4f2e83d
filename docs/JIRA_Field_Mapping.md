customfield_23700 : Business Module (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17021 : Perf. Bm. Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_23701 : Bug Method (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17020 : Planned Perf. Bm. Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_17025 : Creative Output (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_17024 : Category Focus (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_17023 : Request Type - SPCRP (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17022 : Perf. Bm. Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_17029 : Requestor's Reporting Manager (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_17028 : Creative Assets (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_17027 : Comprehensive Design Brief (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_17026 : Design Direction/Theme (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
resolution : Resolution
customfield_12800 : SPPSP Time To Response (com.atlassian.servicedesk:sd-sla-field)
customfield_30000 : Request Product (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_30001 : User Email Access (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_30002 : Head of Department (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_43306 : App version (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_43307 : Speed test (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_43302 : Streamers Device (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_43303 : Viewers UID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_43304 : Viewers Device (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_43305 : Session id (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_17010 : QA Score (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_18100 : Request Type (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:select)
lastViewed : Last Viewed
customfield_18101 : Analysis Type (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_18102 : Effort (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_45501 : SPS Sub Product Line (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_45500 : Quality_Remarks (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_18103 : Report Tool (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17014 : Data Coll. Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_17013 : Data Coll. Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_17012 : Planned Data Coll. Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_17011 : Planned Data Coll. Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_17018 : Data Lbl. Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_17017 : Data Lbl. Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_17016 : Planned Data Lbl. Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_17015 : Planned Data Lbl. Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_18104 : Status (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_18105 : Status PIC (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17019 : Planned Perf. Bm. Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_31100 : CIS Problem Source Type (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_35700 : Request Supporting Product Line (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_11700 : Service Affected (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_31102 : Sign off status (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_31101 : Chatbot-model Change (com.atlassian.jira.plugin.system.customfieldtypes:select)
aggregatetimeoriginalestimate : Σ Original Estimate
issuelinks : Linked Issues
customfield_21505 : Original story points (com.atlassian.jpo:jpo-custom-field-original-story-points)
customfield_21504 : Target end (com.atlassian.jpo:jpo-custom-field-baseline-end)
customfield_21503 : Target start (com.atlassian.jpo:jpo-custom-field-baseline-start)
customfield_21502 : Parent Link (com.atlassian.jpo:jpo-custom-field-parent)
customfield_21501 : Linked major incidents (com.atlassian.servicedesk.incident-management-plugin:sd-incidents-link)
customfield_21500 : Team (com.atlassian.teams:rm-teams-custom-field-team)
customfield_25900 : TD Skip Approved By (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_25901 : Document Ticket Link (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_46600 : test (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17003 : JIRA Issue/s (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_42003 : Task URL (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_42002 : Report ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_42001 : Period of Data (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_42000 : Metrics Needed (please separate by commas if more than one metric) (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_17000 : Time to close after rejection (com.atlassian.servicedesk:sd-sla-field)
customfield_17007 : QA Task Score (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_35708 : Root Cause Category (MPDOD) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_31108 : Standby Duration ( Mins ) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17006 : Reporter Team (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_35707 : Request only applicable for the target country (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_31109 : RollBack Duration ( Mins ) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_31104 : Branch name (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_35704 : Request Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_31103 : Redis Change (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17009 : Dev Score (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_31106 : Provide Standby Channels for this Operation (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_35702 : Submission Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_31105 : Apollo/Config center status (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17008 : Delay Remarks (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_32200 : Request Environment (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_36800 : Test Case ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_10600 : Project Manager (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_20400 : Satisfaction (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_42010 : Expected Delivery Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_47700 : PIC Team (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_43100 : [POP] Dev Effort (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_47701 : Support Team (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_43101 : [POP] QA Effort (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_43102 : Local UAT Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_43103 : Local UAT Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
subtasks : Sub-Tasks
customfield_42011 : Layer 1 Approval (Reporting Manager) (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_37900 : Shopee Vision SLA (com.atlassian.servicedesk:sd-sla-field)
customfield_42007 : Internal Ticket type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_42006 : Data Output Format (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_42005 : Modification Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_42004 : Reference Ticket URL (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_42009 : Internal Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_23500 : Sub-task Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_42008 : Layer 2 Approval (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_44200 : L1 Category (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
issuetype : Issue Type
customfield_44201 : L2 Category (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_18317 : End Date (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_18318 : Revision (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_19402 : RAM Team Code (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_18316 : Begin Date (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_34401 : Site (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_34402 : Email Recipient (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_34400 : Channel (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_22801 : Requestors (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_22800 : Frequency (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_24600 : App Name (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_47702 : Support Team (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_45300 : RAP Service/s (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_45303 : Data Labelling Effort(Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_45302 : Estimated Data Labelling Effort(Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_21700 : Root Cause Category (SPS - Bug) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_25700 : Omission Reason (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17201 : Applicant (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_17200 : Caused By (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
timetracking : Time Tracking
customfield_36602 : QP QA Effort (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_36601 : QP Dev Effort (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_36600 : Planned Milestone (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_10800 : Development (com.atlassian.jira.plugins.jira-development-integration-plugin:devsummary)
customfield_32000 : Pass Live Testing Financial Risk Sign Off (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_20604 : Live security bugs (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_20602 : Testing (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_20603 : Security vulnerabilities (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_20601 : Design flaws identified (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_26801 : If involve the transfer of PII data/ 是否涉及PII (姓名，手机号，地址等信息，详细可参考 https://n.shp.ee/pii )数据传输 (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_26800 : If involve modifications of source code/ 是否涉及代码变动 (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_26803 : If involve payment and transaction(funds, points, gold coins, etc.)/ 是否涉及资金、积分、金币等支付交易功能 (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_26802 : If are only modifications of Front-End style or copywriting/ 是否仅为前端样式或文案修改 (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_47500 : Workstream (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_16101 : Rollback Triggers (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_15011 : Workaround Description (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_16100 : Rollback Duration (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_15010 : Workaround Available (com.atlassian.jira.plugin.system.customfieldtypes:select)
environment : Environment
customfield_37700 : Priority SLA (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_33100 : Time to L1 Response (com.atlassian.servicedesk:sd-sla-field)
customfield_27900 : Role(s) (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_23303 : CMDB Service Name (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_23302 : SLO Description (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_23301 : SLO Name (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_23300 : Background (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_33901 : EKS Problem Category (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_33902 : Submit test report URL (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_12411 : Project Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_33900 : EKS Problem Source Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12410 : Roadmap feature (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12404 : BRD End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11557 : Remarks (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11556 : Expected System Behaviour (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_12403 : Planned BRD Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12406 : Planned PRD Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12405 : BRD Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11558 : Entity (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_12408 : PRD Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12407 : Planned PRD End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12409 : PRD End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_43702 : MR Link (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_21115 : Automation Test Key (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_22203 : Design End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_22202 : Design Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_22201 : Planned Design End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_22200 : Planned Design Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_21111 : TC Group (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_11551 : Business Impact (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_12640 : Service Impact Category (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12400 : Code Review before QA (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_12642 : Name of Tech Lead (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_11553 : User Account (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_11552 : FRF Progress (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12641 : Name of Product Manager (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_30400 : RN - Time to ACK (com.atlassian.servicedesk:sd-sla-field)
customfield_11555 : Actual System Behaviour (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_12402 : Planned BRD End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_30401 : RN - Time to Resolution (com.atlassian.servicedesk:sd-sla-field)
customfield_12401 : Delay Reason (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_12643 : Request Type (General) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_30402 : Rollback Plan Verified (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11554 : User Pwd (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_11546 : PRD Review End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12635 : Planned Integration End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11545 : PRD Review Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12634 : Planned Integration Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12637 : Integration End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11548 : Reporter Entity (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_15901 : Requestor (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_11547 : Affected Countries -SPS (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_12636 : Integration Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
timeestimate : Remaining Estimate
customfield_27000 : Created (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12638 : Data Tracking Checklist (com.okapya.jira.checklist:checklist)
customfield_11549 : Product Line (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_27002 : Depends on (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_27001 : Ideal days (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_27003 : Ticket Type (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_40201 : Blocked SLA (com.atlassian.servicedesk:sd-sla-field)
customfield_44800 : Complexity of Request (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11540 : Impact level (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11542 : Peer review (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_12631 : Request Details (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_31500 : Require UX Designer (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11541 : (New) Issue Distribution (com.atlassian.servicedesk:sd-sla-field)
customfield_12630 : Request Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11300 : groovyFunction (ru.mail.jira.plugins.groovy:groovy-jql-field)
customfield_11544 : Feasibility Study End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12633 : Is this project onboarded to MLP (com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons)
customfield_12632 : Expected Date of Completion (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11543 : Feasibility Study Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12624 : Data security - will the model use/transfer/retain any PII Data or non-public business data? (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_10203 : Reproducibility (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11535 : Worse case scenario (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_12623 : Please specify the productionalization requirements: (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_10204 : Steps to Reproduce (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11537 : Business Monitoring and Other Monitoring Dashboard used for this operation (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_12626 : Instance Requirements (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_10205 : Server Environment (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11536 : Action list (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_12625 : Deployment Cluster/Location (com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons)
customfield_10206 : Severity (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_10207 : Bug Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12628 : Other Requirements (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11539 : Target Countries (com.okapya.jira.checklist:checklist)
customfield_10208 : Bug Resolution (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12627 : Network Requirements (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11538 : Roll Back Plan (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_28100 : Keyword (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_12629 : Product Live Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41300 : Feature Priority (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_20000 : Primary KPI Metrics (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_20001 : Countries Applicable for A/B Test (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_12620 : Expected UAT Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_14800 : Affected CB Seller (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_10201 : Flagged (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_12622 : Productionalization Requirements (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_12621 : Expected Live Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_10202 : People Involved (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_12613 : Group Name (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11524 : SATOS CAB (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_11523 : Grafana Link (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_12612 : Remarks (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11526 : (New) Time to first response (com.atlassian.servicedesk:sd-sla-field)
customfield_12614 : Project Name (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_11525 : SATOS Change Manager (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_12617 : Stakeholders (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11527 : (New) Time to resolution (com.atlassian.servicedesk:sd-sla-field)
customfield_12619 : Link to PRD (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_12618 : Incident Escalation (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_42400 : Request Product Line (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_23900 : Caused By Role (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_23901 : Caused Phase (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_23902 : Regression Testing Suggestions (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_23903 : Region (com.atlassian.jira.plugin.system.customfieldtypes:select)
aggregatetimespent : Σ Time Spent
customfield_11520 : Planned Dev Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11522 : Planned UAT Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_13700 : Release Cycle (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12611 : Traffic Estimation (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_12610 : Capacity (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_33700 : Dev Effort (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11521 : Planned QA Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12602 : Hard Release Timeline (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_11513 : Planned Release Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12601 : Source Address (IP or container name) (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_11512 : UAT Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12604 : Start date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11515 : Target Country (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_11514 : Release Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12603 : Tech Lead (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_12606 : Baseline start date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11517 : QA Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11516 : Dev Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12605 : End date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12608 : Task progress (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_11519 : UAT Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12607 : Baseline end date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
workratio : Work Ratio
customfield_12609 : Task mode (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_43500 : PRD Change (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_43501 : Request has impact to Open API (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_43502 : Time to close after UAT (com.atlassian.servicedesk:sd-sla-field)
customfield_22400 : Task Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_41100 : Web Pentest Checklist (com.okapya.jira.checklist:checklist)
customfield_30201 : FE (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_30202 : BE (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_30203 : DS (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_11511 : Planned UAT Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12600 : Target Address (Domain/IP:Port) (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_34800 : Planned FRF End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11510 : Planned QA Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11502 : Source (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_11501 : URL (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_11504 : Affected Countries (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_11503 : Error Message (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11506 : Business Group (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11505 : Log Info (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11507 : Tech Specialist from Requestor (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_11509 : Planned Dev Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_44600 : AI Agent Knowledge Bots (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_17043 : Duration - SPCRP (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_17042 : Dimension in Pixels - SPCRP (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_21300 : PM lists (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_17041 : Media Placement - SPCRP (com.okapya.jira.checklist:checklist)
customfield_17047 : Media Placement - SPCRP (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_42200 : Data Output URL (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_17046 : Project Objective - SPCRP (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_17045 : Category - SPCRP (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17044 : Function (Photography) - SPCRP (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_35905 : Has Data Impact (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_35904 : Design Effort Required (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_35903 : Level of Reliance on PA Effort (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_35902 : Impact to Open API (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_35901 : PM PIC (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_11500 : Endpoint (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_35900 : UID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_10401 : Designer (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_10402 : PRD Reviewer (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_45700 : Total Native effort(Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_17032 : Campaign End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_17031 : Campaign Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_17030 : Function - SPCRP (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_43300 : Sync Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_43301 : Streamers UID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_17036 : Campaign Name - SPCRP (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_17035 : Campaign Type - SPCRP (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17034 : Type Post - SPCRP (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_17033 : Type of Project - SPCRP (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17039 : Video Type - SPCRP (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17038 : Type of Project (Campaign Feature) - SPCRP (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17037 : Design Reference (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_10400 : Checklist Proxy (com.okapya.jira.checklist:checklistReadOnly)
customfield_13900 : QPS (Normal) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_33500 : Bug Root Cause (com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect)
customfield_13902 : Client (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_13901 : QPS (Peak) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_13904 : Command (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_46804 : monitor_valid (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_46800 : Order SN (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_25207 : Affected Databases (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_25208 : Process Step (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_15400 : NOC (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_15401 : Target Market (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_41927 : Data Labelling Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41926 : Planned Live Testing Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41925 : Planned Live Testing Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41924 : Estimated Data Labelling Effort(Days) (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41929 : Model Training Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41928 : Data Labelling Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_27635 : Impacts System/App version (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_41923 : Planned ABT Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41922 : Planned ABT Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41921 : Need Integration? (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_41920 : Planned Model Training Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_30807 : Report Format (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_30803 : Include Financial Data (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_30804 : Include Platformwide Data (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_30805 : Data Share Externally (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_30806 : Aggregation of Metrics (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_18900 : Request Details (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_14300 : Sub-component (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_30800 : Financial Impact (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_18901 : impact_level_atlassian (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_30801 : Reputational Risk (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_30802 : Include Personal Information (com.atlassian.jira.plugin.system.customfieldtypes:select)
labels : Labels
customfield_13207 : Markets affected (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_13206 : Environment (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_17805 : Need AZ SRE for Review? (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_41936 : Pass Performance Evaluation? (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_13209 : Reporter Country (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_13208 : Country for Implementation (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_41935 : Data Labelling Effort(Days) (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_37200 : Priority (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17808 : STO Team (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_41930 : Model Training Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_27400 : Project Category (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_41934 : Live Testing Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41933 : Live Testing Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41932 : ABT Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41931 : ABT Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_40601 : Time Raise to Complete (com.atlassian.servicedesk:sd-sla-field)
customfield_38300 : CS BR Products SLA (com.atlassian.servicedesk:sd-sla-field)
customfield_12110 : Manually scheduled (WBSGantt) (com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons)
customfield_31900 : Monitor Valid (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_13201 : Sub-category (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_13200 : Category (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_12114 : Live Env. Sanity Check (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_13203 : Testing Objective (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_13202 : Data Extension Path (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_12113 : QA Team (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_13205 : Markets to be tested (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_13204 : Email (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_12107 : Progress (WBSGantt) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_12106 : Baseline finish date (WBSGantt) (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12109 : Units (WBSGantt) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_16704 : Live Test End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12108 : Milestone (WBSGantt) (com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons)
customfield_28501 : Vendor Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_28502 : Supplier Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_28500 : Vendor Info (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_41702 : Campaign Frequency (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_28503 : Delivery Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_41701 : User Stage (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_28504 : Currency (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_41700 : Product Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_28509 : Vendor Name (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_28507 : Payment Terms (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_28508 : Contract Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_39400 : On Hold Reason (com.atlassian.jira.plugin.system.customfieldtypes:select)
reporter : Reporter
customfield_12101 : Planned Start (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_39402 : Translation Tasks (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_12100 : Project Risks (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_39403 : SeaTalk Group Chat (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_16703 : Live Test Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12103 : Start date (WBSGantt) (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_39404 : UAT Checklist (com.okapya.jira.checklist:checklist)
customfield_12102 : Planned End (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_16702 : Planned Live Test End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_39405 : UAT Start Delay Reason (Task) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12105 : Baseline start date (WBSGantt) (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_16701 : Planned Live Test Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_39406 : UAT Extension Reason (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12104 : Finish date (WBSGantt) (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_39407 : PFB Info (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_16700 : TRD/PRD URL (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_42804 : SAST Language (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_27662 : Estimated Story Points (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_25000 : Resolutions (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_28510 : Business Registration Number (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_42803 : Security User Report Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_42802 : [POP] Request Submission Cycle (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_42801 : [POP] Planned Release Date (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_42800 : [POP] Estimated PRD Sign Off Date (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_39410 : UAT Start Delay Reason (Sub-Task) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_39411 : UAT Sign Off Delay Reason (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_39412 : Conditional Sign Off Reason (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_39413 : UAT Failed Reason (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_39414 : Opt Out Reason (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11002 : Dev Effort (days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_39415 : Roll out plan (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_16934 : Stakeholder Signoff (com.okapya.jira.checklist:checklist)
customfield_39416 : Main Epic to trigger UAT to Local/CB (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11003 : QA Effort (days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_15600 : Feature/Service Status (com.okapya.jira.checklist:checklist)
customfield_11004 : Operation Engineer (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_15601 : Earliest Public Live Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_16927 : Release Manager (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_16926 : Future Mitigation Plan (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_39409 : UAT Readiness Checklist (com.okapya.jira.checklist:checklist)
customfield_16925 : Platform (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_16924 : Adhoc Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_26100 : Objective (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_16929 : Release Version (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_16928 : Hotfix Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
watches : Watchers
customfield_30601 : RN Framework - Time to Resolution (com.atlassian.servicedesk:sd-sla-field)
customfield_16923 : Doc Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_16922 : Request Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_16921 : Supporting Information (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_14501 : Financial Risk (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_16920 : P-Priority (com.atlassian.jira.plugin.system.customfieldtypes:radiobuttons)
customfield_30600 : RN Framework - Time to ACK (com.atlassian.servicedesk:sd-sla-field)
customfield_16916 : Affected Text Type (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_16915 : Affected Markets/System (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_16913 : Simple Summary (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_16919 : Issue Details (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_16917 : Language Pair (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_37002 : Time to finish troubleshoot (com.atlassian.servicedesk:sd-sla-field)
customfield_37001 : Data Issue Module (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_37000 : Team Line (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_40401 : Time to Pickup (com.atlassian.servicedesk:sd-sla-field)
updated : Updated
customfield_40402 : Time to Complete (com.atlassian.servicedesk:sd-sla-field)
customfield_40403 : Time to Start (com.atlassian.servicedesk:sd-sla-field)
customfield_23200 : Hours spent (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_38100 : Email Signed Off By (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
timeoriginalestimate : Original Estimate
customfield_31702 : time to assign PIC (com.atlassian.servicedesk:sd-sla-field)
description : Description
customfield_11220 : Reporting Source (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11221 : Root Cause Category (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_31703 : time to waiting for support (com.atlassian.servicedesk:sd-sla-field)
customfield_11222 : Incident Checklist (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_16912 : PM List (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_13401 : PRD Preparation End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_16911 : Defect Escape Reason (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_13400 : PRD Preparation Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_16910 : Defect Inject Reason (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_31700 : Root Cause Category (SPS - Non-bug) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_13402 : Shop Ops Request (com.atlassian.servicedesk:sd-sla-field)
customfield_11215 : Incident Duration (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_16905 : Manpower Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11216 : Downtime (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_16904 : Manpower Role (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11217 : Incident Severity (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_16903 : Location Name and Function (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11218 : Supporting Materials (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11219 : Chat Log (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_16909 : UAT/Live Issue Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_16908 : NCA (Shopee Express Indonesia) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_28300 : Test Missing Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_16907 : Supplies (Shopee Express Indonesia) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_16906 : Department (Shopee Express Indonesia) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_41500 : Planned Release Version (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_22102 : Affected Team(s) (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_22101 : Affected CID(s) (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_22100 : Affected IDC(s) (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_39200 : DOD Issue Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_39201 : DOD Remarks (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_12301 : Development Review Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_16901 : Time for pending (com.atlassian.servicedesk:sd-sla-field)
customfield_11212 : Improvement Plan (com.okapya.jira.checklist:checklist)
customfield_12300 : Request Team (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_16900 : Time to submit to Business Team (com.atlassian.servicedesk:sd-sla-field)
customfield_11213 : Improvement Plan Proxy (com.okapya.jira.checklist:checklistReadOnly)
customfield_32800 : Reference No (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_12302 : Development Review End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11214 : Service (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_11204 : Start Date Time (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_11205 : End Date Time (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_11206 : Impact (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11207 : Timeline (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11208 : Root Cause (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11209 : Fix Action (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_42601 : Criteria (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_42600 : Infra Component (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_45213 : 模块 (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_45212 : 责任人 (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_15000 : Quality Score (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_15001 : Platform (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_46300 : Demo Count (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_45211 : 是否监控告警缺失 (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_45210 : 是否研发免测 (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_15004 : Operation CS Category (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_15005 : Operation CS Issue Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_15002 : Org (com.atlassian.jira.plugin.system.customfieldtypes:select)
fixVersions : Fix Version/s
customfield_15003 : Instance (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_19600 : Analysis Summary (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_15008 : Functionality (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_19601 : Business Feedback (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_15009 : Customers are Impacted (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_15006 : Basic Troubleshooting Completed (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_19602 : Request GitLab (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_15007 : Agents Impacted (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_36500 : From Created to Business Review (com.atlassian.servicedesk:sd-sla-field)
customfield_45209 : 是否漏测 (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_26700 : SDLC Checklist (com.okapya.jira.checklist:checklist)
customfield_45208 : Bug引入阶段 (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_45207 : Bug发现 (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_47403 : Ticket Guide Accuracy (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_47401 : Reporting Tool Identifier Accuracy (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_47402 : Reporting Tool Accuracy (com.atlassian.jira.plugin.system.customfieldtypes:float)
priority : Priority
customfield_18500 : Actionability (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_18501 : Requester (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_37600 : Missed QA (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_21900 : Local PIC (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_27800 : Shop Name (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_27802 : Official Last Day of PIC (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_27801 : Phone Number (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_47414 : List of Agents Affected (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_27803 : Category (Pre-Order) - SPMYSBS (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_47415 : Incentive Batch Affected (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_47412 : Biweekly Period Affected (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
issuekey : Key
customfield_47413 : Expected Duration of Monitoring (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_47410 : Reason for Exemption (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_47411 : Team / SBR Affected (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_17401 : DBA Operation Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17400 : Level of Business Impact (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_34102 : Feature Blocked Time (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_34101 : Environment Blocked Time (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_20800 : DA Support (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_47409 : Exemption Metric (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_20801 : Attachment (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_24300 : Vendor Site ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_47407 : Reporting Tool Accuracy (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_47408 : Ticket Guide Accuracy (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_47405 : Product Line Identifier (Auto) Accuracy (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_28900 : Case Study Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_47406 : Reporting Tool Identifier Accuracy (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_45000 : Domain (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_16302 : BRD Link (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_16301 : Planned Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_16300 : Planned Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_39800 : Document (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_35203 : Pure SLA (com.atlassian.servicedesk:sd-sla-field)
customfield_35200 : response (com.atlassian.servicedesk:sd-sla-field)
customfield_35201 : Tme to first response (KR) (com.atlassian.servicedesk:sd-sla-field)
customfield_47418 : Reason for Target Change (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_47416 : Nominal Structure Proposed (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_47417 : Estimated Budget Impact (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_46100 : Epic Type (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_15200 : Code Reviewers (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_19800 : Time to expiry (com.atlassian.servicedesk:sd-sla-field)
customfield_47204 : Dev Period调整值 (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_47203 : Reopen Count (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_47206 : UAT Period调整值 (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_47205 : Dev Period调整值（Reason） (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_47200 : Planned Dev Test Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
created : Created
customfield_47202 : Reopen Reason (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_47201 : Planned Dev Test Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_14100 : PM Effort (days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_18700 : Business Objective (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_18701 : Metrics (for BI) (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_40804 : Time to response - L2 (UAT) (com.atlassian.servicedesk:sd-sla-field)
customfield_40802 : Estimate Review Effort (days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_37403 : First Detected Channel (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_40803 : Time to resolution - L3 (UAT) (com.atlassian.servicedesk:sd-sla-field)
customfield_40800 : Feature Business Impact (SPMPIR) (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_40801 : Review Effort (days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_13000 : Application (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17600 : sub-team (com.coresoftlabs.component-picker:single-component-picker)
customfield_38500 : Notes (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_41905 : FE Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41904 : Planned BE Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41903 : Planned BE Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41902 : Planned FE Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
attachment : Attachment
customfield_41909 : FE Effort (Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_41908 : BE Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41907 : BE Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41906 : FE Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_24100 : App Name (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_28700 : Updated Vendor Information (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_41901 : Planned FE Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41900 : PRD Revise Times (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_47207 : UAT Period调整值（Reason） (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_27615 : QA(s) (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_27617 : UAT Tester (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_27616 : Developer(s) (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_16500 : Project Nature (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_39600 : PRD Link/s (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_39601 : TD Link/s (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_41916 : Times for Revise (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_41915 : Code Review Effort (Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_41914 : Estimate BE Effort(Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_41913 : Estimate FE Effort(Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_35002 : Service Request with Approvals User SLA (com.atlassian.servicedesk:sd-sla-field)
customfield_41919 : Planned Model Training Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41918 : Planned Data Labelling Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_35000 : Task Ack SLA (com.atlassian.servicedesk:sd-sla-field)
customfield_41917 : Planned Data Labelling Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_35001 : Task Approve/ Reject SLA (com.atlassian.servicedesk:sd-sla-field)
customfield_25200 : Pass PRD/TRD Financial Risk Sign Off (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_25201 : Pass Tech Design/Bug Fix Plan Financial Risk Sign Off (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_25202 : Pass Test Case Design Financial Risk Sign Off (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_25203 : Pass Test Result Financial Risk Sign Off (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_41912 : Selftest Revise Times (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_25204 : Pass Financial Risk Compliance (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_41911 : TD Revise Times (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_41910 : BE Effort (Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_25205 : Financial Risk Control Failure Reason (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_25206 : Operation Date and Time (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_21400 : Is Ads? (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_46700 : UAT Re-Test (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_42102 : Review Cycle (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_42101 : POP Request Link (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_42100 : Request Submission Cycle (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_31203 : Critical Feature? (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_31202 : Party Impacted (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_31205 : No. of Reproduced Users/CS Cases (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_31204 : Acceptable Workaround? (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_35800 : Improvement Category (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_10500 : Workflow Checklist (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_10501 : Development Reviewer (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_10502 : Dependency Checklist (com.okapya.jira.checklist:checklist)
customfield_32300 : Release Version (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_45609 : Task Link (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_20309 : Final User Guide (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_20307 : Reason (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_45606 : 资损 (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_20308 : Initial User Guide (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_45605 : 影响时长 (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_20305 : Update (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_45608 : 复盘文件 (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_41003 : Target Release Month (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_20306 : Update Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_45607 : 是否需要复盘 (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_20303 : Transify Key Link (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_45602 : 拒绝原因 (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_45601 : AI Bot resolve comments/suggestions (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_20304 : EN User Guide (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_20301 : Roadmap(disable) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_45604 : 受影响的用户数量 (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_20302 : Feature Intro (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_45603 : 受影响的用户占比 (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_20300 : Priority Sequence (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_43200 : [POP] Supporting Product Lines (new) (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_36900 : NDRE Problem Category (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_42105 : Phase (com.atlassian.jira.plugin.system.customfieldtypes:select)
assignee : Assignee
customfield_22713 : Deduction log [LINK] (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_46701 : Bug Occurrence Time (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_22712 : User list [LINK] (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_22711 : Amount refunded (USD) (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_22710 : Amount refunded (local) (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_23602 : Tenant (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_19300 : Your Business Team (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_44300 : is_data_issue (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_44301 : Request Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_44302 : Reporter Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_34500 : IRD (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_34501 : SIT Testing Content (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_12901 : Risk probability (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12900 : Operation Checklist (com.okapya.jira.checklist:checklist)
customfield_12903 : Program Increment (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_22709 : Number of fraudsters identified (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_12902 : Risk consequence (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_22708 : Outcome (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_22707 : Placement (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_22706 : Ads ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_22705 : Product ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_22704 : User email (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_22703 : User ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_22702 : Shop ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_22701 : Username (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_22700 : Reported Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_24700 : Avoid Measures (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_45400 : Assigned to Resolved (com.atlassian.servicedesk:sd-sla-field)
customfield_18201 : Permission (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_18202 : Issue (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_34502 : Penetration Test Content (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_34503 : Hardening (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_35601 : Business Goal (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_35600 : Requester Team (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_11800 : Skipped Project Phases (com.okapya.jira.checklist:checklist)
customfield_11803 : Time to remind (com.atlassian.servicedesk:sd-sla-field)
customfield_11805 : Project Score (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11804 : test Groovy template field (ru.mail.jira.plugins.groovy:groovy-template-field)
votes : Votes
worklog : Log Work
customfield_21602 : Bug Reason (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_44303 : Root Cause Category(SPDE) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_46501 : TMS Link (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_17100 : Auto Create Doc Tickets? (com.okapya.jira.checklist:checklist)
customfield_35609 : Reason for Raising Adhoc (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_31008 : Goal (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_35608 : Hard Deadline (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_35606 : Supporting Doc (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_35605 : Feature Business Impact (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_35604 : Request Main Product Line (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_35602 : Business Focus (Sub-Goal) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_36701 : Estimated PRD Sign Off Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_36700 : Auto Renewal (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_35611 : P1 Priority Ranking (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_32100 : Test SLA (com.atlassian.servicedesk:sd-sla-field)
customfield_10700 : Dependency Checklist Proxy (com.okapya.jira.checklist:checklistReadOnly)
customfield_10701 : Approver(s) (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_10702 : Approval Status (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_20500 : Source (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_47600 : Dev Effort (hours) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_16000 : Project Role (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_43000 : Fixer (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_35615 : Classification (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_35614 : Close Reason Code (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_31018 : Request Product (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_35613 : Block Reason Code (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_36702 : LiveStream Session ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_37801 : FE List (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_33202 : Quarterly Planning QA Efforts(Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_37800 : BE List (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_33200 : Quarterly Planning BE Efforts(Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_33201 : Quarterly Planning FE Efforts(Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_23400 : Cross Team with (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_23401 : Cross Product Line with (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_19500 : Time for duty (com.atlassian.servicedesk:sd-sla-field)
customfield_37804 : PD Sign off Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_38900 : Alarm Valid (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_37803 : Total DS Effort (Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_37802 : DS List (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_34300 : Time Zone Blocked Time (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_34301 : Deprioritization Blocked Time (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_22902 : Product Version (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_22901 : Latest Update (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_22900 : Adhoc Remark (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_24500 : gitlab merged url (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_45202 : Bug影响 (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_45201 : 是否因为bug-fix引入 (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_45204 : Bug主要分类 (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_18400 : GST - PS FIrst Response (com.atlassian.servicedesk:sd-sla-field)
customfield_45203 : 业务端 (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_45200 : 是否Incident (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_18401 : GST - Time to Resolution (com.atlassian.servicedesk:sd-sla-field)
duedate : Due Date
customfield_21803 : Live Test Sign-off Status (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_21802 : Overall Live Testing Status (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_21801 : Local Live Testing Status (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_25600 : miss-tested(测漏) (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_25601 : untestable in non-live env(可测性缺失) (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_21004 : Email Thread (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_21003 : Dependency Changes (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_21002 : SDK Version (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_21001 : App Size After SDK Upgrade (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_21000 : App Size Before SDK Upgrade (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_38000 : Integration Effort(days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_11200 : Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_15801 : Script Path (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_11201 : Shopee Region (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_11202 : Responsible Team (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_11203 : Team Involved (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_39100 : Sensitivity Classification (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_14700 : BZT Task completion (com.atlassian.servicedesk:sd-sla-field)
customfield_10100 : Story Points (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_14701 : BZT Past Due Date (com.atlassian.servicedesk:sd-sla-field)
customfield_32701 : Roadmap (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_11425 : Time to first response (com.atlassian.servicedesk:sd-sla-field)
customfield_11424 : Time to resolution (com.atlassian.servicedesk:sd-sla-field)
customfield_11427 : Time to approve normal change (com.atlassian.servicedesk:sd-sla-field)
customfield_11426 : Time to close after resolution (com.atlassian.servicedesk:sd-sla-field)
customfield_29301 : Whether change date(including change start date and completion date) is within HARD code freeze period (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_29304 : Status To Progress in % (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_42501 : Product Line of Epic (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_42500 : Opted in Markets (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_29302 : Whether change date(including change start date and completion date) is within SOFT code freeze period (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_29303 : STO Priority (com.atlassian.jira.plugin.system.customfieldtypes:select)
status : Status
customfield_42503 : Approval Cycle (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_40100 : Team Reviewer (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_33802 : SEH article link (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_33800 : Order ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_11421 : Workaround (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_33801 : Expected Result (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11420 : Root cause (Service Desk) (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11423 : CAB (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_13601 : Release Checklist Content (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_11422 : Change managers (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_11414 : Change completion date (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_12503 : Integrated Test Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11413 : Change start date (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_12502 : Integrated Test Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11416 : Source (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11415 : Urgency (com.atlassian.jira.plugin.system.customfieldtypes:select)
aggregatetimeestimate : Σ Remaining Estimate
customfield_11418 : Product categorization (com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect)
customfield_11417 : Investigation reason (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11419 : Operational categorization (com.atlassian.jira.plugin.system.customfieldtypes:cascadingselect)
customfield_43600 : Time to done after resolution (com.atlassian.servicedesk:sd-sla-field)
creator : Creator
customfield_22300 : Strategy Type (SPFRBCS) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_41200 : Expected Task Progress (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_40110 : Planning complete datetime (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_40111 : Reject Reason Code (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_34902 : Counterpart Name (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_34903 : Draft Agreement (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_30300 : Category Access (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11410 : Change type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_34900 : Contract Name (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_34901 : Contract Value Est. (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_11412 : Change reason (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12501 : Test Case Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11411 : Change risk (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12500 : Test Case Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_11403 : Customer Request Type (com.atlassian.servicedesk:vp-origin)
customfield_11402 : Request participants (com.atlassian.servicedesk:sd-request-participants)
customfield_40109 : Opt in/out response datetime (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_11405 : Satisfaction (com.atlassian.servicedesk:sd-request-feedback)
customfield_11404 : Organizations (com.atlassian.servicedesk:sd-customer-organizations)
customfield_11407 : Pending reason (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11406 : Satisfaction date (com.atlassian.servicedesk:sd-request-feedback-date)
customfield_11409 : Impact (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_11408 : Approvers (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_40103 : Request review pass datetime (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_40104 : Product review approval datetime (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_40101 : Product Reviewer (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_40102 : Request submission datetime (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_40107 : Product Review Assignee (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_40108 : Business approval datetime (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_44700 : Tempo para primeira resposta (com.atlassian.servicedesk:sd-sla-field)
customfield_40106 : Request Review Assignee (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_21202 : Reason (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_21201 : Feature Category (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_21200 : Biz PIC (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_42300 : Testing Scope (com.atlassian.jira.plugin.system.customfieldtypes:select)
timespent : Time Spent
customfield_31400 : Platform(s) (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_11401 : Approvals (com.atlassian.servicedesk.approvals-plugin:sd-approvals)
customfield_11400 : Details (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_10304 : Dev Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_14900 : Time to approval (com.atlassian.servicedesk:sd-sla-field)
customfield_10305 : QA Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_10306 : Product Manager (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_10307 : Developer (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_45807 : [Model]Request ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_10308 : QA (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_45809 : PRD评审次数 (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_40112 : Withdraw Reason Code (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_40113 : Decline Reason Code (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_20101 : Request From (Others) (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_20100 : Request From (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_43400 : UAT Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_42311 : [POP] Request only applicable for the target country (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_42310 : [POP] Request Type (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_32500 : Schedule (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_10300 : Checklist (com.okapya.jira.checklist:checklist)
customfield_13801 : Release Status (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_42309 : [POP] Supporting Product Lines (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_29102 : Technical Impact (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_29100 : Request Time (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_29101 : Response Time (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_42304 : [POP] Submission Type (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_42303 : [POP] Request Name (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_42302 : [POP] Request ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_42301 : Issue Storage SLA (com.atlassian.servicedesk:sd-sla-field)
customfield_46900 : SLA Priority (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_42308 : [POP] Target Release (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_42307 : [POP] Target Country (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_42306 : [POP] Request Main Product Line (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_42305 : [POP] Hard Deadline (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_23800 : Risk Compliance (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_33600 : Ticket Quality Issue (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_33601 : Priority Accuracy Issue (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_12701 : SST - Time to resolution (com.atlassian.servicedesk:sd-sla-field)
customfield_12700 : SST - PS First Response (com.atlassian.servicedesk:sd-sla-field)
customfield_30100 : CIS Problem Category (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_43405 : Request is new Portal/Module/Page (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_42315 : [POP] Status (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_43406 : Skip UAT Deadline for Optional UAT (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_42314 : [POP] Priority (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_42313 : [POP] Request Submission datetime (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_43407 : [POP] Requester (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_42312 : [POP] Classification (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_43401 : Request contains High Financial Risk (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_43402 : Request contains different cross team dependency (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_43403 : Request has Project Size M/L/XL (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_43404 : Request has 3rd Party Integration (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_42316 : [POP] Business Label (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_18001 : Time to first response (Service Request - Data Team) (com.atlassian.servicedesk:sd-sla-field)
customfield_24900 : Team (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_18002 : Time to first response (Data Team) (com.atlassian.servicedesk:sd-sla-field)
customfield_45600 : Issue Resolved by AI Bot (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_24901 : Market (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_41001 : Planned PRD Review End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_41000 : Planned PRD Review Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_18000 : No Task/Sub-task Link Reason (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_24907 : Smoke Test Pass Rate (%) (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_34700 : NOC Incident Report (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_11601 : L1 - Time to first response (com.atlassian.servicedesk:sd-sla-field)
customfield_11600 : Time to update (com.atlassian.servicedesk:sd-sla-field)
customfield_31201 : No. of Users Affected (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_31200 : No. of Orders/Transactions Affected (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_11604 : Transaction Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_20313 : Standup announced? (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_27507 : Actual Dev Effort (Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_27506 : Effort (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_27508 : Actual QA Effort (Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_17700 : Long-term Solution (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_13100 : Time to close after resolution (Problem) (com.atlassian.servicedesk:sd-sla-field)
customfield_38400 : Information for BE (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_28602 : Canary Deployment Strategy Description (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_41800 : Need TD？ (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12000 : Deployment Dependency (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_16600 : IP Address (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_39500 : Request ID (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_12002 : Technical Conclusion (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_12001 : Technical Summary (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_42901 : Code Review End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_42900 : Code Review Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
components : Component/s
customfield_15500 : Operation CS Request Category (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_15501 : Estimated go live date available (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_36000 : Need MPI Dev Effort (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_26201 : status (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_30700 : Root Cause Category (SPTSS) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_14400 : Issue Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_37100 : Debugging Info (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
progress : Progress
customfield_40500 : Request Type(s) (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_40503 : Opt Assignee (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_40504 : Target Release (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_40501 : Tech Role (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_40502 : BA Assignee (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_38200 : Test Steps (com.atlassian.jira.plugin.system.customfieldtypes:float)
project : Project
customfield_31800 : Live Testing Report Created (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_13300 : Product Line (UAT) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12211 : Type of Request (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12210 : Tech Design Reviewer (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_12213 : Reporting Manager Email (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_17901 : AZ SRE Reviewer (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_12212 : Department (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17900 : Environment(s) (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_12206 : Affect SZ Services (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12205 : Affected Countries (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12208 : Tech Design Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_12209 : Tech Design End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
resolutiondate : Resolved
customfield_41600 : Development Review Effort (Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_22002 : Strategy Reviewer (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_22001 : Scenario (SPFRBCS) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_22000 : Case Type (SPFRBCS) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_39300 : TD Link (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_39301 : MQ Problem Category (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_12200 : Code Review after QA (com.atlassian.jira.plugin.system.customfieldtypes:multicheckboxes)
customfield_39303 : L3 Team Responsible (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_39304 : Time of First Ack (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_12202 : QA List (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_12201 : Dev List (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
customfield_16801 : Time to first response (Service Request) (com.atlassian.servicedesk:sd-sla-field)
customfield_12204 : External issue ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_39307 : Project Tracker (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_16800 : Time to fulfilment (com.atlassian.servicedesk:sd-sla-field)
customfield_12203 : External issue ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_15705 : Effort (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_15706 : FRF Link (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_15703 : Email Title (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_15704 : Program (Reg. Ops BI) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_15707 : PRD Link (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_15708 : Test Case Link (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_42700 : UAT Testcase Link (com.atlassian.jira.plugin.system.customfieldtypes:url)
customfield_29500 : Key Project (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_29501 : External Sharing (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_15701 : Requestor Team SGBI (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_15702 : Nature of Work (Reg. Ops BI) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_15700 : Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_10005 : Sprint (com.pyxis.greenhopper.jira:gh-sprint)
customfield_14606 : Problem Time (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_14603 : Response (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_14604 : Admin Site Link (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_14607 : Background (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_26001 : Staging Regression Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_14608 : Expected Effects (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_26000 : Staging Regression Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_43802 : Doc Review Effort (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_43803 : Critical or Not (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_43800 : Doc Reviewer (com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker)
summary : Summary
customfield_30502 : 问题描述 (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_10000 : Rank (com.pyxis.greenhopper.jira:gh-lexo-rank)
customfield_10001 : Epic Link (com.pyxis.greenhopper.jira:gh-epic-link)
customfield_14601 : Module ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_10002 : Epic Status (com.pyxis.greenhopper.jira:gh-epic-status)
customfield_14602 : Request (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_10003 : Epic Name (com.pyxis.greenhopper.jira:gh-epic-label)
customfield_30500 : Include Shopee Data? (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_14600 : App ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_30501 : Canary Deployment (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_10004 : Epic Colour (com.pyxis.greenhopper.jira:gh-epic-color)
customfield_27101 : Bad Case Resolution (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_27100 : DB Change (Requires BIZ attention) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_40301 : Regression Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
comment : Comment
customfield_40300 : Regression Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_44900 : Last Updated User (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_23304 : CMDB Service ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_23305 : SuccessEventQuery (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_23306 : TotalEventQuery (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_23307 : Data Source (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_17300 : Order Impact (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_23308 : Target Tier (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_44000 : Time to Resolution - Improvement (com.atlassian.servicedesk:sd-sla-field)
customfield_44001 : Business Line (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17304 : Incident Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17302 : Incident Owner (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_38800 : Sensitivity Level (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_17306 : DI Sub-Team (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_17305 : Business Team (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_10900 : UAT (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_10901 : Cause (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_34200 : Detailed Root Cause (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_10902 : Solution (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_10903 : Account (com.tempoplugin.tempo-accounts:accounts.customfield)
customfield_10904 : Team (com.tempoplugin.tempo-teams:team.customfield)
customfield_10905 : Team Role (com.tempoplugin.tempo-teams:team.role.customfield)
customfield_10906 : Iteration (com.tempoplugin.tempo-plan-core:tp.iteration.customfield)
customfield_10907 : Task Score (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_10908 : QA Impact (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_20700 : Initiated By (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_45102 : Project Size (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_45105 : New Portal/Module/Page (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_45104 : 3rd Party Integration (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_16200 : Security Impact (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_45101 : Different cross team dependency (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_39900 : Time to first response - Datafix (com.atlassian.servicedesk:sd-sla-field)
customfield_39901 : Time to resolution - Datafix (com.atlassian.servicedesk:sd-sla-field)
customfield_35300 : FRF End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
versions : Affects Version/s
customfield_25500 : Request Category (com.atlassian.jira.plugin.system.customfieldtypes:labels)
customfield_46203 : Request contains High Operational Risk (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_15100 : Agents on Duty (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_46201 : Planned Test Case Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_46200 : Planned Test Case Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_15103 : Country Impacted (com.atlassian.jira.plugin.system.customfieldtypes:multiselect)
customfield_15104 : Email of Impacted Agents (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_15102 : Support Ticket Number (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_19700 : Approver (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_19701 : User (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_36401 : TD Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_36400 : TD Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_45107 : PRD Score (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_45106 : High Operational Risk (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_26600 : RAM Project Code (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_45108 : [POP] Key Project ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_47304 : UAT Support Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_47305 : UAT Support Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_47302 : QA Testing Support Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_47303 : QA Testing Support Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_47300 : DFR (com.atlassian.servicedesk:sd-sla-field)
customfield_47301 : Support Effort (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_14000 : Time for customer response (com.atlassian.servicedesk:sd-sla-field)
aggregateprogress : Σ Progress
customfield_18600 : Issue Category (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_40900 : Plan Assignee (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_38600 : Ticket Type (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_20909 : Feature Affected (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_34005 : QA Pass Rate (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_20907 : Device Model (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_34003 : Signed off (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_20908 : Admin Site Link (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_34004 : QA Test Execution Rate (com.atlassian.jira.plugin.system.customfieldtypes:readonlyfield)
customfield_20905 : Affected User ID(s) (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_34001 : Config Changed (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_34002 : Code Merged (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_20906 : Device OS (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_20903 : Activity / Event ID (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_34000 : DB Changed (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_20904 : Slot / Session ID (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_20901 : Optimize PIC (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_20902 : Publish PIC (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_20900 : Translate PIC (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_28802 : Decouple between Control Plane and Data Plane (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_28803 : Decouple Method Description (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_28800 : Null Value Protection (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_47306 : Release Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_28801 : Null Value Protection Method Description (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_47307 : Release Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
thumbnail : Images
customfield_28804 : Data Plane Test Cases Verification (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_28805 : Data Plane Test Cases Explanation and Result (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_16400 : Latest Status (com.atlassian.jira.plugin.system.customfieldtypes:textarea)
customfield_39700 : SIT Report Sign Off (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_35103 : Approver List (com.okapya.jira.checklist:checklist)
customfield_35101 : Doc Ack SLA (com.atlassian.servicedesk:sd-sla-field)
customfield_35102 : Doc Complete SLA (com.atlassian.servicedesk:sd-sla-field)
customfield_25300 : Trace ID (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_46001 : [Model]Request Link (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_46000 : Project Tracker (com.atlassian.jira.plugin.system.customfieldtypes:textfield)
customfield_15301 : Planned Feasibility Study End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_15302 : Planned Tech Design Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_15300 : Planned Feasibility Study Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_15303 : Planned Tech Design End Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_15304 : Risk Controller (com.atlassian.jira.plugin.system.customfieldtypes:userpicker)
customfield_19900 : Sign-off Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
security : Security Level
customfield_26400 : Category - SPMYSBS (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_47101 : Dev Test Due Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_47100 : Dev Test Start Date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_47103 : BE Test Effort (Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_47102 : FE Test Effort (Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_14201 : Data taken to date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_18801 : Writer - Create Ticket (com.atlassian.servicedesk:sd-sla-field)
customfield_18802 : Verifier - Check if report is fix (com.atlassian.servicedesk:sd-sla-field)
customfield_30900 : Root Cause Category (SPUAT) (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_14200 : Data taken from date (com.atlassian.jira.plugin.system.customfieldtypes:datepicker)
customfield_18800 : Reproducer - Verify Report (com.atlassian.servicedesk:sd-sla-field)
customfield_37300 : Time when issue found (com.atlassian.jira.plugin.system.customfieldtypes:datetime)
customfield_27501 : Total BE/DE Effort (Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_27500 : Total FE Effort (Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_40701 : Active Rollout Product/Feature (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_27503 : Estimate Dev Effort (Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_27502 : Total QA Effort (Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)
customfield_27505 : Role (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_40700 : Active Rollout/BAU Feature (com.atlassian.jira.plugin.system.customfieldtypes:select)
customfield_27504 : Estimate QA Effort (Days) (com.atlassian.jira.plugin.system.customfieldtypes:float)