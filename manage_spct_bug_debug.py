#!/usr/bin/env python3
"""
SPCT Bug提醒调试模式管理脚本
用于方便地开启/关闭调试模式
"""

import os
import sys
import django
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()

from app01.seatalk_group_manager import (
    get_spct_bug_reminder_debug_status,
    set_spct_bug_reminder_debug_mode,
    SPCT_BUG_REMINDER_DEBUG_GROUP
)


def show_status():
    """显示当前调试模式状态"""
    status = get_spct_bug_reminder_debug_status()
    print("=" * 50)
    print("SPCT Bug提醒调试模式状态")
    print("=" * 50)
    print(f"当前模式: {status['status']}")
    print(f"调试模式: {'开启' if status['debug_mode'] else '关闭'}")
    print(f"调试群组ID: {status['debug_group_id']}")
    print("=" * 50)


def enable_debug():
    """开启调试模式"""
    print("🔧 开启调试模式...")
    set_spct_bug_reminder_debug_mode(True)
    print("✅ 调试模式已开启")
    print(f"📤 消息将发送到调试群: {SPCT_BUG_REMINDER_DEBUG_GROUP}")


def disable_debug():
    """关闭调试模式"""
    print("🚀 关闭调试模式...")
    set_spct_bug_reminder_debug_mode(False)
    print("✅ 调试模式已关闭")
    print("📤 消息将发送到对应的正式群组")


def test_via_api():
    """通过API测试调试模式控制"""
    print("🧪 通过API测试调试模式...")
    
    # 测试获取状态
    try:
        print("\n1. 获取当前状态:")
        print("curl -X GET http://localhost:8000/api/spct-bug-debug/")
        
        print("\n2. 开启调试模式:")
        print('curl -X POST http://localhost:8000/api/spct-bug-debug/ \\')
        print('  -H "Content-Type: application/json" \\')
        print('  -d \'{"debug_mode": true}\'')
        
        print("\n3. 关闭调试模式:")
        print('curl -X POST http://localhost:8000/api/spct-bug-debug/ \\')
        print('  -H "Content-Type: application/json" \\')
        print('  -d \'{"debug_mode": false}\'')
        
        print("\n4. 手动触发Bug提醒:")
        print("curl -X GET http://localhost:8000/api/spct-test-bugs/")
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")


def main():
    """主函数"""
    print("🐛 SPCT Bug提醒调试模式管理")
    
    if len(sys.argv) < 2:
        show_status()
        print("\n使用方法:")
        print("  python3 manage_spct_bug_debug.py status     # 查看状态")
        print("  python3 manage_spct_bug_debug.py enable     # 开启调试模式")
        print("  python3 manage_spct_bug_debug.py disable    # 关闭调试模式")
        print("  python3 manage_spct_bug_debug.py api        # 显示API测试命令")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'status':
        show_status()
    elif command == 'enable':
        enable_debug()
        show_status()
    elif command == 'disable':
        disable_debug()
        show_status()
    elif command == 'api':
        test_via_api()
    else:
        print(f"❌ 未知命令: {command}")
        print("可用命令: status, enable, disable, api")


if __name__ == '__main__':
    main()