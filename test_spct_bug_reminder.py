#!/usr/bin/env python3
"""
SPCT/SPCB测试环境Bug提醒功能测试脚本
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()

from datetime import datetime
from django.utils import timezone
from app01.seatalk_group_manager import (
    get_epic_groups_from_db,
    get_assignee_and_tl,
    filter_bugs_by_priority_and_time,
    check_spct_test_bugs,
    get_spct_bug_reminder_debug_status,
    set_spct_bug_reminder_debug_mode
)


def test_get_epic_groups():
    """测试获取Epic群组信息"""
    print("=" * 50)
    print("测试获取Epic群组信息")
    print("=" * 50)
    
    try:
        epic_groups = get_epic_groups_from_db()
        print(f"✅ 找到 {len(epic_groups)} 个Epic群组:")
        for epic_key, group_id in epic_groups.items():
            print(f"  - {epic_key}: {group_id}")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_get_assignee_and_tl():
    """测试人员和TL映射"""
    print("\n" + "=" * 50)
    print("测试人员和TL映射")
    print("=" * 50)
    
    test_cases = [
        "<EMAIL>",  # ChatSS成员
        "<EMAIL>",      # Webchat_FE Leader
        "<EMAIL>",        # 未知用户
    ]
    
    for email in test_cases:
        try:
            result = get_assignee_and_tl(email)
            print(f"✅ {email} -> TL: {result['tl']}")
        except Exception as e:
            print(f"❌ 测试失败 {email}: {e}")
            return False
    
    return True


def test_filter_bugs():
    """测试Bug过滤逻辑"""
    print("\n" + "=" * 50)
    print("测试Bug过滤逻辑")
    print("=" * 50)
    
    # 模拟bug数据
    current_time = timezone.now()
    
    bugs = [
        {
            'key': 'SPCT-123',
            'summary': 'P0 Bug - 紧急问题',
            'priority': 'P0',
            'assignee': '<EMAIL>',
            'created': (current_time - timezone.timedelta(days=2)).strftime('%Y-%m-%dT%H:%M:%S'),
            'status': 'In Progress'
        },
        {
            'key': 'SPCT-124',
            'summary': 'P1 Bug - 中等问题',
            'priority': 'P1',
            'assignee': '<EMAIL>',
            'created': (current_time - timezone.timedelta(days=4)).strftime('%Y-%m-%dT%H:%M:%S'),
            'status': 'To Do'
        },
        {
            'key': 'SPCT-125',
            'summary': 'Medium Bug - 中等问题',
            'priority': 'medium',
            'assignee': '<EMAIL>',
            'created': (current_time - timezone.timedelta(hours=12)).strftime('%Y-%m-%dT%H:%M:%S'),
            'status': 'To Do'
        }
    ]
    
    try:
        reminder_bugs = filter_bugs_by_priority_and_time(bugs, current_time)
        
        print(f"✅ P0/High优先级提醒: {len(reminder_bugs['p0_p1_bugs'])} 个bug")
        for bug in reminder_bugs['p0_p1_bugs']:
            print(f"  - {bug['key']}: {bug['summary']}")
            
        print(f"✅ P1/Medium优先级提醒: {len(reminder_bugs['p1_medium_bugs'])} 个bug")
        for bug in reminder_bugs['p1_medium_bugs']:
            print(f"  - {bug['key']}: {bug['summary']}")
            
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_debug_mode():
    """测试调试模式功能"""
    print("\n" + "=" * 50)
    print("测试调试模式功能")
    print("=" * 50)
    
    try:
        # 获取当前状态
        current_status = get_spct_bug_reminder_debug_status()
        print(f"✅ 当前调试模式状态: {current_status['status']}")
        
        # 测试开启调试模式
        set_spct_bug_reminder_debug_mode(True)
        debug_on_status = get_spct_bug_reminder_debug_status()
        print(f"✅ 开启调试模式后: {debug_on_status['status']}")
        
        # 测试关闭调试模式
        set_spct_bug_reminder_debug_mode(False)
        debug_off_status = get_spct_bug_reminder_debug_status()
        print(f"✅ 关闭调试模式后: {debug_off_status['status']}")
        
        # 恢复原来的状态
        set_spct_bug_reminder_debug_mode(current_status['debug_mode'])
        print(f"✅ 已恢复原始状态: {current_status['status']}")
        
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_main_function():
    """测试主函数（模拟执行）"""
    print("\n" + "=" * 50)
    print("测试主函数执行")
    print("=" * 50)
    
    try:
        # 注意：这里只是导入测试，不实际执行以避免发送消息
        print("✅ 主函数导入成功")
        print("⚠️  为避免发送实际消息，跳过实际执行")
        print("💡 如需完整测试，请手动调用: check_spct_test_bugs()")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始SPCT/SPCB Bug提醒功能测试")
    print("🕐 测试时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    tests = [
        ("Epic群组信息获取", test_get_epic_groups),
        ("人员和TL映射", test_get_assignee_and_tl),
        ("Bug过滤逻辑", test_filter_bugs),
        ("调试模式功能", test_debug_mode),
        ("主函数执行", test_main_function),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
        else:
            print(f"\n❌ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过！")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    print("\n💡 调试模式管理:")
    print("1. 查看调试状态: python3 manage_spct_bug_debug.py status")
    print("2. 开启调试模式: python3 manage_spct_bug_debug.py enable")
    print("3. 关闭调试模式: python3 manage_spct_bug_debug.py disable")
    print("4. 查看API命令: python3 manage_spct_bug_debug.py api")
    
    print("\n💡 手动测试建议:")
    print("1. 调试模式API: /api/spct-bug-debug/")
    print("2. 手动触发API: /api/spct-test-bugs/")
    print("3. 检查cron任务是否正确配置")
    print("4. 验证实际的JIRA查询和SeaTalk消息发送")


if __name__ == '__main__':
    run_all_tests()