# 智能搜索功能说明

## 问题背景

### 原有问题
用户在查询时经常遇到以下问题：
- 只提供用户名时，系统默认使用 `ID + TEST` 环境查询
- 但用户可能实际在其他地区（如SG、TH等）或环境（如LIVE、STAGING等）
- 导致查询失败，用户体验差

### 具体案例
```
用户输入：user banana_goood
系统默认查询：ID地区 + TEST环境
实际用户在：SG地区 + LIVE环境
结果：查询失败 ❌
```

## 解决方案

### 智能搜索策略
1. **探测阶段**：先用第一个接口（account API）进行宽泛查询
2. **检测阶段**：从查询结果中提取用户的真实环境和地区信息
3. **精确查询**：用检测到的信息查询第二个接口（user_info API）
4. **数据合并**：合并两个接口的数据，提供完整信息

### 触发条件
智能搜索在以下条件下自动启用：
- ✅ 只提供了用户名（没有用户ID）
- ✅ 使用默认环境（TEST）
- ✅ 使用默认地区（ID）
- ✅ 启用智能搜索标志（smart_search=True）

## 技术实现

### 1. 核心方法更新

#### query_user_comprehensive 方法
```python
async def query_user_comprehensive(self, username: str = None, userid: Union[str, int] = None, 
                                  env: str = "TEST", country: str = "ID", smart_search: bool = True) -> Dict:
```

新增参数：
- `smart_search`: 是否启用智能搜索（默认True）

### 2. 智能搜索流程

#### 第一步：探测查询
```python
# 用第一个接口进行宽泛查询（不指定环境和地区）
account_result = await self.query_account_info(username=username)
```

#### 第二步：信息提取
```python
if users:
    first_user = users[0]
    real_env = first_user.get('env', env)
    real_country = first_user.get('country', country)
```

#### 第三步：精确查询
```python
# 用真实的环境和地区查询第二个接口
user_info_result = await self.query_user_info(
    country=real_country, 
    env=real_env, 
    username=username
)
```

### 3. 响应结构增强

#### 成功响应
```python
{
    'success': True,
    'users': merged_users,
    'total_count': len(merged_users),
    'smart_search_used': True,
    'detected_env': real_env,
    'detected_country': real_country,
    'api_results': {
        'account_api': account_result,
        'user_info_api': user_info_result
    }
}
```

#### 未找到用户响应
```python
{
    'success': True,
    'users': [],
    'total_count': 0,
    'smart_search_used': True,
    'not_found_suggestion': True,
    'api_results': {...}
}
```

### 4. 用户界面优化

#### 成功时的显示
```
🧠 智能搜索成功！自动检测到用户环境：LIVE，地区：SG

📋 找到 1 个用户：

👤 用户 1:
   用户名: banana_goood
   用户ID: **********
   环境: LIVE
   地区: SG
```

#### 未找到时的建议
```
❌ 简略查询未找到相关用户信息

🔍 建议使用完整指令查询：
如果用户可能在其他地区或环境，请使用包含地区和环境信息的完整指令：

📝 完整指令示例：
• `user username banana_goood sg live` - 查询SG地区LIVE环境的用户
• `user username banana_goood th test` - 查询TH地区TEST环境的用户
• `查询用户banana_goood在SG环境的信息` - 自然语言查询

🌍 支持的地区：id, tw, sg, th, ph, my, vn, br, mx, pl, co, cl, ar
⚙️ 支持的环境：test, uat, staging, live

💡 提示：智能搜索已在所有地区和环境中查找，但未找到该用户。
```

## 使用场景

### 场景1：智能搜索成功
```bash
用户输入：user banana_goood
系统行为：
1. 先查询所有环境和地区
2. 发现用户在SG LIVE环境
3. 用SG LIVE参数精确查询
4. 返回完整用户信息
结果：✅ 查询成功，显示智能检测信息
```

### 场景2：智能搜索未找到
```bash
用户输入：user nonexistent_user
系统行为：
1. 先查询所有环境和地区
2. 未找到任何匹配用户
3. 返回友好的建议信息
结果：✅ 提供完整指令示例和使用建议
```

### 场景3：普通模式（用户指定了参数）
```bash
用户输入：user username banana_goood sg live
系统行为：
1. 检测到用户指定了环境和地区
2. 跳过智能搜索，直接使用指定参数
3. 并发查询两个接口
结果：✅ 使用普通模式查询
```

## 性能优化

### 1. 查询效率
- **智能模式**：最多2次API调用（探测 + 精确查询）
- **普通模式**：2次并发API调用
- **总体影响**：智能模式可能稍慢，但成功率大幅提升

### 2. 缓存策略
- 可以考虑缓存用户的环境和地区信息
- 减少重复的探测查询

### 3. 降级机制
- 如果第一个接口查询失败，自动降级到普通模式
- 确保系统的健壮性

## 测试验证

### 测试场景覆盖
- ✅ 智能搜索成功找到用户（不同环境和地区）
- ✅ 智能搜索未找到用户（提供建议）
- ✅ 普通模式查询（指定参数）
- ✅ 降级机制（第一个接口失败）

### 测试结果
- ✅ 所有场景测试通过
- ✅ 用户体验显著改善
- ✅ 查询成功率大幅提升

## 总结

### 优势
1. **提升成功率**：自动检测用户真实环境，避免默认参数导致的查询失败
2. **改善体验**：用户只需提供用户名，系统自动处理复杂的参数匹配
3. **智能提示**：未找到时提供具体的使用建议和完整指令示例
4. **向下兼容**：不影响现有的查询方式，用户仍可指定具体参数

### 适用性
- 特别适合不熟悉系统参数的用户
- 减少了用户需要记忆的命令格式
- 提高了查询的容错性和智能化程度

这个智能搜索功能解决了用户查询时的核心痛点，大大提升了用户信息查询的成功率和用户体验。
