#!/bin/bash

# ChatbotAR 定时任务调度器管理脚本
# 使用方法: ./task_scheduler.sh {start|stop|restart|status}

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$SCRIPT_DIR/task_scheduler.pid"
LOG_FILE="$SCRIPT_DIR/logs/task_scheduler.log"
DAEMON_LOG_FILE="$SCRIPT_DIR/logs/task_scheduler_daemon.log"

# 创建日志目录
mkdir -p logs

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查虚拟环境
setup_python() {
    if [ -d "venv" ]; then
        print_status "激活虚拟环境..."
        source venv/bin/activate
        PYTHON_CMD="venv/bin/python"
    else
        PYTHON_CMD="python3"
    fi
    
    # 检查Python是否可用
    if ! command -v $PYTHON_CMD &> /dev/null; then
        print_error "Python不可用: $PYTHON_CMD"
        exit 1
    fi
}

# 启动调度器
start_scheduler() {
    print_status "启动ChatbotAR定时任务调度器..."
    
    # 检查是否已经在运行
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            print_warning "调度器已在运行 (PID: $PID)"
            return 0
        else
            print_warning "清理过期的PID文件"
            rm -f "$PID_FILE"
        fi
    fi
    
    # 检查是否有遗留进程
    RUNNING_PIDS=$(ps aux | grep "run_scheduled_tasks" | grep -v grep | awk '{print $2}')
    if [ ! -z "$RUNNING_PIDS" ]; then
        print_warning "发现遗留进程，正在清理..."
        kill -TERM $RUNNING_PIDS 2>/dev/null || true
        sleep 2
        kill -KILL $RUNNING_PIDS 2>/dev/null || true
    fi
    
    setup_python
    
    # 启动调度器
    print_status "启动新的调度器进程..."
    nohup $PYTHON_CMD manage.py run_scheduled_tasks --interval 60 --verbose > "$LOG_FILE" 2>&1 &
    PID=$!
    
    # 保存PID
    echo $PID > "$PID_FILE"
    
    # 验证启动
    sleep 3
    if ps -p $PID > /dev/null 2>&1; then
        print_success "调度器启动成功 (PID: $PID)"
        print_status "日志文件: $LOG_FILE"
        print_status "查看日志: tail -f $LOG_FILE"
        
        # 记录启动到daemon日志
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] ✅ 调度器启动成功，PID: $PID" >> "$DAEMON_LOG_FILE"
    else
        print_error "调度器启动失败"
        rm -f "$PID_FILE"
        exit 1
    fi
}

# 停止调度器
stop_scheduler() {
    print_status "停止ChatbotAR定时任务调度器..."
    
    STOPPED=false
    
    # 通过PID文件停止
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        
        if ps -p $PID > /dev/null 2>&1; then
            print_status "找到调度器进程 (PID: $PID)"
            
            # 发送TERM信号
            kill -TERM $PID 2>/dev/null
            
            # 等待进程停止
            for i in {1..10}; do
                if ! ps -p $PID > /dev/null 2>&1; then
                    print_success "调度器已停止"
                    rm -f "$PID_FILE"
                    STOPPED=true
                    break
                fi
                print_status "等待进程停止... ($i/10)"
                sleep 1
            done
            
            # 强制终止
            if ! $STOPPED && ps -p $PID > /dev/null 2>&1; then
                print_warning "强制终止进程..."
                kill -KILL $PID 2>/dev/null
                sleep 1
                if ! ps -p $PID > /dev/null 2>&1; then
                    print_success "进程已强制终止"
                    rm -f "$PID_FILE"
                    STOPPED=true
                fi
            fi
        else
            print_warning "PID文件中的进程不存在，清理PID文件"
            rm -f "$PID_FILE"
        fi
    fi
    
    # 查找并停止遗留进程
    RUNNING_PIDS=$(ps aux | grep "run_scheduled_tasks" | grep -v grep | awk '{print $2}')
    if [ ! -z "$RUNNING_PIDS" ]; then
        print_status "发现遗留进程，正在停止..."
        kill -TERM $RUNNING_PIDS 2>/dev/null
        sleep 2
        
        # 检查是否还在运行
        STILL_RUNNING=$(ps aux | grep "run_scheduled_tasks" | grep -v grep | awk '{print $2}')
        if [ ! -z "$STILL_RUNNING" ]; then
            print_warning "强制终止遗留进程..."
            kill -KILL $STILL_RUNNING 2>/dev/null
        fi
        print_success "遗留进程已清理"
        STOPPED=true
    fi
    
    if ! $STOPPED; then
        print_warning "未发现运行中的调度器进程"
    fi
    
    # 记录停止到daemon日志
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 🛑 调度器已停止" >> "$DAEMON_LOG_FILE"
}

# 检查状态
check_status() {
    print_status "检查调度器状态..."
    
    RUNNING=false
    
    # 检查PID文件
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            print_success "调度器正在运行 (PID: $PID)"
            RUNNING=true
        else
            print_warning "PID文件存在但进程不存在"
        fi
    fi
    
    # 检查进程
    RUNNING_PIDS=$(ps aux | grep "run_scheduled_tasks" | grep -v grep)
    if [ ! -z "$RUNNING_PIDS" ]; then
        if ! $RUNNING; then
            print_warning "发现未追踪的调度器进程:"
            echo "$RUNNING_PIDS"
        fi
        RUNNING=true
    fi
    
    if ! $RUNNING; then
        print_error "调度器未运行"
        return 1
    fi
    
    # 显示日志文件信息
    if [ -f "$LOG_FILE" ]; then
        print_status "日志文件: $LOG_FILE"
        print_status "最近日志:"
        tail -5 "$LOG_FILE" 2>/dev/null || print_warning "无法读取日志文件"
    fi
    
    return 0
}

# 重启调度器
restart_scheduler() {
    print_status "重启ChatbotAR定时任务调度器..."
    stop_scheduler
    sleep 2
    start_scheduler
}

# 主函数
case "$1" in
    start)
        start_scheduler
        ;;
    stop)
        stop_scheduler
        ;;
    restart)
        restart_scheduler
        ;;
    status)
        check_status
        ;;
    *)
        echo "使用方法: $0 {start|stop|restart|status}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动定时任务调度器"
        echo "  stop    - 停止定时任务调度器"
        echo "  restart - 重启定时任务调度器"
        echo "  status  - 查看调度器状态"
        echo ""
        echo "日志文件:"
        echo "  主日志: $LOG_FILE"
        echo "  管理日志: $DAEMON_LOG_FILE"
        echo ""
        echo "示例:"
        echo "  $0 start     # 启动调度器"
        echo "  $0 status    # 查看状态"
        echo "  $0 stop      # 停止调度器"
        echo ""
        exit 1
        ;;
esac
