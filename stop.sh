#!/bin/bash

# ChatbotAR 服务停止脚本
# 停止Django服务和定时任务调度器

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$SCRIPT_DIR/task_scheduler.pid"

echo "🛑 停止 ChatbotAR 所有服务..."
echo ""

# 停止Django服务
echo "📝 停止Django服务..."
if pgrep -f "manage.py runserver.*8081" > /dev/null; then
    echo "   找到Django进程，正在停止..."
    pkill -f "manage.py runserver.*8081"
    
    # 等待进程停止
    for i in {1..5}; do
        if ! pgrep -f "manage.py runserver.*8081" > /dev/null; then
            echo "   ✅ Django服务已停止"
            break
        fi
        echo "   ⏳ 等待Django停止... ($i/5)"
        sleep 1
    done
    
    # 如果还在运行，强制终止
    if pgrep -f "manage.py runserver.*8081" > /dev/null; then
        echo "   ❌ Django未响应，强制终止..."
        pkill -9 -f "manage.py runserver.*8081"
        sleep 1
        if pgrep -f "manage.py runserver.*8081" > /dev/null; then
            echo "   ❌ 无法停止Django服务"
        else
            echo "   ✅ Django服务已强制停止"
        fi
    fi
else
    echo "   ℹ️  Django服务未运行"
fi

echo ""

# 停止定时任务调度器
echo "⏰ 停止定时任务调度器..."
./task_scheduler.sh stop

echo ""
echo "🎯 服务状态检查:"
echo "   Django: $(pgrep -f 'manage.py runserver.*8081' >/dev/null && echo '❌ 仍在运行' || echo '✅ 已停止')"
echo "   调度器: $(ps aux | grep 'run_scheduled_tasks' | grep -v grep >/dev/null && echo '❌ 仍在运行' || echo '✅ 已停止')"
echo ""
echo "✅ 停止操作完成" 