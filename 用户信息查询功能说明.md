# 用户信息查询功能说明

## 功能概述

本功能实现了一个完整的用户信息查询系统，支持通过固定指令和自然语言查询用户账户信息。系统会调用两个API接口并合并数据，提供清晰的格式化输出。

## 核心特性

### 1. 多种查询方式
- **固定指令格式**：`user + username/userid`
- **自然语言查询**：支持"查询用户信息"、"用户信息查询"等表达
- **意图识别**：自动识别用户信息查询意图

### 2. 支持的参数

#### 地区支持
- **亚洲地区**：id, tw, sg, th, ph, my, vn
- **美洲地区**：br, mx, co, cl, ar
- **欧洲地区**：pl
- **默认地区**：ID

#### 环境支持
- **test**：测试环境
- **uat**：用户验收测试环境
- **staging**：预发布环境
- **live**：生产环境
- **默认环境**：TEST

### 3. 数据处理
- **数据合并**：自动合并两个API接口的响应数据
- **敏感信息过滤**：自动排除SPC_EC和SPC_ST字段
- **结果限制**：最多显示前3条记录
- **参数验证**：自动验证和标准化环境、地区参数

## 使用方法

### 1. 固定指令格式

```bash
# 按用户名查询
user username qiqi.49

# 按用户ID查询
user id **********

# 自动识别类型
user qiqi.49
user **********
```

### 2. 自然语言查询

```bash
# 基本查询
查询用户qiqi.49的信息
用户信息查询 qiqi.49

# 指定环境和地区
查询用户qiqi.49在SG环境的信息
查看用户test在live环境TH地区的数据
```

### 3. 帮助信息

```bash
# 获取帮助
user
user help
```

## API接口说明

### 第一个接口：账户信息查询
- **URL**：`/api/v1/account`
- **参数**：
  - `username`（可选）：用户名
  - `env`（可选）：环境
  - `country`（可选）：地区
- **特点**：支持批量查询，参数都是可选的

### 第二个接口：用户信息查询
- **URL**：`/api/v1/user_info`
- **参数**：
  - `country`（必填）：地区
  - `env`（必填）：环境
  - `username`（可选）：用户名
  - `userid`（可选）：用户ID
- **特点**：环境和地区必填，username和userid二选一

## 输出示例

```
🔍 用户信息查询结果：

📋 找到 1 个用户，显示前 1 个：

👤 用户 1:
   用户名: qiqi.49
   用户ID: **********
   手机号: **********
   邮箱: <EMAIL>
   环境: TEST
   地区: ID
   其他信息: shopid: **********
```

## 错误处理

### 1. 参数验证错误
```
❌ 不支持的地区: invalid_country，支持的地区: id, tw, sg, th, ph, my, vn, br, mx, pl, co, cl, ar
```

### 2. 用户未找到
```
❌ 未找到匹配的用户信息

查询条件：
• 用户名: test
• 用户ID: N/A
• 环境: TEST
• 地区: ID

请检查查询条件是否正确。
```

### 3. API调用失败
```
❌ 用户信息查询失败：HTTP 500: Internal Server Error

请稍后重试或联系管理员。
```

## 技术实现

### 1. 核心组件

#### UserInfoClient
- 负责API调用和数据处理
- 实现参数验证和标准化
- 提供数据合并和格式化功能

#### AI意图识别
- 新增`user_info_query`意图类型
- 支持关键词匹配和自然语言理解
- 集成到现有的意图识别框架

#### 特殊命令处理
- 在`_handle_special_commands`中添加user命令支持
- 支持多种命令格式解析
- 提供详细的帮助信息

### 2. 数据流程

```
用户输入 → 意图识别 → 参数解析 → API调用 → 数据合并 → 格式化输出
```

### 3. 安全特性
- **敏感信息过滤**：自动移除SPC_EC和SPC_ST字段
- **参数验证**：防止无效参数导致的错误
- **错误处理**：提供友好的错误信息和建议

## 配置说明

### 1. API配置
- 基础URL：`https://mkt-admin.test.shopee.sg/mkt/admin/qaservice/api/v1`
- 超时时间：30秒
- 认证：使用完整的cookie和headers

### 2. 默认参数
- 默认环境：TEST
- 默认地区：ID
- 最大显示记录：3条

## 扩展性

### 1. 新增地区支持
在`supported_countries`字典中添加新的地区映射：
```python
self.supported_countries['new_country'] = 'NEW_COUNTRY'
```

### 2. 新增环境支持
在`supported_environments`字典中添加新的环境映射：
```python
self.supported_environments['new_env'] = 'NEW_ENV'
```

### 3. 自定义输出格式
修改`format_user_info`方法以支持不同的输出格式。

## 测试

项目包含完整的测试用例：
- 单元测试：`app01/tests/test_user_info_query.py`
- 独立测试：`test_user_info_standalone.py`

运行测试：
```bash
python test_user_info_standalone.py
```

## 问题修复记录

### 修复的问题
1. **API响应结构解析错误**
   - 问题：代码假设数据在`data`字段中，但实际API响应结构不同
   - 修复：正确解析API响应结构
     - 账户信息API：`{"result_list": [...], "page_num": 1, ...}`
     - 用户信息API：`{"msg": "success", "result": [...]}`

2. **命令解析逻辑问题**
   - 问题：`user id test qiqi.49`被错误解析为userid="test"
   - 修复：改进参数解析逻辑，正确处理复合命令格式

3. **调试信息不足**
   - 问题：API调用失败时缺乏详细的调试信息
   - 修复：添加详细的API响应日志和错误处理

### 测试验证
- ✅ API连接测试通过
- ✅ 数据合并逻辑正确
- ✅ 敏感字段过滤有效
- ✅ 命令解析逻辑修复
- ✅ 错误处理完善

## 总结

用户信息查询功能提供了一个完整、安全、易用的用户信息查询解决方案。通过支持多种查询方式、自动数据合并、参数验证等特性，为用户提供了良好的使用体验。经过问题修复和测试验证，功能现已稳定可用。
