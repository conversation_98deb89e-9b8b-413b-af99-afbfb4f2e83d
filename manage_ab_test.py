#!/usr/bin/env python3
"""
AI模型AB测试管理脚本

使用方法:
    python manage_ab_test.py status                    # 查看当前状态
    python manage_ab_test.py enable dual              # 开启双模型对比
    python manage_ab_test.py enable single a          # 开启A模型单测（别名: gpt）
    python manage_ab_test.py enable single b          # 开启B模型单测（别名: qwen）
    python manage_ab_test.py disable                  # 关闭AB测试（恢复生产模式）
"""

import sys
import os
import re
import argparse
from datetime import datetime

# 确保可以导入Django配置
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatbot_ar_be.settings')

try:
    import django
    django.setup()
    from app01.ai_config import AB_TEST_CONFIG
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)

class ABTestManager:
    """AB测试管理器"""
    
    def __init__(self):
        self.config_path = 'app01/ai_config.py'
    
    def get_current_status(self):
        """获取当前AB测试状态（名称统一由ai_config管理）"""
        enabled = AB_TEST_CONFIG.get('ENABLED', False)
        dual_mode = AB_TEST_CONFIG.get('DUAL_MODEL_COMPARISON', False)
        default_model = AB_TEST_CONFIG.get('DEFAULT_MODEL', 'A')  # 'A' 或 'B'
        model_a_name = AB_TEST_CONFIG.get('MODEL_A_DISPLAY_NAME', 'Model A')
        model_b_name = AB_TEST_CONFIG.get('MODEL_B_DISPLAY_NAME', 'Model B')
        
        if enabled:
            if dual_mode:
                mode = "AB测试双模型对比模式"
                desc = f"同时调用{model_a_name}和{model_b_name}进行对比"
            else:
                model_name = model_a_name if default_model == 'A' else model_b_name
                mode = f"AB测试单模型模式 ({model_name})"
                desc = f"使用{model_name}进行测试"
        else:
            current_name = model_a_name if default_model == 'A' else model_b_name
            mode = f"正式生产模式 ({current_name})"
            desc = f"使用稳定的{current_name}模型"
        
        return {
            'enabled': enabled,
            'dual_mode': dual_mode,
            'default_model': default_model,
            'mode': mode,
            'description': desc
        }
    
    def print_status(self):
        """打印当前状态"""
        status = self.get_current_status()
        
        print("\n🤖 **AI模型AB测试状态**")
        print("=" * 50)
        print(f"📊 当前模式: {status['mode']}")
        print(f"📝 描述: {status['description']}")
        print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
    
    def update_config(self, enabled: bool, dual_mode: bool = False, default_model: str = 'A'):
        """更新配置文件"""
        try:
            # 读取当前配置
            with open(self.config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 备份原配置
            backup_path = f"{self.config_path}.backup.{int(datetime.now().timestamp())}"
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"📁 配置文件已备份至: {backup_path}")
            
            # 更新配置
            content = re.sub(
                r"'ENABLED':\s*(True|False)",
                f"'ENABLED': {enabled}",
                content
            )
            
            content = re.sub(
                r"'DUAL_MODEL_COMPARISON':\s*(True|False)",
                f"'DUAL_MODEL_COMPARISON': {dual_mode}",
                content
            )
            
            content = re.sub(
                r"'DEFAULT_MODEL':\s*'[^']*'",
                f"'DEFAULT_MODEL': '{default_model}'",
                content
            )
            
            # 写回配置文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return True
            
        except Exception as e:
            print(f"❌ 配置更新失败: {e}")
            return False
    
    def enable_ab_test(self, mode: str = 'dual', model: str = 'A'):
        """开启AB测试"""
        if mode == 'dual':
            success = self.update_config(enabled=True, dual_mode=True)
            mode_desc = "双模型对比模式"
        elif mode == 'single':
            # 兼容别名: a/b/gpt/qwen -> A/B
            mapping = {'a': 'A', 'b': 'B'}
            default_model_ab = mapping.get(model.lower(), 'A')
            success = self.update_config(enabled=True, dual_mode=False, default_model=default_model_ab)
            model_a_name = AB_TEST_CONFIG.get('MODEL_A_DISPLAY_NAME', 'Model A')
            model_b_name = AB_TEST_CONFIG.get('MODEL_B_DISPLAY_NAME', 'Model B')
            model_name = model_a_name if default_model_ab == 'A' else model_b_name
            mode_desc = f"单模型测试模式 ({model_name})"
        else:
            print("❌ 无效的模式参数，必须是 'dual' 或 'single'")
            return False
        
        if success:
            print(f"✅ AB测试已开启: {mode_desc}")
            print(f"⏰ 开启时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            if mode == 'dual':
                print("💡 现在将同时调用两个模型，可以对比回答质量！")
            else:
                print(f"💡 现在使用{model_name}进行测试。")
            
            print("\n⚠️  提醒: AB测试适合短期评估，长期使用建议切换回生产模式。")
            return True
        else:
            return False
    
    def disable_ab_test(self):
        """关闭AB测试，恢复生产模式"""
        # 关闭但保留当前默认A/B设置
        success = self.update_config(enabled=False, dual_mode=False, default_model=AB_TEST_CONFIG.get('DEFAULT_MODEL', 'A'))
        
        if success:
            model_a_name = AB_TEST_CONFIG.get('MODEL_A_DISPLAY_NAME', 'Model A')
            model_b_name = AB_TEST_CONFIG.get('MODEL_B_DISPLAY_NAME', 'Model B')
            current_name = model_a_name if AB_TEST_CONFIG.get('DEFAULT_MODEL', 'A') == 'A' else model_b_name
            print("✅ AB测试已关闭，已恢复正式生产模式")
            print(f"⏰ 关闭时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"💡 现在使用稳定的{current_name}模型。")
            return True
        else:
            return False
    
    def print_help(self):
        """打印帮助信息"""
        # 动态读取名称
        model_a_name = AB_TEST_CONFIG.get('MODEL_A_DISPLAY_NAME', 'Model A')
        model_b_name = AB_TEST_CONFIG.get('MODEL_B_DISPLAY_NAME', 'Model B')
        print(f"""🤖 **AI模型AB测试管理工具**

**基本命令:**
  status                    查看当前AB测试状态

**开启AB测试:**
  enable dual              开启双模型对比（同时调用两个模型）
  enable single a          开启{model_a_name}单模型测试（别名: gpt）
  enable single b          开启{model_b_name}单模型测试（别名: qwen）

**关闭AB测试:**
  disable                  关闭AB测试，恢复正式生产模式

**示例:**
  python manage_ab_test.py status
  python manage_ab_test.py enable dual
  python manage_ab_test.py enable single a
  python manage_ab_test.py disable

**注意事项:**
• AB测试模式适合短期测试和对比
• 正式工作环境建议使用生产模式确保稳定性
• 每次配置变更都会自动备份原始配置文件
• 需要重启Django服务才能生效""")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='AI模型AB测试管理工具')
    parser.add_argument('command', nargs='*', help='管理命令')
    
    args = parser.parse_args()
    manager = ABTestManager()
    
    if not args.command:
        manager.print_help()
        return
    
    command = args.command[0].lower()
    
    if command == 'status':
        manager.print_status()
    
    elif command == 'enable':
        if len(args.command) < 2:
            print("❌ enable命令需要指定模式: dual 或 single")
            print("示例: python manage_ab_test.py enable dual")
            print("示例: python manage_ab_test.py enable single a")
            return
        
        mode = args.command[1].lower()
        
        if mode == 'dual':
            manager.enable_ab_test('dual')
        elif mode == 'single':
            if len(args.command) < 3:
                print("❌ single模式需要指定模型: a 或 b")
                print("示例: python manage_ab_test.py enable single a")
                return
            
            model = args.command[2].lower()
            if model not in ['a', 'b']:
                print("❌ 无效的模型名称，支持: a, b")
                return
            
            manager.enable_ab_test('single', model)
        else:
            print("❌ 无效的模式，支持: dual, single")
    
    elif command == 'disable':
        manager.disable_ab_test()
    
    else:
        print(f"❌ 未知命令: {command}")
        manager.print_help()

if __name__ == '__main__':
    main() 