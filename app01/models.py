from django.db import models
from datetime import time
from django.utils import timezone
from cryptography.fernet import Fernet

# Create your models here.


class PipeLine(models.Model):
    id = models.AutoField(primary_key=True)
    dev = models.CharField(max_length=64)
    startTime = models.DateTimeField(auto_now_add=True)
    pipelineURL = models.CharField(max_length=128)
    projectName = models.CharField(max_length=128)
    gitlabProjectURL = models.Char<PERSON>ield(max_length=128)
    branchType = models.CharField(max_length=16)


class AutoTest(models.Model):
    startTime = models.DateTimeField(auto_now_add=True)
    env = models.CharField(max_length=16)
    pipelineURL = models.CharField(max_length=128)
    projectName = models.CharField(max_length=128)
    result = models.CharField(max_length=16)
    fail_count = models.CharField(max_length=16)
    if_shopee = models.Char<PERSON><PERSON>(max_length=16)
    qa_name = models.CharField(max_length=128)
    id = models.AutoField(primary_key=True)


class Jenkins(models.Model):
    id = models.AutoField(primary_key=True)
    jenkinsID = models.CharField(max_length=128)
    projectURL = models.CharField(max_length=128)
    jenkinsProjectURL = models.CharField(max_length=128)
    jobName = models.CharField(max_length=128)
    deployTime = models.DateTimeField(auto_now_add=True)
    env = models.CharField(max_length=16)
    cid = models.CharField(max_length=128)
    result = models.CharField(max_length=16)


class Deploy(models.Model):
    id = models.AutoField(primary_key=True)
    devName = models.CharField(max_length=64, null=True)
    startTime = models.DateTimeField(auto_now_add=True)
    pipelineURL = models.CharField(max_length=128, null=True)
    projectName = models.CharField(max_length=128, null=True)
    gitlabProjectURL = models.CharField(max_length=128, null=True)
    branchType = models.CharField(max_length=128, null=True)
    callbackID = models.CharField(max_length=128, null=True)
    jenkinsDeployResult = models.CharField(max_length=16, null=True)
    jenkinsAutoTestResult = models.CharField(max_length=16, null=True)
    jenkinsAutoTestID = models.CharField(max_length=16, null=True)
    env = models.CharField(max_length=128,null=True)
    pfb = models.BooleanField(default=False,null=True)


class Autorelease(models.Model):
    id = models.AutoField(primary_key=True)
    releaseKey = models.CharField(max_length=64, null=True)
    releaseTitle = models.CharField(max_length=128, null=True)
    releaseFeature = models.TextField()
    releaseData = models.JSONField(null=True)
    releaseTime = models.DateTimeField(auto_now=True)
    stepStatus = models.CharField(max_length=128, null=True)
    servicesList = models.TextField()


class SeatalkGroup(models.Model):
    id = models.AutoField(primary_key=True)
    group_id = models.CharField(max_length=255)
    group_name = models.CharField(max_length=255, null=True)
    
    class Meta:
        verbose_name = 'Seatalk群组'
        verbose_name_plural = 'Seatalk群组'
    
    def __str__(self):
        return f"{self.group_name} ({self.group_id})"

class SeatalkProcess(models.Model):
    job_name = models.CharField(max_length=255, null=True)
    job_process = models.BooleanField(null=True)

class ReleaseTitle(models.Model):
    id = models.AutoField(primary_key=True)
    releaseTitle = models.CharField(max_length=255)

class JIRAReleaseListDetails(models.Model):
    date = models.CharField(max_length=100)
    url = models.CharField(max_length=200)
    count = models.CharField(max_length=50)
    name = models.CharField(max_length=200)

class CalendarJiraReleaseList(models.Model):
    name = models.CharField(max_length=100)
    data = models.JSONField()


# AI 功能相关数据模型
class UserConversation(models.Model):
    """用户会话管理"""
    user_id = models.CharField(max_length=100)  # 用户ID
    group_id = models.CharField(max_length=100, null=True, blank=True)  # 群组ID
    employee_code = models.CharField(max_length=100, null=True, blank=True)  # 私聊用户employee_code
    session_id = models.CharField(max_length=100, unique=True)  # 会话ID
    context = models.JSONField(default=dict)  # 会话上下文
    created_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    
    def is_expired(self, hours: int = 1) -> bool:
        """检查会话是否过期"""
        from datetime import timedelta
        return timezone.now() - self.last_activity > timedelta(hours=hours)
    
    class Meta:
        db_table = 'user_conversation'
        verbose_name = '用户会话'
        verbose_name_plural = '用户会话'


class AIQueryHistory(models.Model):
    """AI查询历史"""
    user_id = models.CharField(max_length=100)
    group_id = models.CharField(max_length=100, null=True, blank=True)
    employee_code = models.CharField(max_length=100, null=True, blank=True)
    query = models.TextField()  # 用户查询
    intent = models.CharField(max_length=50)  # 识别的意图
    jql = models.TextField()  # 生成的JQL
    result_count = models.IntegerField(default=0)  # 结果数量
    success = models.BooleanField(default=False)  # 是否成功
    processing_time = models.FloatField(default=0.0)  # 处理时间(秒)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'ai_query_history'
        verbose_name = 'AI查询历史'
        verbose_name_plural = 'AI查询历史'


class AIConfig(models.Model):
    """AI配置管理"""
    key = models.CharField(max_length=100, unique=True)
    value = models.TextField()
    description = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'ai_config'
        verbose_name = 'AI配置'
        verbose_name_plural = 'AI配置'


class UserJiraToken(models.Model):
    """用户JIRA Token管理"""
    user_email = models.EmailField(unique=True)
    jira_token = models.TextField()  # 加密存储的token
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_used = models.DateTimeField(null=True, blank=True)
    
    def mark_used(self):
        """标记token被使用"""
        self.last_used = timezone.now()
        self.save(update_fields=['last_used'])
    
    def is_expired(self, days: int = 90) -> bool:
        """检查token是否过期（基于最后使用时间）"""
        if not self.last_used:
            return False
        from datetime import timedelta
        return timezone.now() - self.last_used > timedelta(days=days)
    
    class Meta:
        db_table = 'user_jira_token'
        verbose_name = '用户JIRA Token'
        verbose_name_plural = '用户JIRA Token'


class UserScheduledTask(models.Model):
    """用户定时任务"""
    
    TASK_STATUS_CHOICES = [
        ('active', '激活'),
        ('paused', '暂停'),
        ('disabled', '禁用'),
    ]
    
    # 通知方式选择 - 主要用于向后兼容
    NOTIFICATION_TYPE_CHOICES = [
        ('creator', '发送给任务创建人'),
        ('assignee', '发送给查询结果的assignee'), 
        ('group', '发送到指定群'),
        ('auto_group', '发送到查询结果所在群'),
        ('multi', '多种发送方式'),  # 当选择多个时使用
    ]
    
    # 实际的多选发送方式 - 存储在notification_methods字段
    NOTIFICATION_METHODS_CHOICES = [
        ('creator', '发送给任务创建人'),
        ('assignee', '发送给查询结果的assignee'),
        ('group', '发送到指定群'), 
        ('auto_group', '发送到查询结果所在群'),
    ]
    
    FREQUENCY_CHOICES = [
        ('daily', '每天'),
        ('hourly', '每小时'),
        ('interval', '间隔执行'),
        ('weekly', '每周'),
        ('monthly', '每月'),
        ('custom', '自定义'),
    ]
    
    TASK_TYPE_CHOICES = [
        ('jira_query', 'JIRA查询任务'),
        ('reminder', '提醒任务'),
    ]
    
    # 基本信息
    user_id = models.CharField(max_length=100)  # 用户ID
    user_email = models.CharField(max_length=200, null=True, blank=True)  # 用户邮箱
    employee_code = models.CharField(max_length=100, null=True, blank=True)  # 员工代码
    
    # 任务信息
    task_type = models.CharField(max_length=20, choices=TASK_TYPE_CHOICES, default='jira_query', help_text='任务类型：JIRA查询任务或纯提醒任务')
    task_name = models.CharField(max_length=200)  # 任务名称
    task_description = models.TextField(null=True, blank=True)  # 任务描述
    query_text = models.TextField(null=True, blank=True, help_text='JIRA查询文本（JIRA查询任务必填，提醒任务可选）')  # 查询文本（用户的自然语言输入）
    reminder_message = models.TextField(null=True, blank=True, help_text='提醒任务的消息内容')  # 提醒消息
    
    # 调度信息
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES, default='daily')
    schedule_time = models.TimeField()  # 执行时间 (HH:MM)
    schedule_days = models.JSONField(default=list, null=True, blank=True)  # 执行日期 [1,2,3,4,5] 表示周一到周五
    interval_config = models.JSONField(null=True, blank=True, help_text='间隔执行配置: {"start_time": "09:00", "end_time": "18:00", "interval_minutes": 30}')
    timezone = models.CharField(max_length=50, default='Asia/Singapore')  # 时区
    
    # 通知设置
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPE_CHOICES, default='private')
    notification_methods = models.JSONField(default=list, null=True, blank=True, help_text='多选发送方式: ["creator", "assignee", "group", "auto_group"]')
    target_group_id = models.TextField(null=True, blank=True)  # 目标群组ID或智能通知配置JSON
    
    # 状态管理
    status = models.CharField(max_length=20, choices=TASK_STATUS_CHOICES, default='active')
    is_active = models.BooleanField(default=True)
    
    # 执行统计
    total_executions = models.IntegerField(default=0)  # 总执行次数
    successful_executions = models.IntegerField(default=0)  # 成功执行次数
    last_execution = models.DateTimeField(null=True, blank=True)  # 最后执行时间
    next_execution = models.DateTimeField(null=True, blank=True)  # 下次执行时间
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'user_scheduled_task'
        indexes = [
            models.Index(fields=['user_id', 'status']),
            models.Index(fields=['next_execution', 'is_active']),
            models.Index(fields=['schedule_time']),
            models.Index(fields=['task_type']),
        ]
    
    def __str__(self):
        return f"{self.task_name} - {self.user_id}"
    
    def calculate_next_execution(self):
        """计算下次执行时间"""
        from datetime import datetime, timedelta
        import pytz
        
        try:
            tz = pytz.timezone(self.timezone)
            now = datetime.now(tz)
            
            # 验证schedule_time的类型
            if not hasattr(self.schedule_time, 'hour') or not hasattr(self.schedule_time, 'minute'):
                raise ValueError(f"schedule_time类型错误: {type(self.schedule_time)}, 值: {self.schedule_time}")
            
            if self.frequency == 'daily':
                # 每天执行
                next_time = now.replace(
                    hour=self.schedule_time.hour,
                    minute=self.schedule_time.minute,
                    second=0,
                    microsecond=0
                )
                if next_time <= now:
                    next_time += timedelta(days=1)

            elif self.frequency == 'hourly':
                # 每小时执行
                next_time = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)

            elif self.frequency == 'interval':
                # 间隔执行
                if not self.interval_config:
                    # 如果没有配置，默认每30分钟执行一次
                    next_time = now + timedelta(minutes=30)
                else:
                    try:
                        config = self.interval_config if isinstance(self.interval_config, dict) else {}
                        start_time_str = config.get('start_time', '09:00')
                        end_time_str = config.get('end_time', '18:00')
                        interval_minutes = config.get('interval_minutes', 30)

                        # 解析开始和结束时间
                        start_hour, start_minute = map(int, start_time_str.split(':'))
                        end_hour, end_minute = map(int, end_time_str.split(':'))

                        # 今天的开始和结束时间
                        today_start = now.replace(hour=start_hour, minute=start_minute, second=0, microsecond=0)
                        today_end = now.replace(hour=end_hour, minute=end_minute, second=0, microsecond=0)

                        if now < today_start:
                            # 当前时间早于开始时间，下次执行就是今天的开始时间
                            next_time = today_start
                        elif now >= today_end:
                            # 当前时间晚于结束时间，下次执行是明天的开始时间
                            next_time = today_start + timedelta(days=1)
                        else:
                            # 在执行时间段内，计算下一个间隔时间
                            minutes_since_start = (now - today_start).total_seconds() / 60
                            next_interval = ((int(minutes_since_start) // interval_minutes) + 1) * interval_minutes
                            next_time = today_start + timedelta(minutes=next_interval)

                            # 如果超过了结束时间，设置为明天的开始时间
                            if next_time > today_end:
                                next_time = today_start + timedelta(days=1)
                    except Exception as e:
                        # 配置解析失败，使用默认间隔
                        next_time = now + timedelta(minutes=30)
                    
            elif self.frequency == 'weekly':
                # 每周执行（基于 schedule_days）
                # 处理schedule_days的不同格式
                target_weekdays = []
                
                if isinstance(self.schedule_days, str):
                    # 字符串格式: "1,2,3,4,5"
                    try:
                        target_weekdays = [int(d.strip()) for d in self.schedule_days.split(',') if d.strip()]
                    except (ValueError, AttributeError):
                        target_weekdays = [1, 2, 3, 4, 5]  # 默认工作日
                elif isinstance(self.schedule_days, list):
                    # 列表格式: [1, 2, 3, 4, 5] 或 ["1", "2", "3", "4", "5"]
                    try:
                        target_weekdays = [int(d) for d in self.schedule_days if str(d).isdigit()]
                    except (ValueError, TypeError):
                        target_weekdays = [1, 2, 3, 4, 5]  # 默认工作日
                else:
                    # 默认工作日
                    target_weekdays = [1, 2, 3, 4, 5]
                
                # 确保target_weekdays是整数列表
                if not target_weekdays:
                    target_weekdays = [1, 2, 3, 4, 5]  # 默认工作日
                    
                next_time = None
                
                for i in range(7):  # 最多查找7天
                    check_date = now + timedelta(days=i)
                    if check_date.weekday() + 1 in target_weekdays:  # weekday() 返回 0-6，我们使用 1-7
                        candidate_time = check_date.replace(
                            hour=self.schedule_time.hour,
                            minute=self.schedule_time.minute,
                            second=0,
                            microsecond=0
                        )
                        if candidate_time > now:
                            next_time = candidate_time
                            break
                            
            elif self.frequency == 'monthly':
                # 每月执行（支持多个日期）
                # 处理schedule_days的不同格式
                target_days = []
                try:
                    if isinstance(self.schedule_days, str):
                        # 字符串格式: "15" 或 "15,20"
                        target_days = [int(d.strip()) for d in self.schedule_days.split(',') if d.strip()]
                    elif isinstance(self.schedule_days, list) and self.schedule_days:
                        # 列表格式: [15] 或 [15, 20] 或 [15, -1]
                        target_days = [int(d) for d in self.schedule_days if str(d).lstrip('-').isdigit()]

                    if not target_days:
                        target_days = [1]  # 默认每月1号
                except (ValueError, TypeError):
                    target_days = [1]  # 默认每月1号

                # 找到下一个执行日期
                next_time = None
                current_month = now.month
                current_year = now.year

                # 先检查本月是否还有可执行的日期
                for target_day in sorted(target_days):
                    try:
                        if target_day == -1:
                            # 月末：获取当月最后一天
                            import calendar
                            last_day = calendar.monthrange(current_year, current_month)[1]
                            candidate_time = now.replace(
                                day=last_day,
                                hour=self.schedule_time.hour,
                                minute=self.schedule_time.minute,
                                second=0,
                                microsecond=0
                            )
                        else:
                            # 具体日期
                            candidate_time = now.replace(
                                day=target_day,
                                hour=self.schedule_time.hour,
                                minute=self.schedule_time.minute,
                                second=0,
                                microsecond=0
                            )

                        if candidate_time > now:
                            next_time = candidate_time
                            break
                    except ValueError:
                        # 日期无效（如2月30日），跳过
                        continue

                # 如果本月没有可执行的日期，找下个月的第一个日期
                if next_time is None:
                    next_month = current_month + 1 if current_month < 12 else 1
                    next_year = current_year if current_month < 12 else current_year + 1

                    for target_day in sorted(target_days):
                        try:
                            if target_day == -1:
                                # 月末：获取下月最后一天
                                import calendar
                                last_day = calendar.monthrange(next_year, next_month)[1]
                                next_time = now.replace(
                                    year=next_year,
                                    month=next_month,
                                    day=last_day,
                                    hour=self.schedule_time.hour,
                                    minute=self.schedule_time.minute,
                                    second=0,
                                    microsecond=0
                                )
                            else:
                                # 具体日期
                                next_time = now.replace(
                                    year=next_year,
                                    month=next_month,
                                    day=target_day,
                                    hour=self.schedule_time.hour,
                                    minute=self.schedule_time.minute,
                                    second=0,
                                    microsecond=0
                                )
                            break
                        except ValueError:
                            # 日期无效，跳过
                            continue

                # 如果还是没有找到有效日期，默认下个月1号
                if next_time is None:
                    next_month = current_month + 1 if current_month < 12 else 1
                    next_year = current_year if current_month < 12 else current_year + 1
                    next_time = now.replace(
                        year=next_year,
                        month=next_month,
                        day=1,
                        hour=self.schedule_time.hour,
                        minute=self.schedule_time.minute,
                        second=0,
                        microsecond=0
                    )
            else:
                # 未知频率，默认设置为明天同一时间
                next_time = now.replace(
                    hour=self.schedule_time.hour,
                    minute=self.schedule_time.minute,
                    second=0,
                    microsecond=0
                ) + timedelta(days=1)
            
            self.next_execution = next_time
            return next_time
            
        except Exception as e:
            # 如果计算失败，设置为当前时间+1小时作为默认值
            import logging
            from django.utils import timezone
            logger = logging.getLogger(__name__)
            logger.error(f"计算下次执行时间失败 (任务ID: {self.id}): {str(e)}")
            logger.error(f"任务详情 - frequency: {self.frequency}, schedule_time: {self.schedule_time}, schedule_days: {self.schedule_days}")
            
            # 设置默认的下次执行时间（1小时后）
            default_next = timezone.now() + timedelta(hours=1)
            self.next_execution = default_next
            return default_next


class TaskExecutionLog(models.Model):
    """任务执行日志"""
    
    task = models.ForeignKey(UserScheduledTask, on_delete=models.CASCADE, related_name='execution_logs')
    execution_time = models.DateTimeField(auto_now_add=True)
    success = models.BooleanField(default=False)
    
    # 执行结果
    query_result = models.JSONField(null=True, blank=True)  # 查询结果
    response_text = models.TextField(null=True, blank=True)  # 响应文本
    error_message = models.TextField(null=True, blank=True)  # 错误信息
    
    # 性能指标
    execution_duration = models.FloatField(null=True, blank=True)  # 执行耗时（秒）
    result_count = models.IntegerField(default=0)  # 结果数量
    
    class Meta:
        db_table = 'task_execution_log'
        indexes = [
            models.Index(fields=['task', 'execution_time']),
            models.Index(fields=['success']),
        ]
    
    def __str__(self):
        return f"{self.task.task_name} - {self.execution_time}"


class TaskTemplate(models.Model):
    """任务模板"""
    
    TEMPLATE_CATEGORY_CHOICES = [
        ('daily', '日常任务'),
        ('weekly', '周报相关'),
        ('monthly', '月度统计'),
        ('project', '项目管理'),
        ('bug_tracking', 'Bug跟踪'),
        ('custom', '自定义'),
    ]
    
    name = models.CharField(max_length=200)  # 模板名称
    description = models.TextField(null=True, blank=True)  # 模板描述
    category = models.CharField(max_length=50, choices=TEMPLATE_CATEGORY_CHOICES, default='custom')
    
    # 模板内容
    query_template = models.TextField()  # 查询模板（支持变量替换）
    default_schedule = models.CharField(max_length=100)  # 默认调度表达式
    
    # 模板变量定义
    template_variables = models.JSONField(default=dict)  # 模板变量配置
    
    # 权限和状态
    is_public = models.BooleanField(default=True)  # 是否公开
    is_active = models.BooleanField(default=True)  # 是否激活
    created_by = models.CharField(max_length=100, null=True, blank=True)  # 创建者
    
    # 使用统计
    usage_count = models.IntegerField(default=0)  # 使用次数
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'task_template'
        indexes = [
            models.Index(fields=['category', 'is_active']),
            models.Index(fields=['is_public', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.category})"


class AdvancedTaskFeatureWhitelist(models.Model):
    """高级功能白名单"""
    
    FEATURE_CHOICES = [
        ('group_notification', '群聊通知'),
        ('conditional_trigger', '条件触发'),
        ('task_template', '任务模板'),
        ('batch_management', '批量管理'),
        ('statistics_report', '统计报表'),
        ('all_features', '所有高级功能'),
    ]
    
    user_id = models.CharField(max_length=100)  # 用户ID
    user_email = models.CharField(max_length=200, null=True, blank=True)  # 用户邮箱
    employee_code = models.CharField(max_length=100, null=True, blank=True)  # 员工代码
    
    # 权限配置
    allowed_features = models.JSONField(default=list)  # 允许的功能列表
    
    # 限制配置
    max_tasks = models.IntegerField(default=20)  # 最大任务数
    max_templates = models.IntegerField(default=10)  # 最大模板数
    
    # 状态
    is_active = models.BooleanField(default=True)
    granted_by = models.CharField(max_length=100, null=True, blank=True)  # 授权人
    granted_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)  # 过期时间
    
    class Meta:
        db_table = 'advanced_task_feature_whitelist'
        unique_together = ['user_id']
        indexes = [
            models.Index(fields=['user_id', 'is_active']),
            models.Index(fields=['expires_at']),
        ]
    
    def __str__(self):
        return f"Advanced Features for {self.user_id}"
    
    def has_feature(self, feature_name: str) -> bool:
        """检查用户是否有特定功能权限"""
        if not self.is_active:
            return False
        
        # 检查是否过期
        if self.expires_at and timezone.now() > self.expires_at:
            return False
        
        return (
            'all_features' in self.allowed_features or 
            feature_name in self.allowed_features
        )


class ConditionalTrigger(models.Model):
    """条件触发器"""
    
    TRIGGER_TYPE_CHOICES = [
        ('jira_event', 'JIRA事件'),
        ('time_based', '时间条件'),
        ('status_change', '状态变化'),
        ('field_change', '字段变化'),
    ]
    
    CONDITION_OPERATOR_CHOICES = [
        ('equals', '等于'),
        ('not_equals', '不等于'),
        ('contains', '包含'),
        ('greater_than', '大于'),
        ('less_than', '小于'),
        ('in_list', '在列表中'),
    ]
    
    user_id = models.CharField(max_length=100)
    trigger_name = models.CharField(max_length=200)
    trigger_type = models.CharField(max_length=50, choices=TRIGGER_TYPE_CHOICES)
    
    # 条件配置
    condition_field = models.CharField(max_length=100)  # 监控字段
    condition_operator = models.CharField(max_length=50, choices=CONDITION_OPERATOR_CHOICES)
    condition_value = models.TextField()  # 条件值
    
    # 动作配置
    action_query = models.TextField()  # 触发时执行的查询
    notification_message = models.TextField(null=True, blank=True)  # 自定义通知消息
    
    # 状态
    is_active = models.BooleanField(default=True)
    last_triggered = models.DateTimeField(null=True, blank=True)
    trigger_count = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'conditional_trigger'
        indexes = [
            models.Index(fields=['user_id', 'is_active']),
            models.Index(fields=['trigger_type', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.trigger_name} - {self.user_id}"


class TaskExecutionStatistics(models.Model):
    """任务执行统计"""
    
    user_id = models.CharField(max_length=100)
    date = models.DateField()  # 统计日期
    
    # 执行统计
    total_executions = models.IntegerField(default=0)
    successful_executions = models.IntegerField(default=0)
    failed_executions = models.IntegerField(default=0)
    
    # 性能统计
    avg_execution_time = models.FloatField(default=0.0)  # 平均执行时间
    max_execution_time = models.FloatField(default=0.0)  # 最大执行时间
    min_execution_time = models.FloatField(default=0.0)  # 最小执行时间
    
    # 任务统计
    active_tasks_count = models.IntegerField(default=0)  # 活跃任务数
    paused_tasks_count = models.IntegerField(default=0)  # 暂停任务数
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'task_execution_statistics'
        unique_together = ['user_id', 'date']
        indexes = [
            models.Index(fields=['user_id', 'date']),
            models.Index(fields=['date']),
        ]
    
    def __str__(self):
        return f"Stats for {self.user_id} on {self.date}"


class ProcessedMessage(models.Model):
    """已处理的消息记录，用于去重"""
    event_id = models.CharField(max_length=100, unique=True, db_index=True, help_text="事件ID")
    message_id = models.CharField(max_length=200, db_index=True, help_text="消息ID") 
    seatalk_id = models.CharField(max_length=50, help_text="用户ID")
    group_id = models.CharField(max_length=50, null=True, blank=True, help_text="群组ID")
    query_content = models.TextField(help_text="查询内容")
    processed_at = models.DateTimeField(auto_now_add=True, help_text="处理时间")
    processing_duration = models.FloatField(default=0, help_text="处理耗时(秒)")
    
    class Meta:
        db_table = 'processed_messages'
        indexes = [
            models.Index(fields=['event_id']),
            models.Index(fields=['message_id']),
            models.Index(fields=['processed_at']),
        ]
        verbose_name = "已处理消息"
        verbose_name_plural = "已处理消息"
    
    def __str__(self):
        return f"Event: {self.event_id} - {self.query_content[:50]}"


class UserTodoItem(models.Model):
    """用户代办事项"""
    
    PRIORITY_CHOICES = [
        ('low', '低'),
        ('medium', '中'),
        ('high', '高'),
        ('urgent', '紧急'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '待办'),
        ('in_progress', '进行中'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
    ]
    
    CATEGORY_CHOICES = [
        ('personal', '个人'),
        ('work', '工作'),
        ('jira', 'JIRA'),
        ('meeting', '会议'),
    ]
    
    # 基本信息
    user_id = models.CharField(max_length=100, help_text="用户ID")
    user_email = models.CharField(max_length=200, null=True, blank=True, help_text="用户邮箱")
    employee_code = models.CharField(max_length=100, null=True, blank=True, help_text="员工代码")
    
    # 代办事项内容
    title = models.CharField(max_length=300, help_text="标题")
    description = models.TextField(blank=True, help_text="详细描述")
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='personal', help_text="分类")
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium', help_text="优先级")
    
    # 状态和时间
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', help_text="状态")
    due_date = models.DateField(null=True, blank=True, help_text="截止日期")
    completed_at = models.DateTimeField(null=True, blank=True, help_text="完成时间")
    completion_note = models.TextField(blank=True, help_text="完成备注")
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, help_text="创建时间")
    updated_at = models.DateTimeField(auto_now=True, help_text="更新时间")
    
    class Meta:
        db_table = 'user_todo_item'
        indexes = [
            models.Index(fields=['user_id', 'status']),
            models.Index(fields=['user_id', 'due_date']),
            models.Index(fields=['user_id', 'priority']),
            models.Index(fields=['created_at']),
        ]
        verbose_name = "用户代办事项"
        verbose_name_plural = "用户代办事项"
    
    def __str__(self):
        return f"{self.user_id} - {self.title}"
    
    @property
    def priority_order(self):
        """优先级排序值"""
        priority_map = {'urgent': 4, 'high': 3, 'medium': 2, 'low': 1}
        return priority_map.get(self.priority, 2)
    
    def is_overdue(self):
        """检查是否逾期"""
        if not self.due_date:
            return False
        if self.status in ['completed', 'cancelled']:
            return False
        return timezone.now().date() > self.due_date


class TodoCategory(models.Model):
    """代办事项分类"""
    name = models.CharField(max_length=50, unique=True, help_text="分类名称")
    description = models.CharField(max_length=200, blank=True, help_text="分类描述")
    color = models.CharField(max_length=7, default='#6B7280', help_text="分类颜色")
    is_active = models.BooleanField(default=True, help_text="是否激活")
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'todo_category'
        verbose_name = "代办事项分类"
        verbose_name_plural = "代办事项分类"
    
    def __str__(self):
        return self.name


class UserServiceUsage(models.Model):
    """用户服务使用记录"""
    
    SERVICE_TYPES = [
        ('ai_query', 'AI查询'),
        ('jira_query', 'JIRA查询'),
        ('jira_write', 'JIRA写操作'),
        ('document_processing', '文档处理'),
        ('schedule_management', '定时任务'),
        ('todo_management', '代办事项'),
        ('statistics', '统计分析'),
        ('traditional_command', '传统命令'),
    ]
    
    # 用户信息
    user_id = models.CharField(max_length=100, help_text="用户ID")
    user_email = models.CharField(max_length=200, null=True, blank=True, help_text="用户邮箱")
    employee_code = models.CharField(max_length=100, null=True, blank=True, help_text="员工代码")
    group_id = models.CharField(max_length=100, null=True, blank=True, help_text="群组ID")
    
    # 服务信息
    service_type = models.CharField(max_length=30, choices=SERVICE_TYPES, help_text="服务类型")
    intent = models.CharField(max_length=50, null=True, blank=True, help_text="识别的意图")
    query_content = models.TextField(help_text="查询内容")
    
    # 执行结果
    success = models.BooleanField(default=False, help_text="是否成功")
    response_length = models.IntegerField(default=0, help_text="响应长度")
    processing_time = models.FloatField(default=0.0, help_text="处理时间(秒)")
    error_message = models.TextField(blank=True, help_text="错误信息")
    
    # 额外数据
    metadata = models.JSONField(default=dict, blank=True, help_text="额外元数据")
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, help_text="使用时间")
    
    class Meta:
        db_table = 'user_service_usage'
        indexes = [
            models.Index(fields=['user_id', 'service_type']),
            models.Index(fields=['user_id', 'created_at']),
            models.Index(fields=['service_type', 'created_at']),
            models.Index(fields=['success', 'created_at']),
        ]
        verbose_name = "用户服务使用记录"
        verbose_name_plural = "用户服务使用记录"
    
    def __str__(self):
        return f"{self.user_id} - {self.service_type} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"


class SPCPMTimelineReminder(models.Model):
    """SPCPM项目里程碑自动提醒管理表"""
    request_id = models.CharField(max_length=32, unique=True, help_text="SPCPM Request ID，如SPCPM-123456")
    created_by = models.CharField(max_length=100, null=True, blank=True, help_text="添加人employee_code")
    group_id = models.CharField(max_length=100, null=True, blank=True, help_text="群组ID，用于指定发送提醒的群组")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'spcpm_timeline_reminder'
        verbose_name = 'SPCPM里程碑提醒项目'
        verbose_name_plural = 'SPCPM里程碑提醒项目'

    def __str__(self):
        return f"{self.request_id}"


# ================================
# 数据统计系统相关模型
# ================================

import uuid
from django.utils import timezone


class BotAccessEvent(models.Model):
    """机器人访问事件记录"""

    ACCESS_EVENT_TYPES = [
        ('user_enter_chatroom_with_bot', '用户进入机器人聊天室'),
        ('user_leave_chatroom_with_bot', '用户离开机器人聊天室'),
        ('bot_added_to_group_chat', '机器人被添加到群聊'),
        ('bot_removed_from_group_chat', '机器人被移出群聊'),
        ('user_start_chat_with_bot', '用户开始与机器人私聊'),
        ('user_block_bot', '用户屏蔽机器人'),
        ('user_unblock_bot', '用户取消屏蔽机器人'),
    ]

    # 事件基本信息
    event_id = models.CharField(max_length=100, unique=True, help_text="事件唯一ID")
    event_type = models.CharField(max_length=50, choices=ACCESS_EVENT_TYPES, help_text="事件类型")

    # 用户信息
    user_id = models.CharField(max_length=100, help_text="用户SeaTalk ID")
    employee_code = models.CharField(max_length=100, null=True, blank=True, help_text="员工代码")
    email = models.CharField(max_length=200, null=True, blank=True, help_text="用户邮箱")

    # 群组信息
    group_id = models.CharField(max_length=100, null=True, blank=True, help_text="群组ID")
    group_name = models.CharField(max_length=200, null=True, blank=True, help_text="群组名称")

    # 应用信息
    app_id = models.CharField(max_length=100, null=True, blank=True, help_text="应用ID")

    # 时间信息
    timestamp = models.BigIntegerField(help_text="原始时间戳")
    event_time = models.DateTimeField(help_text="事件发生时间")
    created_at = models.DateTimeField(auto_now_add=True, help_text="记录创建时间")

    # 额外数据
    metadata = models.JSONField(default=dict, blank=True, help_text="额外元数据")

    class Meta:
        db_table = 'bot_access_event'
        indexes = [
            models.Index(fields=['user_id', 'event_type']),
            models.Index(fields=['event_type', 'event_time']),
            models.Index(fields=['group_id', 'event_time']),
            models.Index(fields=['event_time']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-event_time']
        verbose_name = "机器人访问事件"
        verbose_name_plural = "机器人访问事件"

    def __str__(self):
        return f"{self.event_type} - {self.user_id} at {self.event_time}"


class CommandExecutionRecord(models.Model):
    """指令执行完整记录"""

    COMMAND_TYPES = [
        ('ai_query', 'AI查询'),
        ('jira_query', 'JIRA查询'),
        ('jira_write', 'JIRA写操作'),
        ('mr_check', 'MR检查'),
        ('spcpm_query', 'SPCPM查询'),
        ('schedule_management', '定时任务管理'),
        ('todo_management', '代办事项管理'),
        ('traditional_command', '传统命令'),
        ('help_request', '帮助请求'),
        ('unknown', '未知指令'),
    ]

    INTENT_TYPES = [
        ('query_tasks', '查询任务'),
        ('create_task', '创建任务'),
        ('update_task', '更新任务'),
        ('delete_task', '删除任务'),
        ('query_release', '查询发布'),
        ('query_mr', '查询MR'),
        ('query_bug', '查询Bug'),
        ('send_notification', '发送通知'),
        ('system_help', '系统帮助'),
        ('other', '其他'),
    ]

    # 执行标识
    execution_id = models.UUIDField(default=uuid.uuid4, unique=True, help_text="执行唯一ID")

    # 用户信息
    user_id = models.CharField(max_length=100, help_text="用户SeaTalk ID")
    user_email = models.CharField(max_length=200, null=True, blank=True, help_text="用户邮箱")
    employee_code = models.CharField(max_length=100, null=True, blank=True, help_text="员工代码")
    group_id = models.CharField(max_length=100, null=True, blank=True, help_text="群组ID")

    # 指令信息
    command_type = models.CharField(max_length=50, choices=COMMAND_TYPES, help_text="指令类型")
    raw_input = models.TextField(help_text="用户原始输入")
    processed_command = models.TextField(null=True, blank=True, help_text="处理后的指令")
    intent = models.CharField(max_length=50, choices=INTENT_TYPES, null=True, blank=True, help_text="识别的意图")

    # 执行结果
    success = models.BooleanField(default=False, help_text="是否执行成功")
    response_content = models.TextField(null=True, blank=True, help_text="返回给用户的内容")
    response_length = models.IntegerField(default=0, help_text="响应内容长度")
    error_message = models.TextField(null=True, blank=True, help_text="错误信息")

    # 性能指标
    processing_time = models.FloatField(default=0.0, help_text="处理时间(秒)")
    start_time = models.DateTimeField(help_text="开始处理时间")
    end_time = models.DateTimeField(null=True, blank=True, help_text="结束处理时间")

    # 技术细节
    api_calls = models.JSONField(default=list, blank=True, help_text="调用的API列表")
    database_queries = models.IntegerField(default=0, help_text="数据库查询次数")
    external_service_calls = models.JSONField(default=dict, blank=True, help_text="外部服务调用统计")

    # 上下文信息
    session_id = models.CharField(max_length=100, null=True, blank=True, help_text="会话ID")
    thread_id = models.CharField(max_length=100, null=True, blank=True, help_text="线程ID")
    message_id = models.CharField(max_length=100, null=True, blank=True, help_text="消息ID")

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, help_text="记录创建时间")

    class Meta:
        db_table = 'command_execution_record'
        indexes = [
            models.Index(fields=['user_id', 'created_at']),
            models.Index(fields=['command_type', 'created_at']),
            models.Index(fields=['success', 'created_at']),
            models.Index(fields=['intent', 'created_at']),
            models.Index(fields=['group_id', 'created_at']),
            models.Index(fields=['start_time']),
            models.Index(fields=['processing_time']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']
        verbose_name = "指令执行记录"
        verbose_name_plural = "指令执行记录"

    def save(self, *args, **kwargs):
        # 自动计算响应长度
        if self.response_content:
            self.response_length = len(self.response_content)

        # 自动计算处理时间
        if self.start_time and self.end_time:
            self.processing_time = (self.end_time - self.start_time).total_seconds()

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.command_type} - {self.user_id} - {'Success' if self.success else 'Failed'}"


class SystemPerformanceMetrics(models.Model):
    """系统性能指标"""

    METRIC_TYPES = [
        ('api_response', 'API响应'),
        ('database_query', '数据库查询'),
        ('external_service', '外部服务调用'),
        ('system_resource', '系统资源'),
        ('business_metric', '业务指标'),
    ]

    # 基本信息
    metric_type = models.CharField(max_length=30, choices=METRIC_TYPES, help_text="指标类型")
    metric_time = models.DateTimeField(help_text="指标时间")

    # API性能
    api_endpoint = models.CharField(max_length=200, null=True, blank=True, help_text="API端点")
    response_time = models.FloatField(default=0.0, help_text="响应时间(秒)")
    status_code = models.IntegerField(null=True, blank=True, help_text="HTTP状态码")

    # 系统资源
    cpu_usage = models.FloatField(null=True, blank=True, help_text="CPU使用率(%)")
    memory_usage = models.FloatField(null=True, blank=True, help_text="内存使用率(%)")
    disk_usage = models.FloatField(null=True, blank=True, help_text="磁盘使用率(%)")
    database_connections = models.IntegerField(null=True, blank=True, help_text="数据库连接数")

    # 业务指标
    active_users = models.IntegerField(default=0, help_text="活跃用户数")
    concurrent_requests = models.IntegerField(default=0, help_text="并发请求数")
    queue_length = models.IntegerField(default=0, help_text="队列长度")

    # 错误统计
    error_count = models.IntegerField(default=0, help_text="错误数量")
    warning_count = models.IntegerField(default=0, help_text="警告数量")

    # 额外数据
    metadata = models.JSONField(default=dict, blank=True, help_text="额外元数据")

    created_at = models.DateTimeField(auto_now_add=True, help_text="记录创建时间")

    class Meta:
        db_table = 'system_performance_metrics'
        indexes = [
            models.Index(fields=['metric_type', 'metric_time']),
            models.Index(fields=['api_endpoint', 'metric_time']),
            models.Index(fields=['metric_time']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-metric_time']
        verbose_name = "系统性能指标"
        verbose_name_plural = "系统性能指标"

    def __str__(self):
        return f"{self.metric_type} at {self.metric_time}"


class CronJobExecutionMonitor(models.Model):
    """定时任务执行监控"""

    JOB_STATUS = [
        ('running', '运行中'),
        ('success', '成功'),
        ('failed', '失败'),
        ('timeout', '超时'),
        ('cancelled', '已取消'),
    ]

    # 任务信息
    job_name = models.CharField(max_length=100, help_text="任务名称")
    execution_id = models.UUIDField(default=uuid.uuid4, help_text="执行ID")

    # 执行时间
    start_time = models.DateTimeField(help_text="开始时间")
    end_time = models.DateTimeField(null=True, blank=True, help_text="结束时间")
    duration = models.FloatField(null=True, blank=True, help_text="执行时长(秒)")

    # 执行状态
    status = models.CharField(max_length=20, choices=JOB_STATUS, default='running', help_text="执行状态")
    success = models.BooleanField(default=False, help_text="是否成功")

    # 执行结果
    output = models.TextField(null=True, blank=True, help_text="执行输出")
    error_message = models.TextField(null=True, blank=True, help_text="错误信息")
    exit_code = models.IntegerField(null=True, blank=True, help_text="退出码")

    # 资源使用
    memory_peak = models.FloatField(null=True, blank=True, help_text="内存峰值(MB)")
    cpu_time = models.FloatField(null=True, blank=True, help_text="CPU时间(秒)")

    # 调度信息
    scheduled_time = models.DateTimeField(null=True, blank=True, help_text="计划执行时间")
    trigger_type = models.CharField(max_length=20, default='cron', help_text="触发类型")

    # 额外数据
    metadata = models.JSONField(default=dict, blank=True, help_text="额外元数据")

    created_at = models.DateTimeField(auto_now_add=True, help_text="记录创建时间")

    class Meta:
        db_table = 'cronjob_execution_monitor'
        indexes = [
            models.Index(fields=['job_name', 'start_time']),
            models.Index(fields=['status', 'start_time']),
            models.Index(fields=['success', 'start_time']),
            models.Index(fields=['start_time']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-start_time']
        verbose_name = "定时任务执行监控"
        verbose_name_plural = "定时任务执行监控"

    def save(self, *args, **kwargs):
        # 自动计算执行时长
        if self.start_time and self.end_time:
            self.duration = (self.end_time - self.start_time).total_seconds()

        # 根据状态设置成功标志
        if self.status == 'success':
            self.success = True
        elif self.status in ['failed', 'timeout', 'cancelled']:
            self.success = False

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.job_name} - {self.status} at {self.start_time}"


class UserActivitySummary(models.Model):
    """用户活动汇总"""

    SUMMARY_PERIODS = [
        ('hourly', '小时'),
        ('daily', '日'),
        ('weekly', '周'),
        ('monthly', '月'),
    ]

    # 汇总信息
    user_id = models.CharField(max_length=100, help_text="用户SeaTalk ID")
    user_email = models.CharField(max_length=200, null=True, blank=True, help_text="用户邮箱")
    employee_code = models.CharField(max_length=100, null=True, blank=True, help_text="员工代码")

    # 时间周期
    period_type = models.CharField(max_length=20, choices=SUMMARY_PERIODS, help_text="汇总周期")
    period_start = models.DateTimeField(help_text="周期开始时间")
    period_end = models.DateTimeField(help_text="周期结束时间")

    # 活动统计
    total_commands = models.IntegerField(default=0, help_text="总指令数")
    successful_commands = models.IntegerField(default=0, help_text="成功指令数")
    failed_commands = models.IntegerField(default=0, help_text="失败指令数")

    # 指令类型分布
    ai_queries = models.IntegerField(default=0, help_text="AI查询次数")
    jira_operations = models.IntegerField(default=0, help_text="JIRA操作次数")
    mr_checks = models.IntegerField(default=0, help_text="MR检查次数")
    schedule_operations = models.IntegerField(default=0, help_text="定时任务操作次数")

    # 性能指标
    avg_response_time = models.FloatField(default=0.0, help_text="平均响应时间(秒)")
    max_response_time = models.FloatField(default=0.0, help_text="最大响应时间(秒)")
    min_response_time = models.FloatField(default=0.0, help_text="最小响应时间(秒)")

    # 使用模式
    peak_hour = models.IntegerField(null=True, blank=True, help_text="使用高峰小时")
    active_days = models.IntegerField(default=0, help_text="活跃天数")
    total_session_time = models.FloatField(default=0.0, help_text="总会话时间(分钟)")

    # 群组活动
    group_interactions = models.IntegerField(default=0, help_text="群组交互次数")
    private_interactions = models.IntegerField(default=0, help_text="私聊交互次数")

    # 错误分析
    common_errors = models.JSONField(default=list, blank=True, help_text="常见错误列表")
    error_rate = models.FloatField(default=0.0, help_text="错误率(%)")

    created_at = models.DateTimeField(auto_now_add=True, help_text="记录创建时间")
    updated_at = models.DateTimeField(auto_now=True, help_text="记录更新时间")

    class Meta:
        db_table = 'user_activity_summary'
        unique_together = ['user_id', 'period_type', 'period_start']
        indexes = [
            models.Index(fields=['user_id', 'period_type', 'period_start']),
            models.Index(fields=['period_type', 'period_start']),
            models.Index(fields=['period_start']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-period_start']
        verbose_name = "用户活动汇总"
        verbose_name_plural = "用户活动汇总"

    def calculate_success_rate(self):
        """计算成功率"""
        if self.total_commands > 0:
            return round((self.successful_commands / self.total_commands) * 100, 2)
        return 0.0

    def __str__(self):
        return f"{self.user_id} - {self.period_type} - {self.period_start.date()}"


class SystemHealthSnapshot(models.Model):
    """系统健康状况快照"""

    HEALTH_STATUS = [
        ('healthy', '健康'),
        ('warning', '警告'),
        ('critical', '严重'),
        ('down', '宕机'),
    ]

    # 快照信息
    snapshot_time = models.DateTimeField(help_text="快照时间")
    overall_status = models.CharField(max_length=20, choices=HEALTH_STATUS, help_text="整体状态")

    # 系统指标
    total_users_today = models.IntegerField(default=0, help_text="今日总用户数")
    active_users_now = models.IntegerField(default=0, help_text="当前活跃用户数")
    total_commands_today = models.IntegerField(default=0, help_text="今日总指令数")
    success_rate_today = models.FloatField(default=0.0, help_text="今日成功率(%)")

    # 性能指标
    avg_response_time = models.FloatField(default=0.0, help_text="平均响应时间(秒)")
    system_load = models.FloatField(default=0.0, help_text="系统负载")
    memory_usage = models.FloatField(default=0.0, help_text="内存使用率(%)")
    cpu_usage = models.FloatField(default=0.0, help_text="CPU使用率(%)")

    # 服务状态
    database_status = models.CharField(max_length=20, default='healthy', help_text="数据库状态")
    redis_status = models.CharField(max_length=20, default='healthy', help_text="Redis状态")
    external_services_status = models.JSONField(default=dict, help_text="外部服务状态")

    # 错误统计
    error_count_last_hour = models.IntegerField(default=0, help_text="最近1小时错误数")
    warning_count_last_hour = models.IntegerField(default=0, help_text="最近1小时警告数")

    # 定时任务状态
    cronjobs_running = models.IntegerField(default=0, help_text="运行中的定时任务数")
    cronjobs_failed_today = models.IntegerField(default=0, help_text="今日失败的定时任务数")

    # 详细信息
    details = models.JSONField(default=dict, blank=True, help_text="详细信息")
    alerts = models.JSONField(default=list, blank=True, help_text="告警信息")

    created_at = models.DateTimeField(auto_now_add=True, help_text="记录创建时间")

    class Meta:
        db_table = 'system_health_snapshot'
        indexes = [
            models.Index(fields=['snapshot_time']),
            models.Index(fields=['overall_status', 'snapshot_time']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-snapshot_time']
        verbose_name = "系统健康快照"
        verbose_name_plural = "系统健康快照"

    def __str__(self):
        return f"Health Snapshot - {self.overall_status} at {self.snapshot_time}"


# ================================
# R&D Efficiency Metrics Models
# ================================

class RDTeam(models.Model):
    """R&D Team Configuration"""
    team_id = models.CharField(max_length=50, unique=True, help_text="团队唯一标识")
    team_name = models.CharField(max_length=100, help_text="团队名称")
    department = models.CharField(max_length=100, help_text="所属部门")
    team_leader_email = models.EmailField(help_text="团队负责人邮箱")
    parent_team_id = models.CharField(max_length=50, null=True, blank=True, help_text="父团队ID（用于层级结构）")
    is_active = models.BooleanField(default=True, help_text="是否激活")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Configuration - 使用TextField存储JSON字符串以兼容MySQL
    jira_projects = models.TextField(default='[]', help_text="关联的JIRA项目列表，JSON格式如['SPCB', 'SPCT']")
    git_repositories = models.TextField(default='[]', help_text="关联的Git仓库列表，JSON格式，来自services_id.json")

    # Test data marker
    is_test_data = models.BooleanField(default=False, help_text="是否为测试数据")

    class Meta:
        db_table = 'rd_team'
        verbose_name = 'R&D团队'
        verbose_name_plural = 'R&D团队'
        indexes = [
            models.Index(fields=['team_id', 'is_active']),
            models.Index(fields=['department']),
            models.Index(fields=['is_test_data']),
        ]

    def __str__(self):
        return f"{self.team_name} ({self.team_id})"


class RDTeamMember(models.Model):
    """R&D Team Member Configuration"""
    ROLE_CHOICES = [
        ('developer', 'Developer'),
        ('tester', 'Tester'),
        ('pm', 'Product Manager'),
        ('lead', 'Tech Lead'),
        ('manager', 'Manager'),
    ]

    team = models.ForeignKey(RDTeam, on_delete=models.CASCADE, related_name='members')
    member_email = models.EmailField(help_text="成员邮箱（唯一标识）")
    member_name = models.CharField(max_length=100, help_text="成员姓名")
    role = models.CharField(max_length=50, choices=ROLE_CHOICES, help_text="角色")
    join_date = models.DateField(help_text="加入日期")
    is_active = models.BooleanField(default=True, help_text="是否激活")

    # JIRA and Git identifiers
    jira_account_id = models.CharField(max_length=100, null=True, blank=True, help_text="JIRA账户ID")
    git_username = models.CharField(max_length=100, null=True, blank=True, help_text="Git用户名")

    # Test data marker
    is_test_data = models.BooleanField(default=False, help_text="是否为测试数据")

    class Meta:
        db_table = 'rd_team_member'
        verbose_name = 'R&D团队成员'
        verbose_name_plural = 'R&D团队成员'
        unique_together = ['team', 'member_email']
        indexes = [
            models.Index(fields=['member_email', 'is_active']),
            models.Index(fields=['team', 'role']),
            models.Index(fields=['is_test_data']),
        ]

    def __str__(self):
        return f"{self.member_name} ({self.team.team_name})"


class RDMetricsSnapshot(models.Model):
    """R&D Metrics Snapshot - Daily/Weekly/Monthly aggregated data"""
    PERIOD_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
    ]

    snapshot_id = models.UUIDField(default=uuid.uuid4, unique=True, help_text="快照唯一ID")
    team = models.ForeignKey(RDTeam, on_delete=models.CASCADE, help_text="关联团队")

    # Time period
    period_type = models.CharField(max_length=20, choices=PERIOD_CHOICES, help_text="统计周期")
    period_start = models.DateField(help_text="周期开始日期")
    period_end = models.DateField(help_text="周期结束日期")

    # Metrics data (使用TextField存储JSON字符串以兼容MySQL)
    metrics_data = models.TextField(default='{}', help_text="指标数据（JSON格式）")

    # Metadata
    calculated_at = models.DateTimeField(auto_now_add=True, help_text="计算时间")
    data_sources = models.TextField(default='{}', help_text="数据源信息（JSON格式）")

    # Test data marker
    is_test_data = models.BooleanField(default=False, help_text="是否为测试数据")

    class Meta:
        db_table = 'rd_metrics_snapshot'
        verbose_name = 'R&D指标快照'
        verbose_name_plural = 'R&D指标快照'
        unique_together = ['team', 'period_type', 'period_start']
        indexes = [
            models.Index(fields=['team', 'period_type', 'period_start']),
            models.Index(fields=['calculated_at']),
            models.Index(fields=['is_test_data']),
        ]

    def __str__(self):
        return f"{self.team.team_name} - {self.period_type} - {self.period_start}"


class RDJiraMetrics(models.Model):
    """JIRA-specific metrics data"""
    team = models.ForeignKey(RDTeam, on_delete=models.CASCADE, help_text="关联团队")
    project_key = models.CharField(max_length=20, help_text="JIRA项目键值，如SPCB、SPCT")

    # Time period
    date = models.DateField(help_text="统计日期")

    # Issue metrics
    total_issues = models.IntegerField(default=0, help_text="总问题数")
    created_issues = models.IntegerField(default=0, help_text="新建问题数")
    resolved_issues = models.IntegerField(default=0, help_text="已解决问题数")
    reopened_issues = models.IntegerField(default=0, help_text="重新打开问题数")

    # Bug metrics
    total_bugs = models.IntegerField(default=0, help_text="总Bug数")
    new_bugs = models.IntegerField(default=0, help_text="新Bug数")
    fixed_bugs = models.IntegerField(default=0, help_text="已修复Bug数")
    bug_fix_time_avg = models.FloatField(null=True, help_text="平均Bug修复时间（天）")

    # Story/Task metrics
    story_points_committed = models.FloatField(default=0, help_text="承诺工时")
    story_points_completed = models.FloatField(default=0, help_text="完成工时")
    velocity = models.FloatField(default=0, help_text="团队速度（工时/周期）")

    # Cycle time metrics
    avg_cycle_time = models.FloatField(null=True, help_text="平均周期时间（天）")
    avg_lead_time = models.FloatField(null=True, help_text="平均前置时间（天）")

    # Quality metrics
    defect_density = models.FloatField(null=True, help_text="缺陷密度")
    rework_rate = models.FloatField(null=True, help_text="返工率")

    # Test data marker
    is_test_data = models.BooleanField(default=False, help_text="是否为测试数据")

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'rd_jira_metrics'
        verbose_name = 'R&D JIRA指标'
        verbose_name_plural = 'R&D JIRA指标'
        unique_together = ['team', 'project_key', 'date']
        indexes = [
            models.Index(fields=['team', 'date']),
            models.Index(fields=['project_key', 'date']),
            models.Index(fields=['is_test_data']),
        ]

    def __str__(self):
        return f"{self.team.team_name} - {self.project_key} - {self.date}"


class RDGitMetrics(models.Model):
    """Git-specific metrics data"""
    team = models.ForeignKey(RDTeam, on_delete=models.CASCADE, help_text="关联团队")
    repository_name = models.CharField(max_length=200, help_text="仓库名称")
    repository_id = models.IntegerField(help_text="仓库ID（来自services_id.json）")

    # Time period
    date = models.DateField(help_text="统计日期")

    # Commit metrics
    total_commits = models.IntegerField(default=0, help_text="总提交数")
    total_authors = models.IntegerField(default=0, help_text="总作者数")
    lines_added = models.IntegerField(default=0, help_text="新增代码行数")
    lines_deleted = models.IntegerField(default=0, help_text="删除代码行数")

    # Merge request metrics
    merge_requests_created = models.IntegerField(default=0, help_text="创建的合并请求数")
    merge_requests_merged = models.IntegerField(default=0, help_text="已合并的合并请求数")
    avg_mr_review_time = models.FloatField(null=True, help_text="平均MR审查时间（小时）")

    # Code quality metrics
    code_churn_rate = models.FloatField(null=True, help_text="代码变动率")
    hotspot_files_count = models.IntegerField(default=0, help_text="热点文件数量")

    # Test data marker
    is_test_data = models.BooleanField(default=False, help_text="是否为测试数据")

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'rd_git_metrics'
        verbose_name = 'R&D Git指标'
        verbose_name_plural = 'R&D Git指标'
        unique_together = ['team', 'repository_id', 'date']
        indexes = [
            models.Index(fields=['team', 'date']),
            models.Index(fields=['repository_id', 'date']),
            models.Index(fields=['is_test_data']),
        ]

    def __str__(self):
        return f"{self.team.team_name} - {self.repository_name} - {self.date}"


class RDMetricsPermission(models.Model):
    """R&D Metrics Access Control"""
    PERMISSION_LEVELS = [
        ('viewer', 'Viewer'),
        ('team_lead', 'Team Lead'),
        ('manager', 'Manager'),
        ('admin', 'Admin'),
    ]

    user_email = models.EmailField(unique=True, help_text="用户邮箱")

    # Permission levels
    permission_level = models.CharField(max_length=20, choices=PERMISSION_LEVELS, help_text="权限级别")

    # Team access (null = all teams for managers/admins)
    accessible_teams = models.ManyToManyField(RDTeam, blank=True, help_text="可访问的团队")

    # Feature permissions
    can_view_individual_metrics = models.BooleanField(default=True, help_text="可查看个人指标")
    can_view_team_comparison = models.BooleanField(default=False, help_text="可查看团队对比")
    can_export_data = models.BooleanField(default=False, help_text="可导出数据")
    can_configure_teams = models.BooleanField(default=False, help_text="可配置团队")

    is_active = models.BooleanField(default=True, help_text="是否激活")
    granted_by = models.EmailField(help_text="授权人邮箱")
    granted_at = models.DateTimeField(auto_now_add=True, help_text="授权时间")
    expires_at = models.DateTimeField(null=True, blank=True, help_text="过期时间")

    # Test data marker
    is_test_data = models.BooleanField(default=False, help_text="是否为测试数据")

    class Meta:
        db_table = 'rd_metrics_permission'
        verbose_name = 'R&D指标权限'
        verbose_name_plural = 'R&D指标权限'
        indexes = [
            models.Index(fields=['user_email', 'is_active']),
            models.Index(fields=['permission_level']),
            models.Index(fields=['is_test_data']),
        ]

    def __str__(self):
        return f"{self.user_email} - {self.permission_level}"

    def has_team_access(self, team):
        """检查是否有特定团队的访问权限"""
        if not self.is_active:
            return False

        # 检查是否过期
        if self.expires_at and timezone.now() > self.expires_at:
            return False

        # 管理员和经理可以访问所有团队
        if self.permission_level in ['admin', 'manager']:
            return True

        # 检查特定团队权限
        return self.accessible_teams.filter(id=team.id).exists()
