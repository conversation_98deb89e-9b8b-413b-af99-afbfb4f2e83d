"""
会话管理模块
负责多轮对话的上下文管理和会话状态维护
"""

import uuid
import asyncio
import logging
from typing import Dict, Optional, List
from datetime import timedelta
from django.utils import timezone
from django.db import models
from django.core.cache import cache
import socket

from app01.models import UserConversation
from app01.utils.db_retry import db_retry, db_retry_async
# Redis连接已移除，使用Django ORM进行会话管理

logger = logging.getLogger(__name__)


class ConversationManager:
    """会话管理器"""
    
    def __init__(self):
        self.session_timeout = 3600  # 1小时
        self.context_max_size = 50   # 最大上下文条目数
        self.max_clarification_rounds = 3  # 最大澄清轮次
        self.session_timeout_hours = 1  # 会话超时时间（小时）
        self.max_context_items = 10     # 最大上下文项目数
        self.debug_group_id = "NzQzMzAxODcyMjAy"  # 调试群组ID
    
    def generate_session_id(self) -> str:
        """生成会话ID"""
        return str(uuid.uuid4())
    
    async def get_or_create_session(self, user_id: str, group_id: str = None, 
                                   employee_code: str = None) -> Dict:
        """
        获取或创建用户会话
        
        Args:
            user_id: 用户ID
            group_id: 群组ID
            employee_code: 私聊用户employee_code
            
        Returns:
            会话信息
        """
        try:
            logger.info(f"📋 开始获取或创建会话 - user_id: {user_id}, group_id: {group_id}, employee_code: {employee_code}")
            
            @db_retry(max_retries=3, delay=1.0, backoff=2.0)
            def _get_or_create():
                try:
                    logger.info(f"📋 查询现有活跃会话")
                    # 查找现有的活跃会话
                    existing_session = UserConversation.objects.filter(
                        user_id=user_id,
                        group_id=group_id,
                        employee_code=employee_code
                    ).order_by('-last_activity').first()
                    
                    # 检查会话是否过期
                    if existing_session and not existing_session.is_expired(self.session_timeout_hours):
                        logger.info(f"📋 找到活跃会话: {existing_session.session_id}, 最后活动时间: {existing_session.last_activity}")
                        # 使用事务更新最后活动时间
                        from django.db import transaction
                        with transaction.atomic():
                            existing_session.last_activity = timezone.now()
                            existing_session.save(update_fields=['last_activity'])
                        logger.info(f"📋 更新会话最后活动时间")
                        
                        return {
                            'session_id': existing_session.session_id,
                            'context': existing_session.context,
                            'is_new': False,
                            'created_at': existing_session.created_at
                        }
                    else:
                        if existing_session:
                            logger.info(f"📋 找到会话但已过期: {existing_session.session_id}, 最后活动时间: {existing_session.last_activity}")
                        else:
                            logger.info(f"📋 未找到现有会话，将创建新会话")
                        
                        # 创建新会话
                        session_id = self.generate_session_id()
                        logger.info(f"📋 生成新会话ID: {session_id}")
                        
                        try:
                            # 使用事务创建新会话
                            from django.db import transaction
                            with transaction.atomic():
                                new_session = UserConversation.objects.create(
                                    user_id=user_id,
                                    group_id=group_id,
                                    employee_code=employee_code,
                                    session_id=session_id,
                                    context={}
                                )
                            logger.info(f"📋 新会话创建成功: {session_id}")
                            
                            return {
                                'session_id': session_id,
                                'context': {},
                                'is_new': True,
                                'created_at': new_session.created_at
                            }
                        except Exception as db_error:
                            logger.error(f"📋 创建新会话失败: {str(db_error)}")
                            import traceback
                            error_stack = traceback.format_exc()
                            logger.error(f"📋 数据库错误堆栈: {error_stack}")
                            # 发送错误到调试群
                            self._send_error_to_debug_group(f"创建新会话失败: {str(db_error)}\n堆栈: {error_stack[:500]}")
                            raise
                except Exception as inner_e:
                    logger.error(f"📋 _get_or_create内部异常: {str(inner_e)}")
                    import traceback
                    error_stack = traceback.format_exc()
                    logger.error(f"📋 内部异常堆栈: {error_stack}")
                    # 发送错误到调试群
                    self._send_error_to_debug_group(f"_get_or_create内部异常: {str(inner_e)}\n堆栈: {error_stack[:500]}")
                    raise
            
            # 在线程池中执行数据库操作
            logger.info(f"📋 准备在线程池中执行数据库操作")
            loop = asyncio.get_event_loop()
            try:
                result = await loop.run_in_executor(None, _get_or_create)
                logger.info(f"📋 数据库操作完成，会话ID: {result['session_id']}, 是否新会话: {result['is_new']}")
                
                return {
                    'success': True,
                    'session': result
                }
            except Exception as exec_error:
                logger.error(f"📋 执行器异常: {str(exec_error)}")
                import traceback
                error_stack = traceback.format_exc()
                logger.error(f"📋 执行器异常堆栈: {error_stack}")
                # 发送错误到调试群
                self._send_error_to_debug_group(f"执行器异常: {str(exec_error)}\n堆栈: {error_stack[:500]}")
                raise
            
        except Exception as e:
            logger.error(f"📋 获取或创建会话失败: {str(e)}")
            import traceback
            error_stack = traceback.format_exc()
            logger.error(f"📋 异常堆栈: {error_stack}")
            # 发送错误到调试群
            self._send_error_to_debug_group(f"获取或创建会话失败: {str(e)}\n堆栈: {error_stack[:500]}")
            return {
                'success': False,
                'error': str(e)
            }
            
    def _send_error_to_debug_group(self, error_message: str):
        """发送错误信息到调试群组"""
        try:
            # 构建错误消息
            timestamp = timezone.now().strftime("%Y-%m-%d %H:%M:%S")
            hostname = socket.gethostname()
            formatted_message = f"🚨 会话管理错误报告\n⏰ 时间: {timestamp}\n🖥️ 主机: {hostname}\n\n❌ 错误信息:\n{error_message}"
            
            # 异步发送消息到调试群
            loop = asyncio.get_event_loop()
            loop.create_task(self._async_send_to_group(self.debug_group_id, formatted_message))
            
            logger.info(f"已将错误信息推送到调试群 {self.debug_group_id}")
        except Exception as e:
            logger.error(f"推送错误信息到调试群失败: {str(e)}")
    
    async def _async_send_to_group(self, group_id: str, message: str):
        """异步发送消息到群组"""
        try:
            from app01.seatalk_group_manager import send_message_to_group
            from asgiref.sync import sync_to_async
            await sync_to_async(send_message_to_group)(group_id, message)
        except Exception as e:
            logger.error(f"异步发送消息到群组失败: {str(e)}")
    
    async def update_session_context(self, session_id: str, context: Dict) -> Dict:
        """
        更新会话上下文（直接替换）
        
        Args:
            session_id: 会话ID
            context: 新的上下文
            
        Returns:
            操作结果
        """
        try:
            def _update_session_context():
                try:
                    session = UserConversation.objects.get(session_id=session_id)
                    session.context = context
                    session.last_activity = timezone.now()
                    session.save(update_fields=['context', 'last_activity'])
                    return True
                except UserConversation.DoesNotExist:
                    return False
            
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(None, _update_session_context)
            
            if success:
                return {
                    'success': True,
                    'message': '会话上下文已更新'
                }
            else:
                return {
                    'success': False,
                    'error': '会话不存在'
                }
                
        except Exception as e:
            logger.error(f"更新会话上下文失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    async def update_context(self, session_id: str, new_context: Dict) -> Dict:
        """
        更新会话上下文
        
        Args:
            session_id: 会话ID
            new_context: 新的上下文数据
            
        Returns:
            更新结果
        """
        try:
            def _update_context():
                from django.db import transaction
                try:
                    session = UserConversation.objects.get(session_id=session_id)

                    # 合并上下文
                    current_context = session.context or {}
                    merged_context = self._merge_context(current_context, new_context)

                    # 限制上下文大小
                    limited_context = self._limit_context_size(merged_context)

                    # 使用事务更新数据库
                    with transaction.atomic():
                        session.context = limited_context
                        session.last_activity = timezone.now()
                        session.save(update_fields=['context', 'last_activity'])

                    return limited_context
                    
                except UserConversation.DoesNotExist:
                    logger.warning(f"会话不存在: {session_id}")
                    return None
            
            loop = asyncio.get_event_loop()
            updated_context = await loop.run_in_executor(None, _update_context)
            
            if updated_context is not None:
                return {
                    'success': True,
                    'context': updated_context
                }
            else:
                return {
                    'success': False,
                    'error': '会话不存在'
                }
                
        except Exception as e:
            logger.error(f"更新会话上下文失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _merge_context(self, current_context: Dict, new_context: Dict) -> Dict:
        """
        合并上下文信息
        
        Args:
            current_context: 当前上下文
            new_context: 新上下文
            
        Returns:
            合并后的上下文
        """
        merged = current_context.copy()
        
        # 合并查询历史
        if 'query_history' not in merged:
            merged['query_history'] = []
        
        if 'query' in new_context:
            merged['query_history'].append({
                'query': new_context['query'],
                'intent': new_context.get('intent'),
                'timestamp': timezone.now().isoformat()
            })
        
        # 更新最近查询的JIRA键值
        if 'jira_keys' in new_context:
            if 'recent_jira_keys' not in merged:
                merged['recent_jira_keys'] = []
            
            # 添加新的JIRA键值，去重
            for key in new_context['jira_keys']:
                if key not in merged['recent_jira_keys']:
                    merged['recent_jira_keys'].insert(0, key)  # 插入到开头
        
        # 更新当前话题
        if 'current_topic' in new_context:
            merged['current_topic'] = new_context['current_topic']
        
        # 更新用户偏好
        if 'user_preferences' in new_context:
            if 'user_preferences' not in merged:
                merged['user_preferences'] = {}
            merged['user_preferences'].update(new_context['user_preferences'])
        
        # 记录最后的意图
        if 'intent' in new_context:
            merged['last_intent'] = new_context['intent']
        
        return merged
    
    def _limit_context_size(self, context: Dict) -> Dict:
        """
        限制上下文大小，避免无限增长
        
        Args:
            context: 上下文数据
            
        Returns:
            限制大小后的上下文
        """
        limited = context.copy()
        
        # 限制查询历史数量
        if 'query_history' in limited:
            limited['query_history'] = limited['query_history'][-self.max_context_items:]
        
        # 限制最近JIRA键值数量
        if 'recent_jira_keys' in limited:
            limited['recent_jira_keys'] = limited['recent_jira_keys'][:self.max_context_items]
        
        return limited
    
    async def get_session(self, session_id: str) -> Dict:
        """
        获取会话信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话信息
        """
        try:
            @db_retry(max_retries=3, delay=1.0, backoff=2.0)
            def _get_session():
                try:
                    session = UserConversation.objects.get(session_id=session_id)
                    return {
                        'session_id': session.session_id,
                        'user_id': session.user_id,
                        'group_id': session.group_id,
                        'employee_code': session.employee_code,
                        'context': session.context or {},
                        'created_at': session.created_at,
                        'last_activity': session.last_activity
                    }
                except UserConversation.DoesNotExist:
                    return None
            
            loop = asyncio.get_event_loop()
            session_data = await loop.run_in_executor(None, _get_session)
            
            if session_data is not None:
                return {
                    'success': True,
                    'session': session_data
                }
            else:
                return {
                    'success': False,
                    'error': '会话不存在'
                }
                
        except Exception as e:
            logger.error(f"获取会话信息失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    async def get_context(self, session_id: str) -> Dict:
        """
        获取会话上下文
        
        Args:
            session_id: 会话ID
            
        Returns:
            上下文信息
        """
        try:
            def _get_context():
                try:
                    session = UserConversation.objects.get(session_id=session_id)
                    return session.context or {}
                except UserConversation.DoesNotExist:
                    return None
            
            loop = asyncio.get_event_loop()
            context = await loop.run_in_executor(None, _get_context)
            
            if context is not None:
                return {
                    'success': True,
                    'context': context
                }
            else:
                return {
                    'success': False,
                    'error': '会话不存在'
                }
                
        except Exception as e:
            logger.error(f"获取会话上下文失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def clear_context(self, session_id: str) -> Dict:
        """
        清除会话上下文
        
        Args:
            session_id: 会话ID
            
        Returns:
            操作结果
        """
        try:
            def _clear_context():
                try:
                    session = UserConversation.objects.get(session_id=session_id)
                    session.context = {}
                    session.last_activity = timezone.now()
                    session.save(update_fields=['context', 'last_activity'])
                    return True
                except UserConversation.DoesNotExist:
                    return False
            
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(None, _clear_context)
            
            if success:
                return {
                    'success': True,
                    'message': '会话上下文已清除'
                }
            else:
                return {
                    'success': False,
                    'error': '会话不存在'
                }
                
        except Exception as e:
            logger.error(f"清除会话上下文失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def cleanup_expired_sessions(self) -> Dict:
        """
        清理过期会话
        
        Returns:
            清理结果
        """
        try:
            def _cleanup():
                cutoff_time = timezone.now() - timedelta(hours=self.session_timeout_hours)
                deleted_count, _ = UserConversation.objects.filter(
                    last_activity__lt=cutoff_time
                ).delete()
                return deleted_count
            
            loop = asyncio.get_event_loop()
            deleted_count = await loop.run_in_executor(None, _cleanup)
            
            logger.info(f"清理过期会话完成，删除了 {deleted_count} 个会话")
            
            return {
                'success': True,
                'deleted_count': deleted_count
            }
            
        except Exception as e:
            logger.error(f"清理过期会话失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_user_sessions(self, user_id: str, days: int = 7) -> Dict:
        """
        获取用户的会话统计
        
        Args:
            user_id: 用户ID
            days: 统计天数
            
        Returns:
            会话统计信息
        """
        try:
            def _get_stats():
                since_date = timezone.now() - timedelta(days=days)
                sessions = UserConversation.objects.filter(
                    user_id=user_id,
                    created_at__gte=since_date
                ).order_by('-created_at')
                
                stats = {
                    'total_sessions': sessions.count(),
                    'active_sessions': sessions.filter(
                        last_activity__gte=timezone.now() - timedelta(hours=self.session_timeout_hours)
                    ).count(),
                    'recent_sessions': []
                }
                
                # 获取最近的几个会话
                for session in sessions[:5]:
                    stats['recent_sessions'].append({
                        'session_id': session.session_id,
                        'created_at': session.created_at.isoformat(),
                        'last_activity': session.last_activity.isoformat(),
                        'group_id': session.group_id,
                        'context_size': len(session.context.get('query_history', []))
                    })
                
                return stats
            
            loop = asyncio.get_event_loop()
            stats = await loop.run_in_executor(None, _get_stats)
            
            return {
                'success': True,
                'stats': stats
            }
            
        except Exception as e:
            logger.error(f"获取用户会话统计失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def extract_context_for_ai(self, context: Dict) -> Dict:
        """
        提取用于AI处理的关键上下文信息
        
        Args:
            context: 完整上下文
            
        Returns:
            AI处理用的简化上下文
        """
        ai_context = {}
        
        # 提取最近的JIRA键值
        if 'recent_jira_keys' in context and context['recent_jira_keys']:
            ai_context['recent_jira_keys'] = context['recent_jira_keys'][:3]  # 只取最近3个
        
        # 提取当前话题
        if 'current_topic' in context:
            ai_context['current_topic'] = context['current_topic']
        
        # 提取最后的意图
        if 'last_intent' in context:
            ai_context['last_intent'] = context['last_intent']
        
        # 提取最近的查询
        if 'query_history' in context and context['query_history']:
            recent_queries = context['query_history'][-2:]  # 最近2个查询
            ai_context['recent_queries'] = [q['query'] for q in recent_queries]
        
        # 提取用户偏好
        if 'user_preferences' in context:
            ai_context['user_preferences'] = context['user_preferences']
        
        return ai_context
    
    def should_use_context(self, current_query: str, context: Dict) -> bool:
        """
        判断是否应该使用上下文
        
        Args:
            current_query: 当前查询
            context: 会话上下文
            
        Returns:
            是否使用上下文
        """
        if not context or not context.get('query_history'):
            return False
        
        # 获取最近的查询
        recent_queries = context['query_history'][-2:]  # 最近2个查询
        if not recent_queries:
            return False
        
        last_query = recent_queries[-1]
        
        # 时间间隔检查（2分钟内）
        if 'timestamp' in last_query:
            try:
                last_time = timezone.datetime.fromisoformat(last_query['timestamp'])
                current_time = timezone.now()
                time_gap = (current_time - last_time).total_seconds()
                
                if time_gap > 120:  # 超过2分钟
                    return False
            except:
                return False
        
        # 语义相关性检查（简化版）
        semantic_similarity = self._calculate_semantic_similarity(
            current_query, last_query.get('query', '')
        )
        
        return semantic_similarity > 0.8
    
    def _calculate_semantic_similarity(self, query1: str, query2: str) -> float:
        """
        计算语义相似度（简化版）
        
        Args:
            query1: 查询1
            query2: 查询2
            
        Returns:
            相似度分数 (0.0-1.0)
        """
        if not query1 or not query2:
            return 0.0
        
        # 简化的关键词匹配
        words1 = set(query1.lower().split())
        words2 = set(query2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        # 计算交集比例
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    async def track_clarification_round(self, session_id: str, user_query: str, 
                                      clarification_type: str) -> Dict:
        """
        跟踪澄清轮次
        
        Args:
            session_id: 会话ID
            user_query: 用户查询
            clarification_type: 澄清类型
            
        Returns:
            澄清状态信息
        """
        try:
            # 获取当前会话
            session_result = await self.get_session(session_id)
            if not session_result['success']:
                return {'success': False, 'error': '会话不存在'}
            
            context = session_result['session']['context']
            
            # 初始化澄清信息
            if 'clarification' not in context:
                context['clarification'] = {
                    'rounds': 0,
                    'history': [],
                    'original_query': user_query,
                    'start_time': timezone.now().isoformat()
                }
            
            clarification_info = context['clarification']
            
            # 检查是否超过最大轮次
            if clarification_info['rounds'] >= self.max_clarification_rounds:
                return {
                    'success': False,
                    'error': f'已达到最大澄清轮次({self.max_clarification_rounds})，请重新开始查询',
                    'should_reset': True
                }
            
            # 记录当前澄清轮次
            clarification_info['rounds'] += 1
            clarification_info['history'].append({
                'round': clarification_info['rounds'],
                'query': user_query,
                'type': clarification_type,
                'timestamp': timezone.now().isoformat()
            })
            
            # 更新会话
            await self.update_session_context(session_id, context)
            
            return {
                'success': True,
                'current_round': clarification_info['rounds'],
                'max_rounds': self.max_clarification_rounds,
                'remaining_rounds': self.max_clarification_rounds - clarification_info['rounds'],
                'original_query': clarification_info['original_query']
            }
            
        except Exception as e:
            logger.error(f"跟踪澄清轮次失败: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    async def clear_clarification(self, session_id: str) -> Dict:
        """
        清除澄清状态
        
        Args:
            session_id: 会话ID
            
        Returns:
            操作结果
        """
        try:
            session_result = await self.get_session(session_id)
            if not session_result['success']:
                return {'success': False, 'error': '会话不存在'}
            
            context = session_result['session']['context']
            
            # 清除澄清信息
            if 'clarification' in context:
                del context['clarification']
                await self.update_session_context(session_id, context)
            
            return {'success': True, 'message': '澄清状态已清除'}
            
        except Exception as e:
            logger.error(f"清除澄清状态失败: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def get_clarification_status(self, context: Dict) -> Dict:
        """
        获取澄清状态
        
        Args:
            context: 会话上下文
            
        Returns:
            澄清状态信息
        """
        clarification_info = context.get('clarification', {})
        
        return {
            'in_clarification': bool(clarification_info),
            'current_round': clarification_info.get('rounds', 0),
            'max_rounds': self.max_clarification_rounds,
            'original_query': clarification_info.get('original_query', ''),
            'history': clarification_info.get('history', [])
        }
    
    async def get_chat_context(self, session_id: str) -> Dict:
        """
        获取聊天上下文（用于直接对话模式）
        
        Args:
            session_id: 会话ID
            
        Returns:
            聊天上下文信息
        """
        try:
            session_result = await self.get_session(session_id)
            if not session_result['success']:
                return {'success': False, 'error': '会话不存在'}
            
            context = session_result['session']['context']
            chat_history = context.get('chat_history', [])
            
            return {
                'success': True,
                'chat_history': chat_history
            }
            
        except Exception as e:
            logger.error(f"获取聊天上下文失败: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    async def add_chat_message(self, session_id: str, role: str, content: str) -> Dict:
        """
        添加聊天消息到上下文（用于直接对话模式）
        
        Args:
            session_id: 会话ID
            role: 角色 ('user' 或 'assistant')
            content: 消息内容
            
        Returns:
            操作结果
        """
        try:
            session_result = await self.get_session(session_id)
            if not session_result['success']:
                return {'success': False, 'error': '会话不存在'}
            
            context = session_result['session']['context']
            
            # 初始化聊天历史
            if 'chat_history' not in context:
                context['chat_history'] = []
            
            # 添加新消息
            context['chat_history'].append({
                'role': role,
                'content': content,
                'timestamp': timezone.now().isoformat()
            })
            
            # 限制聊天历史长度（保留最近20条消息）
            if len(context['chat_history']) > 20:
                context['chat_history'] = context['chat_history'][-20:]
            
            # 更新会话
            await self.update_session_context(session_id, context)
            
            return {'success': True, 'message': '聊天消息已添加'}
            
        except Exception as e:
            logger.error(f"添加聊天消息失败: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    async def clear_chat_history(self, session_id: str) -> Dict:
        """
        清除聊天历史（用于直接对话模式）
        
        Args:
            session_id: 会话ID
            
        Returns:
            操作结果
        """
        try:
            session_result = await self.get_session(session_id)
            if not session_result['success']:
                return {'success': False, 'error': '会话不存在'}
            
            context = session_result['session']['context']
            
            # 清除聊天历史
            if 'chat_history' in context:
                del context['chat_history']
                await self.update_session_context(session_id, context)
            
            return {'success': True, 'message': '聊天历史已清除'}
            
        except Exception as e:
            logger.error(f"清除聊天历史失败: {str(e)}")
            return {'success': False, 'error': str(e)}


# 全局会话管理器实例
conversation_manager = ConversationManager() 