"""
用户定时任务调度管理器
支持用户创建、管理和执行个人定时查询任务
"""

import asyncio
import logging
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional
import pytz
from django.utils import timezone
from django.db import transaction
import json
import socket # Added for socket.gethostname()

from app01.models import UserScheduledTask, TaskExecutionLog
from .ai_assistant import ai_assistant
from .private_chat import PrivateChat
from app01.utils.db_retry import db_retry, db_retry_async

logger = logging.getLogger(__name__)


class TaskScheduler:
    """用户定时任务调度器"""
    
    def __init__(self):
        self.running_tasks = {}  # 正在运行的任务
        self.private_chat = PrivateChat()
    async def create_task(self, user_id: str, user_email: str, employee_code: str,
                          task_name: str, query_text: str, schedule_time: str, 
                          frequency: str = 'daily', schedule_days: List[int] = None,
                          notification_type: str = 'private', target_group_id: str = None,
                          group_title: str = None, task_type: str = 'jira_query',
                          reminder_message: str = None, notification_methods: List[str] = None) -> Dict:
        """
        创建用户定时任务
        
        Args:
            user_id: 用户ID
            user_email: 用户邮箱
            employee_code: 员工代码
            task_name: 任务名称
            query_text: 查询文本（JIRA查询任务必填，提醒任务可选）
            schedule_time: 执行时间 "HH:MM"
            frequency: 频率 (daily/weekly/monthly)
            schedule_days: 执行日期 (weekly: [1,2,3,4,5], monthly: [15])
            notification_type: 通知类型 (private/group/both/smart)
            target_group_id: 目标群组ID或智能通知配置
            group_title: 群标题（用于权限检查）
            task_type: 任务类型 (jira_query/reminder)
            reminder_message: 提醒消息（提醒任务必填）
        """
        # 权限检查（仅对非智能通知任务进行检查，智能通知在advanced_task_manager中检查）
        if notification_type != 'smart':
            from .task_permission_manager import task_permission_manager
            
            logger.info(f"🔒 开始权限检查 - user_email: '{user_email}', notification_type: '{notification_type}'")
            
            # 构造通知配置
            notification_config = {'target_type': notification_type}
            
            permission_result = task_permission_manager.check_task_creation_permission(
                user_email=user_email,
                jql_query=query_text,
                notification_config=notification_config,
                group_title=group_title
            )
            
            logger.info(f"🔒 权限检查结果: allowed={permission_result.allowed}, reason={permission_result.reason}")
            
            if not permission_result.allowed:
                error_msg = f"❌ 权限不足: {permission_result.reason}"
                if permission_result.suggested_scope:
                    error_msg += f"\n💡 建议: {permission_result.suggested_scope}"
                
                return {
                    'success': False,
                    'error': error_msg,
                    'permission_denied': True,
                    'user_role': permission_result.user_role
                }
        
        # 验证任务参数
        if task_type == 'jira_query' and not query_text:
            return {
                'success': False,
                'error': 'JIRA查询任务必须提供query_text参数'
            }
        
        if task_type == 'reminder' and not reminder_message:
            return {
                'success': False,
                'error': '提醒任务必须提供reminder_message参数'
            }
        
        try:
            from asgiref.sync import sync_to_async
            
            logger.info(f"📝 开始创建任务 - 用户: {user_id}, 任务名: {task_name}, 类型: {task_type}")
            
            # 解析时间
            hour, minute = map(int, schedule_time.split(':'))
            schedule_time_obj = time(hour, minute)
            
            logger.info(f"📝 时间解析成功: {schedule_time_obj}")
            
            def create_and_save_task():
                try:
                    logger.info(f"📝 开始创建任务对象")
                    logger.info(f"📝 参数详情: user_id='{user_id}', user_email='{user_email}', task_name='{task_name}'")
                    logger.info(f"📝 调度参数: frequency={frequency}, schedule_time={schedule_time_obj}, notification_type={notification_type}")
                    
                    # 创建任务 - 处理query_text为None的情况
                    safe_query_text = query_text if query_text is not None else ""
                    
                    # 创建任务对象
                    task_data = {
                        'user_id': user_id,
                        'user_email': user_email,
                        'employee_code': employee_code,
                        'task_type': task_type,
                        'task_name': task_name,
                        'query_text': safe_query_text,
                        'reminder_message': reminder_message,
                        'frequency': frequency,
                        'schedule_time': schedule_time_obj,
                        'schedule_days': schedule_days or [],
                        'notification_type': notification_type,
                        'target_group_id': target_group_id,
                        'status': 'active',
                        'is_active': True
                    }
                    
                    # 只在字段存在时添加notification_methods
                    try:
                        # 检查模型是否有notification_methods字段
                        UserScheduledTask._meta.get_field('notification_methods')
                        task_data['notification_methods'] = notification_methods or []
                    except:
                        # 字段不存在，跳过
                        pass
                    
                    task = UserScheduledTask(**task_data)
                    
                    logger.info(f"📝 任务对象创建完成，开始计算下次执行时间")
                    
                    # 计算下次执行时间
                    task.calculate_next_execution()
                    
                    logger.info(f"📝 下次执行时间计算完成: {task.next_execution}")
                    
                    logger.info(f"📝 开始保存任务到数据库")
                    task.save()
                    
                    logger.info(f"📝 任务保存成功，ID: {task.id}")
                    
                    # 验证保存结果
                    logger.info(f"📝 验证数据库中的任务")
                    from app01.models import UserScheduledTask as UST
                    saved_task = UST.objects.filter(id=task.id).first()
                    if saved_task:
                        logger.info(f"📝 验证成功: 任务ID {task.id} 已保存到数据库")
                        logger.info(f"📝 保存的任务详情: user_id='{saved_task.user_id}', task_name='{saved_task.task_name}'")
                    else:
                        logger.error(f"📝 验证失败: 任务ID {task.id} 未在数据库中找到")
                    
                    return task
                    
                except Exception as e:
                    logger.error(f"📝 create_and_save_task 内部异常: {str(e)}")
                    import traceback
                    logger.error(f"📝 异常堆栈: {traceback.format_exc()}")
                    raise
            
            # 使用sync_to_async包装数据库操作
            logger.info("📝 准备执行数据库操作")
            create_task_async = sync_to_async(create_and_save_task)
            logger.info("📝 sync_to_async包装完成，开始执行")
            task = await create_task_async()
            logger.info(f"📝 数据库操作完成，任务ID: {task.id}")
            
            logger.info(f"用户 {user_id} 创建定时任务: {task_name}")
            
            # 构建详细的响应信息
            response = f"✅ **定时任务创建成功**\n\n"
            response += f"📋 **任务名称**: {task_name}\n"
            response += f"🆔 **任务ID**: {task.id}\n"
            
            if task_type == 'reminder':
                response += f"🔔 **提醒内容**: {reminder_message}\n"
                response += f"📝 **任务类型**: 定时提醒\n"
            else:
                response += f"🔍 **查询内容**: `{query_text}`\n"
                response += f"📝 **任务类型**: JIRA查询\n"
            
            # 格式化调度显示
            from .natural_task_creator import natural_task_creator
            if frequency == 'daily':
                schedule_display = f"每天 {schedule_time}"
            elif frequency == 'weekly':
                if schedule_days == [1, 2, 3, 4, 5]:
                    schedule_display = f"每个工作日 {schedule_time}"
                elif len(schedule_days) == 1:
                    weekdays = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日']
                    schedule_display = f"每{weekdays[schedule_days[0]]} {schedule_time}"
                else:
                    weekdays = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日']
                    days_str = '、'.join([weekdays[d] for d in schedule_days])
                    schedule_display = f"每{days_str} {schedule_time}"
            else:
                schedule_display = f"{frequency} {schedule_time}"
                
            response += f"⏰ **执行时间**: {schedule_display}\n"
            
            # 显示通知方式
            if notification_type == 'private':
                response += f"📬 **通知方式**: 私聊通知 ({user_email})\n"
            elif notification_type == 'group' and target_group_id:
                response += f"📬 **通知方式**: 群聊通知\n"
            elif notification_type == 'both':
                response += f"📬 **通知方式**: 私聊+群聊通知\n"
            else:
                response += f"📬 **通知方式**: {notification_type}\n"
            
            # 添加下次执行时间
            if task.next_execution:
                import pytz
                # 确保时区正确
                if task.next_execution.tzinfo is None:
                    task.next_execution = pytz.utc.localize(task.next_execution)
                
                # 转换为新加坡时间
                sg_tz = pytz.timezone('Asia/Singapore')
                sg_time = task.next_execution.astimezone(sg_tz)
                response += f"⏭️ **下次执行**: {sg_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            # 添加管理命令提示
            response += f"\n⚙️ **管理命令**:\n"
            response += f"• 查看任务: `schedule list`\n"
            response += f"• 暂停任务: `schedule pause {task.id}`\n"
            response += f"• 恢复任务: `schedule resume {task.id}`\n"
            response += f"• 删除任务: `schedule delete {task.id}`"
            
            return {
                'success': True,
                'task_id': task.id,
                'task_name': task_name,
                'response': response,  # 使用response字段保持一致性
                'message': f'定时任务 "{task_name}" 创建成功',
                'next_execution': task.next_execution.isoformat() if task.next_execution else None
            }
            
        except Exception as e:
            logger.error(f"创建定时任务失败: {str(e)}")
            return {
                'success': False,
                'error': f'创建任务失败: {str(e)}'
            }
    
    async def list_user_tasks(self, user_id: str, user_email: str = None, 
                             admin_mode: bool = False, target_user_email: str = None,
                             project_filter: str = None) -> Dict:
        """
        获取用户任务列表（支持管理员模式）
        
        Args:
            user_id: 用户ID
            user_email: 用户邮箱（用于权限检查）
            admin_mode: 是否为管理员模式
            target_user_email: 目标用户邮箱（管理员查看他人任务）
            project_filter: 项目过滤（PJM查看项目任务）
        """
        try:
            from asgiref.sync import sync_to_async
            from .task_permission_manager import task_permission_manager
            
            # 权限检查
            if user_email:
                permission_result = task_permission_manager.check_task_management_permission(
                    user_email=user_email,
                    operation='list',
                    target_user_email=target_user_email
                )
                
                if not permission_result.allowed:
                    return {
                        'success': False,
                        'error': permission_result.reason,
                        'permission_denied': True
                    }
            
            def get_tasks():
                from django.db.models import Q
                
                # 基础查询
                query = UserScheduledTask.objects.all()
                
                if user_email:
                    # 获取用户可管理的任务过滤条件
                    filter_config = task_permission_manager.get_user_manageable_tasks_filter(user_email)
                    
                    if filter_config:  # 不是超级管理员
                        conditions = Q()
                        
                        # 包含自己的任务
                        if filter_config.get('include_own'):
                            conditions |= Q(user_email=user_email)
                        
                        # PJM可以看到管理项目的任务
                        if filter_config.get('project_filters'):
                            for project_filter_str in filter_config['project_filters']:
                                # 查找包含特定项目的任务
                                conditions |= Q(query_text__icontains=project_filter_str)
                        
                        # 如果指定了目标用户（管理员查看他人任务）
                        if target_user_email:
                            conditions |= Q(user_email=target_user_email)
                        
                        # 如果指定了项目过滤
                        if project_filter:
                            conditions &= Q(query_text__icontains=f'project = {project_filter}')
                        
                        query = query.filter(conditions)
                    
                    # 如果指定了目标用户且有权限
                    elif target_user_email:
                        query = query.filter(user_email=target_user_email)
                        
                    # 如果指定了项目过滤
                    elif project_filter:
                        query = query.filter(query_text__icontains=f'project = {project_filter}')
                
                else:
                    # 没有邮箱信息，只返回指定用户的任务
                    query = query.filter(user_id=user_id)
                
                tasks = query.order_by('-created_at')
                
                # 获取群组名称映射
                from app01.models import SeatalkGroup
                group_id_to_name = {}
                for group in SeatalkGroup.objects.all():
                    group_id_to_name[group.group_id] = group.group_name
                
                task_list = []
                for task in tasks:
                    # 处理时区
                    next_execution_display = None
                    if task.next_execution:
                        singapore_tz = pytz.timezone('Asia/Singapore')
                        if task.next_execution.tzinfo is None:
                            # 如果没有时区信息，假设是UTC
                            task.next_execution = pytz.utc.localize(task.next_execution)
                        singapore_time = task.next_execution.astimezone(singapore_tz)
                        next_execution_display = singapore_time.isoformat()
                    
                    # 处理通知目标信息 - 使用新的标准化处理
                    notification_target = None
                    methods = []
                    try:
                        methods = getattr(task, 'notification_methods', None) or []
                    except AttributeError:
                        # 字段还不存在，使用默认值
                        methods = []
                    
                    if not methods:
                        # 兼容旧数据
                        legacy_mapping = {
                            'private': ['creator'],
                            'smart': ['assignee'],
                            'both': ['creator', 'group'],
                            'creator': ['creator'],
                            'assignee': ['assignee'],
                            'group': ['group'],
                            'auto_group': ['auto_group'],
                            'multi': ['creator']  # 多选时默认显示创建人
                        }
                        methods = legacy_mapping.get(task.notification_type, ['creator'])
                    
                    # 构建通知目标描述
                    target_parts = []
                    if 'creator' in methods:
                        target_parts.append(f"创建人({task.user_email})")
                    if 'assignee' in methods:
                        target_parts.append("查询结果的Assignee")
                    if 'group' in methods:
                        if task.target_group_id:
                            if task.target_group_id.startswith('keyword:'):
                                keyword = task.target_group_id.replace('keyword:', '')
                                target_parts.append(f"指定群({keyword})")
                            else:
                                group_name = group_id_to_name.get(task.target_group_id, "未知群组")
                                target_parts.append(f"指定群({group_name})")
                        else:
                            target_parts.append("指定群(未配置)")
                    if 'auto_group' in methods:
                        target_parts.append("查询结果所在群")
                    
                    notification_target = " + ".join(target_parts) if target_parts else "未知通知方式"
                    
                    task_info = {
                        'id': task.id,
                        'name': task.task_name,
                        'query': task.query_text,
                        'frequency': task.frequency,
                        'schedule_time': task.schedule_time.strftime('%H:%M'),
                        'status': task.status,
                        'notification_type': task.notification_type,
                        'notification_target': notification_target,  # 新增字段
                        'next_execution': next_execution_display,
                        'total_executions': task.total_executions,
                        'successful_executions': task.successful_executions,
                        'success_rate': round(task.successful_executions / max(task.total_executions, 1) * 100, 1),
                        'creator_email': task.user_email,
                        'creator_id': task.user_id,
                        'created_at': task.created_at.isoformat() if task.created_at else None
                    }
                    task_list.append(task_info)
                
                return task_list
            
            get_tasks_async = sync_to_async(get_tasks)
            task_list = await get_tasks_async()
            
            return {
                'success': True,
                'tasks': task_list,
                'total_count': len(task_list),
                'admin_mode': admin_mode,
                'user_role': getattr(permission_result, 'user_role', 'unknown') if user_email else 'unknown'
            }
            
        except Exception as e:
            logger.error(f"获取用户任务列表失败: {str(e)}")
            return {
                'success': False,
                'error': f'获取任务列表失败: {str(e)}'
            }
    
    async def update_task_status(self, user_id: str, task_id: int, status: str, 
                               user_email: str = None, admin_mode: bool = False) -> Dict:
        """更新任务状态（支持管理员模式）"""
        try:
            from asgiref.sync import sync_to_async
            from .task_permission_manager import task_permission_manager
            
            def get_task_info():
                try:
                    task = UserScheduledTask.objects.get(id=task_id)
                    return {
                        'exists': True,
                        'creator_email': task.user_email,
                        'creator_id': task.user_id,
                        'jql_query': task.query_text,
                        'task_name': task.task_name,
                        'notification_config': {'target_type': task.notification_type}
                    }
                except UserScheduledTask.DoesNotExist:
                    return {'exists': False}
            
            get_task_info_async = sync_to_async(get_task_info)
            task_info = await get_task_info_async()
            
            if not task_info['exists']:
                return {
                    'success': False,
                    'error': '任务不存在'
                }
            
            # 权限检查
            if user_email:
                permission_result = task_permission_manager.check_task_management_permission(
                    user_email=user_email,
                    operation='pause' if status == 'paused' else 'resume',
                    task_info=task_info
                )
                
                if not permission_result.allowed:
                    return {
                        'success': False,
                        'error': permission_result.reason,
                        'permission_denied': True
                    }
            else:
                # 没有邮箱信息，只能操作自己的任务
                if task_info['creator_id'] != user_id:
                    return {
                        'success': False,
                        'error': '任务不存在或无权限'
                    }
            
            def update_task():
                try:
                    task = UserScheduledTask.objects.get(id=task_id)
                    old_status = task.status
                    task.status = status
                    
                    # 如果是重新激活任务，重新计算下次执行时间
                    if status == 'active' and old_status != 'active':
                        task.calculate_next_execution()
                    
                    task.save()
                    return True, task.task_name, None
                except UserScheduledTask.DoesNotExist:
                    return False, None, '任务不存在'
            
            update_task_async = sync_to_async(update_task)
            success, task_name, error = await update_task_async()
            
            if success:
                action = "暂停" if status == 'paused' else "激活" if status == 'active' else "更新"
                return {
                    'success': True,
                    'message': f'任务 "{task_name}" 已{action}'
                }
            else:
                return {
                    'success': False,
                    'error': error
                }
            
        except Exception as e:
            logger.error(f"更新任务状态失败: {str(e)}")
            return {
                'success': False,
                'error': f'更新状态失败: {str(e)}'
            }
    
    async def delete_task(self, user_id: str, task_id: int, user_email: str = None, 
                        admin_mode: bool = False) -> Dict:
        """删除任务（支持管理员模式）"""
        try:
            from asgiref.sync import sync_to_async
            from .task_permission_manager import task_permission_manager
            
            def get_task_info():
                try:
                    task = UserScheduledTask.objects.get(id=task_id)
                    return {
                        'exists': True,
                        'creator_email': task.user_email,
                        'creator_id': task.user_id,
                        'jql_query': task.query_text,
                        'task_name': task.task_name,
                        'notification_config': {'target_type': task.notification_type}
                    }
                except UserScheduledTask.DoesNotExist:
                    return {'exists': False}
            
            get_task_info_async = sync_to_async(get_task_info)
            task_info = await get_task_info_async()
            
            if not task_info['exists']:
                return {
                    'success': False,
                    'error': '任务不存在'
                }
            
            # 权限检查
            if user_email:
                permission_result = task_permission_manager.check_task_management_permission(
                    user_email=user_email,
                    operation='delete',
                    task_info=task_info
                )
                
                if not permission_result.allowed:
                    return {
                        'success': False,
                        'error': permission_result.reason,
                        'permission_denied': True
                    }
            else:
                # 没有邮箱信息，只能操作自己的任务
                if task_info['creator_id'] != user_id:
                    return {
                        'success': False,
                        'error': '任务不存在或无权限'
                    }
            
            def delete_task_db():
                try:
                    task = UserScheduledTask.objects.get(id=task_id)
                    task_name = task.task_name
                    task.delete()
                    return True, task_name, None
                except UserScheduledTask.DoesNotExist:
                    return False, None, '任务不存在'
            
            delete_task_async = sync_to_async(delete_task_db)
            success, task_name, error = await delete_task_async()
            
            if success:
                return {
                    'success': True,
                    'message': f'任务 "{task_name}" 已删除'
                }
            else:
                return {
                    'success': False,
                    'error': error
                }
            
        except Exception as e:
            logger.error(f"删除任务失败: {str(e)}")
            return {
                'success': False,
                'error': f'删除失败: {str(e)}'
            }
    
    async def execute_pending_tasks(self) -> Dict:
        """执行所有待执行的任务"""
        try:
            from asgiref.sync import sync_to_async
            
            @db_retry(max_retries=3, delay=1.0, backoff=2.0)
            def get_and_fix_pending_tasks():
                now = timezone.now()

                # 首先修复next_execution为空或异常的任务（限制修复频率，避免重复日志）
                tasks_need_fix = UserScheduledTask.objects.filter(
                    is_active=True,
                    status='active',
                    next_execution__isnull=True
                )

                fixed_count = 0
                for task in tasks_need_fix:
                    try:
                        task.calculate_next_execution()
                        task.save()
                        fixed_count += 1
                    except Exception as e:
                        logger.error(f"🔧 修复任务 {task.id} ({task.task_name}) 的next_execution时间失败: {str(e)}")

                if fixed_count > 0:
                    logger.info(f"🔧 修复了 {fixed_count} 个任务的next_execution时间")

                # 再次查找真正需要执行的任务
                pending_tasks = UserScheduledTask.objects.filter(
                    is_active=True,
                    status='active',
                    next_execution__lte=now,
                    next_execution__isnull=False  # 确保next_execution不为空
                )
                return list(pending_tasks)
            
            # 使用异步包装查询数据库
            get_pending_tasks_async = sync_to_async(get_and_fix_pending_tasks)
            pending_tasks = await get_pending_tasks_async()
            
            executed_count = 0
            success_count = 0
            
            for task in pending_tasks:
                try:
                    result = await self._execute_single_task(task)
                    executed_count += 1
                    if result['success']:
                        success_count += 1
                        
                except Exception as e:
                    logger.error(f"执行任务 {task.id} 失败: {str(e)}")
                    executed_count += 1
            
            return {
                'success': True,
                'executed_count': executed_count,
                'success_count': success_count,
                'message': f'执行了 {executed_count} 个任务，成功 {success_count} 个'
            }
            
        except Exception as e:
            logger.error(f"批量执行任务失败: {str(e)}")
            return {
                'success': False,
                'error': f'批量执行失败: {str(e)}'
            }
    
    async def _execute_single_task(self, task: UserScheduledTask) -> Dict:
        """执行单个任务"""
        start_time = timezone.now()
        
        try:
            logger.info(f"🔍 开始执行任务 {task.id} ({task.task_name}), 类型: {task.task_type}")
            logger.info(f"🔍 DEBUG任务详情 - task_type: '{task.task_type}', reminder_message: '{task.reminder_message[:50] if task.reminder_message else 'None'}', query_text: '{task.query_text[:50] if task.query_text else 'None'}'")
            
            # 根据任务类型选择执行方式
            if task.task_type == 'reminder':
                # 提醒任务：直接处理提醒消息
                logger.info(f"🔔 执行提醒任务: {task.reminder_message[:50]}...")
                ai_result = {
                    'success': True,
                    'response': task.reminder_message,
                    'intent': 'reminder_task'
                }
                execution_duration = (timezone.now() - start_time).total_seconds()
                logger.info(f"🔔 提醒任务处理完成，耗时: {execution_duration}秒")
            else:
                # JIRA查询任务：调用AI助手处理查询
                logger.info(f"🔍 调用AI助手处理查询: {task.query_text[:50]}...")
                ai_result = await ai_assistant.process_query(
                    user_query=task.query_text,
                    user_id=task.user_id,
                    employee_code=task.employee_code,
                    user_email=task.user_email
                )
                execution_duration = (timezone.now() - start_time).total_seconds()
                logger.info(f"🔍 AI查询执行完成，耗时: {execution_duration}秒")
            
            if ai_result['success']:
                # 根据任务类型和通知类型处理通知
                if task.task_type == 'reminder':
                    # 提醒任务：直接发送提醒消息
                    logger.info(f"🔔 处理提醒通知")
                    notification_result = await self._handle_reminder_notification(task, ai_result['response'])
                elif task.notification_type == 'smart':
                    # JIRA智能通知任务
                    logger.info(f"🔍 处理智能通知")
                    notification_result = await self._handle_smart_notification(task, ai_result)
                else:
                    # JIRA普通通知任务
                    logger.info(f"🔍 处理普通通知")
                    notification_result = await self._handle_regular_notification(task, ai_result['response'])
                
                # 记录成功执行
                logger.info(f"🔍 准备更新任务执行状态")
                try:
                    from asgiref.sync import sync_to_async
                    logger.info(f"🔍 成功导入sync_to_async")
                    
                    def update_task_success():
                        try:
                            logger.info(f"🔍 开始数据库事务")
                            with transaction.atomic():
                                task.total_executions += 1
                                task.successful_executions += 1
                                task.last_execution = start_time
                                task.calculate_next_execution()  # 计算下次执行时间
                                task.save()
                                logger.info(f"🔍 任务状态已更新，下次执行时间: {task.next_execution}")
                                
                                # 记录执行日志
                                TaskExecutionLog.objects.create(
                                    task=task,
                                    success=True,
                                    response_text=notification_result.get('summary', ai_result['response']),
                                    execution_duration=execution_duration,
                                    result_count=ai_result.get('result_count', 0)
                                )
                                logger.info(f"🔍 执行日志已创建")
                        except Exception as db_error:
                            logger.error(f"🔍 数据库操作失败: {str(db_error)}")
                            import traceback
                            error_stack = traceback.format_exc()
                            logger.error(f"🔍 数据库错误堆栈: {error_stack}")
                            # 发送错误到调试群
                            self._send_error_to_debug_group(f"数据库操作失败: {str(db_error)}\n堆栈: {error_stack[:500]}")
                            raise
                    
                    logger.info(f"🔍 创建update_task_success_async")
                    update_task_success_async = sync_to_async(update_task_success)
                    logger.info(f"🔍 调用update_task_success_async")
                    await update_task_success_async()
                    logger.info(f"🔍 update_task_success_async执行完成")
                except ImportError as ie:
                    logger.error(f"🔍 导入sync_to_async失败: {str(ie)}")
                    # 发送错误到调试群
                    self._send_error_to_debug_group(f"导入sync_to_async失败: {str(ie)}")
                    raise
                except Exception as e:
                    logger.error(f"🔍 更新任务状态异常: {str(e)}")
                    import traceback
                    error_stack = traceback.format_exc()
                    logger.error(f"🔍 异常堆栈: {error_stack}")
                    # 发送错误到调试群
                    self._send_error_to_debug_group(f"更新任务状态异常: {str(e)}\n堆栈: {error_stack[:500]}")
                    raise
                
                logger.info(f"任务 {task.task_name} 执行成功")
                return {
                    'success': True,
                    'message': '任务执行成功',
                    'notification_result': notification_result
                }
            else:
                # 记录失败执行
                logger.info(f"🔍 准备更新任务失败状态")
                try:
                    from asgiref.sync import sync_to_async
                    
                    @db_retry(max_retries=3, delay=1.0, backoff=2.0)
                    def update_task_failure():
                        with transaction.atomic():
                            task.total_executions += 1
                            task.last_execution = start_time
                            task.calculate_next_execution()
                            task.save()
                            
                            # 记录执行日志
                            TaskExecutionLog.objects.create(
                                task=task,
                                success=False,
                                response_text=ai_result.get('error', '执行失败'),
                                execution_duration=(timezone.now() - start_time).total_seconds(),
                                result_count=0
                            )
                    
                    update_task_failure_async = sync_to_async(update_task_failure)
                    await update_task_failure_async()
                    
                except Exception as e:
                    logger.error(f"🔍 更新任务失败状态异常: {str(e)}")
                
                logger.info(f"任务 {task.task_name} 执行失败")
                return {
                    'success': False,
                    'error': ai_result.get('error', '任务执行失败'),
                    'notification_result': None
                }
                
        except Exception as e:
            logger.error(f"执行任务 {task.id} 异常: {str(e)}")
            
            # 记录异常执行
            try:
                from asgiref.sync import sync_to_async
                
                @db_retry(max_retries=3, delay=1.0, backoff=2.0)
                def update_task_exception():
                    with transaction.atomic():
                        task.total_executions += 1
                        task.last_execution = start_time
                        task.calculate_next_execution()
                        task.save()
                        
                        # 记录执行日志
                        TaskExecutionLog.objects.create(
                            task=task,
                            success=False,
                            response_text=f"执行异常: {str(e)}",
                            execution_duration=(timezone.now() - start_time).total_seconds(),
                            result_count=0
                        )
                
                update_task_exception_async = sync_to_async(update_task_exception)
                await update_task_exception_async()
                
            except Exception as db_error:
                logger.error(f"🔍 记录异常执行失败: {str(db_error)}")
            
            return {
                'success': False,
                'error': f'执行异常: {str(e)}',
                'notification_result': None
            }
    
    def _send_error_to_debug_group(self, error_message: str):
        """发送错误信息到调试群组"""
        try:
            debug_group_id = "NzQzMzAxODcyMjAy"  # 调试群组ID
            
            # 构建错误消息
            timestamp = timezone.now().strftime("%Y-%m-%d %H:%M:%S")
            hostname = socket.gethostname()
            formatted_message = f"🚨 任务调度器错误报告\n⏰ 时间: {timestamp}\n🖥️ 主机: {hostname}\n\n❌ 错误信息:\n{error_message}"
            
            # 异步发送消息到调试群
            loop = asyncio.get_event_loop()
            loop.create_task(self._async_send_to_group(debug_group_id, formatted_message))
            
            logger.info(f"已将错误信息推送到调试群 {debug_group_id}")
        except Exception as e:
            logger.error(f"推送错误信息到调试群失败: {str(e)}")
    
    async def _async_send_to_group(self, group_id: str, message: str):
        """异步发送消息到群组"""
        try:
            from app01.seatalk_group_manager import send_message_to_group
            await sync_to_async(send_message_to_group)(group_id, message)
        except Exception as e:
            logger.error(f"异步发送消息到群组失败: {str(e)}")
    
    async def _handle_smart_notification(self, task: UserScheduledTask, ai_result: Dict) -> Dict:
        """处理智能通知"""
        try:
            # 获取智能通知管理器（延迟导入避免循环导入）
            from .advanced_task_manager import advanced_task_manager
            
            # 提取JIRA查询结果
            jira_results = self._extract_jira_results(ai_result)
            
            # 执行智能通知
            smart_result = await advanced_task_manager.execute_smart_notification_task(task, jira_results)
            
            return {
                'success': smart_result['success'],
                'summary': smart_result.get('message', '智能通知处理完成'),
                'recipients_count': smart_result.get('recipients_count', 0),
                'tickets_count': smart_result.get('tickets_count', 0)
            }
            
        except Exception as e:
            logger.error(f"处理智能通知失败: {str(e)}")
            return {
                'success': False,
                'summary': f'智能通知处理失败: {str(e)}'
            }
    
    async def _handle_reminder_notification(self, task: UserScheduledTask, reminder_message: str) -> Dict:
        """处理提醒通知"""
        try:
            logger.info(f"🔔 发送提醒通知: {task.task_name}")
            
            # 根据通知类型发送提醒
            if task.notification_type == 'private':
                # 私聊提醒
                try:
                    # 获取employee_code
                    from app01.seatalk_group_manager import get_employee_codes
                    email_to_code = get_employee_codes([task.user_email])
                    employee_code = email_to_code.get(task.user_email) or task.employee_code
                    
                    if employee_code:
                        await self.private_chat.send_text_message(
                            employee_code=employee_code,
                            content=f"🔔 **定时提醒**: {task.task_name}\n\n{reminder_message}",
                            format_type=1  # Markdown格式
                        )
                    else:
                        raise Exception(f"无法获取用户 {task.user_email} 的employee_code")
                    logger.info(f"✅ 私聊提醒发送成功")
                    return {'success': True, 'message': '私聊提醒发送成功'}
                except Exception as e:
                    logger.error(f"❌ 私聊提醒发送失败: {str(e)}")
                    return {'success': False, 'error': f'私聊提醒发送失败: {str(e)}'}
                    
            elif task.notification_type == 'group' and task.target_group_id:
                # 群聊提醒
                try:
                    from app01.views import test_for_seatalk_bot
                    
                    test_for_seatalk_bot(
                        f"🔔 **定时提醒**: {task.task_name}\n\n{reminder_message}",
                        [task.user_id],
                        task.target_group_id
                    )
                    
                    logger.info(f"✅ 群聊提醒发送成功")
                    return {'success': True, 'message': '群聊提醒发送成功'}
                        
                except Exception as e:
                    logger.error(f"❌ 群聊提醒发送失败: {str(e)}")
                    return {'success': False, 'error': f'群聊提醒发送失败: {str(e)}'}
                    
            elif task.notification_type == 'both':
                # 私聊+群聊提醒
                private_result = await self._handle_reminder_notification(
                    UserScheduledTask(
                        **{**task.__dict__, 'notification_type': 'private'}
                    ), 
                    reminder_message
                )
                
                if task.target_group_id:
                    group_result = await self._handle_reminder_notification(
                        UserScheduledTask(
                            **{**task.__dict__, 'notification_type': 'group'}
                        ), 
                        reminder_message
                    )
                    
                    if private_result['success'] and group_result['success']:
                        return {'success': True, 'message': '私聊+群聊提醒发送成功'}
                    else:
                        errors = []
                        if not private_result['success']:
                            errors.append(f"私聊: {private_result['error']}")
                        if not group_result['success']:
                            errors.append(f"群聊: {group_result['error']}")
                        return {'success': False, 'error': '; '.join(errors)}
                else:
                    return private_result
            elif task.notification_type == 'multi':
                # 多选通知方式 - 使用新的通知逻辑
                logger.info("🔔 使用多选通知方式处理提醒任务")
                await self._send_notification(task, reminder_message)
                return {'success': True, 'message': '多选提醒发送完成'}
            else:
                return {'success': False, 'error': '未配置有效的通知目标'}
                
        except Exception as e:
            logger.error(f"处理提醒通知异常: {str(e)}")
            return {'success': False, 'error': f'处理提醒通知异常: {str(e)}'}

    async def _handle_regular_notification(self, task: UserScheduledTask, response_text: str) -> Dict:
        """处理普通通知"""
        try:
            # 发送通知
            await self._send_notification(task, response_text)
            
            return {
                'success': True,
                'summary': '普通通知发送完成'
            }
            
        except Exception as e:
            logger.error(f"处理普通通知失败: {str(e)}")
            return {
                'success': False,
                'summary': f'普通通知处理失败: {str(e)}'
            }
    
    def _extract_jira_results(self, ai_result: Dict) -> List[Dict]:
        """从AI结果中提取JIRA查询结果"""
        try:
            # 尝试从AI结果中提取结构化的JIRA数据
            jira_data = ai_result.get('jira_data', {})
            
            if isinstance(jira_data, dict) and 'issues' in jira_data:
                # 如果有结构化的issues数据
                issues = jira_data['issues']
                
                # 转换为标准格式
                standardized_results = []
                for issue in issues:
                    if isinstance(issue, dict):
                        try:
                            # 提取关键字段
                            fields = issue.get('fields', {})
                            
                            standardized_issue = {
                                'key': issue.get('key', ''),
                                'summary': fields.get('summary', ''),
                                'status': fields.get('status', {}).get('name', '') if isinstance(fields.get('status'), dict) else str(fields.get('status', '')),
                                'priority': fields.get('priority', {}).get('name', '') if isinstance(fields.get('priority'), dict) else str(fields.get('priority', '')),
                                'assignee': self._extract_assignee_from_fields(fields),
                                'created': fields.get('created', ''),
                                'updated': fields.get('updated', ''),
                                'issuetype': fields.get('issuetype', {}).get('name', '') if isinstance(fields.get('issuetype'), dict) else str(fields.get('issuetype', '')),
                                'project': fields.get('project', {}).get('key', '') if isinstance(fields.get('project'), dict) else str(fields.get('project', ''))
                            }
                            
                            standardized_results.append(standardized_issue)
                            
                        except Exception as e:
                            logger.warning(f"解析单个issue失败: {str(e)}")
                            continue
                
                return standardized_results
            
            else:
                # 如果没有结构化数据，返回空列表
                logger.warning("AI结果中没有找到结构化的JIRA数据")
                return []
                
        except Exception as e:
            logger.error(f"提取JIRA结果失败: {str(e)}")
            return []
    
    def _extract_assignee_from_fields(self, fields: Dict) -> Dict:
        """从fields中提取assignee信息"""
        try:
            assignee = fields.get('assignee')
            
            if isinstance(assignee, dict):
                return {
                    'emailAddress': assignee.get('emailAddress', ''),
                    'displayName': assignee.get('displayName', ''),
                    'name': assignee.get('name', '')
                }
            elif isinstance(assignee, str):
                # 如果assignee是字符串，尝试解析
                if '@' in assignee:
                    return {
                        'emailAddress': assignee,
                        'displayName': assignee.split('@')[0],
                        'name': assignee.split('@')[0]
                    }
                else:
                    return {
                        'emailAddress': f"{assignee}@shopee.com",
                        'displayName': assignee,
                        'name': assignee
                    }
            else:
                return None
                
        except Exception as e:
            logger.warning(f"提取assignee信息失败: {str(e)}")
            return None
    
    async def _send_notification(self, task: UserScheduledTask, message: str) -> None:
        """发送通知 - 支持新的多选通知方式"""
        try:
            # 添加任务信息头部和消息附加文本
            # 清理消息中的多余空行
            cleaned_message = message.strip()
            notification_message = f"🔔 **定时任务提醒: {task.task_name}**\n\n{cleaned_message}"

            # 添加消息附加文本（如果有的话）
            reminder_message = getattr(task, 'reminder_message', None)
            logger.info(f"🔍 检查reminder_message字段: {reminder_message}")
            if reminder_message and reminder_message.strip():
                notification_message += f"\n\n📝 **附加信息**: {reminder_message}"
                logger.info(f"🔍 成功添加了附加信息: {reminder_message}")
            else:
                logger.info(f"🔍 没有附加信息或附加信息为空")

            # 获取通知方式，优先使用新的notification_methods字段
            notification_methods = []
            try:
                notification_methods = getattr(task, 'notification_methods', None) or []
                logger.info(f"🔍 从notification_methods字段获取: {notification_methods}")
            except AttributeError:
                notification_methods = []
                logger.info(f"🔍 notification_methods字段不存在")

            # 如果没有新的通知方式，从旧的notification_type转换
            if not notification_methods:
                legacy_mapping = {
                    'private': ['creator'],
                    'creator': ['creator'],
                    'assignee': ['assignee'],
                    'group': ['group'],
                    'auto_group': ['auto_group'],
                    'both': ['creator', 'group'],
                    'smart': ['assignee'],
                    'multi': ['creator']  # 多选时默认发送给创建人
                }
                notification_methods = legacy_mapping.get(task.notification_type, ['creator'])
                logger.info(f"🔍 从notification_type转换: {task.notification_type} -> {notification_methods}")

            logger.info(f"🔔 最终通知方式: {notification_methods}")

            # 调试信息：显示任务的所有通知相关字段
            logger.info(f"🔍 任务通知调试信息:")
            logger.info(f"  - task.id: {task.id}")
            logger.info(f"  - task.notification_type: {getattr(task, 'notification_type', 'N/A')}")
            logger.info(f"  - task.notification_methods: {getattr(task, 'notification_methods', 'N/A')}")
            logger.info(f"  - task.target_group_id: {getattr(task, 'target_group_id', 'N/A')}")

            # 检查是否需要智能通知处理
            needs_smart_notification = any(method in ['assignee', 'auto_group'] for method in notification_methods)

            if needs_smart_notification:
                # 如果包含assignee或auto_group，需要通过智能通知处理
                logger.info("🔍 检测到需要智能通知的方式，切换到智能通知处理")
                try:
                    # 重新执行JIRA查询以获取最新数据
                    from .ai_assistant import ai_assistant
                    ai_result = await ai_assistant.process_query(
                        user_id=task.user_id,
                        user_query=task.query_text,
                        group_id=None,
                        employee_code=task.employee_code
                    )

                    if ai_result['success']:
                        # 获取AI结果中的JIRA数据
                        from .advanced_task_manager import advanced_task_manager
                        jira_results = self._extract_jira_results(ai_result)

                        # 执行智能通知，处理assignee和auto_group
                        smart_result = await advanced_task_manager.execute_smart_notification_task(task, jira_results)
                        logger.info(f"🔍 智能通知处理结果: {smart_result}")
                    else:
                        logger.error(f"❌ 重新查询JIRA失败: {ai_result.get('error', '未知错误')}")
                except Exception as e:
                    logger.error(f"❌ 智能通知处理异常: {str(e)}")

            # 处理其他通知方式
            for method in notification_methods:
                logger.info(f"🔍 处理通知方式: {method}")
                if method == 'creator':
                    # 发送给任务创建人
                    await self._send_to_creator(task, notification_message)
                elif method == 'assignee':
                    # 直接处理assignee通知，复用已有的JIRA查询结果
                    logger.info("🔍 开始处理Assignee通知")
                    # 从第一次AI查询中提取JIRA结果
                    cached_jira_results = None
                    try:
                        # 尝试从当前作用域获取ai_result
                        if 'ai_result' in locals() and ai_result.get('success'):
                            cached_jira_results = self._extract_jira_results(ai_result)
                    except:
                        pass
                    await self._send_to_assignees(task, notification_message, cached_jira_results)
                elif method == 'group':
                    # 发送到指定群
                    await self._send_to_group(task, notification_message)
                elif method == 'auto_group':
                    # 直接处理auto_group通知
                    logger.info("🔍 开始处理Auto group通知")
                    await self._send_to_auto_groups(task, notification_message)

        except Exception as e:
            logger.error(f"发送通知失败: {str(e)}")

    async def _send_to_creator(self, task: UserScheduledTask, message: str) -> None:
        """发送消息给任务创建人"""
        try:
            if task.employee_code:
                result = await self.private_chat.send_text_message(
                    task.employee_code,
                    message
                )
                if result.get('success'):
                    logger.info(f"✅ 私聊通知已发送给创建人: {task.user_email} (employee_code: {task.employee_code})")
                else:
                    logger.error(f"❌ 私聊通知发送失败: {task.user_email} (employee_code: {task.employee_code}) - {result.get('error')}")
            elif task.user_email:
                # 使用正确的API获取employee_code
                from app01.seatalk_group_manager import get_employee_codes
                email_to_code = get_employee_codes([task.user_email])
                employee_code = email_to_code.get(task.user_email)

                if employee_code:
                    result = await self.private_chat.send_text_message(
                        employee_code,
                        message
                    )
                    if result.get('success'):
                        logger.info(f"✅ 私聊通知已发送给创建人: {task.user_email} (employee_code: {employee_code})")
                    else:
                        logger.error(f"❌ 私聊通知发送失败: {task.user_email} (employee_code: {employee_code}) - {result.get('error')}")
                else:
                    logger.warning(f"⚠️ 无法获取employee_code: {task.user_email}，可能是邮箱不存在或已离职")
            else:
                logger.warning(f"⚠️ 任务创建人信息不完整，无法发送私聊通知")
        except Exception as e:
            logger.error(f"❌ 发送给创建人失败: {str(e)}")

    async def _send_to_group(self, task: UserScheduledTask, message: str) -> None:
        """发送消息到指定群"""
        try:
            if task.target_group_id:
                # 处理不同格式的群组ID
                group_id = task.target_group_id

                # 如果是group_id:格式，提取实际的群组ID
                if group_id.startswith('group_id:'):
                    group_id = group_id.replace('group_id:', '')
                elif group_id.startswith('keyword:'):
                    # 如果是关键字格式，需要先搜索群组
                    keyword = group_id.replace('keyword:', '')
                    logger.info(f"🔍 根据关键字搜索群组: {keyword}")

                    # 搜索群组
                    try:
                        from asgiref.sync import sync_to_async
                        from app01.models import SeatalkGroup
                        from django.db.models import Q

                        # 异步搜索群组
                        search_groups = sync_to_async(
                            lambda: list(SeatalkGroup.objects.filter(
                                Q(group_name__icontains=keyword)
                            ).order_by('-id')[:3])
                        )
                        groups = await search_groups()

                        if groups:
                            # 使用第一个匹配的群组
                            group_id = groups[0].group_id
                            logger.info(f"✅ 找到匹配的群组: {groups[0].group_name} ({group_id})")
                        else:
                            logger.warning(f"⚠️ 未找到包含关键字'{keyword}'的群组")
                            return
                    except Exception as e:
                        logger.error(f"❌ 搜索群组失败: {str(e)}")
                        return

                logger.info(f"🔍 准备发送群聊通知到群: {group_id}")

                # 延迟导入避免循环导入
                from app01.views import test_for_seatalk_bot
                result = test_for_seatalk_bot(
                    message,
                    [task.user_id],
                    group_id
                )
                if result:
                    logger.info(f"✅ 群聊通知已发送到群: {group_id}")
                else:
                    logger.error(f"❌ 群聊通知发送失败: {group_id}")
            else:
                logger.warning(f"⚠️ 未指定目标群组，无法发送群聊通知")
        except Exception as e:
            logger.error(f"❌ 发送到群组失败: {str(e)}")

    async def _send_to_assignees(self, task: UserScheduledTask, message: str, jira_results: List[Dict] = None) -> None:
        """发送消息给JIRA查询结果的assignees"""
        try:
            # 如果没有传入jira_results，则重新查询
            if jira_results is None:
                logger.info("🔍 开始查询JIRA获取assignee信息")
                # 重新执行JIRA查询以获取assignee信息
                from .ai_assistant import ai_assistant
                ai_result = await ai_assistant.process_query(
                    user_id=task.user_id,
                    user_query=task.query_text,
                    group_id=None,
                    employee_code=task.employee_code
                )

                if ai_result['success']:
                    # 提取JIRA结果中的assignees
                    jira_results = self._extract_jira_results(ai_result)
                else:
                    logger.error(f"❌ 查询JIRA失败: {ai_result.get('error', '未知错误')}")
                    return
            else:
                logger.info("🔍 使用缓存的JIRA查询结果")
                assignees = set()

                for issue in jira_results:
                    # 从assignee字典中提取email
                    assignee_info = issue.get('assignee', {})
                    if isinstance(assignee_info, dict):
                        assignee_email = assignee_info.get('emailAddress', '')
                        if assignee_email and assignee_email != 'unassigned':
                            assignees.add(assignee_email)
                    elif isinstance(assignee_info, str) and assignee_info != 'unassigned':
                        # 如果assignee是字符串格式
                        if '@' in assignee_info:
                            assignees.add(assignee_info)
                        else:
                            assignees.add(f"{assignee_info}@shopee.com")

                logger.info(f"🔍 找到assignees: {list(assignees)}")

                # 为每个assignee生成个性化消息并发送
                for assignee_email in assignees:
                    try:
                        # 筛选出该assignee的JIRA单
                        assignee_issues = []
                        for issue in jira_results:
                            assignee_info = issue.get('assignee', {})
                            if isinstance(assignee_info, dict):
                                issue_assignee_email = assignee_info.get('emailAddress', '')
                            elif isinstance(assignee_info, str):
                                issue_assignee_email = assignee_info if '@' in assignee_info else f"{assignee_info}@shopee.com"
                            else:
                                continue

                            if issue_assignee_email == assignee_email:
                                assignee_issues.append(issue)

                        if not assignee_issues:
                            continue

                        # 生成该assignee的个性化消息
                        assignee_message = self._generate_assignee_message(task, assignee_issues, assignee_email)

                        from app01.seatalk_group_manager import get_employee_codes
                        email_to_code = get_employee_codes([assignee_email])
                        employee_code = email_to_code.get(assignee_email)

                        if employee_code:
                            result = await self.private_chat.send_text_message(
                                employee_code,
                                assignee_message
                            )
                            logger.info(f"✅ Assignee通知已发送给: {assignee_email} (employee_code: {employee_code}), 包含 {len(assignee_issues)} 个相关单")
                        else:
                            logger.warning(f"⚠️ 无法获取 {assignee_email} 的employee_code")
                    except Exception as e:
                        logger.error(f"❌ 发送给assignee {assignee_email} 失败: {str(e)}")

        except Exception as e:
            logger.error(f"❌ 发送到assignees失败: {str(e)}")

    def _generate_assignee_message(self, task: UserScheduledTask, assignee_issues: List[Dict], assignee_email: str) -> str:
        """为特定assignee生成个性化消息"""
        try:
            # 构建消息头部
            message = f"🔔 **定时任务提醒: {task.task_name}**\n\n"

            # 添加个性化问候
            assignee_name = assignee_email.split('@')[0] if '@' in assignee_email else assignee_email
            message += f"👋 Hi {assignee_name}，以下是分配给您的 {len(assignee_issues)} 个相关单:\n\n"

            # 添加每个issue的详细信息
            for i, issue in enumerate(assignee_issues, 1):
                issue_key = issue.get('key', '')
                summary = issue.get('summary', '')
                status = issue.get('status', '')
                priority = issue.get('priority', '')
                issuetype = issue.get('issuetype', '')

                # 构建标签，过滤空值
                tags = []
                if issuetype and issuetype.strip():
                    tags.append(issuetype)
                if priority and priority.strip():
                    tags.append(priority)
                if status and status.strip():
                    tags.append(status)

                tag_str = '】【'.join(tags)
                if tag_str:
                    tag_str = f"【{tag_str}】"

                message += f"📋{tag_str}{summary}\n"
                message += f"🔗https://jira.shopee.io/browse/{issue_key}\n"

                if i < len(assignee_issues):
                    message += "----------------------------------\n"

            # 添加JQL查询信息（使用代码格式）
            message += f"\n🔍 执行的JQL查询:\n```\n{task.query_text}\n```"

            # 添加消息附加文本（如果有的话）
            reminder_message = getattr(task, 'reminder_message', None)
            if reminder_message and reminder_message.strip():
                message += f"\n\n📝 **附加信息**: {reminder_message}"

            return message

        except Exception as e:
            logger.error(f"生成assignee消息失败: {str(e)}")
            return f"🔔 **定时任务提醒: {task.task_name}**\n\n❌ 消息生成失败，请联系管理员"

    async def _send_to_auto_groups(self, task: UserScheduledTask, message: str) -> None:
        """发送消息到JIRA查询结果所在的群组"""
        try:
            logger.info("🔍 开始查询JIRA获取群组信息")
            # 重新执行JIRA查询以获取JIRA keys
            from .ai_assistant import ai_assistant
            ai_result = await ai_assistant.process_query(
                user_id=task.user_id,
                user_query=task.query_text,
                group_id=None,
                employee_code=task.employee_code
            )

            if ai_result['success']:
                # 提取JIRA结果中的issue keys
                jira_results = self._extract_jira_results(ai_result)
                issue_keys = []

                for issue in jira_results:
                    issue_key = issue.get('key', '')
                    if issue_key:
                        issue_keys.append(issue_key)

                logger.info(f"🔍 找到JIRA keys: {issue_keys}")

                # 为每个issue key查找对应的群组并发送消息
                for issue_key in issue_keys:
                    await self._send_to_issue_groups(issue_key, message, task, jira_results)

            else:
                logger.error(f"❌ 查询JIRA失败: {ai_result.get('error', '未知错误')}")

        except Exception as e:
            logger.error(f"❌ 发送到auto groups失败: {str(e)}")

    async def _send_to_issue_groups(self, issue_key: str, message: str, task: UserScheduledTask, jira_results: List[Dict]) -> None:
        """根据JIRA issue key查找对应的群组并发送消息"""
        try:
            logger.info(f"🔍 为issue {issue_key} 查找对应的群组")

            # 异步导入群组查询功能
            from asgiref.sync import sync_to_async
            from app01.models import SeatalkGroup
            from django.db.models import Q

            # 异步搜索群名包含该JIRA key的群组
            search_groups = sync_to_async(
                lambda: list(SeatalkGroup.objects.filter(
                    Q(group_name__icontains=issue_key)
                ).order_by('-id')[:3])  # 最多发送到前3个群
            )
            groups = await search_groups()

            if groups:
                logger.info(f"🔍 为issue {issue_key} 找到 {len(groups)} 个相关群组")

                # 为每个群组发送消息
                for group in groups:
                    try:
                        logger.info(f"🔍 发送消息到群组: {group.group_name} ({group.group_id})")

                        # 筛选出与该群组相关的JIRA单
                        related_issues = []
                        for issue in jira_results:
                            if issue_key in issue.get('key', ''):
                                related_issues.append(issue)

                        # 生成针对该群组的消息
                        issue_message = self._generate_group_message(task, related_issues, group.group_name)

                        # 发送消息到群组
                        from app01.views import test_for_seatalk_bot
                        result = test_for_seatalk_bot(
                            issue_message,
                            [task.user_id],
                            group.group_id
                        )

                        if result:
                            logger.info(f"✅ Auto group通知已发送到群: {group.group_name} ({group.group_id})")
                        else:
                            logger.error(f"❌ Auto group通知发送失败: {group.group_name} ({group.group_id})")

                    except Exception as e:
                        logger.error(f"❌ 发送到群组 {group.group_name} 失败: {str(e)}")
            else:
                logger.warning(f"⚠️ 未找到包含 {issue_key} 的群组")

        except Exception as e:
            logger.error(f"❌ 为issue {issue_key} 查找群组失败: {str(e)}")

    def _generate_group_message(self, task: UserScheduledTask, related_issues: List[Dict], group_name: str) -> str:
        """为特定群组生成相关JIRA单的消息"""
        try:
            # 构建消息头部
            message = f"🔔 **定时任务提醒: {task.task_name}**\n\n"

            if not related_issues:
                message += "✅ 暂无相关的JIRA单需要处理"
            else:
                message += f"📋 以下是与 **{group_name}** 相关的 {len(related_issues)} 个单:\n\n"

                # 添加每个issue的详细信息
                for i, issue in enumerate(related_issues, 1):
                    issue_key = issue.get('key', '')
                    summary = issue.get('summary', '')
                    status = issue.get('status', '')
                    priority = issue.get('priority', '')
                    issuetype = issue.get('issuetype', '')

                    # 获取assignee信息
                    assignee_info = issue.get('assignee', {})
                    if isinstance(assignee_info, dict):
                        assignee_display = assignee_info.get('emailAddress', 'unassigned')
                    elif isinstance(assignee_info, str):
                        assignee_display = assignee_info
                    else:
                        assignee_display = 'unassigned'

                    # 构建标签，过滤空值
                    tags = []
                    if issuetype and issuetype.strip():
                        tags.append(issuetype)
                    if priority and priority.strip():
                        tags.append(priority)
                    if status and status.strip():
                        tags.append(status)

                    tag_str = '】【'.join(tags)
                    if tag_str:
                        tag_str = f"【{tag_str}】"

                    message += f"📋{tag_str}{summary}\n"
                    message += f"🔗https://jira.shopee.io/browse/{issue_key}\n"
                    message += f"经办人: {assignee_display}\n"

                    if i < len(related_issues):
                        message += "----------------------------------\n"

                # 添加JQL查询信息（使用代码格式）
                message += f"\n🔍 执行的JQL查询:\n```\n{task.query_text}\n```"

            # 添加消息附加文本（如果有的话）
            reminder_message = getattr(task, 'reminder_message', None)
            if reminder_message and reminder_message.strip():
                message += f"\n\n📝 **附加信息**: {reminder_message}"

            return message

        except Exception as e:
            logger.error(f"生成群组消息失败: {str(e)}")
            return f"🔔 **定时任务提醒: {task.task_name}**\n\n❌ 消息生成失败，请联系管理员"

    def parse_schedule_expression(self, expression: str) -> Dict:
        """
        解析调度表达式，支持更灵活的格式理解
        
        支持的格式示例:
        - "每天 10:00" / "每日10:00" / "每天10:00"
        - "工作日 09:30" / "工作日09:30" / "每个工作日 09:30"
        - "每周一 14:00" / "每周一14:00" / "每周二三 15:00"
        - "每月15日 16:00" / "每月15日16:00" / "每月1号 12:00"
        """
        try:
            from app01.ai_module.multi_llm_client import multi_llm_client
            import asyncio
            import re
            
            # 首先尝试使用正则表达式提取时间部分
            time_pattern = r'(\d{1,2}):(\d{2})'
            time_match = re.search(time_pattern, expression)
            
            if not time_match:
                return {
                    'error': '无法识别时间格式，请使用 HH:MM 格式（如 10:00）'
                }
            
            time_part = time_match.group(0)  # 提取到的时间，如 "10:00"
            
            # 构建提示词，让大模型解析调度表达式
            prompt = f"""
            请解析以下调度表达式，并以JSON格式返回解析结果：
            
            调度表达式: {expression}
            
            请返回以下格式的JSON（不要包含其他文本）:
            ```json
            {{
                "frequency": "daily|weekly|monthly", // 频率：每天、每周或每月
                "schedule_time": "HH:MM",           // 时间，如 "10:00"
                "schedule_days": [1,2,3,4,5]        // 对于weekly：1-7表示周一到周日，对于monthly：1-31表示日期
            }}
            ```
            
            解析规则：
            1. 如果表达式包含"每天"、"每日"，frequency为"daily"
            2. 如果表达式包含"每周"或特定星期几，frequency为"weekly"
            3. 如果表达式包含"工作日"或"每个工作日"，frequency为"weekly"，schedule_days为[1,2,3,4,5]
            4. 如果表达式包含"每月"，frequency为"monthly"
            5. 时间应从表达式中提取，格式为"HH:MM"
            6. 对于weekly，应提取出具体的星期几（1-7）
            7. 对于monthly，应提取出具体的日期（1-31）
            """
            
            # 使用安全的异步调用方式
            try:
                import concurrent.futures
                import threading
                
                # 在新线程中运行异步代码，避免与现有事件循环冲突
                def run_async_llm():
                    try:
                        new_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(new_loop)
                        try:
                            result = new_loop.run_until_complete(
                                multi_llm_client.generate_with_retry(prompt, max_retries=2, temperature=0)
                            )
                            return result
                        finally:
                            new_loop.close()
                    except Exception as e:
                        logger.error(f"异步LLM调用失败: {str(e)}")
                        return {'success': False, 'error': str(e)}
                
                # 在线程池中执行
                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(run_async_llm)
                    result = future.result(timeout=15)  # 15秒超时
                
                if not result.get('success', False):
                    logger.error(f"大模型调用失败: {result.get('error', '未知错误')}")
                    response = None
                else:
                    response = result['content']
                
                # 从响应中提取JSON
                if response:
                    import json
                    import re
                    
                    # 尝试直接解析整个响应
                    try:
                        parsed_result = json.loads(response)
                        if all(key in parsed_result for key in ['frequency', 'schedule_time', 'schedule_days']):
                            return parsed_result
                    except:
                        # 如果直接解析失败，尝试从响应中提取JSON部分
                        json_match = re.search(r'```json\s*(\{.*?\})\s*```', response, re.DOTALL)
                        if json_match:
                            try:
                                parsed_result = json.loads(json_match.group(1))
                                if all(key in parsed_result for key in ['frequency', 'schedule_time', 'schedule_days']):
                                    return parsed_result
                            except:
                                pass
            except Exception as e:
                logger.error(f"使用大模型解析调度表达式失败: {str(e)}")
            
            # 如果大模型解析失败，回退到传统解析方法
            logger.info("大模型解析失败，回退到传统解析方法")
            
            # 每天/每日
            if expression.startswith('每天') or expression.startswith('每日'):
                return {
                    'frequency': 'daily',
                    'schedule_time': time_part,
                    'schedule_days': []
                }
            
            # 工作日
            elif expression.startswith('工作日') or expression.startswith('每个工作日'):
                return {
                    'frequency': 'weekly',
                    'schedule_time': time_part,
                    'schedule_days': [1, 2, 3, 4, 5]  # 周一到周五
                }
            
            # 每周特定日期
            elif expression.startswith('每周'):
                # 提取星期部分
                week_part = expression[2:].replace(time_part, '').strip()
                
                day_mapping = {
                    '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '日': 7
                }
                
                schedule_days = []
                for char in week_part:
                    if char in day_mapping:
                        schedule_days.append(day_mapping[char])
                
                if not schedule_days:
                    # 如果没有指定具体星期，默认为工作日
                    schedule_days = [1, 2, 3, 4, 5]
                
                return {
                    'frequency': 'weekly',
                    'schedule_time': time_part,
                    'schedule_days': schedule_days
                }
            
            # 每月特定日期
            elif expression.startswith('每月'):
                # 提取日期数字和特殊关键词
                month_part = expression[2:].replace(time_part, '').strip()
                day_numbers = re.findall(r'\d+', month_part)
                schedule_days = [int(d) for d in day_numbers] if day_numbers else []

                # 检查是否包含"月末"关键词
                if '月末' in month_part or '最后一天' in month_part:
                    schedule_days.append(-1)

                # 如果没有找到任何日期，默认为1号
                if not schedule_days:
                    schedule_days = [1]

                return {
                    'frequency': 'monthly',
                    'schedule_time': time_part,
                    'schedule_days': schedule_days
                }
            
            # 如果以上都不匹配，尝试直接解析为每日任务
            elif time_match:
                # 如果只有时间，默认为每日执行
                return {
                    'frequency': 'daily',
                    'schedule_time': time_part,
                    'schedule_days': []
                }
            
            else:
                return {
                    'error': '不支持的调度表达式格式。支持格式：每天10:00、工作日09:30、每周一14:00、每月15日16:00'
                }
                
        except Exception as e:
            logger.error(f"解析调度表达式失败: {str(e)}")
            return {
                'error': f'解析调度表达式失败: {str(e)}'
            }

    async def get_task_statistics(self, user_email: str, project_filter: str = None) -> Dict:
        """获取任务统计信息"""
        try:
            from asgiref.sync import sync_to_async
            from .task_permission_manager import task_permission_manager
            from django.db.models import Count, Q
            
            # 权限检查
            permission_result = task_permission_manager.check_task_management_permission(
                user_email=user_email,
                operation='stats'
            )
            
            if not permission_result.allowed:
                return {
                    'success': False,
                    'error': permission_result.reason,
                    'permission_denied': True
                }
            
            def get_stats():
                # 获取用户可查看的任务过滤条件
                filter_config = task_permission_manager.get_user_manageable_tasks_filter(user_email)
                
                # 基础查询
                query = UserScheduledTask.objects.all()
                
                if filter_config:  # 不是超级管理员
                    conditions = Q()
                    
                    # 包含自己的任务
                    if filter_config.get('include_own'):
                        conditions |= Q(user_email=user_email)
                    
                    # PJM可以看到管理项目的任务
                    if filter_config.get('project_filters'):
                        for project_filter_str in filter_config['project_filters']:
                            conditions |= Q(query_text__icontains=project_filter_str)
                    
                    query = query.filter(conditions)
                
                # 如果指定了项目过滤
                if project_filter:
                    query = query.filter(query_text__icontains=f'project = {project_filter}')
                
                # 统计数据
                total_tasks = query.count()
                active_tasks = query.filter(status='active').count()
                paused_tasks = query.filter(status='paused').count()
                smart_tasks = query.filter(notification_type='smart').count()
                
                # 按用户统计
                user_stats = query.values('user_email').annotate(
                    task_count=Count('id')
                ).order_by('-task_count')[:10]
                
                # 按项目统计（简单的文本匹配）
                project_stats = {}
                for project in task_permission_manager.SUPPORTED_PROJECTS:
                    count = query.filter(query_text__icontains=f'project = {project}').count()
                    if count > 0:
                        project_stats[project] = count
                
                return {
                    'total_tasks': total_tasks,
                    'active_tasks': active_tasks,
                    'paused_tasks': paused_tasks,
                    'smart_tasks': smart_tasks,
                    'user_stats': list(user_stats),
                    'project_stats': project_stats
                }
            
            get_stats_async = sync_to_async(get_stats)
            stats = await get_stats_async()
            
            return {
                'success': True,
                'statistics': stats,
                'user_role': permission_result.user_role
            }
            
        except Exception as e:
            logger.error(f"获取任务统计失败: {str(e)}")
            return {
                'success': False,
                'error': f'获取统计失败: {str(e)}'
            }


# 全局任务调度器实例
task_scheduler = TaskScheduler() 