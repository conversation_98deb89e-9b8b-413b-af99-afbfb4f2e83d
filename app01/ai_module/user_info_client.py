"""
用户信息查询API客户端
支持调用两个用户信息查询接口并合并数据
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, List, Optional, Union
from icecream import ic

logger = logging.getLogger(__name__)


class UserInfoClient:
    """用户信息查询API客户端"""

    def __init__(self):
        self.base_url = "https://mkt-admin.test.shopee.sg/mkt/admin/qaservice/api/v1"
        self.timeout = 30

        # 支持的地区列表
        self.supported_countries = {
            'id': 'ID', 'tw': 'TW', 'sg': 'SG', 'th': 'TH', 'ph': 'PH', 'my': 'MY',
            'vn': 'VN', 'br': 'BR', 'mx': 'MX', 'pl': 'PL', 'co': 'CO', 'cl': 'CL', 'ar': 'AR'
        }

        # 支持的环境列表
        self.supported_environments = {
            'test': 'TEST', 'uat': 'UAT', 'staging': 'STAGING', 'live': 'LIVE'
        }
        
        # 从curl中提取的完整cookie和headers
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,th;q=0.8,en;q=0.7',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
        }

        # 接口3和4的headers（admin.user.test.shopee.io）
        self.admin_headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,th;q=0.8,en;q=0.7',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'origin': 'https://admin.user.test.shopee.io',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://admin.user.test.shopee.io/',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'service_name': 'account.admin',
            'soup-auto-redirect': 'false',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'x-sap-ri': '5cbe9268c72b3eb4ee858d3e010118fe73ebd3bf54775a1b720f',
            'x-sap-sec': '6SJT0UzSYlRHGlRHCQR5Gl5HCQRHGl5HGlRpGlRHqlNHGlASGlQ2GlRHClRHGCDg4pu5GlRHQlRHGaAHGlRn1Pwm/HDiTRej4X1yIyhLeMc487dGvlWTUF6KiWMeqWgnCYH1zuNoZajGa7Bj+hvccKR/Mjq1DTOHP5yBxX86nCq24Id9jiScnzYHMpacjjCv2zJKlJigT4bfDXsv6LwOgIzFOWiRYd3ICHDtUconsSzldckuDlUwOBR8SIxq0avctrKhJpBG/83KmTISHo3ubzsvJ5nLjIF7iyl4htY8p5uEFs7cenCWjWxYwlRHGi71lzNx8ArYGlRHGZlf4pu5GlRHalRHGm5HGlQm5IFzsFo9MPSlePVWcsL+YQijGQzHGlRyzz0uzzkUzDRHGlQr4pnfwlRHGmAHGlR2GlRHBUYWhWFIc2b8np1FX5pedinixNV5GlRHm9ktz941mirHGlRHwlRrGlzHClR5GlRHwlRHGmAHGlR2GlRHe19w/2cGIQ23yUQ1cfn0IFo36AV5GlRHm+6zlzzClzpHGlRH',
            'cookie': 'ISFE_DF=*********; space_auth_live=MTc1Mzg0NDM3OHxOd3dBTkZsUFExVkpXRUpaTkZoRFVVdEVWVmhIUlU5V05rMURWMWcwU0ZaUlMwcEpUMWhVVHpSQ016WlVRVmxhUmxCRlNsTlBNbEU9fPX2I7_NK55nd3t5Pi2o2zWF8OUAwPolv0syQxMWpYkD; _gid=GA1.2.448986612.1754271833; _ga_VPBMX0QP83=GS2.1.s1754387381$o22$g1$t1754387387$j54$l0$h0; _ga=GA1.1.1294555254.1753322987; SPC_SEC_SI=v1-Q1Mwd2Jad25QbzdBOGlvN0eTdB5SX2nAgUbJVwrxgDzGFVoKg12Fxuy4IW9qEU7Qc/OE0qQ44bNTnf2flab5w3IfY7RRah+CYV6JuLTWizs=; SSO_C=s3uto5l049dt5zvu124i3uwwlub38tb19ncjlykq'
        }

        # 完整的cookie字符串
        self.cookies = 'REC_T_ID=d9c4d235-6795-11f0-87ab-0a80fbacc3c1; SPC_F=F696UWbW5MPMVO2mMaBoT8tJEn920c3q; _gcl_au=1.1.1121492683.1753255377; SC_DFP=jCtxUjPDtXyPMeHnBRqBFytWtwuOPbQW; SPC_CLIENTID=RjY5NlVXYlc1TVBNompoxyjllfjrldjl; SPC_F=5PyaHSuA0XlJbrsmjwWHouATZEqbaqYV; REC_T_ID=69ac9759-69f8-11f0-8b30-fa4c1297337c; SPC_R_T_ID=a2QdwIRjrt8EQgh/YTuL1mvVkegmYOUS7DMhG5h+LLgJQ3fdqbevuxBnmfadTNHB9aQBlMYjU1xR4hzow3NffH3uIGaZTd6necSLu+lzMCgT7BqgjZr7uDgHko4Kz0TlytTYRUKe7B4HBJg/kz0BGqJuQjaByPwVnw21/j1WX6g=; SPC_R_T_IV=UnNrZEN2aW1jMlFqNFZZYw==; SPC_T_ID=a2QdwIRjrt8EQgh/YTuL1mvVkegmYOUS7DMhG5h+LLgJQ3fdqbevuxBnmfadTNHB9aQBlMYjU1xR4hzow3NffH3uIGaZTd6necSLu+lzMCgT7BqgjZr7uDgHko4Kz0TlytTYRUKe7B4HBJg/kz0BGqJuQjaByPwVnw21/j1WX6g=; SPC_T_IV=UnNrZEN2aW1jMlFqNFZZYw==; _fbp=fb.1.1753517624963.458752052280061997; SPC_CLIENTID=5PyaHSuA0XlJbrsmtnxtezfqrgnixqsz; SPC_CDS_CHAT=ea82af25-2702-4c00-8968-4a6ee921e77a; SPC_SC_SA_TK=; SPC_SC_SA_UD=; SPC_SC_OFFLINE_TOKEN=; SC_SSO=-; SC_SSO_U=-; SPC_EC=.ZGRkVW5WbGh4dXpGRjFuSKHoC6uaGopfgStk4Uohy4CldMBiU8JzJ/lWfAVwBeash4MzlYDLg8iq4a8styDf0KqVwKdAlfNd7xGKUYrtPlAGkL+YXbdldBfY2vXFUI/QYOvwt/80RIJwwXgstu5DcbSAARUl30r2IJaJh92nERD95XPLthWEihhowXWf+w5sGbX2STQt+K889NlTZtrJ6DbCSg5MBPp3JNhKaIndfO264MWsGCvIjZR+uSZ99bfo; SPC_ST=.QmVraFJsb21hOWhPMGxSa0wh2CzcAuhDPdTUoLBS++rEmgfRKvTrmkQAVHDbWAGEHmw6V5FpSEZcVuDB4ETHQrI6wPk8nayY6BKDu7bSMWkzQjsVV9yDq16XzBqjcqgcp+x+MA0YIBwqhZ+fs7V+T5k99ElyqUm9jFO6kLMJr7ox1K9VU2JmFHU6MHMZW8M8+2YwqlL8eaxLpuRd1c1BNlc6My/iRnq5I6Y6l+ERrzzy/dfXY15zyNcJPQkGBGB3; SC_DFP=DxCRqsIvclupEAunjfYqIHYlvUQGwoUd; SPC_CDS_CHAT=6ff08c0f-e635-4b85-af8e-de1efb37556d; SPC_T_ID=a2QdwIRjrt8EQgh/YTuL1mvVkegmYOUS7DMhG5h+LLgJQ3fdqbevuxBnmfadTNHB9aQBlMYjU1xR4hzow3NffH3uIGaZTd6necSLu+lzMCgT7BqgjZr7uDgHko4Kz0TlytTYRUKe7B4HBJg/kz0BGqJuQjaByPwVnw21/j1WX6g=; SPC_T_IV=UnNrZEN2aW1jMlFqNFZZYw==; SPC_R_T_ID=a2QdwIRjrt8EQgh/YTuL1mvVkegmYOUS7DMhG5h+LLgJQ3fdqbevuxBnmfadTNHB9aQBlMYjU1xR4hzow3NffH3uIGaZTd6necSLu+lzMCgT7BqgjZr7uDgHko4Kz0TlytTYRUKe7B4HBJg/kz0BGqJuQjaByPwVnw21/j1WX6g=; SPC_R_T_IV=UnNrZEN2aW1jMlFqNFZZYw==; SPC_SC_SA_TK_TS=; SPC_SC_SA_UD_TS=; SPC_SC_OFFLINE_TOKEN=; SC_SSO=-; SC_SSO_U=-; SPC_ST=.RnNSY0ZvZjVabGxBOEJGV4NWyLRfNa6jij7B6xGg1TYszVrm6vUhPmou5W3F05jl6dhvqcw8C7zXjRZN6VphUsFq+n1GCv2Ia1HiT+qif1j8nRIwyPXtbvkpWpo0tchKkgzYj6T6irGejxfG97k8MB9iu8jrNk1QH6EOTgaWeWPK1uPjVEhGkaS059kKyso6R6wRM/dPgr9pMTz/KitQS8rPPm+Tso6uXnaK4NKV9rJBx2DIffT7W8r8Q/DhvaCx; SPC_U=80328; SPC_EC=.M1d6eDloWm01S0ZVNXdUaTbI2fANPKpQPIxRQzxF0jptYCpcGBaww94YXLK4clmZ4p+Hlgcp6SBoE6ElpKJcBSSlETHYPBq1Sb8OYbgdZ24N5+jn1FLoumVFwLsCK9OmCD0f6dQs6ez4F7vQX+iWhRMYRiFsFrXcc9QSDtOkX83PDkvsKy2DVnttHiWgJrkZnVPNvVjLSKDx7FgAtXdmL162Y4vHH02FdUL/BjH1VBu63IvPB3wyP0u72gHfXsJc; SPC_SC_SESSION_TS=gSH/bVtO1fPyxPhy7uYRGLg+tJkJUqObRiepPhe8PVKE1wr2tCfqfYrA9WcbC6H6OiIfL3FiuJ0BIj/wz2Z9thXwDTmlKRtExq7z4iv1bvjRlGPErIVDnUm3c/HfqBSzWbdbP7vVc4hCAcACBmhOK//U3PZiLnhGJRgVvTOGjIvlkWn2nm0cgUVBetmM8Pph5DG6Jdqm9w5iaI3tIM1/wXpULKHkD1elZTP5AAQo9L5Jb+1EWqLcNErefSobNSM5K_1_80328; SPC_STK=Xd4SOzyX+L7gtTTBcotjS7VbnYQkDUes41HrXVseiR8zakM5Nlylgj85+HuNVoY5kQrU9axaBTN5RXS2EKQ2CdAnYqiokUT9kfYOrjsodU3Obw91SV5qJJ2hAjq6C0ePlc479ZqXWEQYT3MiYE4AvhiFq8N3cUf1Kd2mN1M5Pthz++SMVgB8LPdNUIGYJ3aMDX/9UYRl7rmGfksZ8idj4NF8VtNAnjUS2dbF1dxl7yR13m+nTCvyhLgZ7NCLUJvXRKEUC+ysuayH9VOqkbPKnCK84wKP7O8b8jbUPPYZ3CrAJVUql+x259KtmsnzjPAqJcp3KFirm9lY2o4xEyTK+ycxc0u/peRZ+U6gFcDjC2s2J/ygsWLsFm19LnW0iGK7eWEvIkAI8GcYRXw7q7GK2otGejWyJiXpoLIix8cRm1xlQ0pymwfgyCwBVsBRMy9TPN3y2SXLzqwd7DJhte2oQA==; CTOKEN=C8SdhWz4EfCVmpqDL5bmIg%3D%3D; _ga_PN56VNNPQX=GS2.2.s1753878008$o9$g1$t1753878009$j59$l0$h0; _ga_CGXK257VSB=GS2.1.s1753878007$o10$g1$t1753878024$j43$l0$h0; _ga_EZBZ7XZP0H=GS2.1.s1753878009$o16$g1$t1753878024$j45$l0$h0; mktqa_platform_dept=mktqa; mktqa_login_redirect="/mktqa/home"; MKT_QA_MSG="IajfdmsuUEDJgOlFWxvgnaioZP/mqujpHJxwmGDxvcGerzRphlPHFMHmj+zv9rQDW9h06vwGHKr2dJ0cteO/Fn0reWolhp0WJdCO9jamixVAapRafevp1PDcDVKfBHX5kDeksjm2LijKafiAF+PQcchhw8Vex7bZRiHzKeVuKchCfaEX79l/hvZ40mS4gZZ37rPOoYAVJ95j40cg+UHhxw=="; MKT_QA_ROLE=; MKT_QA_EMAIL="<EMAIL>"; _gid=GA1.2.414559304.1754306095; _ga=GA1.1.145582130.1753255378; _ga_C1WK9XY1BE=GS2.1.s1754306096$o15$g1$t1754306130$j26$l0$h0'
        
        # 将cookie字符串添加到headers中
        self.headers['cookie'] = self.cookies
        
        # 敏感字段，需要在输出中排除
        self.sensitive_fields = {'SPC_EC', 'SPC_ST'}

    def extract_info_from_username(self, username: str) -> Dict:
        """从用户名中提取环境和地区信息"""
        if not username:
            return {'env': None, 'country': None}

        username_lower = username.lower()
        extracted_env = None
        extracted_country = None

        # 环境关键词匹配（按优先级排序）
        env_patterns = [
            ('live', 'LIVE'),
            ('staging', 'STAGING'),
            ('stg', 'STAGING'),
            ('uat', 'UAT'),
            ('test', 'TEST')
        ]

        # 地区关键词匹配
        country_patterns = [
            ('sg', 'SG'), ('singapore', 'SG'),
            ('th', 'TH'), ('thailand', 'TH'),
            ('id', 'ID'), ('indonesia', 'ID'),
            ('ph', 'PH'), ('philippines', 'PH'),
            ('my', 'MY'), ('malaysia', 'MY'),
            ('vn', 'VN'), ('vietnam', 'VN'),
            ('tw', 'TW'), ('taiwan', 'TW'),
            ('br', 'BR'), ('brazil', 'BR'),
            ('mx', 'MX'), ('mexico', 'MX'),
            ('pl', 'PL'), ('poland', 'PL'),
            ('co', 'CO'), ('colombia', 'CO'),
            ('cl', 'CL'), ('chile', 'CL'),
            ('ar', 'AR'), ('argentina', 'AR')
        ]

        # 提取环境信息
        for pattern, env_code in env_patterns:
            if pattern in username_lower:
                extracted_env = env_code
                break

        # 提取地区信息
        for pattern, country_code in country_patterns:
            if pattern in username_lower:
                extracted_country = country_code
                break

        ic(f"🧠 从用户名 '{username}' 提取信息 - env: {extracted_env}, country: {extracted_country}")

        return {
            'env': extracted_env,
            'country': extracted_country
        }

    async def query_admin_user_list(self, username: str, region: str = "ID") -> Dict:
        """
        查询用户信息（接口3：admin.user.test.shopee.io）
        只能查询TEST环境的账户

        Args:
            username: 用户名（必填）
            region: 地区（必填）

        Returns:
            查询结果
        """
        try:
            url = "https://admin.user.test.shopee.io/api/gateway/v1/user/management/list"

            # 构建请求数据
            data = {
                "username": username,
                "region": region,
                "operator": "<EMAIL>"
            }

            # 设置地区相关的headers
            headers = self.admin_headers.copy()
            headers['country'] = region
            headers['region'] = region

            ic(f"🔍 查询管理员用户列表 - URL: {url}, 数据: {data}")

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(url, json=data, headers=headers) as response:
                    response_text = await response.text()
                    ic(f"📡 管理员用户列表API响应状态: {response.status}")
                    ic(f"📡 管理员用户列表API响应内容: {response_text[:500]}...")

                    if response.status == 200:
                        try:
                            data = json.loads(response_text)
                            ic(f"✅ 管理员用户列表查询成功")
                            return {
                                'success': True,
                                'data': data,
                                'source': 'admin_user_list_api'
                            }
                        except Exception as json_error:
                            ic(f"❌ 管理员用户列表JSON解析失败: {str(json_error)}")
                            return {
                                'success': False,
                                'error': f"JSON解析失败: {str(json_error)}",
                                'source': 'admin_user_list_api'
                            }
                    else:
                        ic(f"❌ 管理员用户列表查询失败: {response.status} - {response_text}")
                        return {
                            'success': False,
                            'error': f"HTTP {response.status}: {response_text}",
                            'source': 'admin_user_list_api'
                        }

        except Exception as e:
            ic(f"❌ 管理员用户列表查询异常: {str(e)}")
            return {
                'success': False,
                'error': f"查询异常: {str(e)}",
                'source': 'admin_user_list_api'
            }

    async def query_admin_phone_reveal(self, userid: Union[str, int], region: str = "ID") -> Dict:
        """
        查询完整手机号（接口4：admin.user.test.shopee.io）
        只能查询TEST环境的账户

        Args:
            userid: 用户ID（必填）
            region: 地区（必填）

        Returns:
            查询结果
        """
        try:
            url = "https://admin.user.test.shopee.io/api/gateway/v1/sensitive_info/reveal/phone"

            # 构建请求数据
            data = {
                "region": region,
                "operator": "<EMAIL>",
                "entity_id": str(userid),
                "entity_type": "user_detail",
                "field_list": ["phone"],
                "action": "View_Phone",
                "location": "UserDetailPage"
            }

            # 设置地区相关的headers
            headers = self.admin_headers.copy()
            headers['country'] = region
            headers['region'] = region
            headers['referer'] = f'https://admin.user.test.shopee.io/detail?userid={userid}'
            headers['x-sap-ri'] = '80bf9268a18b2a71a7d03e3201014c4c082affc1ea2f14143975'
            headers['x-sap-sec'] = 'u20wUp1nX+StW+St8iS8W+7t8iStW+7tW+SUW+Stj+otW+anW+eZW+St8+StW7ZR6Ui8W+Ste+StWKatW+EHu6uS7K3WCAne+IrEyN3lyQnTxVhAb4h5t/F6d6DI0Y280n0wetUY/ycj7VwG2KdFF0lSgThoK/QRr30QJwNMURfF86HHlBrhYIeFL7ZYsxXW6p0BOBHl6JKvIhLOoN7BM4dDE00WaWoQCrSxUAn80d8QEhDBV/ll7HNxQxWFepXt3sOdt+3y6RPDtfwJLCDvYH8b+VBtxCa3luUgLW3Qo/ODGUSVmtBs5aQg9+StWQ4/vQReUloBW+StW2Ud6Ui8W+StA+StWN7tW+HscF/rLx9dmc3/Z5tpTHcku8cJjk1tW+S7Ul7BUPo/GkStW+eF6Ugd9+StWNatW+SZW+StiIrDHX3czYjrARFCdR1ihR8W9Dk8W+StGI0/v8qBvl0tW+St9+SFW+1t8+S8W+St9+StWNatW+SZW+StyRvDrrO6R0M4l27T2pR2er1VhGi8W+StUQkMU847GP+tW+St'
            headers['x-shopee-language'] = 'en'

            ic(f"🔍 查询完整手机号 - URL: {url}, 数据: {data}")

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(url, json=data, headers=headers) as response:
                    response_text = await response.text()
                    ic(f"📡 手机号查询API响应状态: {response.status}")
                    ic(f"📡 手机号查询API响应内容: {response_text[:500]}...")

                    if response.status == 200:
                        try:
                            data = json.loads(response_text)
                            ic(f"✅ 手机号查询成功")
                            return {
                                'success': True,
                                'data': data,
                                'source': 'admin_phone_reveal_api'
                            }
                        except Exception as json_error:
                            ic(f"❌ 手机号查询JSON解析失败: {str(json_error)}")
                            return {
                                'success': False,
                                'error': f"JSON解析失败: {str(json_error)}",
                                'source': 'admin_phone_reveal_api'
                            }
                    else:
                        ic(f"❌ 手机号查询失败: {response.status} - {response_text}")
                        return {
                            'success': False,
                            'error': f"HTTP {response.status}: {response_text}",
                            'source': 'admin_phone_reveal_api'
                        }

        except Exception as e:
            ic(f"❌ 手机号查询异常: {str(e)}")
            return {
                'success': False,
                'error': f"查询异常: {str(e)}",
                'source': 'admin_phone_reveal_api'
            }

    def normalize_country(self, country: str) -> str:
        """标准化地区代码"""
        if not country:
            return "ID"  # 默认地区

        country_lower = country.lower().strip()
        return self.supported_countries.get(country_lower, country.upper())

    def normalize_environment(self, env: str) -> str:
        """标准化环境代码"""
        if not env:
            return "TEST"  # 默认环境

        env_lower = env.lower().strip()
        return self.supported_environments.get(env_lower, env.upper())

    def validate_parameters(self, country: str = None, env: str = None) -> Dict:
        """验证参数有效性"""
        errors = []

        if country:
            normalized_country = self.normalize_country(country)
            if normalized_country not in self.supported_countries.values():
                errors.append(f"不支持的地区: {country}，支持的地区: {', '.join(self.supported_countries.keys())}")

        if env:
            normalized_env = self.normalize_environment(env)
            if normalized_env not in self.supported_environments.values():
                errors.append(f"不支持的环境: {env}，支持的环境: {', '.join(self.supported_environments.keys())}")

        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'normalized_country': self.normalize_country(country) if country else None,
            'normalized_env': self.normalize_environment(env) if env else None
        }
    
    async def query_account_info(self, username: str = None, env: str = None, country: str = None) -> Dict:
        """
        查询账户信息（第一个接口）

        Args:
            username: 用户名（可选）
            env: 环境（可选）
            country: 地区（可选）

        Returns:
            查询结果
        """
        try:
            # 参数验证和标准化
            validation = self.validate_parameters(country, env)
            if not validation['valid']:
                return {
                    'success': False,
                    'error': '; '.join(validation['errors']),
                    'source': 'account_api'
                }

            url = f"{self.base_url}/account"
            params = {
                'page_num': 1,
                'page_size': 10
            }

            if username:
                params['username'] = username
            if env:
                params['env'] = validation['normalized_env'] or self.normalize_environment(env)
            if country:
                params['country'] = validation['normalized_country'] or self.normalize_country(country)
            
            ic(f"🔍 查询账户信息 - URL: {url}, 参数: {params}")
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(url, params=params, headers=self.headers) as response:
                    response_text = await response.text()
                    ic(f"📡 账户信息API响应状态: {response.status}")
                    ic(f"📡 账户信息API响应内容: {response_text[:500]}...")

                    if response.status == 200:
                        try:
                            data = json.loads(response_text)
                            record_count = 0
                            if isinstance(data, dict):
                                # 账户信息API的响应结构：{"result_list": [...], "page_num": 1, ...}
                                if 'result_list' in data:
                                    record_count = len(data.get('result_list', []))
                                elif 'data' in data:
                                    record_count = len(data.get('data', []))
                                elif 'list' in data:
                                    record_count = len(data.get('list', []))
                                else:
                                    # 可能整个响应就是数据列表
                                    record_count = len(data) if isinstance(data, list) else 1
                            elif isinstance(data, list):
                                record_count = len(data)

                            ic(f"✅ 账户信息查询成功: {record_count} 条记录")
                            return {
                                'success': True,
                                'data': data,
                                'source': 'account_api'
                            }
                        except Exception as json_error:
                            ic(f"❌ 账户信息JSON解析失败: {str(json_error)}")
                            return {
                                'success': False,
                                'error': f"JSON解析失败: {str(json_error)}",
                                'source': 'account_api'
                            }
                    else:
                        ic(f"❌ 账户信息查询失败: {response.status} - {response_text}")
                        return {
                            'success': False,
                            'error': f"HTTP {response.status}: {response_text}",
                            'source': 'account_api'
                        }
                        
        except Exception as e:
            ic(f"❌ 账户信息查询异常: {str(e)}")
            return {
                'success': False,
                'error': f"查询异常: {str(e)}",
                'source': 'account_api'
            }
    
    async def query_user_info(self, country: str, env: str, username: str = None, userid: Union[str, int] = None) -> Dict:
        """
        查询用户信息（第二个接口）

        Args:
            country: 地区（必填）
            env: 环境（必填）
            username: 用户名（与userid二选一）
            userid: 用户ID（与username二选一）

        Returns:
            查询结果
        """
        try:
            if not country or not env:
                return {
                    'success': False,
                    'error': '地区(country)和环境(env)为必填参数',
                    'source': 'user_info_api'
                }

            # 参数验证和标准化
            validation = self.validate_parameters(country, env)
            if not validation['valid']:
                return {
                    'success': False,
                    'error': '; '.join(validation['errors']),
                    'source': 'user_info_api'
                }

            if not username and not userid:
                return {
                    'success': False,
                    'error': 'username和userid必须提供其中一个',
                    'source': 'user_info_api'
                }

            url = f"{self.base_url}/user_info"
            params = {
                'country': validation['normalized_country'],
                'env': validation['normalized_env']
            }
            
            if username:
                params['username'] = username
            if userid:
                params['userid'] = str(userid)
            
            ic(f"🔍 查询用户信息 - URL: {url}, 参数: {params}")
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(url, params=params, headers=self.headers) as response:
                    response_text = await response.text()
                    ic(f"📡 用户信息API响应状态: {response.status}")
                    ic(f"📡 用户信息API响应内容: {response_text[:500]}...")

                    if response.status == 200:
                        try:
                            data = json.loads(response_text)
                            ic(f"✅ 用户信息查询成功")
                            return {
                                'success': True,
                                'data': data,
                                'source': 'user_info_api'
                            }
                        except Exception as json_error:
                            ic(f"❌ 用户信息JSON解析失败: {str(json_error)}")
                            return {
                                'success': False,
                                'error': f"JSON解析失败: {str(json_error)}",
                                'source': 'user_info_api'
                            }
                    else:
                        ic(f"❌ 用户信息查询失败: {response.status} - {response_text}")
                        return {
                            'success': False,
                            'error': f"HTTP {response.status}: {response_text}",
                            'source': 'user_info_api'
                        }
                        
        except Exception as e:
            ic(f"❌ 用户信息查询异常: {str(e)}")
            return {
                'success': False,
                'error': f"查询异常: {str(e)}",
                'source': 'user_info_api'
            }
    
    def _clean_sensitive_data(self, data: Dict) -> Dict:
        """清理敏感数据"""
        if not isinstance(data, dict):
            return data
        
        cleaned = {}
        for key, value in data.items():
            if key not in self.sensitive_fields:
                if isinstance(value, dict):
                    cleaned[key] = self._clean_sensitive_data(value)
                elif isinstance(value, list):
                    cleaned[key] = [self._clean_sensitive_data(item) if isinstance(item, dict) else item for item in value]
                else:
                    cleaned[key] = value
        
        return cleaned
    
    def _merge_user_data(self, account_data: Dict, user_info_data: Dict) -> List[Dict]:
        """
        合并两个接口的用户数据
        
        Args:
            account_data: 账户信息接口数据
            user_info_data: 用户信息接口数据
            
        Returns:
            合并后的用户数据列表
        """
        merged_users = []
        
        # 处理账户信息数据
        account_users = []
        if account_data.get('success') and account_data.get('data'):
            data = account_data['data']
            if isinstance(data, dict):
                # 账户信息API响应结构：{"result_list": [...], ...}
                if 'result_list' in data:
                    account_users = data['result_list']
                elif 'data' in data:
                    account_users = data['data']
            elif isinstance(data, list):
                account_users = data

        # 处理用户信息数据
        user_info_users = []
        if user_info_data.get('success') and user_info_data.get('data'):
            data = user_info_data['data']
            if isinstance(data, dict):
                # 用户信息API响应结构：{"msg": "success", "result": [...]}
                if 'result' in data:
                    result = data['result']
                    if isinstance(result, list):
                        user_info_users = result
                    else:
                        user_info_users = [result]  # 单个用户信息包装成列表
                elif 'data' in data:
                    user_info_users = data['data'] if isinstance(data['data'], list) else [data['data']]
                else:
                    user_info_users = [data]  # 整个data就是用户信息
            elif isinstance(data, list):
                user_info_users = data
        
        # 创建用户映射（以username和userid为键）
        user_map = {}
        
        # 先添加账户信息
        for user in account_users:
            if isinstance(user, dict):
                cleaned_user = self._clean_sensitive_data(user)
                username = cleaned_user.get('username')
                userid = cleaned_user.get('userid')
                
                key = username or str(userid) if userid else None
                if key:
                    user_map[key] = cleaned_user
        
        # 合并用户信息数据（优先级更高）
        for user in user_info_users:
            if isinstance(user, dict):
                cleaned_user = self._clean_sensitive_data(user)
                username = cleaned_user.get('username')
                userid = cleaned_user.get('userid')
                
                key = username or str(userid) if userid else None
                if key:
                    if key in user_map:
                        # 合并数据，用户信息接口的数据优先
                        user_map[key].update(cleaned_user)
                    else:
                        user_map[key] = cleaned_user
        
        # 转换为列表并限制前3条
        merged_users = list(user_map.values())[:3]
        
        return merged_users


    async def query_user_comprehensive(self, username: str = None, userid: Union[str, int] = None,
                                      env: str = "TEST", country: str = "ID", smart_search: bool = True) -> Dict:
        """
        综合查询用户信息（调用两个接口并合并数据）

        Args:
            username: 用户名
            userid: 用户ID
            env: 环境，默认TEST
            country: 地区，默认ID
            smart_search: 是否启用智能搜索（先用第一个接口查找真实的环境和地区）

        Returns:
            合并后的查询结果
        """
        try:
            ic(f"🔍 开始综合查询用户信息 - username: {username}, userid: {userid}, env: {env}, country: {country}, smart_search: {smart_search}")

            # 智能搜索模式：当只提供用户名且使用默认参数时，先用第一个接口探测
            if smart_search and username and not userid and env == "TEST" and country == "ID":
                ic(f"🧠 启用智能搜索模式 - 先用账户信息接口探测用户: {username}")

                # 尝试从用户名中提取环境和地区信息
                extracted_info = self.extract_info_from_username(username)
                suggested_env = extracted_info.get('env')
                suggested_country = extracted_info.get('country')

                # 如果从用户名中提取到了信息，先尝试精确查询
                if suggested_env or suggested_country:
                    ic(f"🎯 从用户名提取到信息，尝试精确查询 - env: {suggested_env}, country: {suggested_country}")

                    # 使用提取的信息进行查询
                    test_env = suggested_env or env
                    test_country = suggested_country or country

                    # 并发查询两个接口（使用提取的信息）
                    ic(f"🔄 并发查询接口1和2 - env: {test_env}, country: {test_country}")
                    tasks = []
                    tasks.append(self.query_account_info(username=username, env=test_env, country=test_country))
                    tasks.append(self.query_user_info(country=test_country, env=test_env, username=username))

                    # 等待两个接口都完成
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    account_result = results[0] if len(results) > 0 else {'success': False, 'error': '账户信息查询失败'}
                    user_info_result = results[1] if len(results) > 1 and not isinstance(results[1], Exception) else {'success': False, 'error': '用户信息查询失败'}

                    # 合并数据
                    merged_users = self._merge_user_data(account_result, user_info_result)

                    if merged_users:
                        # 找到用户，返回结果
                        return {
                            'success': True,
                            'users': merged_users,
                            'total_count': len(merged_users),
                            'smart_search_used': True,
                            'username_extraction_used': True,
                            'detected_env': test_env,
                            'detected_country': test_country,
                            'api_results': {
                                'account_api': account_result,
                                'user_info_api': user_info_result
                            }
                        }
                    else:
                        ic(f"⚠️ 使用提取信息的精确查询未找到用户，继续宽泛查询")

                # 第一步：用第一个接口进行宽泛查询（不指定环境和地区）
                ic(f"🔍 进行宽泛查询（所有环境和地区）")
                account_result = await self.query_account_info(username=username)

                if account_result['success'] and account_result.get('data'):
                    # 检查是否找到了用户
                    data = account_result['data']
                    users = []
                    if isinstance(data, dict) and 'result_list' in data:
                        users = data['result_list']

                    if users:
                        # 找到用户，提取第一个用户的真实环境和地区信息
                        first_user = users[0]
                        real_env = first_user.get('env', env)
                        real_country = first_user.get('country', country)

                        ic(f"🎯 智能搜索发现用户真实信息 - env: {real_env}, country: {real_country}")

                        # 第二步：用真实的环境和地区查询第二个接口
                        user_info_result = await self.query_user_info(
                            country=real_country,
                            env=real_env,
                            username=username
                        )

                        # 合并数据
                        merged_users = self._merge_user_data(account_result, user_info_result)

                        return {
                            'success': True,
                            'users': merged_users,
                            'total_count': len(merged_users),
                            'smart_search_used': True,
                            'detected_env': real_env,
                            'detected_country': real_country,
                            'api_results': {
                                'account_api': account_result,
                                'user_info_api': user_info_result
                            }
                        }
                    else:
                        # 第一个接口没找到用户，尝试接口3和4（仅TEST环境）
                        ic(f"❌ 智能搜索未找到用户: {username}，尝试管理员接口")

                        # 尝试接口3：轮询ID和SG地区
                        admin_result = None
                        admin_phone_result = None
                        test_regions = ['ID', 'SG']

                        for region in test_regions:
                            ic(f"🔍 尝试管理员接口查询 - region: {region}")
                            admin_result = await self.query_admin_user_list(username, region)

                            if admin_result['success'] and admin_result.get('data'):
                                data = admin_result['data']
                                if isinstance(data, dict) and 'data' in data and 'user_list' in data['data']:
                                    user_list = data['data']['user_list']
                                    if user_list:
                                        # 找到用户，尝试获取完整手机号
                                        first_user = user_list[0]
                                        userid = first_user.get('userid')

                                        if userid:
                                            ic(f"🎯 在管理员接口找到用户，尝试获取完整手机号 - userid: {userid}")
                                            admin_phone_result = await self.query_admin_phone_reveal(userid, region)

                                            # 合并手机号信息
                                            if admin_phone_result['success'] and admin_phone_result.get('data'):
                                                phone_data = admin_phone_result['data']
                                                if isinstance(phone_data, dict):
                                                    full_phone = None

                                                    # 接口4的实际响应结构是 {"data": {"value_list": [{"name": "phone", "value": "00123334234234"}]}}
                                                    if 'data' in phone_data and isinstance(phone_data['data'], dict):
                                                        data_section = phone_data['data']
                                                        if 'value_list' in data_section and isinstance(data_section['value_list'], list):
                                                            for item in data_section['value_list']:
                                                                if isinstance(item, dict) and item.get('name') == 'phone':
                                                                    full_phone = item.get('value')
                                                                    break
                                                        else:
                                                            # 备用：直接从data中获取phone
                                                            full_phone = data_section.get('phone')
                                                    else:
                                                        # 备用：直接从根级获取phone
                                                        full_phone = phone_data.get('phone')

                                                    if full_phone:
                                                        first_user['full_phone'] = full_phone
                                                        ic(f"✅ 成功获取完整手机号: {full_phone}")
                                                    else:
                                                        ic(f"⚠️ 手机号数据为空: {phone_data}")
                                                else:
                                                    ic(f"⚠️ 手机号响应格式异常: {phone_data}")
                                            else:
                                                ic(f"⚠️ 手机号查询失败: {admin_phone_result}")

                                        # 返回找到的用户信息
                                        return {
                                            'success': True,
                                            'users': user_list,
                                            'total_count': len(user_list),
                                            'smart_search_used': True,
                                            'admin_api_used': True,
                                            'detected_env': 'TEST',
                                            'detected_country': region,
                                            'api_results': {
                                                'account_api': account_result,
                                                'user_info_api': {'success': False, 'error': '账户信息查询未找到用户，跳过用户信息查询'},
                                                'admin_user_list_api': admin_result,
                                                'admin_phone_reveal_api': admin_phone_result
                                            }
                                        }

                        # 所有接口都没找到用户，返回友好提示
                        ic(f"❌ 所有接口都未找到用户: {username}")
                        return {
                            'success': True,
                            'users': [],
                            'total_count': 0,
                            'smart_search_used': True,
                            'admin_api_used': True,
                            'not_found_suggestion': True,
                            'api_results': {
                                'account_api': account_result,
                                'user_info_api': {'success': False, 'error': '账户信息查询未找到用户，跳过用户信息查询'},
                                'admin_user_list_api': admin_result,
                                'admin_phone_reveal_api': admin_phone_result
                            }
                        }
                else:
                    # 第一个接口查询失败，降级到普通模式
                    ic(f"⚠️ 智能搜索的账户信息查询失败，降级到普通模式")

            # 普通模式：并发调用两个接口
            ic(f"🔄 使用普通查询模式")
            tasks = []

            # 第一个接口：账户信息查询
            tasks.append(self.query_account_info(username=username, env=env, country=country))

            # 第二个接口：用户信息查询
            if username or userid:
                tasks.append(self.query_user_info(country=country, env=env, username=username, userid=userid))
            else:
                # 如果没有提供username或userid，第二个接口无法调用
                tasks.append(asyncio.create_task(asyncio.sleep(0)))  # 占位任务

            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)

            account_result = results[0] if len(results) > 0 else {'success': False, 'error': '账户信息查询失败'}
            user_info_result = results[1] if len(results) > 1 and not isinstance(results[1], Exception) else {'success': False, 'error': '用户信息查询失败'}

            # 合并数据
            merged_users = self._merge_user_data(account_result, user_info_result)

            # 构建响应
            response = {
                'success': True,
                'users': merged_users,
                'total_count': len(merged_users),
                'smart_search_used': False,
                'api_results': {
                    'account_api': account_result,
                    'user_info_api': user_info_result
                }
            }

            ic(f"✅ 综合查询完成，找到 {len(merged_users)} 个用户")
            return response

        except Exception as e:
            ic(f"❌ 综合查询异常: {str(e)}")
            return {
                'success': False,
                'error': f"综合查询异常: {str(e)}",
                'users': []
            }

    def format_user_info(self, users: List[Dict], max_users: int = 3, query_result: Dict = None) -> str:
        """
        格式化用户信息为可读文本

        Args:
            users: 用户信息列表
            max_users: 最大显示用户数
            query_result: 查询结果对象，包含额外信息

        Returns:
            格式化后的文本
        """
        # 检查是否是智能搜索未找到的情况
        if not users and query_result and query_result.get('not_found_suggestion'):
            return """❌ 简略查询未找到相关用户信息

🔍 **建议使用完整指令查询：**
如果用户可能在其他地区或环境，请使用包含地区和环境信息的完整指令：

📝 **完整指令示例：**
• `user username banana_goood sg live` - 查询SG地区LIVE环境的用户
• `user username banana_goood th test` - 查询TH地区TEST环境的用户
• `查询用户banana_goood在SG环境的信息` - 自然语言查询

🌍 **支持的地区：** id, tw, sg, th, ph, my, vn, br, mx, pl, co, cl, ar
⚙️ **支持的环境：** test, uat, staging, live

💡 **提示：** 智能搜索已在所有地区和环境中查找，包括管理员接口（仅TEST环境的ID和SG地区），但未找到该用户。

🔗 **在线查询：** 您也可以访问 https://mkt-admin.test.shopee.sg/mktqa/account/login_msg 进行手动查询。

📝 **其他地区查询：** 如需查询其他地区的TEST环境用户，请明确指定地区，如：`user username test_user th test`"""

        if not users:
            return "❌ 未找到匹配的用户信息"

        # 限制显示数量
        display_users = users[:max_users]

        result_lines = []

        # 简化显示，去掉冗余信息
        # 不再显示查询成功的提示和用户数量统计

        for i, user in enumerate(display_users, 1):
            result_lines.append(f"👤 用户 {i}:")
            result_lines.append(f"   用户名: {user.get('username', 'N/A')}")

            # 密码信息（如果存在）
            if user.get('password') and user.get('password') != 'N/A':
                result_lines.append(f"   密码: {user.get('password')}")

            result_lines.append(f"   用户ID: {user.get('userid', 'N/A')}")

            # 店铺信息（如果存在且不为N/A）
            shopid = user.get('shopid', user.get('shop_id', 'N/A'))
            if shopid != 'N/A':
                result_lines.append(f"   店铺ID: {shopid}")

            # 手机号处理（优先显示完整手机号）
            phone = user.get('full_phone', user.get('phone', 'N/A'))
            result_lines.append(f"   手机号: {phone}")

            # 邮箱处理
            email = user.get('email', 'N/A')
            result_lines.append(f"   邮箱: {email}")

            # 环境信息（优先使用查询结果中的检测信息）
            env_info = user.get('env', 'N/A')
            country_info = user.get('country', 'N/A')

            # 如果用户数据中没有环境和地区信息，使用查询结果中的检测信息
            if query_result:
                if env_info == 'N/A' and query_result.get('detected_env'):
                    env_info = query_result.get('detected_env')
                if country_info == 'N/A' and query_result.get('detected_country'):
                    country_info = query_result.get('detected_country')

            result_lines.append(f"   环境: {env_info}")
            result_lines.append(f"   地区: {country_info}")
            


            # 显示其他重要字段（简化显示，去掉时间戳）
            important_fields = ['shopname', 'email_verified', 'phone_verified', 'is_seller']

            for field in important_fields:
                value = user.get(field)
                if value is not None and value != 'N/A':
                    if field == 'email_verified':
                        result_lines.append(f"   邮箱验证: {'是' if value else '否'}")
                    elif field == 'phone_verified':
                        result_lines.append(f"   手机验证: {'是' if value else '否'}")
                    elif field == 'is_seller':
                        result_lines.append(f"   是否卖家: {'是' if value else '否'}")
                    elif field == 'shopname' and value != user.get('username'):
                        result_lines.append(f"   店铺名: {value}")

            result_lines.append("")

        if len(users) > max_users:
            result_lines.append(f"... 还有 {len(users) - max_users} 个用户未显示")

        return "\n".join(result_lines)

    def get_help_message(self) -> str:
        """获取用户信息查询帮助信息"""
        countries_list = ', '.join(self.supported_countries.keys())
        environments_list = ', '.join(self.supported_environments.keys())

        return f"""📖 用户信息查询帮助

🔍 支持的查询格式：
• `user username <用户名>` - 按用户名查询
• `user id <用户ID>` - 按用户ID查询
• `user <用户名或ID>` - 自动识别类型查询

📝 示例：
• `user username qiqi.49`
• `user id 5956919445`
• `user test`

🌍 支持的地区：
{countries_list}

⚙️ 支持的环境：
{environments_list}

🔧 默认参数：
• 环境：TEST
• 地区：ID

💡 说明：
• 最多显示前3条结果
• 自动合并两个数据源的信息
• 敏感信息已自动过滤

❓ 如需指定环境和地区，请使用自然语言描述，如：
• "查询用户qiqi.49在SG环境的信息"
• "查看用户test在live环境TH地区的数据"
• "user qiqi.49 staging sg"

🔧 调试信息：
• 如果查询返回空结果，可能是：
  1. 用户不存在于指定环境/地区
  2. API权限问题
  3. 参数格式不正确
• 可以尝试不同的环境和地区组合
• 建议先测试已知存在的用户数据

🔗 **在线查询：** 您也可以访问 https://mkt-admin.test.shopee.sg/mktqa/account/login_msg 进行手动查询。
"""


# 全局实例
user_info_client = UserInfoClient()
