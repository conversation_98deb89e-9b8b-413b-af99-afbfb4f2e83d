"""
AI 助手核心模块 - 新版本
支持5大类意图分类和意图澄清机制
"""

import asyncio
import json
import re
import logging
from typing import Dict, List, Optional, Tuple
from django.utils import timezone
from icecream import ic
import time

from app01.bot_config import BotConfig
from .llm_client import llm_client
from .multi_llm_client import multi_llm_client
from .smart_jira_query import SmartJiraQuery, JiraTokenManager
from .conversation_manager import conversation_manager
from .typing_status import typing_manager
from .private_chat import private_chat_client
from .jira_statistics import JiraStatistics
from .prompts.intent_detection import (
    IntentDetectionPrompts, IntentClarification, IntentRanker, GroupInfoExtractor
)
from .prompts.jql_generation import JQLGenerationPrompts
from .prompts.response_formatting import ResponseFormattingPrompts
from .document_integration import document_integration
from .prompts.subtask_creation import SubtaskCreationPrompts

logger = logging.getLogger(__name__)


class AIAssistant:
    """AI 助手主类 - 新版本"""
    
    def __init__(self):
        self.max_response_time = 15  # 最大响应时间(秒)
        self.intent_clarification = IntentClarification()
        self.intent_ranker = IntentRanker()
        self.group_extractor = GroupInfoExtractor()
        
    async def process_query(self, user_query: str, user_id: str, 
                           group_id: str = None, employee_code: str = None,
                           user_email: str = None, group_title: str = None) -> Dict:
        """
        处理用户查询的主流程 - 新版本
        
        Args:
            user_query: 用户查询内容
            user_id: 用户ID
            group_id: 群组ID (群聊时提供)
            employee_code: 用户employee_code (私聊时提供)
            user_email: 用户邮箱 (用于权限验证)
            group_title: 群标题 (用于提取JIRA信息)
            
        Returns:
            处理结果
        """
        start_time = timezone.now()
        
        try:
            ic(f"🤖 AI查询开始 - 用户: {user_id}, 查询: '{user_query}', 群组: {group_id}")
            
            # 1. 获取或创建会话
            session_result = await conversation_manager.get_or_create_session(
                user_id, group_id, employee_code
            )
            
            if not session_result['success']:
                ic(f"❌ 会话管理失败: {session_result['error']}")
                return {
                    'success': False,
                    'error': f"会话管理失败: {session_result['error']}"
                }
            
            session_info = session_result['session']
            session_id = session_info['session_id']
            context = session_info['context']
            ic(f"📝 会话上下文: {context}")
            
            # 2. 处理特殊指令
            special_result = await self._handle_special_commands(
                user_query, user_id, user_email, session_id
            )
            if special_result:
                ic(f"✅ 特殊命令处理完成: {special_result.get('intent', 'unknown')}")
                return special_result
            
            # 3. 提取群组信息
            group_info = {}
            if group_title:
                group_info = self.group_extractor.extract_from_group_title(group_title)
                # 添加群组ID到group_info中
                if group_id:
                    group_info['group_id'] = group_id
                ic(f"🏷️ 群组信息: {group_info}")
            
            # 4. 意图识别和澄清处理
            intent_result = await self._detect_intent(user_query, context, group_info, session_id)
            
            if not intent_result['success']:
                ic(f"❌ 意图识别失败: {intent_result['error']}")
                return intent_result
            
            # 如果需要澄清，返回澄清问题
            if intent_result.get('needs_clarification'):
                ic(f"❓ 需要澄清: {intent_result['clarification']}")
                return {
                    'success': True,
                    'needs_clarification': True,
                    'clarification': intent_result['clarification'],
                    'intent': 'clarification_request'
                }
            
            # 检查是否为自然语言任务创建的澄清回复
            if context.get('natural_task_creation_state'):
                from .natural_task_creator import natural_task_creator
                ic("🔄 检测到自然语言任务创建的澄清回复")
                return await natural_task_creator.process_clarification_response(
                    user_query, session_id, user_id, user_email
                )
            
            intent_data = intent_result['intent_data']
            intent = intent_data['intent']
            extracted_info = intent_data['extracted_info']
            ic(f"🎯 意图识别结果: {intent}, 提取信息: {extracted_info}")
            
            # 5. 根据意图类型执行相应处理
            processing_result = await self._process_by_intent(
                intent, extracted_info, user_query, user_email, group_info, user_id
            )
            
            if not processing_result['success']:
                ic(f"❌ 意图处理失败: {processing_result['error']}")
                return processing_result
            
            # 5.5. 在双模型模式下生成对比报告
            if (multi_llm_client.ab_test_enabled and multi_llm_client.dual_model_comparison and 
                intent in ['jira_query', 'jira_statistics']):
                # 检查是否有对比数据
                has_comparison_data = (
                    intent_result.get('intent_data', {}).get('_comparison_details') or
                    processing_result.get('extra_data', {}).get('_comparison_details')
                )
                
                if has_comparison_data:
                    ic("🔄 生成双模型对比报告")
                    # 构造JQL结果数据（如果有的话）
                    jql_result = {'jql_data': {}}
                    if processing_result.get('extra_data', {}).get('_comparison_details'):
                        jql_result['jql_data']['_comparison_details'] = processing_result['extra_data']['_comparison_details']
                        jql_result['jql_data']['_primary_model'] = processing_result['extra_data'].get('_primary_model')
                    
                    # 生成对比报告
                    comparison_report = await self._generate_comparison_report(
                        intent_result, jql_result, processing_result.get('response', ''), user_query
                    )
                    
                    # 更新响应内容
                    processing_result['response'] = comparison_report
                    ic("✅ 双模型对比报告生成完成")

            # 6. 更新会话上下文
            await self._update_session_context(
                session_id, user_query, intent, extracted_info, processing_result
            )
            
            # 7. 保存查询历史
            await self._save_query_history(
                user_id, group_id, employee_code, user_query, 
                intent, processing_result
            )
            
            processing_time = (timezone.now() - start_time).total_seconds()
            
            # 检查是否需要澄清
            if processing_result.get('needs_clarification'):
                final_result = {
                    'success': True,
                    'needs_clarification': True,
                    'clarification': processing_result.get('clarification', {}),
                    'intent': intent,
                    'processing_time': processing_time
                }
            else:
                final_result = {
                    'success': True,
                    'response': processing_result.get('response', '处理完成，但没有返回内容'),
                    'intent': intent,
                    'processing_time': processing_time,
                    **processing_result.get('extra_data', {})
                }
            
            ic(f"🎉 AI查询完成 - 意图: {intent}, 处理时间: {processing_time:.2f}s")
            return final_result
            
        except Exception as e:
            logger.error(f"AI查询处理异常: {str(e)}")
            processing_time = (timezone.now() - start_time).total_seconds()
            return {
                'success': False,
                'error': f"处理异常: {str(e)}",
                'processing_time': processing_time
            }
    
    async def _handle_special_commands(self, user_query: str, user_id: str,
                                      user_email: str, session_id: str) -> Optional[Dict]:
        """处理特殊指令"""
        query_lower = user_query.lower().strip()
        
        # 处理 /ai 前缀的命令
        if query_lower.startswith('/ai '):
            query_lower = query_lower[4:].strip()  # 去掉 '/ai ' 前缀
            user_query = user_query[4:].strip()  # 同时更新原始查询
            ic(f"🔧 去除/ai前缀后的查询: '{user_query}'")
        
        # ==================== 直接对话模式 ====================
        # 支持 chat 标记进行直接对话，跳过意图识别
        if query_lower.startswith('chat '):
            return await self._handle_direct_chat_command(user_query, user_id, session_id)
        
        # ==================== 权限查询功能 ====================
        # 支持permission命令查看用户权限
        if query_lower.startswith('permission ') or query_lower == 'permission':
            return await self._handle_permission_command(user_query, user_id, user_email)
        
        # ==================== Todo管理功能 ====================
        # 支持todo命令进行待办事项管理
        if query_lower.startswith('todo '):
            return await self._handle_todo_command(user_query, user_id, user_email)
        
        # ==================== 服务统计功能 ====================
        # 支持usage/stats命令查看服务使用统计
        if query_lower.startswith('usage ') or query_lower == 'usage':
            return await self._handle_usage_command(user_query, user_id, user_email)
        
        # 帮助指令 - 使用LLM生成个性化回复
        if (query_lower in ['help', '帮助', 'h', '?'] or 
            '你好' in query_lower or 
            '能做什么' in query_lower or
            '怎么用' in query_lower or
            'hello' in query_lower):
            return await self._generate_personalized_help(user_query)
        
        # 清除上下文
        if query_lower in ['clear', '清除上下文', 'reset']:
            clear_result = await conversation_manager.clear_context(session_id)
            if clear_result['success']:
                return {
                    'success': True,
                    'response': "会话上下文已清除，我们可以开始新的对话。",
                    'intent': 'clear_context'
                }
        
        # 清除聊天历史
        if query_lower in ['clear chat', '清除聊天', 'reset chat']:
            clear_result = await conversation_manager.clear_chat_history(session_id)
            if clear_result['success']:
                return {
                    'success': True,
                    'response': "💬 聊天历史已清除，直接对话模式重新开始。",
                    'intent': 'clear_chat_history'
                }
        
        # 配置管理
        if query_lower.startswith('config '):
            return await self._handle_config_command(user_query, user_email)
        
        # 统计查询
        if query_lower in ['stats', '统计', 'statistics']:
            return await self._handle_stats_command(user_id)
        
        # 字段查询和管理
        if query_lower.startswith('fields ') or query_lower.startswith('字段 '):
            return await self._handle_fields_command(user_query)
        
        # 定时任务管理
        if query_lower.startswith('schedule ') or query_lower.startswith('定时 '):
            return await self._handle_schedule_command(user_query, user_id, user_email, None, None)
        
        # 高级任务功能
        if query_lower.startswith('advanced ') or query_lower.startswith('高级 '):
            return await self._handle_advanced_command(user_query, user_id, user_email)
        
        # AB测试管理命令
        if query_lower.startswith('abtest ') or query_lower == 'abtest':
            return await self._handle_abtest_command(user_query, user_id, user_email)

        # 用户信息查询命令
        if query_lower.startswith('user ') or query_lower == 'user':
            return await self._handle_user_info_command(user_query, user_id, user_email)

        # 文档处理功能
        if (query_lower.startswith('doc ') or query_lower == 'doc' or
            '文档' in query_lower or 'confluence' in query_lower or
            any(keyword in query_lower for keyword in ['总结', '翻译', '分析', '提取信息'])):
            doc_intent = document_integration.detect_document_intent(user_query)
            if doc_intent['has_document_intent'] and doc_intent['confidence'] > 0.3:
                doc_result = await document_integration.process_document_query(
                    user_query=user_query
                )
                if doc_result['success']:
                    return {
                        'success': True,
                        'response': doc_result['response'],
                        'intent': 'document_processing',
                        **doc_result.get('metadata', {})
                    }
        
        # 文档处理帮助
        if query_lower in ['doc help', 'document help', '文档帮助']:
            return {
                'success': True,
                'response': document_integration.get_help_message(),
                'intent': 'document_help'
            }
        
        return None

    async def _handle_user_info_command(self, user_query: str, user_id: str, user_email: str) -> Dict:
        """处理用户信息查询命令"""
        try:
            from .user_info_client import user_info_client

            ic(f"🔍 处理用户信息查询命令: {user_query}")

            # 解析命令
            query_lower = user_query.lower().strip()

            # 移除前缀
            if query_lower.startswith('user '):
                params = user_query[5:].strip()
            else:
                params = ""

            # 如果没有参数，返回帮助信息
            if not params:
                return {
                    'success': True,
                    'response': user_info_client.get_help_message(),
                    'intent': 'user_info_help'
                }

            # 特殊调试命令
            if params.lower() == 'debug':
                return {
                    'success': True,
                    'response': """🔧 用户信息查询调试模式

当前配置：
• API基础URL: https://mkt-admin.test.shopee.sg/mkt/admin/qaservice/api/v1
• 默认环境: TEST
• 默认地区: ID
• 超时时间: 30秒

支持的地区: id, tw, sg, th, ph, my, vn, br, mx, pl, co, cl, ar
支持的环境: test, uat, staging, live

调试建议：
1. 检查API是否可访问
2. 确认用户数据在指定环境/地区存在
3. 验证cookie和认证信息是否有效
4. 尝试不同的参数组合

使用 'user test qiqi.49' 进行测试查询""",
                    'intent': 'user_info_debug'
                }

            # 解析参数
            username = None
            userid = None
            env = "TEST"  # 默认环境
            country = "ID"  # 默认地区

            # 解析不同的命令格式
            parts = params.split()
            ic(f"🔍 解析命令参数: {parts}")

            # 预定义的环境和地区关键词
            env_keywords = {'test': 'TEST', 'uat': 'UAT', 'staging': 'STAGING', 'live': 'LIVE'}
            country_keywords = {
                'id': 'ID', 'tw': 'TW', 'sg': 'SG', 'th': 'TH', 'ph': 'PH', 'my': 'MY',
                'vn': 'VN', 'br': 'BR', 'mx': 'MX', 'pl': 'PL', 'co': 'CO', 'cl': 'CL', 'ar': 'AR'
            }

            if len(parts) == 0:
                return {
                    'success': False,
                    'response': f"❌ 参数格式错误\n\n{user_info_client.get_help_message()}",
                    'intent': 'user_info_error'
                }
            elif len(parts) == 1:
                # 只有一个参数，自动识别类型
                param_value = parts[0]
                try:
                    userid = int(param_value)
                except ValueError:
                    username = param_value
            elif len(parts) == 2:
                # 两个参数的情况
                if parts[0].lower() in ['username', 'name']:
                    # user username qiqi.49
                    username = parts[1]
                elif parts[0].lower() in ['userid', 'id']:
                    # user id 123456
                    try:
                        userid = int(parts[1])
                    except ValueError:
                        # user id test_user (当作用户名处理)
                        username = parts[1]
                else:
                    # 可能是环境/地区 + 用户名的组合
                    # 检查第一个参数是否是环境或地区
                    first_lower = parts[0].lower()
                    if first_lower in env_keywords:
                        env = env_keywords[first_lower]
                        username = parts[1]
                    elif first_lower in country_keywords:
                        country = country_keywords[first_lower]
                        username = parts[1]
                    else:
                        # 默认第一个是用户名
                        username = parts[0]
                        # 检查第二个参数是否是环境或地区
                        second_lower = parts[1].lower()
                        if second_lower in env_keywords:
                            env = env_keywords[second_lower]
                        elif second_lower in country_keywords:
                            country = country_keywords[second_lower]
            elif len(parts) >= 3:
                # 三个或更多参数的情况
                # 可能的格式：
                # 1. user live sg banana_goood
                # 2. user username test_user id test
                # 3. user waikit_test_id4 id test

                if parts[0].lower() in ['username', 'name']:
                    # user username test_user [env] [country]
                    username = parts[1]
                    # 解析后续的环境和地区参数
                    for part in parts[2:]:
                        part_lower = part.lower()
                        if part_lower in env_keywords:
                            env = env_keywords[part_lower]
                        elif part_lower in country_keywords:
                            country = country_keywords[part_lower]
                elif parts[0].lower() in ['userid', 'id']:
                    # user id 123456 [env] [country]
                    try:
                        userid = int(parts[1])
                    except ValueError:
                        username = parts[1]
                    # 解析后续的环境和地区参数
                    for part in parts[2:]:
                        part_lower = part.lower()
                        if part_lower in env_keywords:
                            env = env_keywords[part_lower]
                        elif part_lower in country_keywords:
                            country = country_keywords[part_lower]
                else:
                    # 智能解析：环境 地区 用户名 或 用户名 环境 地区
                    remaining_parts = parts[:]
                    extracted_env = None
                    extracted_country = None

                    # 提取环境和地区参数
                    for part in parts:
                        part_lower = part.lower()
                        if part_lower in env_keywords and extracted_env is None:
                            extracted_env = env_keywords[part_lower]
                            remaining_parts.remove(part)
                        elif part_lower in country_keywords and extracted_country is None:
                            extracted_country = country_keywords[part_lower]
                            remaining_parts.remove(part)

                    # 剩余的参数应该是用户名
                    if remaining_parts:
                        username = remaining_parts[0]  # 取第一个剩余参数作为用户名

                    # 应用提取的环境和地区
                    if extracted_env:
                        env = extracted_env
                    if extracted_country:
                        country = extracted_country

            # 从查询中提取环境和地区信息（增强的关键词匹配）
            query_upper = user_query.upper()

            # 地区匹配
            country_mapping = {
                'SG': ['SG', 'SINGAPORE'],
                'TH': ['TH', 'THAILAND'],
                'VN': ['VN', 'VIETNAM'],
                'MY': ['MY', 'MALAYSIA'],
                'PH': ['PH', 'PHILIPPINES'],
                'TW': ['TW', 'TAIWAN'],
                'BR': ['BR', 'BRAZIL'],
                'MX': ['MX', 'MEXICO'],
                'PL': ['PL', 'POLAND'],
                'CO': ['CO', 'COLOMBIA'],
                'CL': ['CL', 'CHILE'],
                'AR': ['AR', 'ARGENTINA']
            }

            for country_code, keywords in country_mapping.items():
                if any(keyword in query_upper for keyword in keywords):
                    country = country_code
                    break

            # 环境匹配
            env_mapping = {
                'LIVE': ['LIVE', 'PROD', 'PRODUCTION'],
                'STAGING': ['STAGING', 'STG'],
                'UAT': ['UAT'],
                'TEST': ['TEST']
            }

            for env_code, keywords in env_mapping.items():
                if any(keyword in query_upper for keyword in keywords):
                    env = env_code
                    break

            ic(f"📝 解析结果 - username: {username}, userid: {userid}, env: {env}, country: {country}")

            # 验证参数
            if not username and not userid:
                return {
                    'success': False,
                    'response': f"❌ 请提供用户名或用户ID\n\n{user_info_client.get_help_message()}",
                    'intent': 'user_info_error'
                }

            # 执行查询
            result = await user_info_client.query_user_comprehensive(
                username=username,
                userid=userid,
                env=env,
                country=country
            )

            if result['success']:
                users = result.get('users', [])
                if users:
                    formatted_response = user_info_client.format_user_info(users, query_result=result)
                    return {
                        'success': True,
                        'response': f"🔍 用户信息查询结果：\n\n{formatted_response}",
                        'intent': 'user_info_query',
                        'user_count': len(users),
                        'smart_search_used': result.get('smart_search_used', False),
                        'query_params': {
                            'username': username,
                            'userid': userid,
                            'env': env,
                            'country': country
                        }
                    }
                else:
                    # 检查是否是智能搜索的特殊情况
                    if result.get('not_found_suggestion'):
                        formatted_response = user_info_client.format_user_info(users, query_result=result)
                        return {
                            'success': True,
                            'response': formatted_response,
                            'intent': 'user_info_not_found_with_suggestion'
                        }
                    else:
                        return {
                            'success': True,
                            'response': f"❌ 未找到匹配的用户信息\n\n查询条件：\n• 用户名: {username or 'N/A'}\n• 用户ID: {userid or 'N/A'}\n• 环境: {env}\n• 地区: {country}\n\n请检查查询条件是否正确。",
                            'intent': 'user_info_not_found'
                        }
            else:
                error_msg = result.get('error', '未知错误')
                return {
                    'success': False,
                    'response': f"❌ 用户信息查询失败：{error_msg}\n\n请稍后重试或联系管理员。",
                    'intent': 'user_info_error'
                }

        except Exception as e:
            ic(f"❌ 用户信息查询命令处理异常: {str(e)}")
            return {
                'success': False,
                'response': f"❌ 处理用户信息查询时发生异常：{str(e)}",
                'intent': 'user_info_error'
            }

    async def _handle_user_info_query(self, extracted_info: Dict, user_query: str, user_email: str, user_id: str) -> Dict:
        """处理用户信息查询意图"""
        try:
            from .user_info_client import user_info_client

            ic(f"🔍 处理用户信息查询意图 - 提取信息: {extracted_info}")

            # 从提取的信息中获取参数
            username = extracted_info.get('username')
            userid = extracted_info.get('userid')
            env = extracted_info.get('env', 'TEST')
            country = extracted_info.get('country', 'ID')

            # 使用用户信息客户端进行参数标准化
            if env:
                env = user_info_client.normalize_environment(env)
            if country:
                country = user_info_client.normalize_country(country)

            # 如果没有提取到用户信息，尝试从查询中解析
            if not username and not userid:
                # 简单的解析逻辑
                query_lower = user_query.lower()

                # 查找用户名模式
                import re
                username_patterns = [
                    r'用户名[：:\s]*([a-zA-Z0-9._-]+)',
                    r'username[：:\s]*([a-zA-Z0-9._-]+)',
                    r'查询用户[：:\s]*([a-zA-Z0-9._-]+)',
                    r'用户[：:\s]*([a-zA-Z0-9._-]+)',
                ]

                for pattern in username_patterns:
                    match = re.search(pattern, user_query, re.IGNORECASE)
                    if match:
                        username = match.group(1)
                        break

                # 查找用户ID模式
                if not username:
                    userid_patterns = [
                        r'用户ID[：:\s]*(\d+)',
                        r'userid[：:\s]*(\d+)',
                        r'id[：:\s]*(\d+)',
                    ]

                    for pattern in userid_patterns:
                        match = re.search(pattern, user_query, re.IGNORECASE)
                        if match:
                            userid = int(match.group(1))
                            break

                # 如果还是没有找到，尝试从查询中提取数字或用户名
                if not username and not userid:
                    # 查找纯数字（可能是用户ID）
                    number_match = re.search(r'\b(\d{6,})\b', user_query)
                    if number_match:
                        userid = int(number_match.group(1))
                    else:
                        # 查找可能的用户名
                        name_match = re.search(r'\b([a-zA-Z][a-zA-Z0-9._-]{2,})\b', user_query)
                        if name_match:
                            username = name_match.group(1)

            # 验证参数
            if not username and not userid:
                return {
                    'success': False,
                    'response': f"❌ 无法从查询中提取用户信息\n\n{user_info_client.get_help_message()}",
                    'intent': 'user_info_error'
                }

            ic(f"📝 解析结果 - username: {username}, userid: {userid}, env: {env}, country: {country}")

            # 执行查询
            result = await user_info_client.query_user_comprehensive(
                username=username,
                userid=userid,
                env=env,
                country=country
            )

            if result['success']:
                users = result.get('users', [])
                if users:
                    formatted_response = user_info_client.format_user_info(users, query_result=result)
                    return {
                        'success': True,
                        'response': f"🔍 用户信息查询结果：\n\n{formatted_response}",
                        'intent': 'user_info_query',
                        'user_count': len(users),
                        'smart_search_used': result.get('smart_search_used', False),
                        'query_params': {
                            'username': username,
                            'userid': userid,
                            'env': env,
                            'country': country
                        }
                    }
                else:
                    # 检查是否是智能搜索的特殊情况
                    if result.get('not_found_suggestion'):
                        formatted_response = user_info_client.format_user_info(users, query_result=result)
                        return {
                            'success': True,
                            'response': formatted_response,
                            'intent': 'user_info_not_found_with_suggestion'
                        }
                    else:
                        return {
                            'success': True,
                            'response': f"❌ 未找到匹配的用户信息\n\n查询条件：\n• 用户名: {username or 'N/A'}\n• 用户ID: {userid or 'N/A'}\n• 环境: {env}\n• 地区: {country}\n\n请检查查询条件是否正确。",
                            'intent': 'user_info_not_found'
                        }
            else:
                error_msg = result.get('error', '未知错误')
                return {
                    'success': False,
                    'response': f"❌ 用户信息查询失败：{error_msg}\n\n请稍后重试或联系管理员。",
                    'intent': 'user_info_error'
                }

        except Exception as e:
            ic(f"❌ 用户信息查询意图处理异常: {str(e)}")
            return {
                'success': False,
                'response': f"❌ 处理用户信息查询时发生异常：{str(e)}",
                'intent': 'user_info_error'
            }
    
    async def _handle_direct_chat_command(self, user_query: str, user_id: str, session_id: str) -> Dict:
        """处理直接对话命令"""
        # 去除前缀
        actual_query = user_query
        if user_query.lower().startswith('chat '):
            actual_query = user_query[5:].strip()
        
        ic(f"🗣️ 直接对话模式 - 查询: '{actual_query}'")
        
        try:
            # 获取最近的对话历史
            history_result = await conversation_manager.get_chat_history(session_id, limit=5)
            recent_history = []
            
            if history_result['success']:
                recent_history = history_result['history']
            
            # 构建上下文消息
            context_messages = []
            
            for msg in recent_history:
                if msg.get('role') == 'user':
                    context_messages.append(f"用户: {msg.get('content', '')}")
                elif msg.get('role') == 'assistant':
                    context_messages.append(f"助手: {msg.get('content', '')}")
            
            # 构建系统提示词
            system_prompt = f"""你是{BotConfig.BOT_NAME}的AI助手，现在处于直接对话模式。

在这个模式下：
1. 你可以进行自然对话，回答各种问题
2. 支持多轮对话，可以参考之前的对话内容
3. 可以提供技术咨询、代码帮助、翻译服务等
4. 不需要进行JIRA查询或其他业务操作
5. 保持友好、专业的对话风格
6. 用中文回答，除非用户要求其他语言

{'对话历史:\n' + chr(10).join(context_messages) + chr(10) if context_messages else ''}

请根据用户的问题进行自然对话。"""

            # 调用LLM进行对话
            result = await multi_llm_client.generate_with_retry(
                actual_query, system_prompt, max_retries=2
            )
            
            if result['success']:
                response_content = result['content']
                
                # 保存对话到上下文
                await conversation_manager.add_chat_message(session_id, 'user', actual_query)
                await conversation_manager.add_chat_message(session_id, 'assistant', response_content)
                
                ic(f"✅ 直接对话完成: {response_content[:100]}...")
                
                return {
                    'success': True,
                    'response': f"💬 {response_content}",
                    'intent': 'direct_chat',
                    'chat_mode': True
                }
            else:
                ic(f"❌ 直接对话失败: {result['error']}")
                return {
                    'success': False,
                    'error': f"对话生成失败: {result['error']}"
                }
        except Exception as e:
            ic(f"❌ 直接对话异常: {str(e)}")
            return {
                'success': False,
                'error': f"对话处理异常: {str(e)}"
            }
    
    async def _generate_personalized_help(self, user_query: str, context: Dict = None) -> Dict:
        """生成个性化帮助信息"""
        try:
            ic(f"🎭 生成个性化帮助信息，用户查询: {user_query}")

            # 优先使用新的LLM帮助系统
            try:
                from ..llm_help_system.llm_help_manager import llm_help_manager

                # 从上下文中获取用户信息
                user_email = context.get('user_email', '') if context else ''

                # 直接使用LLM生成个性化帮助
                help_message = await llm_help_manager.generate_help_response(
                    user_query=user_query,
                    context_type='private',  # AI助手默认私聊模式
                    user_email=user_email,
                    user_role='normal'  # 默认普通用户，会根据邮箱自动调整
                )

                if help_message:
                    ic(f"✅ 使用LLM帮助系统生成个性化回复")
                    return {
                        'success': True,
                        'response': help_message,
                        'intent': 'general_help'
                    }
            except ImportError:
                ic("⚠️ LLM帮助系统不可用，尝试使用兼容性适配器")

                # 尝试使用兼容性适配器
                try:
                    from ..llm_help_system.compatibility_adapter import help_intent_enhancer

                    # 构建上下文信息
                    help_context = context or {}
                    help_context['type'] = 'private'  # 默认私聊模式

                    # 生成上下文相关的帮助信息
                    help_message = help_intent_enhancer.generate_contextual_help(
                        user_query=user_query,
                        context=help_context,
                        user_role='normal'  # 默认普通用户
                    )

                    if help_message:
                        ic(f"✅ 使用兼容性适配器生成个性化回复")
                        return {
                            'success': True,
                            'response': help_message,
                            'intent': 'general_help'
                        }
                except ImportError:
                    ic("⚠️ 兼容性适配器不可用，使用传统方法")

            # 备用方案：使用传统帮助信息
            from ..bot_config import bot_config
            user_email = context.get('user_email', '') if context else ''
            help_message = bot_config.generate_help_message(
                context_type='private',
                user_query=user_query,
                user_email=user_email
            )

            ic(f"✅ 使用传统帮助信息")
            return {
                'success': True,
                'response': help_message,
                'intent': 'general_help'
            }

        except Exception as e:
            ic(f"❌ 帮助信息生成异常: {str(e)}")
            return {
                'success': True,
                'response': self._get_help_message(),
                'intent': 'general_help'
            }

    def _is_help_related_query(self, user_query: str) -> bool:
        """判断是否为帮助相关查询"""
        query_lower = user_query.lower().strip()

        # 首先检查是否包含明确的查询意图关键词，如果有则不是帮助查询
        query_intent_patterns = [
            # JIRA查询相关
            '查询', '查看', '看看', '检查', '搜索', '找', '获取', '提交了', '分配给', '处理',
            'query', 'search', 'find', 'get', 'check', 'look', 'assigned', 'submitted',
            # 时间相关查询
            '上周', '本周', '上月', '本月', '昨天', '今天', '明天', '最近',
            'last week', 'this week', 'last month', 'this month', 'yesterday', 'today', 'tomorrow', 'recent',
            # JIRA单号模式
            r'[A-Z]+-\d+',
            # 人员相关
            '给.*提交', '分配给.*的', '我的.*任务', '.*的bug'
        ]

        # 如果包含明确的查询意图，不认为是帮助查询
        # 但要排除明确的帮助查询（如"如何使用jira查询"）
        import re
        for pattern in query_intent_patterns:
            if re.search(pattern, query_lower):
                # 检查是否是帮助相关的查询（如"如何使用"、"怎么查询"等）
                help_query_patterns = [
                    '如何.*查询', '怎么.*查询', '如何使用.*查询', '怎么使用.*查询',
                    'how to.*query', 'how to use.*query'
                ]
                is_help_query = any(re.search(help_pattern, query_lower) for help_pattern in help_query_patterns)
                if not is_help_query:
                    return False

        # 帮助相关的关键词（更严格的匹配）
        help_keywords = [
            # 直接帮助请求
            'help', '帮助', '?', '？', 'usage', '使用方法', '怎么用', '如何使用',

            # 功能询问（完整匹配）
            '你能做什么', '你可以做什么', '有什么功能', '支持什么功能', '能干什么',
            'what can you do', 'what do you do', 'capabilities',

            # 固定指令相关（完整匹配）
            '固定指令', '有哪些固定指令', '你有哪些固定指令', '固定指令有哪些',
            '指令列表', '命令列表', '支持的指令', '支持哪些指令',
            '固定命令', '有哪些命令', '命令有哪些','常用命令', '支持的命令',
            'what are the commands', 'list of commands', 'supported commands',

            # 具体功能帮助（完整匹配）
            '如何创建定时任务', '定时任务怎么用', '怎么创建定时任务', '定时任务如何使用',
            '如何使用固定指令', '固定指令怎么用', '怎么使用固定指令',
            '如何使用jira查询', 'jira查询怎么用', '怎么查询jira', 'jira查询功能',
            '如何查询jira', 'jira怎么查', 'jira查询如何使用',

            # 功能介绍（完整匹配）
            '介绍一下功能', '功能介绍', '系统功能', '有哪些功能',
            '使用指南', '操作指南', '用户手册'
        ]

        # 检查是否包含帮助关键词（完整匹配）
        for keyword in help_keywords:
            if keyword == query_lower or query_lower.startswith(keyword):
                return True

        # 检查是否为简单的功能询问模式（但排除明显的非帮助查询）
        simple_patterns = [
            '功能', '帮助', '使用', '怎么', '如何', 'help', 'usage'
        ]

        # 排除明显的非帮助查询
        non_help_patterns = [
            '天气', '时间', '日期', '计算', '数学', '翻译', '今天', '明天', '昨天',
            'weather', 'time', 'date', 'calculate', 'math', 'translate',
            # 添加更多查询相关的排除模式
            '查询', '查看', '看看', '检查', '搜索', '找', '获取', '提交', '分配', '处理',
            'query', 'search', 'find', 'get', 'check', 'look', 'assigned', 'submitted'
        ]

        # 如果包含非帮助模式，不认为是帮助查询
        if any(pattern in query_lower for pattern in non_help_patterns):
            return False

        # 如果查询很短且包含帮助模式，很可能是帮助请求
        if len(query_lower) < 20 and any(pattern in query_lower for pattern in simple_patterns):
            return True

        return False

    async def _handle_config_command(self, user_query: str, user_email: str) -> Dict:
        """处理配置指令"""
        parts = user_query.split()
        if len(parts) < 3:
            return {
                'success': True,
                'response': "配置格式: config jira_token YOUR_TOKEN",
                'intent': 'config_help'
            }
        
        config_type = parts[1].lower()
        if config_type == 'jira_token':
            token = parts[2]
            
            if not user_email:
                return {
                    'success': False,
                    'error': "无法确定用户邮箱，无法保存token"
                }
            
            # 验证token
            validation = JiraTokenManager.validate_token(token)
            if not validation['valid']:
                return {
                    'success': False,
                    'error': validation['message']
                }
            
            # 保存token
            save_result = await JiraTokenManager.save_user_token_async(user_email, token)
            if save_result['success']:
                return {
                    'success': True,
                    'response': save_result['message'],
                    'intent': 'config_jira_token'
                }
            else:
                return {
                    'success': False,
                    'error': save_result['message']
                }
        
        return {
            'success': True,
            'response': "支持的配置项: jira_token",
            'intent': 'config_help'
        }
    
    async def _handle_stats_command(self, user_id: str) -> Dict:
        """处理统计查询指令"""
        try:
            # 获取查询统计
            jira_query = SmartJiraQuery()
            stats_result = await jira_query.get_user_query_stats(user_id)
            
            if not stats_result['success']:
                return {
                    'success': False,
                    'error': f"获取统计失败: {stats_result['error']}"
                }
            
            stats = stats_result['stats']
            response = f"您的使用统计 (最近7天):\n\n"
            response += f"总查询次数: {stats['total_queries']}\n"
            response += f"成功查询: {stats['successful_queries']}\n"
            response += f"成功率: {stats['success_rate']:.1f}%\n"
            response += f"平均响应时间: {stats['avg_response_time']}秒\n"
            
            if stats['intent_stats']:
                response += "\n常用功能:\n"
                for intent, count in stats['intent_stats'].items():
                    response += f"• {intent}: {count}次\n"
            
            return {
                'success': True,
                'response': response,
                'intent': 'query_stats'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"统计查询异常: {str(e)}"
            }
    
    async def _handle_fields_command(self, user_query: str) -> Dict:
        """处理字段查询指令"""
        try:
            from .field_mapper import field_mapper
            
            parts = user_query.split()
            if len(parts) < 2:
                return {
                    'success': True,
                    'response': "字段查询格式:\n• fields search 关键词 - 搜索jira可用字段的 ID\n• fields status - 查看更新状态\n• fields update - 强制更新字段映射",
                    'intent': 'fields_help'
                }
            
            command = parts[1].lower()
            
            if command == 'status':
                # 查看字段映射状态
                status = await field_mapper.get_update_status()
                response = f"🔧 JIRA 字段映射状态:\n\n"
                response += f"📅 上次更新: {status['last_update'] or '从未更新'}\n"
                if status['hours_since_update']:
                    response += f"⏰ 距离上次更新: {status['hours_since_update']:.1f} 小时\n"
                response += f"🔄 需要更新: {'是' if status['needs_update'] else '否'}\n"
                response += f"📊 总字段数: {status['total_fields']}\n"
                response += f"🔧 自定义字段数: {status['custom_fields_count']}\n"
                response += f"⭐ 重要字段数: {status['important_fields_count']}"
                
                return {
                    'success': True,
                    'response': response,
                    'intent': 'fields_status'
                }
            
            elif command == 'update':
                # 强制更新字段映射
                result = await field_mapper.force_update()
                if result['success']:
                    response = f"✅ {result['message']}\n"
                    response += f"📊 总字段数: {result['total_fields']}\n"
                    response += f"🔧 自定义字段数: {result['custom_fields']}"
                else:
                    response = f"❌ 更新失败: {result['error']}"
                
                return {
                    'success': result['success'],
                    'response': response,
                    'intent': 'fields_update'
                }
            
            elif command == 'search':
                # 搜索字段
                if len(parts) < 3:
                    return {
                        'success': False,
                        'error': "请提供搜索关键词，格式: fields search 关键词"
                    }
                
                keyword = ' '.join(parts[2:])
                results = await field_mapper.search_fields(keyword)
                
                if not results:
                    return {
                        'success': True,
                        'response': f"未找到包含 '{keyword}' 的字段",
                        'intent': 'fields_search'
                    }
                
                response = f"🔍 搜索 '{keyword}' 的结果:\n\n"
                for i, field in enumerate(results[:10]):  # 限制显示前10个结果
                    important_mark = "⭐" if field.get('important', False) else ""
                    custom_mark = "🔧" if field.get('custom', False) else "🔹"
                    response += f"{custom_mark}{important_mark} **{field['name']}**\n"
                    response += f"   ID: `{field['id']}`\n"
                    response += f"   类型: {field.get('type', 'unknown')}\n"
                    if field.get('shopee_category'):
                        response += f"   分类: {field['shopee_category']}\n"
                    response += "\n"
                
                if len(results) > 10:
                    response += f"... 还有 {len(results) - 10} 个结果"
                
                return {
                    'success': True,
                    'response': response,
                    'intent': 'fields_search'
                }
            
            else:
                return {
                    'success': False,
                    'error': f"未知的字段命令: {command}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"字段查询异常: {str(e)}"
            }
    
    async def _handle_schedule_command(self, user_query: str, user_id: str, user_email: str, group_title: str = None, group_id: str = None) -> Dict:
        """处理定时任务管理指令"""
        try:
            from .task_scheduler import task_scheduler
            from .natural_task_creator import natural_task_creator

            ic(f"📅 定时任务命令处理 - 用户查询: '{user_query}'")

            # 首先检查是否为自然语言的任务列表查询
            query_lower = user_query.lower()
            list_keywords = ['定时任务', '任务列表', '我的任务', '有哪些', '列表', '查看任务', '任务有哪些']
            if any(keyword in query_lower for keyword in list_keywords):
                ic("📋 检测到自然语言任务列表查询，直接调用list功能")
                # 直接调用list功能
                result = await task_scheduler.list_user_tasks(
                    user_id=user_id,
                    user_email=user_email,
                    admin_mode=False,
                    target_user_email=None,
                    project_filter=None
                )
                ic(f"📋 list_user_tasks返回结果: {result}")

                if result['success']:
                    if result['total_count'] == 0:
                        response = "📋 您还没有创建任何定时任务。\n\n使用 `schedule create` 创建您的第一个定时任务。"
                    else:
                        response = f"📋 您的定时任务列表 (共 {result['total_count']} 个):\n\n"

                        for i, task in enumerate(result['tasks'], 1):
                            status_emoji = "✅" if task['status'] == 'active' else "⏸️" if task['status'] == 'paused' else "❌"
                            response += f"{status_emoji} 📋 **{task['name']}**\n"
                            response += f"   ID: {task['id']}\n"

                            # 根据任务类型显示不同内容
                            if task.get('task_type') == 'reminder':
                                # 提醒任务显示提醒内容
                                reminder_msg = task.get('reminder_message', '无提醒内容')
                                if len(reminder_msg) > 60:
                                    response += f"   提醒内容:\n```\n{reminder_msg}\n```\n"
                                else:
                                    response += f"   提醒内容: `{reminder_msg}`\n"
                                response += f"   任务类型: 定时提醒\n"
                            else:
                                # JIRA查询任务显示查询内容
                                query_text = task.get('query', '无查询内容')
                                if query_text and len(query_text) > 60:
                                    # 长查询使用多行显示，带代码格式
                                    response += f"   查询:\n```\n{query_text}\n```\n"
                                else:
                                    response += f"   查询: `{query_text}`\n"
                                response += f"   任务类型: JIRA查询\n"
                            response += f"   频率: {task['frequency']} @ {task['schedule_time']}\n"

                            # 友好显示通知类型和目标信息
                            notification_type_display = {
                                'private': '私聊',
                                'group': '群聊'
                            }.get(task['notification_type'], task['notification_type'])
                            
                            # 显示通知信息（合并类型和目标）
                            if 'notification_target' in task and task['notification_target']:
                                response += f"   通知: {task['notification_target']}\n"
                            else:
                                response += f"   通知: {notification_type_display}\n"

                            if i < len(result['tasks']):
                                response += "\n"

                    return {
                        'success': True,
                        'response': response,
                        'intent': 'schedule_list'
                    }
                else:
                    return {
                        'success': False,
                        'error': result.get('error', '获取任务列表失败'),
                        'intent': 'schedule_list'
                    }

            parts = user_query.split()
            ic(f"📅 分割后的部分: {parts}")

            # 智能检测：如果参数不足或者是自然语言请求，启用自然语言处理
            if len(parts) < 2:
                ic("📅 参数不足，返回帮助信息")
                return {
                    'success': True,
                    'response': self._get_schedule_help_message(),
                    'intent': 'schedule_help'
                }

            command = parts[1].lower()
            ic(f"📅 识别的命令: '{command}'")
            
            # 检查是否为自然语言创建请求
            if command == 'create' or command == '创建':
                # 如果参数太少，返回澄清信息
                if len(parts) < 3:
                    ic("📅 create命令参数不足，返回澄清信息")
                    return {
                        'success': True,
                        'needs_clarification': True,
                        'clarification': {
                            'type': 'schedule_create',
                            'question': '创建定时任务需要更多参数信息'
                        },
                        'intent': 'schedule_help'
                    }
                
                # 检查是否为完整的命令格式
                is_complete_command = len(parts) >= 5 and all(
                    part.startswith('"') and part.endswith('"') 
                    for part in parts[2:5]
                )
                
                remaining_query = ' '.join(parts[2:])
                
                # 如果不是完整命令格式，或者明显是自然语言，启用自然语言处理
                if not is_complete_command or natural_task_creator.is_natural_language_request(remaining_query):
                    ic("🌟 检测到自然语言创建请求，启用智能处理")
                    # 获取会话ID
                    session_result = await conversation_manager.get_or_create_session(
                        user_id, None, None
                    )
                    if session_result['success']:
                        session_id = session_result['session']['session_id']
                        return await natural_task_creator.process_natural_request(
                            remaining_query,  # 去掉 "schedule create" 部分
                            user_id, user_email, session_id, group_id, group_title
                        )
                    else:
                        ic(f"❌ 获取会话失败: {session_result['error']}")
            
            if command == 'create' or command == '创建':
                # 创建定时任务
                ic("📅 进入创建任务分支")
                result = await self._handle_schedule_create(user_query, user_id, user_email, group_title)
                ic(f"📅 创建任务结果: success={result.get('success')}, intent={result.get('intent')}")
                return result
            
            elif command == 'list' or command == '列表':
                # 列出用户的定时任务
                ic(f"📋 执行list命令 - user_id: '{user_id}'")
                
                # 解析管理员参数
                admin_mode = False
                target_user_email = None
                project_filter = None
                
                if len(parts) > 2:
                    for i, part in enumerate(parts[2:], 2):
                        if part.startswith('--user='):
                            target_user_email = part.split('=')[1]
                            admin_mode = True
                        elif part.startswith('--project='):
                            project_filter = part.split('=')[1]
                            admin_mode = True
                        elif part == '--all':
                            admin_mode = True
                        elif part == '--admin':
                            admin_mode = True
                
                result = await task_scheduler.list_user_tasks(
                    user_id=user_id,
                    user_email=user_email,
                    admin_mode=admin_mode,
                    target_user_email=target_user_email,
                    project_filter=project_filter
                )
                ic(f"📋 list_user_tasks返回结果: {result}")
                
                if result['success']:
                    if result['total_count'] == 0:
                        if admin_mode:
                            response = "📋 在指定范围内没有找到任务。\n\n"
                            if target_user_email:
                                response += f"用户 {target_user_email} 没有创建任何定时任务。"
                            elif project_filter:
                                response += f"项目 {project_filter} 没有相关的定时任务。"
                            else:
                                response += "没有找到符合条件的任务。"
                        else:
                            response = "📋 您还没有创建任何定时任务。\n\n使用 `schedule create` 创建您的第一个定时任务。"
                    else:
                        # 根据用户角色显示不同的标题
                        user_role = result.get('user_role', 'unknown')
                        if admin_mode and user_role in ['super_admin', 'project_manager']:
                            if target_user_email:
                                response = f"📋 用户 {target_user_email} 的任务列表 (共 {result['total_count']} 个):\n\n"
                            elif project_filter:
                                response = f"📋 项目 {project_filter} 的任务列表 (共 {result['total_count']} 个):\n\n"
                            else:
                                response = f"📋 管理范围内的任务列表 (共 {result['total_count']} 个):\n\n"
                        else:
                            response = f"📋 您的定时任务列表 (共 {result['total_count']} 个):\n\n"
                        
                        for i, task in enumerate(result['tasks'], 1):
                            status_emoji = "✅" if task['status'] == 'active' else "⏸️" if task['status'] == 'paused' else "❌"
                            response += f"{status_emoji} 📋 **{task['name']}**\n"
                            response += f"   ID: {task['id']}\n"
                            
                            # 在管理员模式下显示创建者信息
                            if admin_mode and task.get('creator_email') != user_email:
                                response += f"   创建者: {task['creator_email']}\n"
                            
                            # 根据任务类型显示不同内容
                            if task.get('task_type') == 'reminder':
                                # 提醒任务显示提醒内容
                                reminder_msg = task.get('reminder_message', '无提醒内容')
                                if len(reminder_msg) > 60:
                                    response += f"   提醒内容:\n```\n{reminder_msg}\n```\n"
                                else:
                                    response += f"   提醒内容: `{reminder_msg}`\n"
                                response += f"   任务类型: 定时提醒\n"
                            else:
                                # JIRA查询任务显示查询内容
                                query_text = task.get('query', '无查询内容')
                                if query_text and len(query_text) > 60:
                                    # 长查询使用多行显示，带代码格式
                                    response += f"   查询:\n```\n{query_text}\n```\n"
                                else:
                                    response += f"   查询: `{query_text}`\n"
                                response += f"   任务类型: JIRA查询\n"
                            response += f"   频率: {task['frequency']} @ {task['schedule_time']}\n"
                            
                            # 友好显示通知类型和目标信息
                            notification_type_display = {
                                'private': '私聊',
                                'group': '群聊'
                            }.get(task['notification_type'], task['notification_type'])
                            
                            # 显示通知信息（合并类型和目标）
                            if 'notification_target' in task and task['notification_target']:
                                response += f"   通知: {task['notification_target']}\n"
                            else:
                                response += f"   通知: {notification_type_display}\n"
                            
                            response += f"   状态: {task['status']}\n"
                            response += f"   成功率: {task['success_rate']}% ({task['successful_executions']}/{task['total_executions']})\n"
                            if task['next_execution']:
                                response += f"   下次执行: {task['next_execution'][:19].replace('T', ' ')}\n"
                            
                            # 显示创建时间（管理员模式）
                            if admin_mode and task.get('created_at'):
                                response += f"   创建时间: {task['created_at'][:19].replace('T', ' ')}\n"
                            
                            response += "\n"
                        
                        # 添加管理员模式的额外提示
                        if admin_mode and user_role in ['super_admin', 'project_manager']:
                            response += "\n🔧 **管理员操作**:\n"
                            response += "• 查看特定用户任务: `schedule list --user=<EMAIL>`\n"
                            response += "• 查看项目任务: `schedule list --project=SPCB`\n"
                            response += "• 管理员暂停任务: `schedule pause <ID> --admin`\n"
                            response += "• 查看统计信息: `schedule stats`\n"
                else:
                    response = f"❌ 获取任务列表失败: {result['error']}"
                
                return {
                    'success': result['success'],
                    'response': response,
                    'intent': 'schedule_list',
                    'permission_denied': result.get('permission_denied', False)
                }
            
            elif command == 'pause' or command == '暂停':
                # 暂停任务
                if len(parts) < 3:
                    return {
                        'success': False,
                        'error': "请提供任务ID，格式: schedule pause <任务ID> [--admin]"
                    }
                
                try:
                    task_id = int(parts[2])
                    admin_mode = '--admin' in parts
                    result = await task_scheduler.update_task_status(
                        user_id=user_id, 
                        task_id=task_id, 
                        status='paused',
                        user_email=user_email,
                        admin_mode=admin_mode
                    )
                    return {
                        'success': result['success'],
                        'response': result.get('message', result.get('error')),
                        'intent': 'schedule_pause',
                        'permission_denied': result.get('permission_denied', False)
                    }
                except ValueError:
                    return {
                        'success': False,
                        'error': "任务ID必须是数字"
                    }
            
            elif command == 'resume' or command == '恢复':
                # 恢复任务
                if len(parts) < 3:
                    return {
                        'success': False,
                        'error': "请提供任务ID，格式: schedule resume <任务ID> [--admin]"
                    }
                
                try:
                    task_id = int(parts[2])
                    admin_mode = '--admin' in parts
                    result = await task_scheduler.update_task_status(
                        user_id=user_id, 
                        task_id=task_id, 
                        status='active',
                        user_email=user_email,
                        admin_mode=admin_mode
                    )
                    return {
                        'success': result['success'],
                        'response': result.get('message', result.get('error')),
                        'intent': 'schedule_resume',
                        'permission_denied': result.get('permission_denied', False)
                    }
                except ValueError:
                    return {
                        'success': False,
                        'error': "任务ID必须是数字"
                    }
            
            elif command == 'delete' or command == '删除':
                # 删除任务
                if len(parts) < 3:
                    return {
                        'success': False,
                        'error': "请提供任务ID，格式: schedule delete <任务ID> [--admin]"
                    }
                
                try:
                    task_id = int(parts[2])
                    admin_mode = '--admin' in parts
                    result = await task_scheduler.delete_task(
                        user_id=user_id, 
                        task_id=task_id,
                        user_email=user_email,
                        admin_mode=admin_mode
                    )
                    return {
                        'success': result['success'],
                        'response': result.get('message', result.get('error')),
                        'intent': 'schedule_delete',
                        'permission_denied': result.get('permission_denied', False)
                    }
                except ValueError:
                    return {
                        'success': False,
                        'error': "任务ID必须是数字"
                    }
            
            elif command == 'stats' or command == '统计':
                # 查看任务统计
                project_filter = None
                if len(parts) > 2 and parts[2].startswith('--project='):
                    project_filter = parts[2].split('=')[1]
                
                result = await task_scheduler.get_task_statistics(
                    user_email=user_email,
                    project_filter=project_filter
                )
                
                if result['success']:
                    stats = result['statistics']
                    user_role = result['user_role']
                    
                    response = f"📊 **任务统计信息**\n\n"
                    
                    # 基础统计
                    response += f"📈 **总体统计**:\n"
                    response += f"• 总任务数: {stats['total_tasks']}\n"
                    response += f"• 活跃任务: {stats['active_tasks']}\n"
                    response += f"• 暂停任务: {stats['paused_tasks']}\n"
                    response += f"• 智能任务: {stats['smart_tasks']}\n\n"
                    
                    # 项目统计（如果有权限）
                    if stats['project_stats'] and user_role in ['super_admin', 'project_manager']:
                        response += f"📋 **项目分布**:\n"
                        for project, count in sorted(stats['project_stats'].items(), key=lambda x: x[1], reverse=True):
                            response += f"• {project}: {count} 个任务\n"
                        response += "\n"
                    
                    # 用户统计（仅管理员）
                    if stats['user_stats'] and user_role == 'super_admin':
                        response += f"👥 **用户排行** (Top {len(stats['user_stats'])}):\n"
                        for user_stat in stats['user_stats']:
                            response += f"• {user_stat['user_email']}: {user_stat['task_count']} 个任务\n"
                        response += "\n"
                    
                    # 角色说明
                    role_display = {
                        'super_admin': '超级管理员',
                        'project_manager': '项目管理员',
                        'regular_user': '普通用户'
                    }.get(user_role, user_role)
                    response += f"🏷️ **您的角色**: {role_display}\n"
                    
                    if project_filter:
                        response += f"🔍 **项目过滤**: {project_filter}"
                else:
                    response = f"❌ 获取统计信息失败: {result['error']}"
                
                return {
                    'success': result['success'],
                    'response': response,
                    'intent': 'schedule_stats',
                    'permission_denied': result.get('permission_denied', False)
                }
            
            else:
                return {
                    'success': False,
                    'error': f"未知的定时任务命令: {command}\n\n{self._get_schedule_help_message()}"
                }
                
        except Exception as e:
            ic(f"📅 定时任务管理异常: {str(e)}")
            return {
                'success': False,
                'error': f"定时任务管理异常: {str(e)}"
            }
    
    async def _handle_schedule_create(self, user_query: str, user_id: str, user_email: str, group_title: str = None) -> Dict:
        """处理定时任务创建"""
        try:
            ic(f"📝 开始创建定时任务 - 查询: '{user_query}'")
            # 解析任务创建参数
            import re
            
            # 检查是否为智能通知任务
            is_smart = any(keyword in user_query.lower() for keyword in [
                'smart', '智能', 'assignee', '分配', '合并', 'merge'
            ])
            ic(f"📝 智能任务检测结果: is_smart={is_smart}, 用户查询: '{user_query}'")
            
            # 使用更智能的参数解析，支持嵌套引号
            params = self._parse_quoted_parameters(user_query)
            ic(f"📝 解析到的参数: {params}")
            
            if len(params) < 3:
                ic(f"📝 参数不足，只有{len(params)}个参数")
                return {
                    'success': False,
                    'error': '参数不足。格式: schedule create "任务名称" "查询内容" "调度表达式" [智能配置]'
                }
            
            task_name = params[0]
            query_text = params[1]
            schedule_expression = params[2]
            
            ic(f"📝 解析的参数: 任务名={task_name}, 查询={query_text}, 调度={schedule_expression}")
            
            # 解析调度表达式
            from .task_scheduler import task_scheduler
            ic(f"📝 调度表达式原文: '{schedule_expression}'")
            schedule_info = task_scheduler.parse_schedule_expression(schedule_expression)
            ic(f"📝 调度解析结果: {schedule_info}")
            if 'error' in schedule_info:
                ic(f"📝 调度表达式解析失败: {schedule_info['error']}")
                return {
                    'success': False,
                    'error': f"调度表达式解析失败: {schedule_info['error']}"
                }
            
            if is_smart:
                # 创建智能通知任务
                logger.info("📝 进入智能任务创建分支")
                logger.info(f"📝 调用_create_smart_notification_task参数: user_id={user_id}, user_email={user_email}")
                result = await self._create_smart_notification_task(
                    user_id, user_email, task_name, query_text, 
                    schedule_info, user_query, params, group_title
                )
                logger.info(f"📝 _create_smart_notification_task返回结果: {result}")
                return result
            else:
                # 创建普通任务
                ic("📝 创建普通任务")
                result = await task_scheduler.create_task(
                    user_id=user_id,
                    user_email=user_email,
                    employee_code=None,
                    task_name=task_name,
                    query_text=query_text,
                    schedule_time=schedule_info['schedule_time'],
                    frequency=schedule_info['frequency'],
                    schedule_days=schedule_info['schedule_days'],
                    notification_type='creator',
                    group_title=group_title
                )
                ic(f"📝 task_scheduler.create_task 返回结果: {result}")
                
                if result['success']:
                    response = f"🎉 **定时任务创建成功！**\n\n"
                    response += f"📋 **任务名称**: {task_name}\n"
                    response += f"🆔 **任务ID**: {result['task_id']}\n"
                    response += f"🔍 **JQL查询**: `{query_text}`\n"
                    
                    # 格式化调度显示
                    from .natural_task_creator import natural_task_creator
                    schedule_display = natural_task_creator._format_schedule_display(schedule_expression)
                    response += f"⏰ **执行频率**: {schedule_display}\n"
                    response += f"📬 **通知方式**: 私聊通知\n"
                    
                    if result['next_execution']:
                        formatted_time = result['next_execution'][:19].replace('T', ' ')
                        response += f"⏰ **下次执行**: {formatted_time}\n"
                    
                    response += f"🟢 **任务状态**: 已激活\n\n"
                    
                    # 操作提示
                    response += "🔧 **管理任务**:\n"
                    response += f"• 查看所有任务: `schedule list`\n"
                    response += f"• 暂停此任务: `schedule pause {result['task_id']}`\n"
                    response += f"• 删除此任务: `schedule delete {result['task_id']}`\n\n"
                    
                    response += "✅ 任务已开始运行，请确认以上配置是否符合您的需求。"
                else:
                    response = f"❌ 创建任务失败: {result['error']}"
                
                return {
                    'success': result['success'],
                    'response': response,
                    'intent': 'schedule_create'
                }
            
        except Exception as e:
            ic(f"📝 创建定时任务异常: {str(e)}")
            return {
                'success': False,
                'error': f"创建定时任务异常: {str(e)}"
            }
    
    async def _create_smart_notification_task(self, user_id: str, user_email: str, 
                                            task_name: str, query_text: str,
                                            schedule_info: Dict, user_query: str, 
                                            matches: List[str], group_title: str = None, target_group_id: str = None) -> Dict:
        """创建通知任务（原智能通知简化版）"""
        try:
            logger.info("📝 开始执行_create_smart_notification_task")
            logger.info(f"📝 参数: user_id={user_id}, user_email={user_email}, task_name={task_name}")
            
            from .task_scheduler import task_scheduler
            
            # 解析通知配置
            notification_config = self._parse_smart_notification_config(user_query, matches)
            logger.info(f"📝 解析的通知配置: {notification_config}")
            
            # 确定通知类型 - 使用标准化的值
            target_type = notification_config.get('target_type', 'creator')
            notification_type = 'creator'  # 默认发送给创建人
            final_target_group_id = target_group_id  # 使用传入的群组ID
            
            if target_type in ['group', 'both']:
                notification_type = 'group'
                # 如果没有传入群组ID，尝试从群组映射获取
                if not final_target_group_id and 'group_mapping' in notification_config and notification_config['group_mapping']:
                    for _, group_id in notification_config['group_mapping'].items():
                        final_target_group_id = group_id
                        break
            
            # 创建普通通知任务
            logger.info(f"📝 准备创建{notification_type}通知任务")
            result = await task_scheduler.create_task(
                user_id=user_id,
                user_email=user_email,
                employee_code=None,
                task_name=task_name,
                query_text=query_text,
                schedule_time=schedule_info['schedule_time'],
                frequency=schedule_info['frequency'],
                schedule_days=schedule_info['schedule_days'],
                notification_type=notification_type,
                target_group_id=final_target_group_id,
                group_title=group_title,
                task_type='jira_query'  # 明确指定为JIRA查询任务
            )
            logger.info(f"📝 task_scheduler.create_task返回: {result}")
            
            if result['success']:
                # 检查任务ID是否正确返回
                task_id = result.get('task_id')
                if not task_id:
                    logger.error(f"❌ 通知任务创建返回成功但task_id为空: {result}")
                    return {
                        'success': False,
                        'error': "通知任务创建失败：未能获取任务ID，请检查数据库连接"
                    }
                
                logger.info(f"✅ 通知任务创建成功，task_id: {task_id}")
                
                response = f"🎉 **通知任务创建成功！**\n\n"
                response += f"📋 **任务名称**: {task_name}\n"
                response += f"🆔 **任务ID**: {task_id}\n"
                response += f"🔍 **JQL查询**: `{query_text}`\n"
                
                # 格式化调度显示
                from .natural_task_creator import natural_task_creator
                schedule_display = natural_task_creator._format_schedule_display(
                    f"{schedule_info['frequency']} {schedule_info['schedule_time']}"
                )
                response += f"⏰ **执行频率**: {schedule_display}\n"
                
                # 显示通知类型的友好名称
                target_type_display = {
                    'private': '私聊通知',
                    'group': '群聊通知'
                }.get(notification_type, notification_type)
                response += f"📬 **通知方式**: {target_type_display}\n"
                
                # 添加下次执行时间
                try:
                    from app01.models import UserScheduledTask
                    from asgiref.sync import sync_to_async
                    
                    def get_next_execution():
                        try:
                            task = UserScheduledTask.objects.get(id=task_id)
                            if task.next_execution:
                                from django.utils import timezone
                                import pytz
                                
                                # 确保时区正确
                                if task.next_execution.tzinfo is None:
                                    task.next_execution = pytz.utc.localize(task.next_execution)
                                
                                # 转换为新加坡时间
                                sg_tz = pytz.timezone('Asia/Singapore')
                                sg_time = task.next_execution.astimezone(sg_tz)
                                
                                return sg_time.strftime('%Y-%m-%d %H:%M:%S')
                            return None
                        except Exception as e:
                            logger.error(f"获取下次执行时间失败: {str(e)}")
                            return None
                    
                    get_next_execution_async = sync_to_async(get_next_execution)
                    formatted_time = await get_next_execution_async()
                    
                    if formatted_time:
                        response += f"⏭️ **下次执行**: {formatted_time}\n"
                    
                except Exception as e:
                    logger.error(f"获取下次执行时间异常: {str(e)}")
                
                response += f"🟢 **任务状态**: 已激活\n\n"
                
                # 操作提示
                response += "🔧 **管理任务**:\n"
                response += f"• 查看所有任务: `schedule list`\n"
                response += f"• 暂停此任务: `schedule pause {task_id}`\n"
                response += f"• 删除此任务: `schedule delete {task_id}`\n\n"
                
                response += "✅ 任务已开始运行，请确认以上配置是否符合您的需求。"
                
                return {
                    'success': True,
                    'task_id': task_id,
                    'response': response,
                    'intent': 'schedule_created'
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"创建通知任务失败: {str(e)}")
            return {
                'success': False,
                'error': f'创建通知任务失败: {str(e)}'
            }
    
    def _parse_quoted_parameters(self, user_query: str) -> List[str]:
        """智能解析引号参数，支持嵌套引号"""
        try:
            # 首先提取 schedule create 后面的部分
            create_index = user_query.lower().find('schedule create')
            if create_index == -1:
                return []
            
            # 获取参数部分
            params_part = user_query[create_index + len('schedule create'):].strip()
            ic(f"📝 参数部分: {params_part}")
            
            # 使用更简单的方法：逐个字符分析，计算引号平衡
            params = []
            current_param = ""
            quote_depth = 0
            i = 0
            
            while i < len(params_part):
                char = params_part[i]
                
                if char == '"':
                    if quote_depth == 0:
                        # 开始一个新参数
                        quote_depth = 1
                        current_param = ""
                    elif quote_depth == 1:
                        # 检查这是否是参数结束还是嵌套引号
                        # 向前看，如果下一个非空格字符是引号或结束，则这是参数结束
                        j = i + 1
                        while j < len(params_part) and params_part[j] == ' ':
                            j += 1
                        
                        if j >= len(params_part) or params_part[j] == '"':
                            # 这是参数结束
                            params.append(current_param)
                            current_param = ""
                            quote_depth = 0
                        else:
                            # 这是嵌套引号
                            current_param += char
                elif quote_depth > 0:
                    # 在引号内，添加字符
                    current_param += char
                
                i += 1
            
            # 处理未完成的参数
            if current_param and quote_depth > 0:
                params.append(current_param)
            
            ic(f"📝 引号平衡解析结果: {params}")
            return params
            
        except Exception as e:
            # 如果解析失败，回退到简单的正则表达式
            ic(f"📝 引号平衡解析失败，使用备用方法: {str(e)}")
            return re.findall(r'"([^"]*)"', user_query)
    
    def _parse_smart_notification_config(self, user_query: str, matches: List[str]) -> Dict:
        """解析通知配置"""
        config = {
            'target_type': 'private',  # 默认为私聊
            'merge_messages': False,   # 默认不合并消息
        }
        
        # 检查是否指定了群聊
        if any(keyword in user_query.lower() for keyword in ['群聊', '群组', 'group']):
            config['target_type'] = 'group'
        
        # 从matches中提取更精确的配置
        for match in matches:
            match_lower = match.lower()
            
            # 通知类型
            if match_lower in ['private', '私聊']:
                config['target_type'] = 'private'
            elif match_lower in ['group', '群聊']:
                config['target_type'] = 'group'
        
        return config
    
    def _get_schedule_help_message(self) -> str:
        """获取定时任务帮助信息"""
        response = """📅 **定时任务管理帮助**

## 基础命令：
• `schedule create "任务名" "JQL查询" "时间表达式"` - 创建定时任务
• `schedule list` - 查看您的任务列表
• `schedule pause <ID>` - 暂停任务
• `schedule resume <ID>` - 恢复任务
• `schedule delete <ID>` - 删除任务
• `schedule stats` - 查看任务统计

## 管理员命令：
• `schedule list --user=<EMAIL>` - 查看指定用户的任务
• `schedule list --project=SPCB` - 查看项目任务
• `schedule list --all` - 查看所有可管理的任务
• `schedule pause <ID> --admin` - 管理员暂停任务
• `schedule resume <ID> --admin` - 管理员恢复任务
• `schedule delete <ID> --admin` - 管理员删除任务
• `schedule stats --project=SPCB` - 查看项目统计


• `daily 09:00` - 每天上午9点
• `weekly 17:00 1 2 3 4 5` - 工作日下午5点
• `weekly 09:00 1` - 每周一上午9点
• `monthly 1 08:00` - 每月1号上午8点

## 示例：
```
schedule create "每日bug检查" "project = SPCB AND status = Open AND priority = High" "daily 09:00"
schedule create "智能通知" "assignee in (user1, user2) AND status = 'In Progress'" "workdays 17:00"
schedule list --project=SPCB
schedule pause 123 --admin
```

## 自然语言支持：
您也可以使用自然语言创建定时任务，例如：
• "每个工作日 11:33提醒我有哪些未完成的子任务"
• "每天上午10点提醒我查看邮件"
• "每周一上午9点提醒我参加团队会议"

## 权限说明：
• 普通用户：只能管理自己的任务
• 项目管理员：可以管理指定项目的任务
• 超级管理员：可以管理所有任务

需要更多帮助？请告诉我您的具体需求！"""
        
        return response
    
    async def _handle_advanced_command(self, user_query: str, user_id: str, user_email: str) -> Dict:
        """处理高级任务功能指令"""
        try:
            from .advanced_task_manager import advanced_task_manager
            
            parts = user_query.split()
            if len(parts) < 2:
                return {
                    'success': True,
                    'response': self._get_advanced_help_message(),
                    'intent': 'advanced_help'
                }
            
            command = parts[1].lower()
            
            # ==================== 群聊通知功能 ====================
            if command == 'group' or command == '群聊':
                return await self._handle_group_notification_command(user_query, user_id, user_email)
            
            # ==================== 任务模板功能 ====================
            elif command == 'template' or command == '模板':
                return await self._handle_template_command(user_query, user_id, user_email)
            
            # ==================== 批量管理功能 ====================
            elif command == 'batch' or command == '批量':
                return await self._handle_batch_command(user_query, user_id, user_email)
            
            # ==================== 统计报表功能 ====================
            elif command == 'stats' or command == '统计':
                return await self._handle_advanced_stats_command(user_query, user_id, user_email)
            
            # ==================== 白名单管理 ====================
            elif command == 'whitelist' or command == '白名单':
                return await self._handle_whitelist_command(user_query, user_id, user_email)
            
            else:
                return {
                    'success': False,
                    'error': f"未知的高级功能命令: {command}\n\n{self._get_advanced_help_message()}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"高级功能处理异常: {str(e)}"
            }
    
    async def _handle_group_notification_command(self, user_query: str, user_id: str, user_email: str) -> Dict:
        """处理群聊通知命令"""
        try:
            from .advanced_task_manager import advanced_task_manager
            
            # 解析群聊通知创建命令
            # 格式: advanced group create "任务名称" "查询内容" "调度表达式" "群组ID"
            import re
            matches = re.findall(r'"([^"]*)"', user_query)
            
            if 'create' in user_query and len(matches) >= 4:
                task_name = matches[0]
                query_text = matches[1]
                schedule_expression = matches[2]
                target_group_id = matches[3]
                
                # 解析调度表达式
                from .task_scheduler import task_scheduler
                schedule_info = task_scheduler.parse_schedule_expression(schedule_expression)
                if 'error' in schedule_info:
                    return {
                        'success': False,
                        'error': f"调度表达式解析失败: {schedule_info['error']}"
                    }
                
                # 创建群聊通知任务
                result = await advanced_task_manager.create_group_notification_task(
                    user_id=user_id,
                    user_email=user_email,
                    task_name=task_name,
                    query_text=query_text,
                    schedule_time=schedule_info['schedule_time'],
                    frequency=schedule_info['frequency'],
                    target_group_id=target_group_id,
                    schedule_days=schedule_info['schedule_days']
                )
                
                return {
                    'success': result['success'],
                    'response': result.get('message', result.get('error')),
                    'intent': 'advanced_group_create'
                }
            else:
                return {
                    'success': True,
                    'response': """📢 **群聊通知功能**

**创建群聊通知任务:**
`advanced group create "任务名称" "查询内容" "调度表达式" "群组ID"`

**示例:**
`advanced group create "团队日报" "今天团队完成的任务" "每天 18:00" "group_123"`

**注意:**
• 需要白名单权限才能使用
• 任务结果将发送到指定群聊
• 群组ID需要是有效的群聊标识""",
                    'intent': 'advanced_group_help'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"群聊通知功能异常: {str(e)}"
            }
    
    async def _handle_template_command(self, user_query: str, user_id: str, user_email: str) -> Dict:
        """处理任务模板命令"""
        try:
            from .advanced_task_manager import advanced_task_manager
            
            parts = user_query.split()
            
            if 'list' in user_query or '列表' in user_query:
                # 列出模板
                category = None
                if len(parts) > 3:
                    category = parts[3]
                
                result = await advanced_task_manager.list_task_templates(user_id, category)
                
                if result['success']:
                    if result['total_count'] == 0:
                        response = "📋 暂无可用的任务模板。"
                    else:
                        response = f"📋 **可用任务模板** (共 {result['total_count']} 个):\n\n"
                        
                        current_category = None
                        for template in result['templates']:
                            if template['category'] != current_category:
                                current_category = template['category']
                                response += f"\n**{template['category']}:**\n"
                            
                            public_mark = "🌐" if template['is_public'] else "🔒"
                            response += f"{public_mark} **{template['name']}** (ID: {template['id']})\n"
                            response += f"   描述: {template['description']}\n"
                            response += f"   查询: {template['query_template'][:50]}...\n"
                            response += f"   调度: {template['default_schedule']}\n"
                            response += f"   使用次数: {template['usage_count']}\n\n"
                else:
                    response = f"❌ 获取模板列表失败: {result['error']}"
                
                return {
                    'success': result['success'],
                    'response': response,
                    'intent': 'advanced_template_list'
                }
            
            elif 'create' in user_query and 'from' in user_query:
                # 从模板创建任务
                # 格式: advanced template create from <template_id> "任务名称" [变量值]
                import re
                matches = re.findall(r'"([^"]*)"', user_query)
                
                if len(matches) >= 1:
                    # 提取模板ID
                    template_id_match = re.search(r'from\s+(\d+)', user_query)
                    if template_id_match:
                        template_id = int(template_id_match.group(1))
                        task_name = matches[0]
                        
                        # 提取变量值（如果有）
                        variable_values = {}
                        if len(matches) > 1:
                            # 简化处理：假设变量值以key=value格式提供
                            for i in range(1, len(matches)):
                                if '=' in matches[i]:
                                    key, value = matches[i].split('=', 1)
                                    variable_values[key.strip()] = value.strip()
                        
                        result = await advanced_task_manager.create_task_from_template(
                            user_id=user_id,
                            user_email=user_email,
                            template_id=template_id,
                            task_name=task_name,
                            variable_values=variable_values
                        )
                        
                        return {
                            'success': result['success'],
                            'response': result.get('message', result.get('error')),
                            'intent': 'advanced_template_create_from'
                        }
                
                return {
                    'success': False,
                    'error': '格式错误。正确格式: advanced template create from <模板ID> "任务名称"'
                }
            
            else:
                return {
                    'success': True,
                    'response': """📋 **任务模板功能**

**查看模板列表:**
`advanced template list [分类]`

**从模板创建任务:**
`advanced template create from <模板ID> "任务名称"`

**示例:**
• `advanced template list daily`
• `advanced template create from 1 "我的每日检查"`

**注意:**
• 需要白名单权限才能使用
• 可以使用公开模板和自己创建的模板
• 模板支持变量替换功能""",
                    'intent': 'advanced_template_help'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"任务模板功能异常: {str(e)}"
            }
    
    async def _handle_batch_command(self, user_query: str, user_id: str, user_email: str) -> Dict:
        """处理批量管理命令"""
        try:
            from .advanced_task_manager import advanced_task_manager
            
            if 'pause' in user_query or '暂停' in user_query:
                # 批量暂停任务
                # 格式: advanced batch pause 1,2,3,4
                import re
                ids_match = re.search(r'pause\s+([\d,\s]+)', user_query)
                if ids_match:
                    task_ids = [int(x.strip()) for x in ids_match.group(1).split(',') if x.strip().isdigit()]
                    
                    result = await advanced_task_manager.batch_update_task_status(
                        user_id=user_id,
                        task_ids=task_ids,
                        new_status='paused'
                    )
                    
                    if result['success']:
                        response = f"✅ {result['message']}\n\n详细结果:\n"
                        for item in result['results']:
                            status = "✅" if item['success'] else "❌"
                            response += f"{status} 任务 {item['task_id']}: {item['message']}\n"
                    else:
                        response = f"❌ 批量暂停失败: {result['error']}"
                    
                    return {
                        'success': result['success'],
                        'response': response,
                        'intent': 'advanced_batch_pause'
                    }
            
            elif 'resume' in user_query or '恢复' in user_query:
                # 批量恢复任务
                import re
                ids_match = re.search(r'resume\s+([\d,\s]+)', user_query)
                if ids_match:
                    task_ids = [int(x.strip()) for x in ids_match.group(1).split(',') if x.strip().isdigit()]
                    
                    result = await advanced_task_manager.batch_update_task_status(
                        user_id=user_id,
                        task_ids=task_ids,
                        new_status='active'
                    )
                    
                    if result['success']:
                        response = f"✅ {result['message']}\n\n详细结果:\n"
                        for item in result['results']:
                            status = "✅" if item['success'] else "❌"
                            response += f"{status} 任务 {item['task_id']}: {item['message']}\n"
                    else:
                        response = f"❌ 批量恢复失败: {result['error']}"
                    
                    return {
                        'success': result['success'],
                        'response': response,
                        'intent': 'advanced_batch_resume'
                    }
            
            else:
                return {
                    'success': True,
                    'response': """🔄 **批量管理功能**

**批量暂停任务:**
`advanced batch pause 1,2,3,4`

**批量恢复任务:**
`advanced batch resume 1,2,3,4`

**示例:**
• `advanced batch pause 1,3,5` - 暂停任务1,3,5
• `advanced batch resume 2,4,6` - 恢复任务2,4,6

**注意:**
• 需要白名单权限才能使用
• 任务ID用逗号分隔
• 只能操作自己的任务""",
                    'intent': 'advanced_batch_help'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"批量管理功能异常: {str(e)}"
            }
    
    async def _handle_advanced_stats_command(self, user_query: str, user_id: str, user_email: str) -> Dict:
        """处理高级统计命令"""
        try:
            from .advanced_task_manager import advanced_task_manager
            from datetime import date, timedelta
            
            # 解析日期范围（如果提供）
            start_date = None
            end_date = None
            
            import re
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})\s+to\s+(\d{4}-\d{2}-\d{2})', user_query)
            if date_match:
                start_date = date.fromisoformat(date_match.group(1))
                end_date = date.fromisoformat(date_match.group(2))
            
            result = await advanced_task_manager.generate_user_statistics_report(
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )
            
            if result['success']:
                report = result['report']
                
                response = f"📊 **任务执行统计报表**\n\n"
                response += f"📅 **报表周期:** {report['report_period']['start_date']} 至 {report['report_period']['end_date']}\n\n"
                
                # 任务概览
                task_summary = report['task_summary']
                response += f"📋 **任务概览:**\n"
                response += f"• 总任务数: {task_summary['total_tasks']}\n"
                response += f"• 活跃任务: {task_summary['active_tasks']}\n"
                response += f"• 暂停任务: {task_summary['paused_tasks']}\n"
                response += f"• 禁用任务: {task_summary['disabled_tasks']}\n\n"
                
                # 执行统计
                exec_summary = report['execution_summary']
                response += f"⚡ **执行统计:**\n"
                response += f"• 总执行次数: {exec_summary['total_executions']}\n"
                response += f"• 成功执行: {exec_summary['successful_executions']}\n"
                response += f"• 失败执行: {exec_summary['failed_executions']}\n"
                response += f"• 成功率: {exec_summary['success_rate']}%\n\n"
                
                # 性能统计
                perf_summary = report['performance_summary']
                response += f"🚀 **性能统计:**\n"
                response += f"• 平均执行时间: {perf_summary['avg_execution_time']}秒\n"
                response += f"• 最大执行时间: {perf_summary['max_execution_time']}秒\n"
                response += f"• 最小执行时间: {perf_summary['min_execution_time']}秒\n\n"
                
                # 任务频率分布
                if report['task_frequency_distribution']:
                    response += f"📈 **任务频率分布:**\n"
                    for freq_stat in report['task_frequency_distribution']:
                        response += f"• {freq_stat['frequency']}: {freq_stat['count']} 个任务\n"
                
            else:
                response = f"❌ 生成统计报表失败: {result['error']}"
            
            return {
                'success': result['success'],
                'response': response,
                'intent': 'advanced_stats_report'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"高级统计功能异常: {str(e)}"
            }
    
    async def _handle_whitelist_command(self, user_query: str, user_id: str, user_email: str) -> Dict:
        """处理白名单管理命令"""
        try:
            from app01.models import AdvancedTaskFeatureWhitelist
            
            if 'status' in user_query or '状态' in user_query:
                # 查看白名单状态
                try:
                    whitelist = AdvancedTaskFeatureWhitelist.objects.get(
                        user_id=user_id, 
                        is_active=True
                    )
                    
                    response = f"🎫 **您的高级功能权限状态:**\n\n"
                    response += f"👤 用户ID: {whitelist.user_id}\n"
                    response += f"📧 邮箱: {whitelist.user_email or '未设置'}\n"
                    response += f"🆔 员工代码: {whitelist.employee_code or '未设置'}\n\n"
                    
                    response += f"🔓 **已授权功能:**\n"
                    for feature in whitelist.allowed_features:
                        feature_names = {
                            'group_notification': '群聊通知',
                            'conditional_trigger': '条件触发',
                            'task_template': '任务模板',
                            'batch_management': '批量管理',
                            'statistics_report': '统计报表',
                            'all_features': '所有高级功能'
                        }
                        response += f"• {feature_names.get(feature, feature)}\n"
                    
                    response += f"\n📊 **使用限制:**\n"
                    response += f"• 最大任务数: {whitelist.max_tasks}\n"
                    response += f"• 最大模板数: {whitelist.max_templates}\n"
                    
                    response += f"\n📅 **权限信息:**\n"
                    response += f"• 授权时间: {whitelist.granted_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
                    response += f"• 授权人: {whitelist.granted_by or '系统'}\n"
                    if whitelist.expires_at:
                        response += f"• 过期时间: {whitelist.expires_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
                    else:
                        response += f"• 过期时间: 永不过期\n"
                    
                except AdvancedTaskFeatureWhitelist.DoesNotExist:
                    response = "❌ 您没有高级功能权限。请联系管理员申请白名单权限。"
                
                return {
                    'success': True,
                    'response': response,
                    'intent': 'advanced_whitelist_status'
                }
            
            else:
                return {
                    'success': True,
                    'response': """🎫 **白名单权限管理**

**查看权限状态:**
`advanced whitelist status`

**可申请的高级功能:**
• 群聊通知 - 任务结果发送到群聊
• 任务模板 - 使用和创建任务模板
• 批量管理 - 批量操作多个任务
• 统计报表 - 生成详细的执行统计

**申请权限:**
请联系系统管理员申请相应的白名单权限。

**注意:**
• 高级功能需要白名单权限才能使用
• 不同功能有不同的使用限制
• 权限可能有过期时间""",
                    'intent': 'advanced_whitelist_help'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"白名单管理功能异常: {str(e)}"
            }
    
    def _get_advanced_help_message(self) -> str:
        """获取高级功能帮助信息"""
        return """🚀 **高级定时任务功能**

**群聊通知:**
• `advanced group create "任务名" "查询" "调度" "群组ID"`

**任务模板:**
• `advanced template list [分类]` - 查看模板
• `advanced template create from <ID> "任务名"` - 从模板创建

**批量管理:**
• `advanced batch pause 1,2,3` - 批量暂停
• `advanced batch resume 1,2,3` - 批量恢复

**统计报表:**
• `advanced stats` - 生成统计报表
• `advanced stats 2024-01-01 to 2024-01-31` - 指定日期范围

**权限管理:**
• `advanced whitelist status` - 查看权限状态

**注意:**
• 所有高级功能都需要白名单权限
• 请联系管理员申请相应权限
• 功能使用有一定限制"""
    
    async def _handle_abtest_command(self, user_query: str, user_id: str, user_email: str) -> Dict:
        """处理AB测试管理命令"""
        try:
            from ..ai_config import AB_TEST_CONFIG
            import re
            import os
            from datetime import datetime
            
            parts = user_query.split()
            if len(parts) < 2:
                # 显示当前状态
                return await self._get_abtest_status()
            
            command = parts[1].lower()
            
            if command == 'status':
                # 查看当前AB测试状态
                return await self._get_abtest_status()
            
            elif command == 'enable':
                # 开启AB测试
                if len(parts) < 3:
                    return {
                        'success': False,
                        'error': "enable命令需要指定模式: dual 或 single\n示例: abtest enable dual\n示例: abtest enable single a"
                    }
                
                mode = parts[2].lower()
                
                if mode == 'dual':
                    # 开启双模型对比
                    result = await self._update_abtest_config(enabled=True, dual_mode=True)
                    if result['success']:
                        return {
                            'success': True,
                            'response': "✅ AB测试已开启: 双模型对比模式\n⏰ 开启时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "\n💡 现在将同时调用两个模型，可以对比回答质量！\n\n⚠️  提醒: AB测试适合短期评估，长期使用建议切换回生产模式。",
                            'intent': 'abtest_enable_dual'
                        }
                    else:
                        return result
                
                elif mode == 'single':
                    # 开启单模型测试
                    if len(parts) < 4:
                        return {
                            'success': False,
                        'error': "single模式需要指定模型: a 或 b\n示例: abtest enable single a"
                        }
                    
                    model = parts[3].lower()
                    if model not in ['a', 'b']:
                        return {
                            'success': False,
                            'error': "无效的模型名称，支持: a, b"
                        }
                    
                    # 规范为A/B
                    mapping = {'a': 'A', 'b': 'B'}
                    default_model_ab = mapping.get(model, 'A')
                    result = await self._update_abtest_config(enabled=True, dual_mode=False, default_model=default_model_ab)
                    if result['success']:
                        from ..ai_config import AB_TEST_CONFIG as _AB
                        model_name = _AB.get('MODEL_A_DISPLAY_NAME', 'Model A') if default_model_ab == 'A' else _AB.get('MODEL_B_DISPLAY_NAME', 'Model B')
                        return {
                            'success': True,
                            'response': f"✅ AB测试已开启: 单模型测试模式 ({model_name})\n⏰ 开启时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n💡 现在使用{model_name}进行测试。\n\n⚠️  提醒: AB测试适合短期评估，长期使用建议切换回生产模式。",
                            'intent': 'abtest_enable_single'
                        }
                    else:
                        return result
                
                else:
                    return {
                        'success': False,
                        'error': "无效的模式，支持: dual, single"
                    }
            
            elif command == 'disable':
                # 关闭AB测试
                result = await self._update_abtest_config(enabled=False)
                if result['success']:
                    from ..ai_config import AB_TEST_CONFIG as _AB
                    model_a_name = _AB.get('MODEL_A_DISPLAY_NAME', 'Model A')
                    model_b_name = _AB.get('MODEL_B_DISPLAY_NAME', 'Model B')
                    current_name = model_a_name if _AB.get('DEFAULT_MODEL', 'A') == 'A' else model_b_name
                    return {
                        'success': True,
                        'response': "✅ AB测试已关闭，已恢复正式生产模式\n⏰ 关闭时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + f"\n💡 现在使用稳定的{current_name}模型。",
                        'intent': 'abtest_disable'
                    }
                else:
                    return result
            
            elif command == 'help':
                # 显示帮助信息
                return {
                    'success': True,
                    'response': self._get_abtest_help_message(),
                    'intent': 'abtest_help'
                }
            
            else:
                return {
                    'success': False,
                    'error': f"未知命令: {command}\n\n{self._get_abtest_help_message()}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"AB测试管理异常: {str(e)}"
            }
    
    async def _get_abtest_status(self) -> Dict:
        """获取AB测试当前状态"""
        try:
            from ..ai_config import AB_TEST_CONFIG
            from datetime import datetime
            
            enabled = AB_TEST_CONFIG.get('ENABLED', False)
            dual_mode = AB_TEST_CONFIG.get('DUAL_MODEL_COMPARISON', False)
            default_model = AB_TEST_CONFIG.get('DEFAULT_MODEL', 'A')
            model_a_name = AB_TEST_CONFIG.get('MODEL_A_DISPLAY_NAME', 'Model A')
            model_b_name = AB_TEST_CONFIG.get('MODEL_B_DISPLAY_NAME', 'Model B')
            
            if enabled:
                if dual_mode:
                    mode = "AB测试双模型对比模式"
                    desc = f"同时调用{model_a_name}和{model_b_name}进行对比"
                else:
                    current_name = model_a_name if default_model == 'A' else model_b_name
                    mode = f"AB测试单模型模式 ({current_name})"
                    desc = f"使用{current_name}进行测试"
            else:
                current_name = model_a_name if default_model == 'A' else model_b_name
                mode = f"正式生产模式 ({current_name})"
                desc = f"使用稳定的{current_name}模型"
            
            response = f"🤖 **AI模型AB测试状态**\n"
            response += f"{'=' * 50}\n"
            response += f"📊 当前模式: {mode}\n"
            response += f"📝 描述: {desc}\n"
            response += f"🤖 {model_a_name}: 可用\n"
            response += f"🧭 {model_b_name}: 可用\n\n"
            
            if enabled:
                response += "✅ AB测试已开启\n"
                if dual_mode:
                    response += "🔄 双模型对比模式\n"
                else:
                    response += f"🎯 单模型模式 ({default_model})\n"
            else:
                response += "⭕ AB测试已关闭（正式生产模式）\n"
                current_name = model_a_name if default_model == 'A' else model_b_name
                response += f"🎯 使用{current_name}模型\n"
            
            response += f"\n⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            return {
                'success': True,
                'response': response,
                'intent': 'abtest_status'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"获取AB测试状态失败: {str(e)}"
            }
    
    async def _update_abtest_config(self, enabled: bool, dual_mode: bool = False, default_model: str = 'qwen') -> Dict:
        """更新AB测试配置"""
        try:
            import re
            from datetime import datetime
            
            config_path = 'app01/ai_config.py'
            
            # 读取当前配置
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 备份原配置
            backup_path = f"{config_path}.backup.{int(datetime.now().timestamp())}"
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 更新配置
            content = re.sub(
                r"'ENABLED':\s*(True|False)",
                f"'ENABLED': {enabled}",
                content
            )
            
            content = re.sub(
                r"'DUAL_MODEL_COMPARISON':\s*(True|False)",
                f"'DUAL_MODEL_COMPARISON': {dual_mode}",
                content
            )
            
            content = re.sub(
                r"'DEFAULT_MODEL':\s*'[^']*'",
                f"'DEFAULT_MODEL': '{default_model}'",
                content
            )
            
            # 写回配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 重新加载配置
            from .multi_llm_client import multi_llm_client
            multi_llm_client.reload_config()
            
            return {
                'success': True,
                'message': f"配置已更新并重新加载，备份文件: {backup_path}"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"配置更新失败: {str(e)}"
            }
    
    def _get_abtest_help_message(self) -> str:
        """获取AB测试帮助信息"""
        from ..ai_config import AB_TEST_CONFIG as _AB
        model_a_name = _AB.get('MODEL_A_DISPLAY_NAME', 'Model A')
        model_b_name = _AB.get('MODEL_B_DISPLAY_NAME', 'Model B')
        return f"""🤖 **AI模型AB测试管理工具**

**基本命令:**
• `/ai abtest status` - 查看当前AB测试状态

**开启AB测试:**
• `/ai abtest enable dual` - 开启双模型对比（同时调用两个模型）
• `/ai abtest enable single a` - 开启{model_a_name}单模型测试
• `/ai abtest enable single b` - 开启{model_b_name}单模型测试

**关闭AB测试:**
• `/ai abtest disable` - 关闭AB测试，恢复正式生产模式

**帮助信息:**
• `/ai abtest help` - 显示此帮助信息

**注意事项:**
• AB测试模式适合短期测试和对比
• 正式工作环境建议使用生产模式确保稳定性
• 每次配置变更都会自动备份原始配置文件
• 配置变更实时生效，无需重启服务"""

    async def _check_write_permission(self, user_email: str, intent: str) -> Dict:
        """检查写操作权限"""
        if not user_email:
            return {
                'success': False,
                'error': "写操作需要用户身份验证，请确保您已正确登录。"
            }
        
        jira_query = SmartJiraQuery(user_email)
        permission = jira_query.validate_write_permission()
        
        if not permission['has_permission']:
            return {
                'success': False,
                'error': permission['message'],
                'need_token': permission.get('need_token', False),
                'need_token_refresh': permission.get('need_token_refresh', False)
            }
        
        return {'success': True}

    async def _execute_query(self, intent: str, extracted_info: Dict, 
                           user_query: str, user_email: str) -> Dict:
        """执行查询操作"""
        try:
            # 配置管理和帮助类意图不需要JIRA查询
            if intent in ['config_management', 'general_help', 'config_jira_token', 
                         'config_help', 'query_stats', 'clear_context']:
                return {
                    'success': True,
                    'jql': '',
                    'jira_data': {},
                    'result_count': 0,
                    'quick_format': True
                }
            
            # 1. 生成JQL
            jql_result = await self._generate_jql(intent, extracted_info, user_query)
            if not jql_result['success']:
                return jql_result
            
            jql_data = jql_result['jql_data']
            jql = jql_data['jql']
            
            # 替换JQL中的currentUser()为用户实际邮箱
            if user_email:
                jql = self._fix_jql_email_issues(jql, user_query, user_email)
                logger.info(f"🔄 JQL处理后: {jql}")
            
            # 2. 确定查询字段 - 检查是否为子任务查询
            intent_type = intent
            fields = jql_data.get('fields_needed', SmartJiraQuery.get_optimal_fields(intent))
            
            # 检查是否为子任务查询，确保包含必要字段
            if self._is_subtask_query(jql, user_query, extracted_info):
                intent_type = 'query_subtask'  # 修正意图类型
                subtask_fields = [
                    'key', 'summary', 'status', 'priority', 'assignee', 'reporter', 
                    'created', 'updated', 'issuetype', 'resolved',
                    'customfield_16300',  # Planned Start Date
                    'customfield_16301',  # Planned Due Date
                    'customfield_10100',  # Story Points
                    'customfield_10004',  # Story Point Estimate (备用)
                ]
                fields = subtask_fields
                logger.info(f"🎯 检测到子任务查询，使用专用字段: {subtask_fields}")
            
            # 3. 执行JIRA查询
            jira_query = SmartJiraQuery(user_email)
            
            if extracted_info.get('operation_type') == 'write':
                # 写操作 (更新issue等)
                if 'jira_keys' in extracted_info and extracted_info['jira_keys']:
                    issue_key = extracted_info['jira_keys'][0]
                    update_fields = self._extract_update_fields(extracted_info)
                    update_result = await jira_query.update_issue(issue_key, update_fields)
                    
                    return {
                        'success': update_result['success'],
                        'jql': f"key = {issue_key}",
                        'jira_data': update_result,
                        'result_count': 1 if update_result['success'] else 0,
                        'error': update_result.get('error'),
                        'intent': intent_type
                    }
                else:
                    return {
                        'success': False,
                        'error': '写操作需要指定JIRA单号'
                    }
            else:
                # 读操作 (查询)
                query_result = await jira_query.execute_jql(jql, fields)
                
                # 4. 格式化响应 - 使用正确的意图类型
                response = await self._format_response(
                    intent_type, user_query, jql, 
                    query_result['data'], query_result.get('quick_format', False)
                )
                
                # 构建返回结果
                result = {
                    'success': True,
                    'response': response,
                    'extra_data': {
                        'jql': jql,
                        'result_count': query_result.get('result_count', 0),
                        'jira_data': query_result['data'],
                        'query_type': intent_type
                    }
                }
                
                # 如果JQL生成有对比信息，添加到extra_data中
                if jql_result.get('jql_data', {}).get('_comparison_details'):
                    result['extra_data']['_comparison_details'] = jql_result['jql_data']['_comparison_details']
                    result['extra_data']['_primary_model'] = jql_result['jql_data'].get('_primary_model')
                
                return result
                
        except Exception as e:
            logger.error(f"查询执行异常: {str(e)}")
            return {
                'success': False,
                'error': f"查询执行异常: {str(e)}"
            }

    async def _generate_jql(self, intent: str, extracted_info: Dict, 
                          user_query: str) -> Dict:
        """生成JQL查询语句"""
        try:
            from .prompts.jql_generation import JQLGenerationPrompts
            
            system_prompt, user_prompt = JQLGenerationPrompts.build_prompt(
                intent, extracted_info, user_query
            )
            
            # 调用LLM进行JQL生成 - 使用流水线方法支持双模型对比
            result = await multi_llm_client.generate_for_pipeline_step(
                'jql_generation', user_prompt, system_prompt, max_retries=2
            )
            
            if not result['success']:
                return {
                    'success': False,
                    'error': result['error']
                }
            
            # 记录对比信息（如果有的话）
            if 'comparison_details' in result:
                logger.info(f"🔍 JQL生成对比 - 主要结果来自: {result.get('primary_model', 'Unknown')}")
                
                # 统一A/B命名，兼容旧键名
                comp = result['comparison_details']
                model_a_result = comp.get('model_a_result', comp.get('deepseek_result', {}))
                model_b_result = comp.get('model_b_result', comp.get('qwen_result', {}))
                
                if model_b_result.get('success') and model_a_result.get('success'):
                    logger.info("🔍 两个模型都成功生成JQL，可进行对比分析")
                    # 这里可以添加JQL生成结果的对比逻辑
                    try:
                        import json
                        model_b_content = model_b_result['content'].strip()
                        model_a_content = model_a_result['content'].strip()
                        
                        # 简单的JQL对比日志
                        if model_b_content != model_a_content:
                            logger.info("📊 JQL生成结果存在差异，建议人工评估")
                        else:
                            logger.info("✅ JQL生成结果一致")
                    except Exception as e:
                        logger.warning(f"JQL对比分析失败: {e}")

            # 解析LLM返回的JSON
            try:
                import json
                content = result['content']
                
                # 清理markdown代码块格式
                if content.startswith('```json'):
                    content = content[7:]  # 移除 ```json
                if content.startswith('```'):
                    content = content[3:]   # 移除 ```
                if content.endswith('```'):
                    content = content[:-3]  # 移除结尾的 ```
                
                # 去除首尾空白字符
                content = content.strip()
                
                # 更强力的JSON提取：寻找第一个完整的JSON对象
                import re
                
                # 尝试找到JSON对象的开始和结束
                json_start = content.find('{')
                if json_start == -1:
                    raise json.JSONDecodeError("No JSON object found", content, 0)
                
                # 从第一个{开始，找到匹配的}
                brace_count = 0
                json_end = -1
                for i, char in enumerate(content[json_start:], json_start):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            json_end = i + 1
                            break
                
                if json_end == -1:
                    raise json.JSONDecodeError("Incomplete JSON object", content, json_start)
                
                # 提取纯JSON部分
                json_content = content[json_start:json_end]
                
                # 移除JSON中的注释行（// 开头的行）
                lines = json_content.split('\n')
                clean_lines = []
                for line in lines:
                    # 检查是否在字符串内部
                    comment_pos = line.find('//')
                    if comment_pos != -1:
                        # 简单检查：统计注释前的引号数量
                        before_comment = line[:comment_pos]
                        quote_count = before_comment.count('"') - before_comment.count('\\"')
                        # 如果引号数量为偶数，说明注释在字符串外部
                        if quote_count % 2 == 0:
                            line = line[:comment_pos].rstrip()
                    
                    if line.strip():  # 只保留非空行
                        clean_lines.append(line)
                
                json_content = '\n'.join(clean_lines)
                
                # 解析JSON
                jql_data = json.loads(json_content)
                
                # 如果有对比信息，添加到结果中
                if 'comparison_details' in result:
                    jql_data['_comparison_details'] = result['comparison_details']
                    jql_data['_primary_model'] = result.get('primary_model')
                
                return {
                    'success': True,
                    'jql_data': jql_data
                }
            except json.JSONDecodeError as e:
                logger.error(f"JQL生成JSON解析失败: {str(e)}, 内容: {result['content']}")
                return {
                    'success': False,
                    'error': f"JQL生成结果解析失败: {str(e)}"
                }
                
        except Exception as e:
            logger.error(f"JQL生成异常: {str(e)}")
            return {
                'success': False,
                'error': f"JQL生成异常: {str(e)}"
            }

    def _fix_jql_email_issues(self, jql: str, user_query: str, user_email: str = None) -> str:
        """修复JQL中的邮箱地址问题"""
        import re
        
        # 如果提供了用户邮箱，将currentUser()替换为实际邮箱
        if user_email and 'currentUser()' in jql:
            logger.info(f"🔄 替换currentUser()为用户实际邮箱: {user_email}")
            jql = jql.replace('currentUser()', f'"{user_email}"')
            return jql
        
        # 首先保护 currentUser() 函数，避免被错误修改
        if 'currentUser()' in jql:
            # 如果JQL中已经有正确的currentUser()，不要修改它
            logger.info(f"🔒 JQL包含currentUser()函数，保持不变: {jql}")
            return jql
        
        # 更简单的方法：使用split和join来处理
        # 先按AND分割JQL，然后查找assignee条件
        jql_parts = [part.strip() for part in jql.split(' AND ')]
        assignee_parts = []
        other_parts = []
        
        for part in jql_parts:
            if part.lower().startswith('assignee'):
                assignee_parts.append(part)
            else:
                other_parts.append(part)
        
        # 如果有多个assignee条件，说明有重复
        if len(assignee_parts) > 1:
            logger.warning(f"🚨 检测到重复assignee条件: {assignee_parts}")
            
            # 如果用户查询包含具体邮箱地址，优先使用具体邮箱
            # 修复正则表达式，确保只匹配邮箱地址格式
            email_pattern = r'([a-zA-Z][a-zA-Z0-9\.]*@[a-zA-Z0-9\.]+\.[a-zA-Z]{2,})'
            email_match = re.search(email_pattern, user_query)
            if email_match:
                email = email_match.group(1)
                # 使用用户指定的邮箱作为唯一的assignee条件
                correct_assignee = f'assignee = "{email}"'
                
                # 重新组装JQL
                all_parts = [correct_assignee] + other_parts
                result_jql = ' AND '.join([part for part in all_parts if part.strip()])
                
                logger.info(f"🔧 重复assignee修复: 使用用户指定的邮箱 {email}")
                return result_jql
        
        # 如果用户查询包含"我的"、"我有"、"分配给我"等个人相关词汇，强制使用用户邮箱
        personal_keywords = ["我的", "我有", "我上周", "我这周", "我本月", "分配给我", "我完成"]
        is_personal_query = any(keyword in user_query for keyword in personal_keywords)
        
        if is_personal_query:
            # 如果有用户邮箱，使用用户邮箱，否则使用currentUser()
            if user_email:
                replacement = f'assignee = "{user_email}"'
            else:
                replacement = 'assignee = currentUser()'
                
            # 查找并替换邮箱地址
            email_pattern = r'assignee\s*=\s*["\']?[\w\.]+@[\w\.]+["\']?'
            if re.search(email_pattern, jql):
                fixed_jql = re.sub(email_pattern, replacement, jql)
                logger.info(f"🔧 个人查询修复: 使用{'用户邮箱' if user_email else 'currentUser()'}")
                return fixed_jql
        
        # 处理已经包含完整邮箱地址的情况（确保邮箱地址被双引号包围）
        full_email_pattern = r'assignee\s*=\s*(["\']?)([\w\.]+@[\w\.]+)\1'
        def fix_full_email(match):
            quote = match.group(1)
            email = match.group(2)
            # 如果没有引号或者是单引号，则用双引号包围
            if not quote or quote == "'":
                return f'assignee = "{email}"'
            else:
                return match.group(0)  # 已经有双引号，保持不变
        
        if re.search(full_email_pattern, jql):
            fixed_jql = re.sub(full_email_pattern, fix_full_email, jql)
            logger.info(f"🔧 邮箱转义修复: {jql} → {fixed_jql}")
            return fixed_jql
        
        # 识别并转换用户名为完整邮箱地址（处理不含@的用户名）
        # 重要：排除currentUser函数，避免将其误识别为用户名
        username_pattern = r'assignee\s*=\s*["\']?([a-zA-Z][a-zA-Z0-9\.]{2,19})["\']?(?!@)(?!\(\))'
        
        def convert_username_to_email(match):
            username = match.group(1)
            # 确保用户名不包含@符号，不是currentUser，并且符合用户名规范
            if '@' not in username and username.lower() != 'currentuser' and re.match(r'^[a-zA-Z][a-zA-Z0-9\.]{2,19}$', username):
                email = f'{username}@shopee.com'
                logger.info(f"🔧 用户名转换: {username} → {email}")
                return f'assignee = "{email}"'
            return match.group(0)  # 不符合条件，保持原样
        
        if re.search(username_pattern, jql):
            fixed_jql = re.sub(username_pattern, convert_username_to_email, jql)
            logger.info(f"🔧 JQL修复: {jql} → {fixed_jql}")
            return fixed_jql
        
        # 修复未转义的邮箱地址（直接包含@但没有引号）
        unescaped_email_pattern = r'assignee\s*=\s*([a-zA-Z][\w\.]*@[\w\.]+)(?=[^"\'])'
        def escape_email(match):
            email = match.group(1)
            return f'assignee = "{email}"'
        
        if re.search(unescaped_email_pattern, jql):
            fixed_jql = re.sub(unescaped_email_pattern, escape_email, jql)
            logger.info(f"🔧 邮箱转义: {jql} → {fixed_jql}")
            return fixed_jql
        
        return jql

    async def _format_response(self, intent: str, user_query: str, jql: str,
                             jira_data: Dict, quick_format: bool = False, is_group_chat: bool = False) -> str:
        """格式化响应结果"""
        try:
            from .prompts.response_formatting import ResponseFormattingPrompts

            # 构建JQL信息（如果有的话）
            jql_info = ""
            if jql and jql.strip() and intent not in ['general_help', 'config_help', 'clear_context']:
                jql_info = f"\n\n🔍 **执行的JQL查询:**\n```\n{jql}\n```"

            # 对于简单查询，使用快速格式化
            if quick_format or not jira_data.get('issues'):
                if 'issues' in jira_data:
                    base_response = ResponseFormattingPrompts.format_quick_response(
                        intent, jira_data['issues'], is_group_chat
                    )
                    return base_response + jql_info
                else:
                    base_response = str(jira_data.get('message', '操作完成'))
                    return base_response + jql_info
            
            # 使用LLM进行智能格式化 - 使用流水线方法支持双模型对比
            system_prompt, user_prompt = ResponseFormattingPrompts.build_prompt(
                intent, user_query, jql, jira_data
            )
            
            result = await multi_llm_client.generate_for_pipeline_step(
                'response_formatting', user_prompt, system_prompt, max_retries=1
            )
            
            if result['success']:
                # 对于响应格式化，如果是双模型对比，直接返回合并的结果
                if 'comparison_details' in result:
                    # 这是双模型对比的结果，已经包含了两个模型的格式化结果
                    return result['content'] + jql_info
                else:
                    # 单模型结果
                    return result['content'] + jql_info
            else:
                # LLM格式化失败时使用快速格式化作为备选
                logger.warning(f"LLM格式化失败，使用快速格式化: {result['error']}")
                if 'issues' in jira_data:
                    base_response = ResponseFormattingPrompts.format_quick_response(
                        intent, jira_data['issues'], is_group_chat
                    )
                    return base_response + jql_info
                else:
                    return "查询完成，但结果格式化失败。" + jql_info
                    
        except Exception as e:
            logger.error(f"响应格式化异常: {str(e)}")
            return f"查询完成，但结果格式化异常: {str(e)}"

    def _extract_update_fields(self, extracted_info: Dict) -> Dict:
        """从提取信息中构建更新字段"""
        fields = {}
        
        if 'priority' in extracted_info and extracted_info['priority']:
            fields['priority'] = {'name': extracted_info['priority'][0]}
        
        if 'status' in extracted_info and extracted_info['status']:
            fields['status'] = {'name': extracted_info['status'][0]}
        
        # 可以扩展更多字段...
        
        return fields

    def _get_help_message(self) -> str:
        """获取帮助信息"""
        from ..bot_config import bot_config
        # 直接使用bot_config中的帮助信息
        return bot_config.generate_help_message('private')

    def _is_subtask_query(self, jql: str, user_query: str, extracted_info: Dict) -> bool:
        """判断是否为子任务查询"""
        # 检查JQL中是否包含sub-task类型
        if 'type = sub-task' in jql.lower() or 'type=sub-task' in jql.lower():
            return True
        
        # 检查用户查询中的关键词
        subtask_keywords = ['子任务', 'subtask', 'sub-task', 'sub task']
        user_query_lower = user_query.lower()
        if any(keyword in user_query_lower for keyword in subtask_keywords):
            return True
        
        # 检查提取信息中的类型
        if extracted_info.get('issuetype') and any('sub-task' in str(t).lower() for t in extracted_info['issuetype']):
            return True
        
        return False

    async def _update_session_context(self, session_id: str, user_query: str,
                                     intent: str, extracted_info: Dict, 
                                     query_result: Dict) -> None:
        """更新会话上下文"""
        try:
            from .conversation_manager import conversation_manager
            
            new_context = {
                'query': user_query,
                'intent': intent
            }
            
            # 添加JIRA键值到上下文
            if 'jira_keys' in extracted_info:
                new_context['jira_keys'] = extracted_info['jira_keys']
            
            # 添加当前话题
            if intent in ['query_epic', 'query_timeline']:
                if extracted_info.get('jira_keys'):
                    new_context['current_topic'] = extracted_info['jira_keys'][0]
            
            await conversation_manager.update_context(session_id, new_context)
            
        except Exception as e:
            logger.error(f"更新会话上下文失败: {str(e)}")

    async def _detect_intent(self, user_query: str, context: Dict, group_info: Dict = None, session_id: str = None) -> Dict:
        """
        意图识别和澄清处理
        
        Args:
            user_query: 用户查询
            context: 会话上下文
            group_info: 群组信息
            session_id: 会话ID（用于澄清管理）
            
        Returns:
            意图识别结果
        """
        try:
            from .conversation_manager import conversation_manager
            
            # 预处理：检查是否明确是创建子任务的请求
            # 如果包含JIRA单号和创建子任务关键词，优先识别为jira_write操作
            query_lower = user_query.lower()
            create_subtask_keywords = ["创建", "新建", "建立", "建单", "建子任务", "建subtask", "创建子任务"]
            has_create_keyword = any(keyword in query_lower for keyword in create_subtask_keywords)
            has_jira_key = re.search(r'[A-Z]+-\d+', user_query) is not None
            has_subtask_keyword = any(keyword in query_lower for keyword in ['子任务', 'subtask', 'sub-task', 'sub task'])
            
            # 如果明确是创建子任务的请求，进行部分预处理，但将语义理解部分交给大模型
            if has_jira_key and has_create_keyword:
                logger.info(f"🎯 预处理检测到可能的创建子任务请求: '{user_query}'")
                # 提取JIRA单号
                jira_key_match = re.search(r'([A-Z]+-\d+)', user_query)
                jira_key = jira_key_match.group(1) if jira_key_match else None
                
                # 不再在这里提取标题和工作量，而是交给大模型处理
                # 执行意图识别，但提供更明确的提示
                from .prompts.subtask_creation import SubtaskCreationPrompts
                
                # 构建子任务创建专用提示词
                system_prompt, user_prompt = SubtaskCreationPrompts.build_prompt(
                    user_query, context, group_info
                )
                
                # 调用LLM进行信息提取
                result = await multi_llm_client.generate_for_pipeline_step(
                    'subtask_creation', user_prompt, system_prompt, max_retries=2
                )
                
                if not result['success']:
                    return {
                        'success': False,
                        'error': result['error']
                    }
                
                # 解析LLM返回的JSON
                try:
                    import json
                    content = result['content']
                    
                    # 清理markdown代码块格式
                    if content.startswith('```json'):
                        content = content[7:]  # 移除 ```json
                    if content.startswith('```'):
                        content = content[3:]   # 移除 ```
                    if content.endswith('```'):
                        content = content[:-3]  # 移除结尾的 ```
                    
                    # 去除首尾空白字符
                    content = content.strip()
                    
                    # 尝试找到JSON对象的开始和结束
                    json_start = content.find('{')
                    if json_start == -1:
                        raise json.JSONDecodeError("No JSON object found", content, 0)
                    
                    # 从第一个{开始，找到匹配的}
                    brace_count = 0
                    json_end = -1
                    for i, char in enumerate(content[json_start:], json_start):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                json_end = i + 1
                                break
                    
                    if json_end == -1:
                        raise json.JSONDecodeError("Incomplete JSON object", content, json_start)
                    
                    # 提取纯JSON部分
                    json_content = content[json_start:json_end]
                    
                    # 移除JSON中的注释行
                    lines = json_content.split('\n')
                    clean_lines = []
                    for line in lines:
                        # 移除行内注释
                        comment_pos = line.find('//')
                        if comment_pos != -1:
                            # 检查//是否在字符串内
                            before_comment = line[:comment_pos]
                            quote_count = before_comment.count('"') - before_comment.count('\\"')
                            if quote_count % 2 == 0:  # //不在字符串内
                                line = line[:comment_pos].rstrip()
                        
                        if line.strip():  # 只保留非空行
                            clean_lines.append(line)
                    
                    json_content = '\n'.join(clean_lines)
                    
                    # 解析JSON
                    extracted_data = json.loads(json_content)
                    
                    # 检查是否成功提取信息
                    if extracted_data.get('success') and 'extracted_info' in extracted_data:
                        extracted_info = extracted_data['extracted_info']
                        
                        # 确保父单号一致（如果大模型提取的不同，以规则提取的为准）
                        if jira_key and 'parent_issue_key' in extracted_info:
                            extracted_info['parent_issue_key'] = jira_key
                        
                        # 记录提取结果
                        logger.info(f"🔍 大模型提取的子任务信息: {extracted_info}")
                        
                        # 构建意图结果
                        return {
                            'success': True,
                            'needs_clarification': extracted_data.get('needs_clarification', False),
                            'clarification_reason': extracted_data.get('clarification_reason'),
                            'intent_data': {
                                'intent': 'jira_write',
                                'confidence': 0.95,
                                'extracted_info': {
                                    'operation_type': 'create_subtask',
                                    **extracted_info
                                }
                            }
                        }
                    
                except Exception as e:
                    logger.error(f"解析大模型返回的子任务创建信息失败: {str(e)}")
                    # 解析失败时，继续使用通用意图识别
            
            # 1. 执行意图识别
            ai_context = conversation_manager.extract_context_for_ai(context)
            
            # 判断是否使用上下文
            use_context = conversation_manager.should_use_context(user_query, context)
            if not use_context:
                ai_context = {}  # 不使用上下文
            
            logger.info(f"🧠 开始意图识别，使用上下文: {use_context}, 上下文: {ai_context}")
            
            # 执行真正的意图识别（调用LLM）
            intent_result = await self._execute_intent_detection(user_query, ai_context, group_info)
            
            if not intent_result['success']:
                return intent_result
            
            intent_data = intent_result['intent_data']
            
            # 简化版本：直接返回意图识别结果，不进行复杂的澄清处理
            return {
                'success': True,
                'needs_clarification': False,
                'intent_data': intent_data
            }
            
        except Exception as e:
            logger.error(f"意图识别和澄清处理异常: {str(e)}")
            return {
                'success': False,
                'error': f"意图识别异常: {str(e)}"
            }

    async def _execute_intent_detection(self, user_query: str, context: Dict, group_info: Dict = None) -> Dict:
        """
        执行真正的意图识别逻辑
        
        Args:
            user_query: 用户查询
            context: AI上下文
            group_info: 群组信息
            
        Returns:
            意图识别结果
        """
        try:
            from .prompts.intent_detection import IntentDetectionPrompts
            
            # 构建意图识别提示词
            system_prompt, user_prompt = IntentDetectionPrompts.build_prompt(
                user_query, context, group_info
            )
            
            # 调用LLM进行意图识别 - 使用流水线方法支持双模型对比
            result = await multi_llm_client.generate_for_pipeline_step(
                'intent_detection', user_prompt, system_prompt, max_retries=2
            )
            
            if not result['success']:
                return {
                    'success': False,
                    'error': result['error']
                }
            
            # 记录对比信息（如果有的话）
            comparison_details = None
            primary_model = None
            if 'comparison_details' in result:
                comparison_details = result['comparison_details']
                primary_model = result.get('primary_model', 'Unknown')
                logger.info(f"🧠 意图识别对比 - 主要结果来自: {primary_model}")
                
                # 可以在这里添加更详细的对比分析（统一A/B命名，兼容旧键名）
                model_a_result = comparison_details.get('model_a_result', comparison_details.get('deepseek_result', {}))
                model_b_result = comparison_details.get('model_b_result', comparison_details.get('qwen_result', {}))
            
            # 解析LLM返回的JSON
            try:
                import json
                content = result['content']
                
                # 清理markdown代码块格式
                if content.startswith('```json'):
                    content = content[7:]  # 移除 ```json
                if content.startswith('```'):
                    content = content[3:]   # 移除 ```
                if content.endswith('```'):
                    content = content[:-3]  # 移除结尾的 ```
                
                # 去除首尾空白字符
                content = content.strip()
                
                # 尝试找到JSON对象的开始和结束
                json_start = content.find('{')
                if json_start == -1:
                    raise json.JSONDecodeError("No JSON object found", content, 0)
                
                # 从第一个{开始，找到匹配的}
                brace_count = 0
                json_end = -1
                for i, char in enumerate(content[json_start:], json_start):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            json_end = i + 1
                            break
                
                if json_end == -1:
                    raise json.JSONDecodeError("Incomplete JSON object", content, json_start)
                
                # 提取纯JSON部分
                json_content = content[json_start:json_end]
                
                # 移除JSON中的注释行（以//开头的行）
                lines = json_content.split('\n')
                clean_lines = []
                for line in lines:
                    # 移除行内注释
                    comment_pos = line.find('//')
                    if comment_pos != -1:
                        # 检查//是否在字符串内
                        before_comment = line[:comment_pos]
                        quote_count = before_comment.count('"') - before_comment.count('\\"')
                        if quote_count % 2 == 0:  # //不在字符串内
                            line = line[:comment_pos].rstrip()
                    
                    if line.strip():  # 只保留非空行
                        clean_lines.append(line)
                
                json_content = '\n'.join(clean_lines)
                
                intent_data = json.loads(json_content)
                
                # 验证intent字段，如果为空或无效，设置默认值
                intent = intent_data.get('intent', '').strip()
                if not intent or intent not in ['jira_query', 'jira_statistics', 'jira_write', 'schedule_management', 'document_processing', 'user_info_query', 'general']:
                    # 基于意图识别结果进行判断，而不是简单的关键词匹配
                    # 如果用户意图是jira_write类型，但缺少必要信息，则返回建单指南
                    if 'jira_write' in intent_data.get('possible_intents', []) or self._is_create_issue_intent(user_query):
                        # 如果是关于建单的问题，设置为jira_write并指定为create_issue
                        intent_data['intent'] = 'jira_write'
                        intent_data['confidence'] = 0.8
                        if not intent_data.get('extracted_info'):
                            intent_data['extracted_info'] = {}
                        intent_data['extracted_info']['operation_type'] = 'create_issue'
                        logger.info(f"🔄 检测到建单相关意图，fallback为jira_write/create_issue")
                    elif any(keyword in user_query.lower() for keyword in ['需求', '内容', '什么', '主要']):
                        if group_info and group_info.get('jira_keys'):
                            # 如果群组有JIRA单号，可能是查询需求内容
                            intent_data['intent'] = 'jira_query'
                            intent_data['confidence'] = 0.7
                            if not intent_data.get('extracted_info'):
                                intent_data['extracted_info'] = {}
                            intent_data['extracted_info']['jira_keys'] = group_info['jira_keys']
                            intent_data['extracted_info']['operation_type'] = 'read'
                            logger.info(f"🔄 意图为空，基于群组JIRA单号fallback为jira_query: {group_info['jira_keys']}")
                        else:
                            # 没有明确JIRA单号，使用general
                            intent_data['intent'] = 'general'
                            intent_data['confidence'] = 0.6
                            if not intent_data.get('extracted_info'):
                                intent_data['extracted_info'] = {}
                            intent_data['extracted_info']['operation_type'] = 'read'
                            logger.info("🔄 意图为空，fallback为general")
                    else:
                        # 其他情况默认为general
                        intent_data['intent'] = 'general'
                        intent_data['confidence'] = 0.5
                        if not intent_data.get('extracted_info'):
                            intent_data['extracted_info'] = {}
                        intent_data['extracted_info']['operation_type'] = 'read'
                        logger.info("🔄 意图为空，默认fallback为general")
                
                # 如果有对比信息，添加到intent_data中
                if comparison_details:
                    intent_data['_comparison_details'] = comparison_details
                    intent_data['_primary_model'] = primary_model
                
                return {
                    'success': True,
                    'intent_data': intent_data
                }
                
            except json.JSONDecodeError as e:
                logger.error(f"意图识别JSON解析失败: {str(e)}, 内容: {result['content']}")
                # 强化的fallback机制
                fallback_intent_data = self._create_fallback_intent_data(user_query, context, group_info)
                logger.info(f"🔄 使用fallback意图数据: {fallback_intent_data}")
                
                return {
                    'success': True,
                    'intent_data': fallback_intent_data
                }
                
        except Exception as e:
            logger.error(f"意图识别执行异常: {str(e)}")
            # 创建emergency fallback
            emergency_intent_data = self._create_emergency_fallback_intent_data(user_query)
            logger.info(f"🚨 使用紧急fallback意图数据: {emergency_intent_data}")
            
            return {
                'success': True,
                'intent_data': emergency_intent_data
            }
    
    def _create_fallback_intent_data(self, user_query: str, context: Dict, group_info: Dict = None) -> Dict:
        """创建fallback意图数据"""
        try:
            # 基于关键词分析确定意图
            query_lower = user_query.lower()
            
            # 检查是否是建单相关问题（使用更全面的意图判断）
            if self._is_create_issue_intent(query_lower):
                logger.info(f"🔄 检测到建单相关意图，fallback为jira_write/create_issue")
                return {
                    'intent': 'jira_write',
                    'confidence': 0.8,
                    'extracted_info': {
                        'operation_type': 'create_issue',
                        'fallback_reason': 'JSON解析失败，检测到建单相关意图',
                        'error_type': 'building_guide'
                    }
                }
            
            # 检查定时任务相关关键词
            schedule_keywords = ['定时', '每天', '每周', '每月', '提醒', '通知', '创建', '帮我', '设置']
            if any(keyword in query_lower for keyword in schedule_keywords):
                return {
                    'intent': 'schedule_management',
                    'confidence': 0.7,
                    'extracted_info': {
                        'keywords': [kw for kw in schedule_keywords if kw in query_lower],
                        'operation_type': 'create',
                        'fallback_reason': 'JSON解析失败，基于关键词分析'
                    }
                }
            
            # 检查JIRA查询关键词
            jira_keywords = ['查询', '看', '找', '我的', '任务', 'bug', '状态']
            if any(keyword in query_lower for keyword in jira_keywords):
                intent_data = {
                    'intent': 'jira_query',
                    'confidence': 0.6,
                    'extracted_info': {
                        'keywords': [kw for kw in jira_keywords if kw in query_lower],
                        'operation_type': 'read',
                        'fallback_reason': 'JSON解析失败，基于关键词分析'
                    }
                }
                
                # 如果有群组JIRA信息，添加进去
                if group_info and group_info.get('jira_keys'):
                    intent_data['extracted_info']['jira_keys'] = group_info['jira_keys']
                
                return intent_data
            
            # 检查统计分析关键词
            stats_keywords = ['统计', '分析', '多少', '数量', '汇总', '报告']
            if any(keyword in query_lower for keyword in stats_keywords):
                return {
                    'intent': 'jira_statistics',
                    'confidence': 0.6,
                    'extracted_info': {
                        'keywords': [kw for kw in stats_keywords if kw in query_lower],
                        'operation_type': 'read',
                        'fallback_reason': 'JSON解析失败，基于关键词分析'
                    }
                }
            
            # 默认为general
            return {
                'intent': 'general',
                'confidence': 0.5,
                'extracted_info': {
                    'keywords': [],
                    'operation_type': 'read',
                    'fallback_reason': 'JSON解析失败，默认为general'
                }
            }
            
        except Exception as e:
            logger.error(f"创建fallback意图数据失败: {str(e)}")
            return self._create_emergency_fallback_intent_data(user_query)
    
    def _create_emergency_fallback_intent_data(self, user_query: str) -> Dict:
        """创建紧急fallback意图数据"""
        return {
            'intent': 'general',
            'confidence': 0.3,
            'extracted_info': {
                'keywords': [],
                'operation_type': 'read',
                'fallback_reason': '系统异常，使用紧急fallback',
                'original_query': user_query
            }
        }

    def _is_create_issue_intent(self, query: str) -> bool:
        """判断是否为建单相关意图"""
        # 检查是否包含建单指南相关关键词
        query_lower = query.lower()
        guide_keywords = ["建单指南", "怎么建单", "如何建单", "建单流程", "建单步骤", "建单方法", "建单规则", "建单标准"]
        if any(keyword in query_lower for keyword in guide_keywords):
            return True
        
        # 检查是否包含JIRA单号
        has_jira_key = re.search(r'[A-Z]+-\d+', query) is not None
        
        # 检查是否包含创建关键词
        create_keywords = ["创建", "新建", "建立", "建单", "建子任务", "建subtask", "建 subtask", "创建子任务", "新建子任务", "新建 subtask"]
        has_create_keyword = any(keyword in query_lower for keyword in create_keywords)
        
        # 如果同时包含JIRA单号和创建关键词，可能是创建子任务的意图，不是建单指南查询
        if has_jira_key and has_create_keyword:
            # 检查是否包含"子任务"或"subtask"关键词，如果有则不是建单指南查询
            subtask_keywords = ['子任务', 'subtask', 'sub-task', 'sub task']
            if any(keyword in query_lower for keyword in subtask_keywords):
                return False
                
            # 特殊处理：包含"bug"关键词但明确是创建子任务的情况
            # 例如："在SPCB-12345下创建一个bug验证的子任务"
            if "bug" in query_lower and any(pattern in query_lower for pattern in ["验证", "检查", "确认", "测试"]):
                # 这是创建子任务的意图，而不是建单指南查询
                return False
        
        return False

    async def _process_by_intent(self, intent: str, extracted_info: Dict, 
                               user_query: str, user_email: str, group_info: Dict, user_id: str = None) -> Dict:
        """
        根据意图类型执行相应处理
        
        Args:
            intent: 意图类型
            extracted_info: 提取的信息
            user_query: 用户查询
            user_email: 用户邮箱
            group_info: 群组信息
            
        Returns:
            处理结果
        """
        try:
            if intent == 'jira_query':
                return await self._handle_jira_query(extracted_info, user_query, user_email, group_info)
            
            elif intent == 'jira_statistics':
                return await self._handle_jira_statistics(extracted_info, user_query, user_email)
            
            elif intent == 'jira_write':
                return await self._handle_jira_write(extracted_info, user_query, user_email, group_info)
            
            elif intent == 'schedule_management':
                return await self._handle_schedule_management(extracted_info, user_query, user_email, user_id, group_info)
            
            elif intent == 'general':
                return await self._handle_general_query(extracted_info, user_query, group_info)

            elif intent == 'user_info_query':
                return await self._handle_user_info_query(extracted_info, user_query, user_email, user_id)

            elif intent == 'document_processing':
                return await self._handle_document_processing(extracted_info, user_query)
            
            else:
                return {
                    'success': False,
                    'error': f"未知的意图类型: {intent}"
                }
                
        except Exception as e:
            logger.error(f"意图处理异常 [{intent}]: {str(e)}")
            return {
                'success': False,
                'error': f"处理异常: {str(e)}"
            }

    async def _handle_jira_query(self, extracted_info: Dict, user_query: str, 
                               user_email: str, group_info: Dict) -> Dict:
        """处理JIRA查询类意图"""
        try:
            # 合并群组信息到提取信息中
            if group_info.get('jira_keys'):
                if not extracted_info.get('jira_keys'):
                    extracted_info['jira_keys'] = group_info['jira_keys']
                else:
                    # 合并JIRA单号
                    all_keys = list(set(extracted_info['jira_keys'] + group_info['jira_keys']))
                    extracted_info['jira_keys'] = all_keys
            
            # 检查是否为PRD/TRD相关查询
            is_prd_trd_query = self._is_prd_trd_query(user_query, extracted_info)
            
            # 1. 生成JQL
            jql_result = await self._generate_jql('jira_query', extracted_info, user_query)
            if not jql_result['success']:
                return jql_result
            
            jql_data = jql_result['jql_data']
            jql = jql_data['jql']
            
            # 2. 确定查询字段 - 检查是否为子任务查询
            intent_type = 'jira_query'
            fields = jql_data.get('fields_needed', SmartJiraQuery.get_optimal_fields('jira_query'))
            
            # 如果是PRD/TRD查询，确保包含customfield_16700
            if is_prd_trd_query:
                if 'customfield_16700' not in fields:
                    fields.append('customfield_16700')
                logger.info(f"🔍 检测到PRD/TRD查询，添加customfield_16700字段")
            
            # 检查是否为子任务查询，确保包含必要字段
            if self._is_subtask_query(jql, user_query, extracted_info):
                intent_type = 'query_subtask'  # 修正意图类型
                subtask_fields = [
                    'key', 'summary', 'status', 'priority', 'assignee', 'reporter', 
                    'created', 'updated', 'issuetype',
                    'customfield_16300',  # Planned Start Date
                    'customfield_16301',  # Planned Due Date
                    'customfield_10100',  # Story Points
                    'customfield_10004',  # Story Point Estimate (备用)
                ]
                fields = subtask_fields
                logger.info(f"🎯 检测到子任务查询，使用专用字段: {subtask_fields}")
            
            # 3. 执行JIRA查询
            jira_query = SmartJiraQuery(user_email)
            query_result = await jira_query.execute_jql(jql, fields)
            
            if not query_result['success']:
                return {
                    'success': False,
                    'error': f"JIRA查询失败: {query_result['error']}"
                }
            
            # 4. 如果是PRD/TRD查询且有结果，尝试获取并处理文档
            if is_prd_trd_query and query_result.get('data', {}).get('issues'):
                doc_result = await self._handle_prd_trd_document_processing(
                    query_result['data'], user_query, extracted_info
                )
                if doc_result['success']:
                    return doc_result
                else:
                    # 如果文档处理失败，记录错误但继续返回JIRA查询结果
                    logger.warning(f"文档处理失败: {doc_result['error']}")
            
            # 5. 格式化响应 - 使用正确的意图类型
            response = await self._format_response(
                intent_type, user_query, jql, 
                query_result['data'], query_result.get('quick_format', False)
            )
            
            # 构建返回结果
            result = {
                'success': True,
                'response': response,
                'extra_data': {
                    'jql': jql,
                    'result_count': query_result.get('result_count', 0),
                    'jira_data': query_result['data'],
                    'query_type': intent_type
                }
            }
            
            # 如果JQL生成有对比信息，添加到extra_data中
            if jql_result.get('jql_data', {}).get('_comparison_details'):
                result['extra_data']['_comparison_details'] = jql_result['jql_data']['_comparison_details']
                result['extra_data']['_primary_model'] = jql_result['jql_data'].get('_primary_model')
            
            return result
            
        except Exception as e:
            logger.error(f"JIRA查询处理异常: {str(e)}")
            return {
                'success': False,
                'error': f"JIRA查询处理异常: {str(e)}"
            }

    async def _handle_jira_statistics(self, extracted_info: Dict, user_query: str, user_email: str) -> Dict:
        """处理JIRA数据统计类意图"""
        try:
            if not user_email:
                return {
                    'success': False,
                    'error': "数据统计功能需要用户身份验证，请确保您已正确登录。"
                }
            
            # 使用统计分析模块
            from .jira_statistics import jira_statistics
            from .multi_llm_client import multi_llm_client
            
            # 记录实际意图
            logger.info(f"🎯 处理JIRA统计意图 - 查询: '{user_query}'")
            
            # 首先生成JQL查询
            jql_result = await self._generate_jql('jira_query', extracted_info, user_query)
            
            if not jql_result['success']:
                return {
                    'success': False,
                    'error': f"JQL生成失败: {jql_result['error']}"
                }
            
            jql_data = jql_result['jql_data']
            jql = jql_data.get('jql', '')
            fields = jql_data.get('fields', [])
            
            # 执行JIRA查询获取数据
            jira_query = SmartJiraQuery(user_email)
            query_result = await jira_query.execute_jql(jql, fields)
            
            if not query_result['success']:
                return {
                    'success': False,
                    'error': f"数据查询失败: {query_result['error']}"
                }
            
            # 获取统计类型
            statistics_type = extracted_info.get('statistics_type', 'count')
            field = extracted_info.get('group_by') or extracted_info.get('field')
            time_range = extracted_info.get('time_range')
            
            # 记录统计分析开始
            logger.info(f"📊 开始统计分析 - 类型: {statistics_type}, 字段: {field}")
            
            # 执行统计分析
            analysis_result = await jira_statistics.analyze_data(
                query_result['data'], 
                statistics_type, 
                field, 
                time_range
            )
            
            if not analysis_result['success']:
                return analysis_result
            
            # 格式化统计结果
            response = self._format_statistics_response(analysis_result, user_query)
            
            # 构建返回结果
            result_data = {
                'success': True,
                'response': response,
                'extra_data': {
                    'statistics': analysis_result.get('data', {}),
                    'chart_data': analysis_result.get('chart_data'),
                    'analysis_type': analysis_result.get('analysis_type'),
                    'summary': analysis_result.get('summary')
                },
                'intent': 'jira_statistics'  # 明确设置意图类型
            }
            
            # 如果JQL生成有对比信息，传递到结果中
            if jql_result.get('jql_data', {}).get('_comparison_details'):
                result_data['extra_data']['_comparison_details'] = jql_result['jql_data']['_comparison_details']
                result_data['extra_data']['_primary_model'] = jql_result['jql_data'].get('_primary_model')
                logger.info(f"✅ 已添加JQL对比信息到统计结果中")
            
            # 记录统计分析完成
            logger.info(f"✅ 统计分析完成 - 成功: {result_data['success']}")
            
            return result_data
            
        except Exception as e:
            logger.error(f"JIRA统计处理异常: {str(e)}")
            return {
                'success': False,
                'error': f"统计分析处理异常: {str(e)}"
            }

    async def _handle_jira_write(self, extracted_info: Dict, user_query: str, user_email: str, group_info: Dict = None) -> Dict:
        """处理JIRA写操作类意图"""
        try:
            if not user_email:
                return {
                    'success': False,
                    'error': "写操作需要用户身份验证，请确保您已正确登录。"
                }
            
            # 创建JIRA查询对象，不再检查权限
            jira_query = SmartJiraQuery(user_email)
            jira_query.last_query = user_query  # 保存当前查询
            # 移除权限检查，允许所有用户进行写操作
            
            # 检查操作类型
            operation_type = extracted_info.get('operation_type')
            
            if operation_type == 'create_subtask':
                # 创建子任务
                return await self._handle_create_subtask(extracted_info, jira_query, user_query, group_info)
            elif 'comment_text' in extracted_info:
                # 添加评论
                return await self._handle_add_comment(extracted_info, jira_query)
            elif 'priority' in extracted_info or 'status' in extracted_info:
                # 更新字段
                return await self._handle_update_fields(extracted_info, jira_query)
            # 检查是否是关于建单的问题
            elif operation_type == 'create_issue' or '建单' in user_query or '如何建单' in user_query or '怎么建单' in user_query:
                # 返回简洁的建单指南，使用代码引用格式
                return {
                    'success': False,
                    'error': "📝 **建单指令格式说明：**\n"
                             "```\n"
                             "• 基本格式：在[JIRA单号]建单：[子任务标题] [工作量]\n"
                             "• 特殊格式：在[JIRA单号]建单：--[类型] [工作量] (自动组合父任务标题)\n"
                             "```\n\n"
                             "📋 **示例：**\n"
                             "```\n"
                             "• 在SPCB-12345建单：完成登录功能测试 1d\n"
                             "• 建个单：--测试任务 2d (在群聊中自动提取JIRA单号)\n"
                             "• SPCB-12345建单：--设计文档 0.5d (自动组合父任务标题)\n"
                             "```\n\n"
                             "🔄 **私聊与群聊的差异：**\n"
                             "• **私聊模式**：必须明确指定父单号，如：`在SPCB-12345建单：...`\n"
                             "• **群聊模式**：如果群名包含JIRA单号，可以省略父单号，直接使用：`建单：...`\n"
                             "• **群聊智能提取**：系统会自动从群名中提取JIRA单号作为父单号",
                    'error_type': 'building_guide'  # 添加错误类型标记，避免被错误处理函数修改
                }
            else:
                return {
                    'success': False,
                    'error': "暂不支持该类型的写操作，请提供更具体的操作描述。"
                }
                
        except Exception as e:
            logger.error(f"JIRA写操作处理异常: {str(e)}")
            return {
                'success': False,
                'error': f"写操作处理异常: {str(e)}"
            }

    async def _handle_create_subtask(self, extracted_info: Dict, jira_query: SmartJiraQuery, user_query: str, group_info: Dict = None) -> Dict:
        """处理创建子任务的请求，确保使用用户邮箱替代currentUser()"""
        """处理创建子任务"""
        try:
            # 记录群组信息
            logger.info(f"📋 群组信息: {group_info}")
            
            # 检查必需参数
            parent_issue_key = extracted_info.get('parent_issue_key')
            logger.info(f"📋 从提取信息中获取的父单号: {parent_issue_key}")
            
            # 如果指令中没有指定父单号，但群组标题中包含JIRA单号，则使用群组中的单号
            if not parent_issue_key and group_info and group_info.get('jira_keys'):
                # 从群组信息中获取JIRA单号
                jira_keys = group_info.get('jira_keys', [])
                logger.info(f"📋 从群组信息中提取的JIRA单号: {jira_keys}")
                
                # 优先筛选SPCB和SPCT项目的单号
                spc_keys = [key for key in jira_keys if key.startswith('SPCB-') or key.startswith('SPCT-')]
                logger.info(f"📋 筛选后的SPCB/SPCT单号: {spc_keys}")
                
                if spc_keys:
                    # 优先使用SPCB或SPCT单号作为父单号
                    parent_issue_key = spc_keys[0]
                    logger.info(f"📎 从群组标题中提取SPCB/SPCT父单号: {parent_issue_key}")
                elif jira_keys:
                    # 如果没有SPCB或SPCT单号，则使用第一个JIRA单号
                    parent_issue_key = jira_keys[0]
                    logger.info(f"📎 从群组标题中提取其他项目父单号: {parent_issue_key}")
            
            if not parent_issue_key:
                return {
                    'success': False,
                    'error': "❌ 创建子任务失败：需要指定Epic或Task作为父单号\n\n"
                             "📝 **建单指令格式说明：**\n"
                             "```\n"
                             "• 基本格式：在[JIRA单号]建单：[子任务标题] [工作量]\n"
                             "• 特殊格式：在[JIRA单号]建单：--[类型] [工作量] (自动组合父任务标题)\n"
                             "```\n\n"
                             "📋 **示例：**\n"
                             "```\n"
                             "• 在SPCB-12345建单：完成登录功能测试 1d\n"
                             "• 建个单：--测试任务 2d (在群聊中自动提取JIRA单号)\n"
                             "• SPCB-12345建单：--设计文档 0.5d (自动组合父任务标题)\n"
                             "```\n\n"
                             "🔄 **私聊与群聊的差异：**\n"
                             "• **私聊模式**：必须明确指定父单号，如：`在SPCB-12345建单：...`\n"
                             "• **群聊模式**：如果群名包含JIRA单号，可以省略父单号，直接使用：`建单：...`\n"
                             "• **群聊智能提取**：系统会自动从群名中提取JIRA单号作为父单号，支持所有项目单号",
                    'error_type': 'building_guide'
                }
            
            subtask_summary = extracted_info.get('subtask_summary')
            if not subtask_summary:
                return {
                    'success': False,
                    'error': "❌ 创建子任务失败：需要提供子任务标题\n\n"
                             "📝 **建单指令格式说明：**\n"
                             "```\n"
                             "• 基本格式：在[JIRA单号]建单：[子任务标题] [工作量]\n"
                             "• 特殊格式：在[JIRA单号]建单：--[类型] [工作量] (自动组合父任务标题)\n"
                             "```\n\n"
                             "📋 **示例：**\n"
                             "```\n"
                             "• 在SPCB-12345建单：完成登录功能测试 1d\n"
                             "• 建个单：--测试任务 2d (在群聊中自动提取JIRA单号)\n"
                             "• SPCB-12345建单：--设计文档 0.5d (自动组合父任务标题)\n"
                             "```\n\n"
                             "🔄 **私聊与群聊的差异：**\n"
                             "• **私聊模式**：必须明确指定父单号，如：`在SPCB-12345建单：...`\n"
                             "• **群聊模式**：如果群名包含JIRA单号，可以省略父单号，直接使用：`建单：...`\n"
                             "• **群聊智能提取**：系统会自动从群名中提取JIRA单号作为父单号，支持所有项目单号",
                    'error_type': 'building_guide'
                }
            
            # 检查原始查询中是否包含"--"前缀的子任务标题
            # 例如："建个单：--case 设计 1d" 中的 "--case 设计"
            import re
            special_format_match = re.search(r'[:：]\s*(--.+?)(?:\s+\d+[dh天小时周]|\s*$)', user_query)
            
            # 如果原始查询中包含"--"前缀，但提取的标题中没有，则恢复前缀
            if special_format_match and not subtask_summary.startswith("--"):
                original_title = special_format_match.group(1).strip()
                logger.info(f"🔍 从原始查询中提取到特殊格式标题: '{original_title}'")
                subtask_summary = original_title
                # 更新extracted_info中的标题
                extracted_info['subtask_summary'] = subtask_summary
            
            # 检查子任务标题是否包含"--"前缀
            is_special_format = subtask_summary.startswith("--")
            logger.info(f"🔍 子任务标题: '{subtask_summary}', 是否为特殊格式: {is_special_format}")
            
            # 解析工作量
            story_points = self._parse_story_points(extracted_info.get('story_points', 1))
            
            # 解析日期
            planned_start_date = self._parse_date(extracted_info.get('planned_start_date'))
            planned_due_date = self._parse_date(extracted_info.get('planned_due_date'))
            
            # 构建子任务数据
            subtask_data = {
                'summary': subtask_summary,
                'story_points': story_points,
                'assignee': extracted_info.get('assignee', 'currentUser()'),
                'description': extracted_info.get('description', subtask_summary) or '',  # 确保description是字符串
                'planned_start_date': planned_start_date,
                'planned_due_date': planned_due_date,
                'original_query': user_query  # 添加原始查询文本
            }
            
            # 记录原始查询和解析结果
            logger.info(f"🔍 子任务创建 - 原始查询: '{user_query}'")
            logger.info(f"🔍 子任务创建 - 解析结果: 工作量={story_points}, 开始日期={planned_start_date}, 结束日期={planned_due_date}")
            
            # 检查父单号是否可能是Epic
            original_parent_key = parent_issue_key
            # 所有项目都可能是Epic，不再限制只有SPCB或SPCT
            is_epic_candidate = True
            
            # 尝试查找用户在Epic下的Task
            if is_epic_candidate:
                logger.info(f"🔍 检测到潜在Epic: {parent_issue_key}，尝试查找用户在Epic下的Task")
                
                # 查找用户在Epic下的Task
                task_result = await jira_query.find_user_task_in_epic(parent_issue_key)
                
                if task_result['success'] and task_result['tasks']:
                    # 找到了用户在Epic下的Task，使用第一个Task作为父任务
                    user_task = task_result['tasks'][0]
                    parent_issue_key = user_task['key']
                    logger.info(f"✅ 找到用户在Epic下的Task: {parent_issue_key}，将使用该Task作为父任务")
                else:
                    logger.info(f"ℹ️ 未找到用户在Epic下的Task，将使用原始父单号: {parent_issue_key}")
            
            # 如果是特殊格式的子任务标题（以"--"开头），获取父任务标题并组合
            original_summary = subtask_data['summary']
            if is_special_format:
                logger.info(f"🔍 检测到特殊格式的子任务标题: '{original_summary}'，将获取父任务标题")
                
                # 获取父任务标题
                parent_title_result = await jira_query.get_issue_title(parent_issue_key)
                
                if parent_title_result['success'] and parent_title_result['title']:
                    parent_title = parent_title_result['title']
                    logger.info(f"✅ 获取到父任务标题: '{parent_title}'")
                    
                    # 优化父任务标题，去掉" - "后面的内容
                    optimized_title = parent_title
                    dash_index = parent_title.find(" - ")
                    if dash_index > 0:
                        optimized_title = parent_title[:dash_index]
                        logger.info(f"✅ 优化后的父任务标题: '{optimized_title}'")
                    
                    # 组合新的标题: "优化后的父任务标题--子任务类型"
                    subtask_data['summary'] = f"{optimized_title}{original_summary}"
                    logger.info(f"✅ 组合后的子任务标题: '{subtask_data['summary']}'")
                else:
                    logger.warning(f"⚠️ 获取父任务标题失败: {parent_title_result.get('error', '未知错误')}，将使用原始标题")
            
            # 创建子任务
            result = await jira_query.create_subtask(parent_issue_key, subtask_data)
            
            if result['success']:
                # 如果使用了智能父任务选择，在响应中添加说明
                if parent_issue_key != original_parent_key:
                    result['smart_parent_selection'] = True
                    result['original_parent_key'] = original_parent_key
                
                response = self._format_subtask_creation_response(result, parent_issue_key)
                
                # 如果使用了智能父任务选择，添加说明
                if parent_issue_key != original_parent_key:
                    response += f"\n• 注意：子任务已智能创建在您负责的Task {parent_issue_key} 下，而不是直接在父单 {original_parent_key} 下"
                
                # 如果使用了特殊格式的子任务标题，添加说明
                if is_special_format:
                    # 如果优化了父任务标题，添加更详细的说明
                    if 'optimized_title' in locals() and optimized_title != parent_title:
                        response += f"\n• 检测到特殊格式的子任务标题（以\"--\"开头），已自动组合并优化为：\"{subtask_data['summary']}\""
                        response += f"\n  (原父任务标题: \"{parent_title}\")"
                    else:
                        response += f"\n• 检测到特殊格式的子任务标题（以\"--\"开头），已自动组合为：\"{subtask_data['summary']}\""
                
                return {
                    'success': True,
                    'response': response,
                    'subtask_key': result['key'],
                    'parent_key': parent_issue_key,
                    'original_parent_key': original_parent_key if parent_issue_key != original_parent_key else None
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"创建子任务异常: {str(e)}")
            return {
                'success': False,
                'error': f"创建子任务异常: {str(e)}"
            }

    def _parse_story_points(self, story_points_input):
        """解析工作量为数字"""
        if isinstance(story_points_input, (int, float)):
            return story_points_input
        
        if isinstance(story_points_input, str):
            # 移除空格
            story_points_input = story_points_input.strip()
            
            # 处理各种格式
            if story_points_input.endswith('d') or story_points_input.endswith('天'):
                # "2d" 或 "2天" 或 "0.5d"
                try:
                    # 提取数字部分
                    number_part = story_points_input[:-1].strip()
                    # 尝试转换为浮点数
                    value = float(number_part)
                    # 如果是整数值，转换为整数
                    if value.is_integer():
                        return int(value)
                    return value
                except ValueError:
                    return 1
            elif story_points_input.endswith('h') or story_points_input.endswith('小时'):
                # "8h" 或 "8小时" 或 "4.5h"
                try:
                    hours = float(story_points_input[:-1])
                    days = hours / 8  # 按8小时一天计算
                    # 如果是整数天，返回整数
                    if days.is_integer():
                        return int(days)
                    # 否则保留小数
                    return max(0.5, days)  # 最小0.5天
                except ValueError:
                    return 1
            elif story_points_input.endswith('周'):
                # "1周" 或 "0.5周"
                try:
                    weeks = float(story_points_input[:-1])
                    days = weeks * 5  # 一周按5天计算
                    # 如果是整数天，返回整数
                    if days.is_integer():
                        return int(days)
                    return days
                except ValueError:
                    return 1
            else:
                # 尝试直接解析为数字
                try:
                    value = float(story_points_input)
                    # 如果是整数值，转换为整数
                    if value.is_integer():
                        return int(value)
                    return value
                except ValueError:
                    return 1
        
        return 1

    def _parse_date(self, date_input) -> Optional[str]:
        """解析日期为JIRA格式"""
        if not date_input:
            return None
            
        if isinstance(date_input, str):
            from datetime import datetime, timedelta
            import re
            
            # 获取今天的日期
            today = datetime.now()
            ic(f"🗓️ 当前服务器日期: {today.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 处理相对日期
            if date_input in ['今天', 'today']:
                return today.strftime('%Y-%m-%d')
            elif date_input in ['明天', 'tomorrow']:
                return (today + timedelta(days=1)).strftime('%Y-%m-%d')
            elif date_input in ['昨天', 'yesterday']:
                return (today - timedelta(days=1)).strftime('%Y-%m-%d')
            elif date_input in ['后天']:
                return (today + timedelta(days=2)).strftime('%Y-%m-%d')
            elif date_input in ['前天']:
                return (today - timedelta(days=2)).strftime('%Y-%m-%d')
            elif date_input in ['下周一']:
                days_ahead = 7 - today.weekday()  # 0=Monday, 6=Sunday
                return (today + timedelta(days=days_ahead)).strftime('%Y-%m-%d')
            elif date_input in ['本周五']:
                days_ahead = 4 - today.weekday()  # 4=Friday
                if days_ahead < 0:  # 如果今天是周五之后
                    days_ahead += 7
                return (today + timedelta(days=days_ahead)).strftime('%Y-%m-%d')
            
            # 尝试解析绝对日期格式
            date_patterns = [
                r'(\d{4})-(\d{1,2})-(\d{1,2})',  # 2024-01-01
                r'(\d{1,2})-(\d{1,2})',  # 01-01 (当年)
                r'(\d{1,2})月(\d{1,2})日',  # 1月1日
            ]
            
            for pattern in date_patterns:
                match = re.match(pattern, date_input)
                if match:
                    try:
                        if len(match.groups()) == 3:
                            year, month, day = match.groups()
                            return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                        elif len(match.groups()) == 2:
                            month, day = match.groups()
                            return f"{today.year}-{month.zfill(2)}-{day.zfill(2)}"
                    except:
                        pass
        
        return date_input  # 返回原始输入，让后续处理逻辑尝试解析

    def _format_subtask_creation_response(self, result: Dict, parent_issue_key: str) -> str:
        """格式化子任务创建响应"""
        subtask_key = result['key']
        summary = result['summary']
        assignee = result['assignee']
        story_points = result['story_points']
        url = result['url']
        planned_start_date = result.get('planned_start_date', '')
        planned_due_date = result.get('planned_due_date', '')
        original_parent_key = result.get('original_parent_key')
        
        # 简化版响应格式
        response = f"✅ **子任务创建成功！**\n\n"
        response += f"• Summary:{summary}\n"
        response += f"• Url：{url}\n"
        
        # 显示父任务信息
        if original_parent_key:
            # 如果使用了智能父任务选择，同时显示Epic和直接父任务
            response += f"• 父任务：https://jira.shopee.io/browse/{parent_issue_key}\n"
            response += f"• 关联Epic：https://jira.shopee.io/browse/{original_parent_key}\n"
        else:
            # 如果没有使用智能父任务选择，只显示父任务
            response += f"• 父单：https://jira.shopee.io/browse/{parent_issue_key}\n"
        
        response += f"• 指派：{assignee} | 工作量：{story_points}d\n"
        
        # 添加计划日期信息
        if planned_start_date or planned_due_date:
            response += f"• 计划日期：开始 {planned_start_date} | 结束 {planned_due_date}\n"
        
        return response

    async def _handle_schedule_management(self, extracted_info: Dict, user_query: str, user_email: str, user_id: str = None, group_info: Dict = None) -> Dict:
        """处理定时任务管理意图"""
        try:
            from .natural_task_creator import natural_task_creator
            
            # 从group_info中提取group_id和group_title
            extracted_group_id = None
            group_title = None
            if group_info:
                if 'group_id' in group_info:
                    extracted_group_id = group_info['group_id']
                if 'title' in group_info:
                    group_title = group_info['title']
            
            # 检查是否为自然语言任务创建请求
            if natural_task_creator.is_natural_language_request(user_query):
                ic("🌟 检测到自然语言任务创建意图")
                # 获取会话ID
                session_result = await conversation_manager.get_or_create_session(
                    user_id or "unknown_user", None, None
                )
                if session_result['success']:
                    session_id = session_result['session']['session_id']
                    
                    return await natural_task_creator.process_natural_request(
                        user_query, user_id or "unknown_user", user_email, session_id, extracted_group_id
                    )
                else:
                    ic(f"❌ 获取会话失败: {session_result['error']}")
            
            # 否则调用现有的定时任务管理功能
            return await self._handle_schedule_command(user_query, user_id or "unknown_user", user_email, group_title, extracted_group_id)
            
        except Exception as e:
            ic(f"定时任务管理处理异常: {str(e)}")
            return {
                'success': False,
                'error': f"定时任务管理处理异常: {str(e)}"
            }

    async def _handle_general_query(self, extracted_info: Dict, user_query: str, group_info: Dict = None) -> Dict:
        """处理其他类意图"""
        try:
            # 首先检查是否为帮助相关查询
            if self._is_help_related_query(user_query):
                ic(f"🎯 检测到帮助相关查询，使用LLM帮助系统: {user_query}")

                # 使用LLM帮助系统处理
                try:
                    from ..llm_help_system.llm_help_manager import llm_help_manager

                    # 从extracted_info中获取用户信息
                    user_email = extracted_info.get('user_email', '')

                    # 根据group_info判断上下文类型
                    context_type = 'group' if group_info and group_info.get('group_id') else 'private'

                    help_message = await llm_help_manager.generate_help_response(
                        user_query=user_query,
                        context_type=context_type,  # 根据实际环境确定上下文类型
                        user_email=user_email,
                        user_role='normal'  # 默认普通用户，会根据邮箱自动调整
                    )

                    if help_message:
                        return {
                            'success': True,
                            'response': help_message,
                            'intent': 'general_help'
                        }
                except Exception as e:
                    ic(f"⚠️ LLM帮助系统调用失败，使用通用AI回答: {str(e)}")

            # 使用LLM直接回答非帮助类查询
            system_prompt = """你是一个智能助手，可以回答各种问题。请用中文回答，保持友好和专业的语调。

对于以下类型的问题，请提供有用的回答：
- 闲聊和问候
- 文本翻译
- 问题咨询
- 代码生成和解释
- 技术问题分析
- 一般性技术咨询

如果是关于JIRA的问题，建议用户使用具体的JIRA查询功能。"""
            
            result = await multi_llm_client.generate_with_retry(
                user_query, system_prompt, max_retries=2
            )
            
            if result['success']:
                return {
                    'success': True,
                    'response': result['content']
                }
            else:
                return {
                    'success': True,
                    'response': self._get_help_message()
                }
                
        except Exception as e:
            ic(f"通用查询处理异常: {str(e)}")
            return {
                'success': True,
                'response': "抱歉，我现在无法处理您的请求。请稍后再试或使用具体的JIRA查询功能。"
            }
    
    async def _handle_document_processing(self, extracted_info: Dict, user_query: str) -> Dict:
        """处理文档处理类意图"""
        try:
            # 提取JIRA键值（如果有的话）
            jira_key = None
            if extracted_info.get('jira_keys'):
                jira_key = extracted_info['jira_keys'][0]  # 取第一个
            
            # 提取目标语言（翻译时使用）
            target_language = extracted_info.get('target_language', '中文')
            
            # 调用文档集成器处理
            result = await document_integration.process_document_query(
                user_query=user_query,
                jira_key=jira_key,
                target_language=target_language
            )
            
            return result
            
        except Exception as e:
            ic(f"文档处理异常: {str(e)}")
            return {
                'success': False,
                'error': f"文档处理异常: {str(e)}"
            }

    def _format_statistics_response(self, analysis_result: Dict, user_query: str) -> str:
        """格式化统计分析响应"""
        summary = analysis_result.get('summary', '')
        data = analysis_result.get('data', {})
        analysis_type = analysis_result.get('analysis_type', 'unknown')
        chart_data = analysis_result.get('chart_data', {})
        
        response = f"📊 **JIRA数据统计分析**\n\n{summary}\n\n"
        
        # 根据分析类型添加详细信息
        if analysis_type == 'count':
            basic_stats = data.get('basic_stats', {})
            if basic_stats.get('statuses'):
                response += "**按状态统计：**\n"
                for status, count in list(basic_stats['statuses'].items())[:5]:
                    response += f"• {status}: {count}个\n"
                response += "\n"
        
        elif analysis_type == 'distribution':
            distribution = data.get('distribution', [])
            if distribution:
                response += f"**{data.get('field', '字段')}分布：**\n"
                for item in distribution[:5]:
                    response += f"• {item['value']}: {item['count']}个 ({item['percentage']}%)\n"
                response += "\n"
        
        elif analysis_type == 'trend':
            time_series = data.get('time_series', [])
            if time_series:
                response += "**趋势数据：**\n"
                for item in time_series[-5:]:  # 显示最近5个时间点
                    response += f"• {item['time']}: {item['count']}个\n"
                response += "\n"
        
        elif analysis_type == 'efficiency':
            avg_time = data.get('avg_resolution_time', 0)
            completion_rate = data.get('completion_rate', 0)
            if avg_time > 0:
                response += f"**效率指标：**\n"
                response += f"• 平均解决时间: {avg_time}天\n"
                response += f"• 完成率: {completion_rate}%\n\n"
        
        # 添加图表数据和提示
        if chart_data:
            response += "\n📈 **图表数据已生成，可用于可视化展示**\n"
            # 添加图表配置信息，便于前端渲染
            chart_type = chart_data.get('type', 'unknown')
            chart_title = chart_data.get('title', '统计图表')
            response += f"📊 图表类型: {chart_type} | 标题: {chart_title}\n"
            
            # 如果是饼图，显示数据概览
            if chart_type == 'pie' and chart_data.get('data'):
                response += "🥧 **饼图数据概览:**\n"
                for item in chart_data['data'][:3]:  # 显示前3项
                    response += f"• {item['name']}: {item['value']}\n"
                if len(chart_data['data']) > 3:
                    response += f"• ...还有{len(chart_data['data']) - 3}项\n"
            
            # 如果是柱状图，显示类别信息
            elif chart_type == 'bar' and chart_data.get('categories'):
                response += "📊 **柱状图类别:**\n"
                categories = chart_data['categories'][:5]  # 显示前5个类别
                response += f"• 包含类别: {', '.join(categories)}\n"
                if len(chart_data['categories']) > 5:
                    response += f"• ...还有{len(chart_data['categories']) - 5}个类别\n"
        
        return response

    async def _save_query_history(self, user_id: str, group_id: str, employee_code: str,
                                user_query: str, intent: str, processing_result: Dict) -> None:
        """保存查询历史"""
        try:
            # 如果有JIRA查询结果，保存到历史
            if 'jql' in processing_result.get('extra_data', {}):
                jira_query = SmartJiraQuery()
                await jira_query.save_query_history(
                    user_id, group_id, employee_code, user_query, 
                    intent, processing_result['extra_data']['jql'], processing_result
                )
        except Exception as e:
            logger.error(f"保存查询历史失败: {str(e)}")

    async def _handle_add_comment(self, extracted_info: Dict, jira_query: SmartJiraQuery) -> Dict:
        """处理添加评论操作"""
        try:
            jira_keys = extracted_info.get('jira_keys', [])
            comment_text = extracted_info.get('comment_text', '')
            
            if not jira_keys or not comment_text:
                return {
                    'success': False,
                    'error': "缺少JIRA单号或评论内容"
                }
            
            # 这里应该调用JIRA API添加评论
            # 暂时返回模拟结果
            return {
                'success': True,
                'response': f"✅ 已为 {', '.join(jira_keys)} 添加评论：{comment_text}"
            }
            
        except Exception as e:
            logger.error(f"添加评论失败: {str(e)}")
            return {
                'success': False,
                'error': f"添加评论失败: {str(e)}"
            }

    async def _handle_update_fields(self, extracted_info: Dict, jira_query: SmartJiraQuery) -> Dict:
        """处理字段更新操作"""
        try:
            jira_keys = extracted_info.get('jira_keys', [])
            
            if not jira_keys:
                return {
                    'success': False,
                    'error': "缺少JIRA单号"
                }
            
            update_fields = self._extract_update_fields(extracted_info)
            
            if not update_fields:
                return {
                    'success': False,
                    'error': "没有找到需要更新的字段"
                }
            
            # 这里应该调用JIRA API更新字段
            # 暂时返回模拟结果
            field_desc = ', '.join([f"{k}={v}" for k, v in update_fields.items()])
            return {
                'success': True,
                'response': f"✅ 已更新 {', '.join(jira_keys)} 的字段：{field_desc}"
            }
            
        except Exception as e:
            logger.error(f"更新字段失败: {str(e)}")
            return {
                'success': False,
                'error': f"更新字段失败: {str(e)}"
            }

    async def _generate_comparison_report(self, intent_result: Dict, jql_result: Dict, 
                                         format_result: str, user_query: str) -> str:
        """生成双模型对比的详细报告"""
        try:
            from .multi_llm_client import multi_llm_client
            model_a_name = multi_llm_client.model_a_display_name
            model_b_name = multi_llm_client.model_b_display_name
            report_parts = []
            
            # 检查是否有对比数据
            has_intent_comparison = intent_result.get('intent_data', {}).get('_comparison_details')
            has_jql_comparison = jql_result.get('jql_data', {}).get('_comparison_details')
            
            if not has_intent_comparison and not has_jql_comparison:
                return format_result  # 没有对比数据，直接返回格式化结果
            
            report_parts.append("🔄 **AI模型对比分析报告**\n")
            
            # 意图识别对比
            if has_intent_comparison:
                intent_details = intent_result['intent_data']['_comparison_details']
                primary_model = intent_result['intent_data'].get('_primary_model', 'Unknown')
                
                report_parts.append("## 🧠 **意图识别对比**")
                report_parts.append(f"**选择结果:** {primary_model}")
                
                # 兼容旧键，统一为A/B
                qwen_success = intent_details.get('qwen_success', intent_details.get('model_b_success', False))
                secondary_success = intent_details.get('secondary_success', intent_details.get('model_a_success', False))
                
                report_parts.append(f"- 🧭 {model_b_name}: {'✅成功' if qwen_success else '❌失败'}")
                report_parts.append(f"- 🤖 {model_a_name}: {'✅成功' if secondary_success else '❌失败'}")
                
                # 如果两个模型都成功，可以进行更详细的对比
                if qwen_success and secondary_success:
                    try:
                        qwen_content = (intent_details.get('model_b_result') or intent_details.get('qwen_result') or {}).get('content', '')
                        secondary_content = (intent_details.get('model_a_result') or intent_details.get('secondary_result') or {}).get('content', '')
                        # 简单的内容对比
                        if qwen_content.strip() == secondary_content.strip():
                            report_parts.append("- 📊 **结果一致性:** ✅ 两个模型识别结果完全一致")
                        else:
                            report_parts.append("- 📊 **结果一致性:** ⚠️ 两个模型识别结果存在差异")
                    except Exception as e:
                        report_parts.append(f"- 📊 **结果对比:** 对比分析失败 ({e})")
                
                report_parts.append("")
            
            # JQL生成对比
            if has_jql_comparison:
                jql_details = jql_result['jql_data']['_comparison_details']
                primary_model = jql_result['jql_data'].get('_primary_model', 'Unknown')
                
                report_parts.append("## 🔍 **JQL生成对比**")
                report_parts.append(f"**选择结果:** {primary_model}")
                
                qwen_success = jql_details.get('qwen_success', jql_details.get('model_b_success', False))
                secondary_success = jql_details.get('secondary_success', jql_details.get('model_a_success', False))
                
                report_parts.append(f"- 🧭 {model_b_name}: {'✅成功' if qwen_success else '❌失败'}")
                report_parts.append(f"- 🤖 {model_a_name}: {'✅成功' if secondary_success else '❌失败'}")
                
                # 如果两个模型都成功，对比JQL内容
                if qwen_success and secondary_success:
                    try:
                        import json
                        # 兼容旧键名，统一A/B内容
                        model_b_content = (jql_details.get('model_b_result') or jql_details.get('qwen_result') or {}).get('content', '').strip()
                        model_a_content = (jql_details.get('model_a_result') or jql_details.get('secondary_result') or {}).get('content', '').strip()
                        
                        # 解析两个模型的JQL结果
                        qwen_jql = None
                        secondary_jql = None
                        qwen_explanation = None
                        secondary_explanation = None
                        
                        # 解析B模型的结果
                        try:
                            # 清理JSON格式
                            qwen_clean = model_b_content.replace('```json', '').replace('```', '').strip()
                            if qwen_clean.startswith('{'):
                                json_start = qwen_clean.find('{')
                                json_end = qwen_clean.rfind('}') + 1
                                qwen_json_str = qwen_clean[json_start:json_end]
                                qwen_data = json.loads(qwen_json_str)
                                qwen_jql = qwen_data.get('jql', '')
                                qwen_explanation = qwen_data.get('explanation', '')
                        except Exception as e:
                            logger.warning(f"解析{model_b_name} JQL结果失败: {e}")
                            qwen_jql = "解析失败"
                        
                        # 解析次要模型的结果
                        try:
                            # 清理JSON格式
                            secondary_clean = model_a_content.replace('```json', '').replace('```', '').strip()
                            if secondary_clean.startswith('{'):
                                json_start = secondary_clean.find('{')
                                json_end = secondary_clean.rfind('}') + 1
                                secondary_json_str = secondary_clean[json_start:json_end]
                                secondary_data = json.loads(secondary_json_str)
                                secondary_jql = secondary_data.get('jql', '')
                                secondary_explanation = secondary_data.get('explanation', '')
                        except Exception as e:
                            logger.warning(f"解析{model_a_name} JQL结果失败: {e}")
                            secondary_jql = "解析失败"
                        
                        # 对比JQL
                        if qwen_jql and secondary_jql:
                            if qwen_jql.strip() == secondary_jql.strip():
                                report_parts.append("- 📊 **JQL一致性:** ✅ 两个模型生成的JQL完全一致")
                                report_parts.append(f"  - **共同JQL:** `{qwen_jql}`")
                            else:
                                report_parts.append("- 📊 **JQL一致性:** ⚠️ 两个模型生成的JQL存在差异")
                                report_parts.append("  - **详细对比:**")
                                report_parts.append(f"    - 🧭 **{model_b_name} JQL:** `{qwen_jql}`")
                                if qwen_explanation:
                                    report_parts.append(f"      *说明: {qwen_explanation}*")
                                report_parts.append(f"    - 🤖 **{model_a_name} JQL:** `{secondary_jql}`")
                                if secondary_explanation:
                                    report_parts.append(f"      *说明: {secondary_explanation}*")
                                
                                # 分析差异类型
                                diff_analysis = self._analyze_jql_differences(qwen_jql, secondary_jql)
                                if diff_analysis:
                                    report_parts.append(f"    - 🔍 **差异分析:** {diff_analysis}")
                        else:
                            report_parts.append("- 📊 **JQL对比:** ⚠️ 部分结果解析失败")
                            if qwen_jql:
                                report_parts.append(f"  - 🧭 **{model_b_name} JQL:** `{qwen_jql}`")
                            if secondary_jql:
                                report_parts.append(f"  - 🤖 **{model_a_name} JQL:** `{secondary_jql}`")
                        
                    except Exception as e:
                        report_parts.append(f"- 📊 **JQL对比:** 对比分析失败 ({e})")
                        # 即使解析失败，也尝试显示原始内容的前100字符
                        try:
                            qwen_preview = model_b_content[:100] + "..." if len(model_b_content) > 100 else model_b_content
                            secondary_preview = model_a_content[:100] + "..." if len(model_a_content) > 100 else model_a_content
                            report_parts.append(f"  - 🧭 **{model_b_name}原始结果:** {qwen_preview}")
                            report_parts.append(f"  - 🤖 **{model_a_name}原始结果:** {secondary_preview}")
                        except:
                            pass
                
                report_parts.append("")
            
            # 性能对比
            report_parts.append("## ⚡ **性能对比**")
            
            if has_intent_comparison:
                intent_details = intent_result['intent_data']['_comparison_details']
                qwen_time = intent_details.get('qwen_result', {}).get('response_time', 0)
                secondary_time = intent_details.get('secondary_result', {}).get('response_time', 0)
                report_parts.append(f"**意图识别耗时:** 🧭 {qwen_time:.2f}s | 🤖 {secondary_time:.2f}s")
            
            if has_jql_comparison:
                jql_details = jql_result['jql_data']['_comparison_details']
                qwen_time = jql_details.get('qwen_result', {}).get('response_time', 0)
                secondary_time = jql_details.get('secondary_result', {}).get('response_time', 0)
                report_parts.append(f"**JQL生成耗时:** 🧭 {qwen_time:.2f}s | 🤖 {secondary_time:.2f}s")
            
            report_parts.append("\n---\n")
            
            # 添加实际的查询结果
            report_parts.append("## 📋 **查询结果**")
            report_parts.append(format_result)
            
            return '\n'.join(report_parts)
            
        except Exception as e:
            logger.error(f"生成对比报告失败: {str(e)}")
            return format_result  # 失败时返回原始格式化结果

    def _analyze_jql_differences(self, model_b_jql: str, model_a_jql: str) -> str:
        """分析JQL差异"""
        try:
            # 简单的差异分析
            differences = []
            
            # 检查关键字差异
            qwen_lower = model_b_jql.lower()
            deepseek_lower = model_a_jql.lower()
            
            # 检查操作符差异
            if 'not in' in qwen_lower and '!=' in deepseek_lower:
                differences.append("操作符差异(NOT IN vs !=)")
            elif '!=' in qwen_lower and 'not in' in deepseek_lower:
                differences.append("操作符差异(!= vs NOT IN)")
            
            # 检查字段差异
            if 'assignee' in qwen_lower and 'assignee' not in deepseek_lower:
                differences.append("字段差异(assignee)")
            elif 'assignee' not in qwen_lower and 'assignee' in deepseek_lower:
                differences.append("字段差异(assignee)")
            
            # 检查状态值差异
            qwen_statuses = set()
            deepseek_statuses = set()
            
            # 简单提取状态值
            import re
            qwen_status_matches = re.findall(r'(?:status|Status)\s*(?:=|in|not in)\s*\(?([^)]+)\)?', model_b_jql, re.IGNORECASE)
            deepseek_status_matches = re.findall(r'(?:status|Status)\s*(?:=|in|not in)\s*\(?([^)]+)\)?', model_a_jql, re.IGNORECASE)
            
            if qwen_status_matches != deepseek_status_matches:
                differences.append("状态值差异")
            
            # 检查长度差异
            if abs(len(model_b_jql) - len(model_a_jql)) > 20:
                differences.append("查询复杂度差异")
            
            if differences:
                return ", ".join(differences)
            else:
                return "语法结构差异"
                
        except Exception as e:
            logger.warning(f"JQL差异分析失败: {e}")
            return "无法分析差异"

    def _is_prd_trd_query(self, user_query: str, extracted_info: Dict) -> bool:
        """判断是否为PRD/TRD文档查询"""
        # 1. 检查查询文本中的关键词
        query_lower = user_query.lower()
        prd_trd_keywords = ['prd', 'trd', '产品需求', '技术方案', '需求文档', '技术文档', 
                           'product requirement', 'technical requirement',
                           'design document', '设计文档']
        
        has_keyword = any(keyword in query_lower for keyword in prd_trd_keywords)
        
        # 2. 检查提取的信息中是否有文档相关标记
        info_has_doc = False
        if extracted_info:
            # 检查是否有文档ID或URL
            if extracted_info.get('document_ids') or extracted_info.get('document_urls'):
                info_has_doc = True
                
            # 检查是否有文档类型标记
            doc_type = extracted_info.get('document_type', '').lower()
            if doc_type and any(keyword in doc_type for keyword in ['prd', 'trd', 'requirement', 'design']):
                info_has_doc = True
        
        return has_keyword or info_has_doc
    
    async def _handle_prd_trd_document_processing(self, jira_data: Dict, user_query: str, extracted_info: Dict) -> Dict:
        """处理PRD/TRD文档查询"""
        try:
            # 提取JIRA单中的文档URL
            issues = jira_data.get('issues', [])
            if not issues:
                return {'success': False, 'error': "未找到相关JIRA单"}
            
            # 查找文档字段
            doc_urls = []
            for issue in issues:
                fields = issue.get('fields', {})
                # 检查customfield_16700字段 (PRD/TRD文档链接)
                doc_field = fields.get('customfield_16700')
                if doc_field and isinstance(doc_field, str):
                    # 提取URL
                    doc_urls.append(doc_field)
            
            if not doc_urls:
                return {'success': False, 'error': "未在JIRA单中找到文档链接"}
            
            # 使用文档处理模块处理
            from .document_integration import document_integration
            
            # 构建文档查询
            doc_query = f"处理以下文档: {' '.join(doc_urls)}。{user_query}"
            
            # 执行文档处理
            doc_result = await document_integration.process_document_query(
                user_query=doc_query,
                document_urls=doc_urls
            )
            
            if doc_result['success']:
                return {
                    'success': True,
                    'response': doc_result['response'],
                    'intent': 'document_processing',
                    **doc_result.get('metadata', {})
                }
            else:
                return {'success': False, 'error': doc_result['error']}
                
        except Exception as e:
            logger.error(f"PRD/TRD文档处理异常: {str(e)}")
            return {'success': False, 'error': f"文档处理异常: {str(e)}"}
            
    async def _handle_usage_command(self, user_query: str, user_id: str, user_email: str) -> Dict:
        """
        处理服务使用统计命令
        格式: usage [参数]
        
        Args:
            user_query: 完整的用户查询 (包含usage前缀)
            user_id: 用户ID
            user_email: 用户邮箱
            
        Returns:
            服务统计结果
        """
        try:
            # 提取实际的命令内容 (去掉 "usage " 前缀)
            if user_query.lower().startswith('usage '):
                command_text = user_query[6:].strip()  # 去掉 "usage " (6个字符)
            else:
                command_text = ""
            
            if not command_text or command_text.lower() in ['help', '帮助', 'h', '?']:
                return {
                    'success': True,
                    'response': self._get_usage_help_message(),
                    'intent': 'usage_help'
                }
            
            # 导入ServiceAnalytics
            from .service_analytics import ServiceAnalytics
            service_analytics = ServiceAnalytics()
            
            # 处理统计命令
            result = await service_analytics.process_analytics_command(command_text, user_id, user_email)
            
            if result['success']:
                return {
                    'success': True,
                    'response': result['response'],
                    'intent': 'service_analytics',
                    'analytics_type': result.get('analytics_type', 'user')
                }
            else:
                return {
                    'success': False,
                    'error': result['error'],
                    'intent': 'service_analytics'
                }
                
        except Exception as e:
            logger.error(f"服务统计命令处理异常: {str(e)}")
            return {
                'success': False,
                'error': f"服务统计命令处理异常: {str(e)}",
                'intent': 'service_analytics'
            }
    
    def _get_usage_help_message(self) -> str:
        """获取服务统计功能帮助信息"""
        return """📊 **服务使用统计**

**基本命令:**
• `usage` - 查看您的个人使用统计
• `usage summary` - 查看系统整体使用摘要
• `usage details` - 查看详细使用数据
• `usage feature <功能名>` - 查看特定功能的使用情况
• `usage period <天数>` - 查看最近N天的使用统计

**示例:**
• `usage` - 显示个人使用统计
• `usage summary` - 显示系统概览
• `usage details` - 显示详细统计
• `usage feature jira_query` - 查看JIRA查询功能使用情况
• `usage period 7` - 查看最近7天的使用统计

**注意:** 某些高级统计功能可能需要管理员权限。
"""

    async def _handle_todo_command(self, user_query: str, user_id: str, user_email: str) -> Dict:
        """
        处理Todo命令
        格式: todo <子命令> [参数]
        
        Args:
            user_query: 完整的用户查询 (包含todo前缀)
            user_id: 用户ID
            user_email: 用户邮箱
            
        Returns:
            Todo处理结果
        """
        try:
            # 提取实际的todo命令内容 (去掉 "todo " 前缀)
            command_text = user_query[5:].strip()  # 去掉 "todo " (5个字符)
            
            if not command_text or command_text.lower() in ['help', '帮助', 'h', '?']:
                return {
                    'success': True,
                    'response': self._get_todo_help_message(),
                    'intent': 'todo_help'
                }
            
            # 导入TodoManager
            from .todo_manager import TodoManager
            todo_manager = TodoManager()
            
            # 处理todo命令
            result = await todo_manager.process_command(command_text, user_id, user_email)
            
            if result['success']:
                return {
                    'success': True,
                    'response': result['response'],
                    'intent': 'todo_management',
                    'todo_action': result.get('action', 'unknown')
                }
            else:
                return {
                    'success': False,
                    'error': result['error'],
                    'intent': 'todo_management'
                }
                
        except Exception as e:
            logger.error(f"Todo命令处理异常: {str(e)}")
            return {
                'success': False,
                'error': f"Todo命令处理异常: {str(e)}",
                'intent': 'todo_management'
            }
    
    def _get_todo_help_message(self) -> str:
        """获取Todo功能帮助信息"""
        return """📋 **Todo 待办事项管理**

**基本命令:**
• `todo add <标题> [@分类] [@优先级] [@日期]` - 创建新待办事项
• `todo list [状态] [分类]` - 列出待办事项
• `todo done <ID>` - 完成待办事项
• `todo delete <ID>` - 删除待办事项
• `todo today` - 查看今日待办摘要

**示例:**
• `todo add 完成项目文档 @work @high @2023-12-31`
• `todo list pending work`
• `todo done 5`
• `todo delete 3`
• `todo today`

**参数说明:**
• 分类: 使用 `@分类名` 格式，如 `@work`, `@personal`
• 优先级: 使用 `@优先级` 格式，如 `@high`, `@medium`, `@low`
• 日期: 使用 `@YYYY-MM-DD` 格式，如 `@2023-12-31`
"""

    async def _handle_permission_command(self, user_query: str, user_id: str, user_email: str) -> Dict:
        """处理权限查询命令"""
        try:
            from .task_permission_manager import task_permission_manager
            
            parts = user_query.lower().split()
            
            if len(parts) == 1 or (len(parts) == 2 and parts[1] in ['info', 'check']):
                # 查看自己的权限信息
                if not user_email:
                    return {
                        'success': False,
                        'error': "需要用户邮箱信息才能查询权限"
                    }
                
                permission_info = task_permission_manager.get_user_permission_info(user_email)
                
                response = f"🔐 **您的权限信息**\n\n"
                response += f"👤 **用户**: {permission_info['user_email']}\n"
                response += f"🏷️ **角色**: {permission_info['role']}\n\n"
                
                # 根据角色显示不同的信息
                if permission_info['role'] == 'super_admin':
                    response += "🔴 **超级管理员权限**:\n"
                elif permission_info['role'] == 'project_manager':
                    response += f"🟡 **项目管理员权限** (管理项目: {', '.join(permission_info.get('managed_projects', []))})\n"
                else:
                    response += "🟢 **普通用户权限**:\n"
                
                for perm in permission_info['permissions']:
                    response += f"{perm}\n"
                
                response += "\n💡 **权限说明**:\n"
                response += "• 定时任务创建会根据您的权限进行检查\n"
                response += "• 普通用户只能创建发送给自己或指定项目群的通知\n"
                response += "• 项目管理员只能在管理的项目范围内创建任务\n"
                response += "• 超级管理员拥有所有权限\n\n"
                response += "📖 查看详细权限文档: `permission help`"
                
                return {
                    'success': True,
                    'response': response,
                    'intent': 'permission_info'
                }
            
            elif len(parts) >= 2 and parts[1] == 'help':
                # 权限帮助信息
                response = f"🔐 **权限管理系统帮助**\n\n"
                response += "## 🎯 **用户角色分级**\n\n"
                response += "### 🔴 **超级管理员** (Super Admin)\n"
                response += "• 用户: `<EMAIL>`\n"
                response += "• 权限: 无限制，可以创建任意范围的定时任务\n\n"
                
                response += "### 🟡 **项目管理员** (Project Manager - PJM)\n"
                response += "• 当前配置:\n"
                response += "  - `<EMAIL>` → 管理 `SPCB` 项目\n"
                response += "  - `<EMAIL>` → 管理 `SPCT` 项目\n"
                response += "• 权限: 只能在指定项目范围内创建任务\n\n"
                
                response += "### 🟢 **普通用户** (Regular User)\n"
                response += "• 权限: 只能创建发送给自己或指定项目群的通知\n"
                response += "• 限制: 不能创建影响他人的通知\n\n"
                
                response += "## 📝 **使用示例**\n\n"
                response += "### ✅ **允许的操作**\n"
                response += "```bash\n"
                response += "# 普通用户 - 自己的任务私聊通知\n"
                response += 'schedule create "我的bug提醒" "assignee = currentUser()" "daily 10:00"\n\n'
                response += "# 普通用户 - 发送到指定项目群\n"
                response += 'schedule create "SPCB bug通知" "project = SPCB" "daily 09:00"\n\n'
                response += "# 项目管理员 - 在管理项目内\n"
                response += 'schedule create "SPCB项目监控" "project = SPCB" "daily 10:00"\n'
                response += "```\n\n"
                
                response += "### ❌ **禁止的操作**\n"
                response += "```bash\n"
                response += "# 普通用户 - 全局查询\n"
                response += 'schedule create "全局监控" "issuetype = Bug" "daily 10:00"\n'
                response += "# 错误: 普通用户不能创建全局范围的任务\n\n"
                response += "# PJM - 超出管理范围\n"
                response += 'schedule create "SPCT监控" "project = SPCT" "daily 10:00"\n'
                response += "# 错误: 您没有权限操作项目: SPCT\n"
                response += "```\n\n"
                
                response += "## 🔍 **命令列表**\n"
                response += "• `permission` - 查看您的权限信息\n"
                response += "• `permission help` - 查看权限帮助\n"
                response += "• `permission check <jql>` - 检查特定查询的权限\n\n"
                
                response += "📖 **详细文档**: 查看 `TASK_PERMISSION_MANAGEMENT.md`"
                
                return {
                    'success': True,
                    'response': response,
                    'intent': 'permission_help'
                }
            
            elif len(parts) >= 3 and parts[1] == 'check':
                # 检查特定查询的权限
                if not user_email:
                    return {
                        'success': False,
                        'error': "需要用户邮箱信息才能检查权限"
                    }
                
                # 提取JQL查询 (去掉 "permission check " 部分)
                jql_query = user_query[len('permission check '):].strip()
                
                if not jql_query:
                    return {
                        'success': False,
                        'error': "请提供要检查的JQL查询，格式: permission check <JQL查询>"
                    }
                
                # 检查权限 (使用默认的私聊通知配置)
                permission_result = task_permission_manager.check_task_creation_permission(
                    user_email=user_email,
                    jql_query=jql_query,
                    notification_config={'target_type': 'private'},
                    group_title=None
                )
                
                response = f"🔍 **权限检查结果**\n\n"
                response += f"📝 **查询**: `{jql_query}`\n"
                response += f"👤 **用户**: {user_email}\n"
                response += f"🏷️ **角色**: {permission_result.user_role}\n\n"
                
                if permission_result.allowed:
                    response += "✅ **结果**: 允许\n"
                    response += f"📋 **原因**: {permission_result.reason}\n"
                else:
                    response += "❌ **结果**: 禁止\n"
                    response += f"📋 **原因**: {permission_result.reason}\n"
                    if permission_result.suggested_scope:
                        response += f"💡 **建议**: {permission_result.suggested_scope}\n"
                
                return {
                    'success': True,
                    'response': response,
                    'intent': 'permission_check'
                }
            
            else:
                return {
                    'success': False,
                    'error': f"未知的权限命令: {' '.join(parts[1:])}\n\n使用 `permission help` 查看帮助"
                }
                
        except Exception as e:
            logger.error(f"权限查询异常: {str(e)}")
            return {
                'success': False,
                'error': f"权限查询异常: {str(e)}"
            }


# 全局AI助手实例
ai_assistant = AIAssistant() 