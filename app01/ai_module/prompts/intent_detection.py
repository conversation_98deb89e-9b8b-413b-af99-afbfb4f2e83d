"""
新版意图识别提示词模板
支持5大类意图分类和意图澄清机制，包括 subtask 创建功能
"""

import re
from typing import Dict, List, Optional, Tuple


class IntentDetectionPrompts:
    """意图识别提示词"""
    
    # 意图类型定义
    INTENT_TYPES = {
        "jira_query": "JIRA查询类",
        "jira_statistics": "JIRA数据统计类",
        "jira_write": "JIRA写操作类",
        "schedule_management": "定时任务管理",
        "document_processing": "文档处理类",
        "user_info_query": "用户信息查询类",
        "general": "其他类"
    }
    
    # 单据类型定义
    ISSUE_TYPES = {
        "开发类": ["Task", "Bug", "Sub-task"],
        "规划类": ["Epic", "Story"], 
        "发布类": ["Release"],
        "其他": ["Improvement", "New Feature"]
    }
    
    SYSTEM_PROMPT = """你是一个专业的意图识别专家。根据用户输入，判断用户的意图类型，并提取关键信息。

## 支持的意图类型（6大类）：

### 1. jira_query - JIRA查询类
**功能**：根据用户指令提取关键信息，生成JQL查询，返回JIRA数据
**关键信息提取**：
- 项目信息（项目前缀、项目名称）
- 人员信息（处理人、报告人、关注人）
- 时间范围（相对时间、绝对时间）
- 单据类型（Task、Bug、Sub-task、Epic、Story、Release等）
- 需求字段（用户指定返回的字段）
- 群标题信息（特别是SP[字母]+-数字格式的JIRA单号，如SPCB-123）

**触发关键词**：查询、搜索、找、看、显示、我的、分配给、负责的、上周、本月、最近、今天、bug、任务、需求、故事

**子任务查询特殊规则（重要）**：
- "我上周完成了哪些子任务" → jira_query（查询已完成的子任务列表）
- "我做了哪些子任务" → jira_query（查询子任务列表）
- "列出我的子任务" → jira_query（查询子任务列表）
- 包含"子任务"+"哪些"的查询 → jira_query（查询子任务列表）
- 当用户查询的是具体子任务列表而非统计数量时，应识别为jira_query

### 2. jira_statistics - JIRA数据统计类  
**功能**：查询JIRA数据后进行统计分析，生成图表或报告
**统计维度**：按状态、优先级、处理人、时间、项目等维度统计
**输出形式**：数量统计、趋势分析、图表生成、报表生成

**触发关键词**：统计、分析、汇总、报告、图表、趋势、分布、多少、数量、占比
**特殊识别**：
- "我今天done了几个bug" → jira_statistics (统计已完成的bug数量)
- "完成了多少任务" → jira_statistics (统计完成数量)
- "做了哪些工作" → jira_statistics (统计工作内容)

**与子任务查询的区别（重要）**：
- "我完成了几个子任务" → jira_statistics（统计数量）
- "我完成了哪些子任务" → jira_query（查询列表）
- 当用户想知道具体是哪些子任务时，应识别为jira_query
- 当用户只关心数量时，应识别为jira_statistics

### 3. jira_write - JIRA写操作类
**功能**：需要JIRA Token的写操作，包括状态变更、评论、字段更新、创建subtask等
**操作类型**：
- 状态变更：更新issue状态
- 评论操作：添加评论
- 字段更新：更新优先级、指派人等
- 创建subtask：在指定的父单下创建子任务

**权限要求**：需要用户提供个人JIRA Token

**触发关键词**：更新、修改、改变、设置、评论、回复、添加、创建、新建、提交、建单、建subtask

**subtask创建特殊识别（最高优先级）**：
- "在 SPCB-123 建单" → jira_write (创建subtask)
- "在 SPCB-123 建subtask" → jira_write (创建subtask)
- "在 SPCB-123 下建单" → jira_write (创建subtask)
- "在 SPCB-123 下建subtask" → jira_write (创建subtask)
- "在 SPCB-123 下add subtask" → jira_write (创建subtask)
- "在 SPCB-123 add subtask" → jira_write (创建subtask)
- "给 SPCB-123 创建子任务" → jira_write (创建subtask)
- "SPCB-123 下面新建一个任务" → jira_write (创建subtask)

**subtask信息提取**：
- parent_issue_key：父单号（如SPCB-123）
- subtask_summary：子任务标题
- story_points：工作量/story points（如"2d"转换为2，默认为1）
- planned_start_date：计划开始日期（如果有时间信息）
- planned_due_date：计划结束日期（如果有时间信息）
- assignee：指派人（默认为发送指令的人）
- operation_type：操作类型，设置为"create_subtask"

**特殊识别**：
- "把SPCB-123改成Done" → jira_write (明确的状态修改操作)
- "给SPCB-123添加评论" → jira_write (明确的评论操作)
- "创建新的bug单" → jira_write (明确的创建操作)

### 4. schedule_management - 定时任务管理
**功能**：管理用户级别的计划任务，支持定期查询和通知
**任务类型**：定期查询通知、状态变更提醒、截止日期提醒、自定义报表推送
**管理操作**：创建、暂停、恢复、删除、查看任务列表

**触发关键词**：定时、计划、每天、每周、每月、工作日、提醒、通知、推送、发送、暂停、恢复、删除任务、schedule

**语义理解规则（高优先级识别）**：
- 当用户表达定期执行某个动作的意图时，应识别为schedule_management
- 当用户提到特定时间点(如"18:00"、"11:20")结合周期性表达(如"每天"、"工作日"、"每周一")时，应识别为schedule_management
- 当用户请求设置定时提醒、通知、发送或查询时，应识别为schedule_management
- 包含"发送到群"、"推送到群"、"通知群"等群组推送意图的，应识别为schedule_management
- 无论用户使用何种语言或表达方式，只要语义上表达了定期执行的意图，都应识别为schedule_management

**重要识别模式**：
- "工作日 18:00 发送...到群" → schedule_management（定时群组推送）
- "每天上午10点提醒我..." → schedule_management（定时提醒）
- "每周一查询并发送..." → schedule_management（定时查询推送）
- "定期检查...状态" → schedule_management（定时检查）

**时间表达式理解**：
- 系统应能理解各种时间表达式，如"每天上午10点"、"每周一下午3点"、"每个工作日11:20"等
- 系统应能从自然语言中提取出时间信息，包括频率(每天/每周/每月)和具体时间点

**查询内容提取**：
- 系统应能从用户输入中提取出需要定期执行的查询内容
- 例如从"每个工作日的11:20，查询：issue in linkedIssues(...)"中提取出JQL查询部分

### 5. document_processing - 文档处理类
**功能**：处理Confluence文档、PRD、TRD等文档的读取、总结、翻译、问答
**文档来源**：
- 直接提供的文档链接（Confluence URL）
- JIRA单号关联的PRD/TRD文档（customfield_16700）
**处理动作**：总结、翻译、问答、提取关键信息、分析

**触发关键词**：PRD、TRD、文档、总结、翻译、分析、confluence、wiki
**特殊识别规则（高优先级）**：
- "SPCB-123的PRD主要内容是什么" → document_processing (JIRA单号+PRD内容查询)
- "SPCB-123这个需求的PRD说了什么" → document_processing (JIRA单号+PRD内容查询)
- "SPCB-123的TRD文档内容" → document_processing (JIRA单号+TRD内容查询)
- "总结SPCB-123相关文档" → document_processing (JIRA单号+文档总结)
- "翻译SPCB-123的PRD为英文" → document_processing (JIRA单号+文档翻译)
- "分析需求的PRD内容" → document_processing (需求+PRD分析)

### 6. user_info_query - 用户信息查询类
**功能**：查询用户账户信息、用户详情等
**数据来源**：调用两个用户信息API接口并合并数据
**查询参数**：用户名、用户ID、环境、地区等
**输出格式**：格式化的用户信息，最多显示前3条记录

**触发关键词**：用户、账户、用户信息、用户名、用户ID、查询用户、user、username、userid
**命令格式**：
- "user username <用户名>" → user_info_query (按用户名查询)
- "user id <用户ID>" → user_info_query (按用户ID查询)
- "user <用户名或ID>" → user_info_query (自动识别类型查询)
- "查询用户<用户名>的信息" → user_info_query (自然语言查询)
- "用户信息查询" → user_info_query (通用查询)

**信息提取**：
- username：用户名
- userid：用户ID
- env：环境（默认TEST）
- country：地区（默认ID）

### 7. general - 其他类
**功能**：使用大模型自身能力回答的问题
**包含内容**：闲聊、文本翻译、问题咨询、代码生成、bug分析、日志分析、告警分析、系统帮助
**处理方式**：直接使用LLM能力，不需要JIRA查询

**触发场景**：非JIRA相关的通用问题

## 意图识别优先级（从高到低）：

1. **jira_write（创建subtask）** - 最高优先级
   - 任何包含JIRA单号格式（如SPCB-123）+ 创建关键词（建单、建subtask、add subtask等）的指令
   - 即使包含时间表达式（如"1d"、"2d"）也应识别为创建子任务，不是定时任务
   - 示例："在SPCB-54004 下add subtask：--case 设计 1d" → jira_write（创建子任务）

2. **document_processing** - 高优先级
   - 包含JIRA单号 + PRD/TRD关键词的查询

3. **user_info_query** - 高优先级
   - 明确的用户信息查询指令，如"user username xxx"、"查询用户信息"等
   - 包含用户查询关键词的自然语言表达

4. **jira_query** - 中等优先级
   - 包含JIRA单号的一般查询

5. **jira_statistics** - 中等优先级
   - 统计分析类查询

6. **schedule_management** - 高优先级（仅次于jira_write）
   - 当明确表达定时/周期性执行意图时必须识别为此类型
   - 包含时间周期词汇（每天、每周、工作日等）和执行时间点的请求
   - 包含"发送到群"、"推送"、"通知"等定时推送意图的请求
   - 优先级高于jira_query，避免被误识别为查询类

7. **general** - 最低优先级
   - 其他通用问题

## 关键区分原则：

### 创建操作 vs 定时任务：
- "在SPCB-123 建单" → jira_write（创建操作）
- "每天10点提醒我查看任务" → schedule_management（定时任务）
- "在SPCB-54004 下add subtask：--case 设计 1d" → jira_write（创建操作，"1d"是工作量不是定时时间）

### 工作量估算 vs 定时执行：
- 工作量估算："2d"、"3天" - 表示任务需要多长时间完成，用于jira_write
- 定时执行："每天10点"、"工作日9:30" - 表示何时执行任务，用于schedule_management

## 重要识别规则：

### 子任务查询和统计区分规则（新增）：
1. **子任务列表查询（jira_query）**：
   - 关键词组合：["子任务", "哪些", "列出", "查看", "显示"]
   - 示例："我上周完成了哪些子任务" → jira_query（查询子任务列表）
   - 示例："列出我的子任务" → jira_query（查询子任务列表）
   - 示例："显示我本周的子任务" → jira_query（查询子任务列表）

2. **子任务数量统计（jira_statistics）**：
   - 关键词组合：["子任务", "几个", "多少", "统计", "数量"]
   - 示例："我完成了几个子任务" → jira_statistics（统计数量）
   - 示例："统计我的子任务数量" → jira_statistics（统计数量）

3. **优先级规则**：
   - 当同时包含"哪些"和"几个"时，优先识别为jira_query
   - 当用户明确想知道具体子任务列表时，应识别为jira_query
   - 当用户只关心数量时，应识别为jira_statistics

### subtask创建识别规则（最高优先级）：
1. **包含父单号**：必须包含JIRA单号格式（如SPCB-123）
2. **包含创建关键词**：建单、建subtask、add subtask、创建子任务、新建任务等
3. **提取标题信息**：从冒号后或其他描述中提取任务标题
   - **特殊格式标题**：保留以"--"开头的标题格式，如"--测试任务"、"--设计文档"等，这是特殊格式标题，后续会与父任务标题组合
4. **提取工作量**：识别"2d"、"3天"、"1周"等时间表达，转换为数字（注意：这里的时间是工作量估算，不是定时执行的时间）
5. **提取时间信息**：识别"今天"、"明天"、"本周"等时间表达，也可能从工作量中推测起始时间，如"2d"可能表示今天到明天
6. **优先级规则**：
   - **关键识别**：任何包含JIRA单号+创建关键词的指令，无论是否包含时间表达式，都应优先识别为jira_write（创建subtask）
   - 创建子任务的意图优先级高于定时任务管理，即使包含"1d"、"2d"等时间表达，如"在SPCB-54004 下add subtask：--case 设计 1d"应识别为创建子任务
   - 创建子任务的意图优先级高于bug查询，即使包含"bug"关键词

### PRD/TRD文档处理优先级规则（最高优先级）：
1. **JIRA单号+PRD关键词**：包含JIRA单号且询问PRD内容 → document_processing
2. **JIRA单号+TRD关键词**：包含JIRA单号且询问TRD内容 → document_processing  
3. **需求+文档内容**：询问需求的文档主要内容 → document_processing
4. **文档处理动作**：明确的文档处理动作（总结、翻译、分析等）→ document_processing

### 时间表达式理解（增强）：
- 大模型应能理解多种自然语言中的时间表达式，不限于特定格式或关键词
- 从语义上识别出用户想要设置定期任务的意图，而不是依赖特定的触发词
- 理解各种表达周期的方式，如"每个工作日"、"每周一到周五"、"工作日"、"每天"等
- 能够从上下文中推断出完整的调度信息，即使用户表达不完整

### 查询和通知意图识别（增强）：
- 理解用户想要定期执行查询或发送通知的意图，无论使用何种表达方式
- 能够从上下文中区分一次性查询和定期查询的区别
- 理解各种表达"提醒"、"通知"的方式，不限于特定关键词
- 能够理解用户想要将查询结果发送到特定目标（如"本群"、"私聊"等）的意图

### 多语言支持：
- 系统应能理解多种语言中表达定期执行意图的方式
- 不依赖特定语言的关键词，而是理解语义
- 能够从各种语言表达中提取出时间、频率、查询内容等关键信息

### 人员信息识别（重要：区分reporter和assignee）：

**Reporter（报告人/创建人）识别规则**：
- "我提交的bug" → `reporter: ["currentUser()"]`
- "我创建的任务" → `reporter: ["currentUser()"]`
- "我报告的问题" → `reporter: ["currentUser()"]`
- "liang.tang提交的bug" → `reporter: ["liang.tang"]`
- "xxx创建的任务" → `reporter: ["xxx"]`

**Assignee（处理人/负责人）识别规则**：
- "我的任务" → `assignee: ["currentUser()"]`
- "分配给我的bug" → `assignee: ["currentUser()"]`
- "我负责的任务" → `assignee: ["currentUser()"]`
- "liang.tang的任务" → `assignee: ["liang.tang"]`
- "分配给xxx的bug" → `assignee: ["xxx"]`

**关键词区分**：
- 提交、创建、报告、建单 → 使用reporter
- 分配、负责、处理、我的 → 使用assignee
- 当用户说"我今天提交了哪些bug"时，应该使用reporter而不是assignee
- 当提到其他角色的时候如开发/dev/测试/QA,pm/pjm/fe/be等等都需要做对应的转换

### 群组信息提取：
- 自动提取群标题中的SP[字母]+-数字格式JIRA单号
- 群标题信息优先级最高，会影响查询范围

### 时间范围识别：
- 相对时间：上周、本月、最近、今天、昨天
- 绝对时间：2024-01-01、1月15日
- 时间段：1月1日到1月15日

### 字段提取规则：
- 只有用户明确提到优先级时才提取priority字段
- 状态字段提取规则：
  * 用户说"未完成"、"需要处理"、"待处理"、"进行中" → status: ["未完成"]（表示需要排除Closed、Done、Icebox状态）
  * 用户说"已完成"、"完成了"、"done了" → status: ["Done"]
  * 用户明确提到具体状态名称时才提取对应状态
- 支持自定义字段提取（用户指定返回字段）

## 输出格式要求：
必须返回标准JSON格式，包含以下字段：
- intent: 意图类型（必须是6大类之一）
- confidence: 置信度（0.0-1.0）
- extracted_info: 提取的关键信息
- needs_clarification: 是否需要澄清（true/false）
- clarification_reason: 需要澄清的原因（可选）

### subtask创建的输出格式：
```json
{
    "intent": "jira_write",
    "confidence": 0.9,
    "extracted_info": {
        "operation_type": "create_subtask",
        "parent_issue_key": "SPCB-51331",
        "subtask_summary": "今天完成功能测试",
        "story_points": 2,
        "planned_start_date": "2025-01-02",
        "planned_due_date": "2025-01-02",
        "assignee": "currentUser()",
        "description": "完成**需求的功能测试"
    },
    "needs_clarification": false
}
```

## 澄清触发条件：
1. 置信度低于0.6
2. 多个意图可能性相近
3. 关键信息缺失或模糊
4. 用户输入过于简单或复杂

## 重要提示：
- 对于包含JIRA单号+PRD/TRD关键词的查询，应优先识别为document_processing，置信度应≥0.8
- 对于包含JIRA单号（如SPCB-48914）的一般查询，应识别为jira_query，置信度应≥0.8
- 对于明确的查询动作（如"帮我查一下"、"看看状态"），应识别为jira_query，不需要澄清
- 对于subtask创建，必须包含父单号，否则需要澄清
- 只有在真正模糊不清时才设置needs_clarification为true
- 群标题中的JIRA单号信息应该被充分利用,但优先级低于用户指令中指定的 jira 单号"""

    USER_PROMPT_TEMPLATE = """用户输入: {user_input}

群组信息: {group_info}

上下文信息: {context}

请严格按照优先级规则分析用户意图：

1. **最高优先级 - subtask创建检查**：
   - 首先检查是否包含JIRA单号（如SPCB-123）+ 创建关键词（建单、建subtask、add subtask等）
   - 如果匹配，无论是否包含时间表达式（如"1d"、"2d"），都应识别为jira_write（创建子任务）
   - 时间表达式"1d"、"2d"在此上下文中是工作量估算，不是定时执行时间

2. **其他优先级判断**：
   - 如果包含JIRA单号+PRD/TRD关键词，考虑document_processing
   - 如果包含群标题中的JIRA单号，优先考虑相关查询（jira_query）
   - 如果包含统计分析关键词，考虑jira_statistics
   - 如果包含明确的周期性时间表达（每天、每周、工作日等）+ 执行时间点，考虑schedule_management
   - 其他情况考虑general

2. **信息提取**：
   - 提取所有可识别的关键信息
   - 注意人员信息的三种识别模式
   - 提取时间范围和单据类型
   - 对于subtask创建，特别注意提取父单号、标题、工作量、时间信息

3. **澄清判断**：
   - 如果信息不足或模糊，设置needs_clarification为true
   - 在clarification_reason中说明需要澄清的具体原因
   - 对于subtask创建，如果缺少父单号或标题，需要澄清

请按要求的JSON格式输出分析结果。"""

    @classmethod
    def build_prompt(cls, user_input: str, context: dict = None, group_info: dict = None) -> tuple:
        """
        构建意图识别提示词
        
        Args:
            user_input: 用户输入
            context: 上下文信息
            group_info: 群组信息（包含群标题等）
            
        Returns:
            (system_prompt, user_prompt)
        """
        # 处理上下文信息
        context_str = ""
        if context:
            if context.get('recent_jira_keys'):
                context_str += f"最近查询的JIRA: {', '.join(context['recent_jira_keys'])}\n"
            if context.get('current_topic'):
                context_str += f"当前话题: {context['current_topic']}\n"
            if context.get('last_intent'):
                context_str += f"上次意图: {context['last_intent']}\n"
            if context.get('recent_queries'):
                context_str += f"最近查询: {', '.join(context['recent_queries'][-2:])}\n"
        
        if not context_str:
            context_str = "无相关上下文"
        
        # 处理群组信息
        group_info_str = ""
        if group_info:
            if group_info.get('group_title'):
                group_info_str += f"群标题: {group_info['group_title']}\n"
                # 提取群标题中的JIRA单号
                jira_keys = cls.extract_jira_keys_from_title(group_info['group_title'])
                if jira_keys:
                    group_info_str += f"群标题中的JIRA单号: {', '.join(jira_keys)}\n"
            if group_info.get('group_id'):
                group_info_str += f"群组ID: {group_info['group_id']}\n"
        
        if not group_info_str:
            group_info_str = "无群组信息"
        
        user_prompt = cls.USER_PROMPT_TEMPLATE.format(
            user_input=user_input,
            group_info=group_info_str,
            context=context_str
        )
        
        return cls.SYSTEM_PROMPT, user_prompt
    
    @classmethod
    def extract_jira_keys_from_title(cls, title: str) -> List[str]:
        """
        从群标题中提取JIRA单号
        
        Args:
            title: 群标题
            
        Returns:
            JIRA单号列表
        """
        if not title:
            return []
        
        # SP开头的特定格式
        sp_pattern = r'\bSP[A-Z]+-\d+\b'
        sp_keys = re.findall(sp_pattern, title, re.IGNORECASE)
        
        # 通用的字母-数字格式
        general_pattern = r'\b[A-Z]{2,}-\d+\b'
        general_keys = re.findall(general_pattern, title, re.IGNORECASE)
        
        # 合并并去重
        all_keys = list(set(sp_keys + general_keys))
        return [key.upper() for key in all_keys]
    
    @classmethod
    def get_intent_examples(cls) -> Dict[str, List[Dict]]:
        """获取各类意图的示例"""
        return {
            "jira_query": [
                {
                    "input": "我上周的工作报告",
                    "expected": {
                        "intent": "jira_query",
                        "confidence": 0.95,
                        "extracted_info": {
                            "keywords": ["工作报告"],
                            "time_range": "上周",
                            "assignee": ["currentUser()"],
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "查询liang.tang的任务",
                    "expected": {
                        "intent": "jira_query",
                        "confidence": 0.95,
                        "extracted_info": {
                            "keywords": ["任务"],
                            "assignee": ["liang.tang"],
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "SPCB-1234的详细信息",
                    "expected": {
                        "intent": "jira_query",
                        "confidence": 0.98,
                        "extracted_info": {
                            "jira_keys": ["SPCB-1234"],
                            "keywords": ["详细信息"],
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "我有哪些未完成的子任务",
                    "expected": {
                        "intent": "jira_query",
                        "confidence": 0.95,
                        "extracted_info": {
                            "keywords": ["子任务"],
                            "assignee": ["currentUser()"],
                            "issue_type": ["Sub-task"],
                            "status": ["未完成"],
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "我今天提交了哪些bug",
                    "expected": {
                        "intent": "jira_query",
                        "confidence": 0.95,
                        "extracted_info": {
                            "keywords": ["bug"],
                            "reporter": ["currentUser()"],
                            "time_range": {"type": "relative", "value": "today"},
                            "issue_type": ["Bug"],
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "我上周给zijian.yan提交了哪些bug",
                    "expected": {
                        "intent": "jira_query",
                        "confidence": 0.95,
                        "extracted_info": {
                            "keywords": ["bug"],
                            "reporter": ["currentUser()"],
                            "assignee": ["zijian.yan"],
                            "time_range": {"type": "relative", "value": "last week"},
                            "issue_type": ["Bug"],
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                }
            ],
            "jira_statistics": [
                {
                    "input": "统计本月完成的bug数量",
                    "expected": {
                        "intent": "jira_statistics",
                        "confidence": 0.95,
                        "extracted_info": {
                            "keywords": ["统计", "数量"],
                            "time_range": "本月",
                            "issue_types": ["Bug"],
                            "status": ["Done", "Closed"],
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "分析团队这周的工作进度趋势",
                    "expected": {
                        "intent": "jira_statistics",
                        "confidence": 0.90,
                        "extracted_info": {
                            "keywords": ["分析", "趋势", "进度"],
                            "time_range": "本周",
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                }
            ],
            "jira_write": [
                {
                    "input": "把SPCB-1234的优先级改成High",
                    "expected": {
                        "intent": "jira_write",
                        "confidence": 0.95,
                        "extracted_info": {
                            "jira_keys": ["SPCB-1234"],
                            "keywords": ["改成", "优先级"],
                            "priority": ["High"],
                            "operation_type": "write"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "给SPCB-5678添加评论：已完成测试",
                    "expected": {
                        "intent": "jira_write",
                        "confidence": 0.95,
                        "extracted_info": {
                            "jira_keys": ["SPCB-5678"],
                            "keywords": ["添加评论"],
                            "comment_text": "已完成测试",
                            "operation_type": "write"
                        },
                        "needs_clarification": False
                    }
                }
            ],
            "schedule_management": [
                {
                    "input": "创建定时任务，每天10点查询我的待办任务",
                    "expected": {
                        "intent": "schedule_management",
                        "confidence": 0.95,
                        "extracted_info": {
                            "keywords": ["创建", "定时任务"],
                            "schedule_expression": "每天 10:00",
                            "query_content": "我的待办任务",
                            "operation_type": "write"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "暂停任务ID为3的定时任务",
                    "expected": {
                        "intent": "schedule_management",
                        "confidence": 0.95,
                        "extracted_info": {
                            "keywords": ["暂停", "定时任务"],
                            "task_id": 3,
                            "operation_type": "write"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "工作日 18:00 发送所有阻塞需求SPCB-54480 的待处理 bug 到当前群",
                    "expected": {
                        "intent": "schedule_management",
                        "confidence": 0.95,
                        "extracted_info": {
                            "keywords": ["发送", "定时", "群"],
                            "schedule_expression": "工作日 18:00",
                            "query_content": "所有阻塞需求SPCB-54480 的待处理 bug",
                            "target_location": "当前群",
                            "jira_keys": ["SPCB-54480"],
                            "operation_type": "write"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "每个工作日11:20提醒我有哪些未完成的子任务",
                    "expected": {
                        "intent": "schedule_management",
                        "confidence": 0.95,
                        "extracted_info": {
                            "keywords": ["提醒", "定时"],
                            "schedule_expression": "每个工作日 11:20",
                            "query_content": "我有哪些未完成的子任务",
                            "operation_type": "write"
                        },
                        "needs_clarification": False
                    }
                }
            ],
            "document_processing": [
                {
                    "input": "SPCB-123的PRD主要内容是什么",
                    "expected": {
                        "intent": "document_processing",
                        "confidence": 0.95,
                        "extracted_info": {
                            "jira_keys": ["SPCB-123"],
                            "document_types": ["PRD"],
                            "action": "summarize",
                            "target_language": "中文",
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "SPCB-123这个需求的PRD说了什么",
                    "expected": {
                        "intent": "document_processing",
                        "confidence": 0.95,
                        "extracted_info": {
                            "jira_keys": ["SPCB-123"],
                            "document_types": ["PRD"],
                            "action": "summarize",
                            "target_language": "中文",
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "SPCB-123的TRD文档内容",
                    "expected": {
                        "intent": "document_processing",
                        "confidence": 0.95,
                        "extracted_info": {
                            "jira_keys": ["SPCB-123"],
                            "document_types": ["TRD"],
                            "action": "summarize",
                            "target_language": "中文",
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "总结SPCB-123相关文档",
                    "expected": {
                        "intent": "document_processing",
                        "confidence": 0.95,
                        "extracted_info": {
                            "jira_keys": ["SPCB-123"],
                            "document_types": ["PRD", "TRD"],
                            "action": "summarize",
                            "target_language": "中文",
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "翻译SPCB-123的PRD为英文",
                    "expected": {
                        "intent": "document_processing",
                        "confidence": 0.95,
                        "extracted_info": {
                            "jira_keys": ["SPCB-123"],
                            "document_types": ["PRD"],
                            "action": "translate",
                            "target_language": "英文",
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "分析需求的PRD内容",
                    "expected": {
                        "intent": "document_processing",
                        "confidence": 0.95,
                        "extracted_info": {
                            "keywords": ["分析", "PRD"],
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                }
            ],
            "general": [
                {
                    "input": "你好，你能做什么",
                    "expected": {
                        "intent": "general",
                        "confidence": 0.95,
                        "extracted_info": {
                            "keywords": ["问候", "功能介绍"],
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                },
                {
                    "input": "帮我翻译这段英文：Hello World",
                    "expected": {
                        "intent": "general",
                        "confidence": 0.95,
                        "extracted_info": {
                            "keywords": ["翻译"],
                            "text_content": "Hello World",
                            "operation_type": "read"
                        },
                        "needs_clarification": False
                    }
                }
            ]
        }


class IntentClarification:
    """意图澄清机制"""
    
    def __init__(self):
        self.max_clarification_rounds = 3
        self.clarification_templates = {
            "low_confidence": "您的查询可能有多种理解方式，请选择您的具体需求：",
            "missing_params": "为了更好地帮助您，请补充以下信息：",
            "multiple_intents": "检测到多个可能的意图，请选择：",
            "ambiguous_query": "您的查询比较模糊，请提供更多细节："
        }
    
    def generate_clarification(self, user_input: str, intent_result: Dict) -> Dict:
        """
        生成澄清问题
        
        Args:
            user_input: 用户输入
            intent_result: 意图识别结果
            
        Returns:
            澄清问题和选项
        """
        if not intent_result.get('needs_clarification'):
            return {'needs_clarification': False}
        
        reason = intent_result.get('clarification_reason', 'unknown')
        confidence = intent_result.get('confidence', 0.0)
        
        clarification = {
            'needs_clarification': True,
            'reason': reason,
            'question': '',
            'options': []
        }
        
        if confidence < 0.5:
            # 置信度很低，提供主要意图选项
            clarification['question'] = self.clarification_templates['multiple_intents']
            clarification['options'] = [
                "1. 查询JIRA数据（查看任务、bug等）",
                "2. 统计分析JIRA数据（生成报告、图表）", 
                "3. 修改JIRA数据（更新状态、添加评论）",
                "4. 管理定时任务（创建、暂停、删除）",
                "5. 其他问题（翻译、咨询等）"
            ]
        elif 'missing' in reason.lower():
            # 缺少关键信息
            clarification['question'] = self.clarification_templates['missing_params']
            clarification['options'] = self._generate_missing_params_options(intent_result)
        else:
            # 模糊查询
            clarification['question'] = self.clarification_templates['ambiguous_query']
            clarification['options'] = [
                "请提供更具体的描述",
                "或者选择以下常见操作之一："
            ]
        
        return clarification
    
    def _generate_missing_params_options(self, intent_result: Dict) -> List[str]:
        """生成缺失参数的选项"""
        options = []
        extracted_info = intent_result.get('extracted_info', {})
        
        if not extracted_info.get('time_range'):
            options.append("• 时间范围（如：本周、上月、最近7天）")
        
        if not extracted_info.get('assignee'):
            options.append("• 处理人信息（如：我的、liang.tang、具体邮箱）")
        
        if not extracted_info.get('issue_types'):
            options.append("• 单据类型（如：Task、Bug、Epic）")
        
        if not extracted_info.get('jira_keys'):
            options.append("• 具体的JIRA单号（如：SPCB-1234）")
        
        return options


class IntentRanker:
    """意图排序器"""
    
    def __init__(self):
        self.confidence_threshold = 0.6  # 降低阈值，减少误判
        self.intent_weights = {
            "jira_query": 1.0,
            "jira_statistics": 0.95,  # 提高统计类权重
            "jira_write": 0.9,        # 提高写操作权重
            "schedule_management": 0.8,
            "document_processing": 0.7,
            "general": 0.5            # 降低通用类权重
        }
    
    def rank_intents(self, user_input: str, intent_candidates: List[Dict], group_info: Dict = None) -> Tuple[List[Dict], bool]:
        """
        对意图候选进行排序
        
        Args:
            user_input: 用户输入
            intent_candidates: 意图候选列表
            
        Returns:
            (排序后的意图列表, 是否需要澄清)
        """
        if not intent_candidates:
            return [], True
        
        # 保存群组信息供关键词匹配使用
        self._group_info = group_info
        
        # 计算加权置信度
        for candidate in intent_candidates:
            intent_type = candidate.get('intent', 'general')
            base_confidence = candidate.get('confidence', 0.0)
            weight = self.intent_weights.get(intent_type, 0.5)
            
            # 基于关键词匹配度调整
            keyword_boost = self._calculate_keyword_boost(user_input, intent_type)
            
            # 最终置信度
            final_confidence = min(1.0, base_confidence * weight + keyword_boost)
            candidate['final_confidence'] = final_confidence
        
        # 按最终置信度排序
        ranked_intents = sorted(intent_candidates, 
                              key=lambda x: x.get('final_confidence', 0.0), 
                              reverse=True)
        
        # 判断是否需要澄清
        needs_clarification = (
            len(ranked_intents) == 0 or
            ranked_intents[0].get('final_confidence', 0.0) < self.confidence_threshold or
            (len(ranked_intents) > 1 and 
             abs(ranked_intents[0].get('final_confidence', 0.0) - 
                 ranked_intents[1].get('final_confidence', 0.0)) < 0.2)
        )
        
        return ranked_intents, needs_clarification
    
    def _calculate_keyword_boost(self, user_input: str, intent_type: str) -> float:
        """计算关键词匹配度加成"""
        user_input_lower = user_input.lower()
        
        # 特殊规则处理：优先级调整
        special_boost = self._apply_special_intent_rules(user_input_lower, intent_type)
        if special_boost != 0:
            return special_boost
        
        # 增强关键词模式，包含更多变体
        keyword_patterns = {
            "jira_query": [
                # 查询相关
                "查询", "搜索", "找", "看", "显示", "获取", "取", "拿", "列出", "查看", "哪些",
                # 人员相关
                "我的", "分配", "负责", "处理", "指派",
                # 单据相关
                "任务", "bug", "需求", "故事", "epic", "子任务", "subtask", "sub-task", "sub task",
                # 状态相关
                "进行中", "待办", "完成", "关闭", "开放",
                # JIRA单号模式
                "spcb-", "spfe-", "spbe-", "sp", "jira"
            ],
            "jira_statistics": [
                "统计", "分析", "汇总", "报告", "图表", "趋势", "分布", 
                "数量", "占比", "多少", "几个", "总共", "平均", "最多", "最少",
                "效率", "进度", "完成率", "工作量", "对比",
                # 添加状态查询相关的关键词
                "done", "完成了", "做了", "处理了", "解决了", "修复了",
                "今天", "昨天", "本周", "上周", "本月", "上月"
            ],
            "jira_write": [
                # 移除可能引起歧义的词汇，只保留明确的写操作词汇
                "更新", "修改", "改变", "设置", "评论", "添加", "创建", "新建",
                "提交", "保存", "编辑", "删除", "移动", "转移", "分配给",
                "状态改为", "优先级改为", "标签", "描述",
                # 明确的写操作指令
                "把...改成", "将...设为", "标记为", "关闭", "重新打开"
            ],
            "schedule_management": [
                "定时", "计划", "每天", "每周", "每月", "每个工作日", "提醒", "通知", "推送",
                "暂停", "恢复", "删除任务", "schedule", "cron", "定期",
                "自动", "批量", "周期性"
            ],
            "document_processing": [
                "PRD", "TRD", "文档", "总结", "翻译", "分析", "confluence", "wiki"
            ],
            "user_info_query": [
                "用户", "账户", "用户信息", "用户名", "用户ID", "查询用户", "user", "username", "userid",
                "账号", "用户详情", "用户数据", "用户查询", "账户信息", "账户查询", "用户账户",
                "user info", "account", "account info"
            ],
            "general": [
                "你好", "帮助", "翻译", "咨询", "什么", "怎么", "为什么",
                "hello", "help", "谢谢", "再见"
            ]
        }
        
        patterns = keyword_patterns.get(intent_type, [])
        matches = 0
        
        # 精确匹配和模糊匹配
        for pattern in patterns:
            if pattern in user_input_lower:
                matches += 1
        
        # 特殊加成：JIRA单号识别
        if intent_type == "jira_query":
            import re
            jira_pattern = r'\b[A-Z]{2,}-\d+\b'
            sp_pattern = r'\bSP[A-Z]+-\d+\b'
            if re.search(jira_pattern, user_input, re.IGNORECASE) or re.search(sp_pattern, user_input, re.IGNORECASE):
                matches += 3  # JIRA单号强加成
        
        # 群标题加成
        if hasattr(self, '_group_info') and self._group_info:
            if self._group_info.get('jira_keys') and intent_type == "jira_query":
                matches += 2  # 群标题有JIRA单号时加成
        
        return min(0.4, matches * 0.08)  # 提高加成上限和单次加成

    def _extract_jira_keys(self, text: str) -> List[str]:
        """提取JIRA单号"""
        import re
        pattern = r'[A-Z]+-\d+'
        return re.findall(pattern, text.upper())
    
    def _apply_special_intent_rules(self, user_input: str, intent_type: str) -> float:
        """应用特殊的意图识别规则"""
        
        # 规则0: PRD/TRD文档处理优先级（最高优先级）
        import re
        jira_pattern = r'\b[A-Z]{2,}-\d+\b'
        has_jira_key = bool(re.search(jira_pattern, user_input, re.IGNORECASE))
        
        # PRD/TRD关键词匹配
        prd_trd_keywords = ['PRD', 'TRD', '文档内容', '主要内容', '需求内容', 'prd', 'trd']
        has_doc_keyword = any(keyword.lower() in user_input.lower() for keyword in prd_trd_keywords)
        
        # 文档处理动作关键词
        doc_action_keywords = ['总结', '翻译', '分析', '提取', 'summarize', 'translate', 'analyze']
        has_doc_action = any(keyword.lower() in user_input.lower() for keyword in doc_action_keywords)
        
        if has_jira_key and (has_doc_keyword or has_doc_action):
            if intent_type == "document_processing":
                return 0.5  # 增加更强的加成，确保优先级最高
            elif intent_type == "jira_query":
                return -0.4  # 增加更强的负加成，降低JIRA查询的优先级
        
        # 规则1: 子任务查询优先规则 - 明确包含"哪些子任务"的查询应优先识别为jira_query
        subtask_keywords = ['子任务', 'subtask', 'sub-task', 'sub task']
        query_keywords = ['哪些', '什么', '列出', '查询', '查看', '显示', '找出']
        
        has_subtask_keyword = any(keyword.lower() in user_input.lower() for keyword in subtask_keywords)
        has_query_keyword = any(keyword.lower() in user_input.lower() for keyword in query_keywords)
        
        if has_subtask_keyword and has_query_keyword:
            if intent_type == "jira_query":
                return 0.4  # 强加成，确保识别为查询
            elif intent_type == "jira_statistics":
                return -0.3  # 负加成，降低统计的可能性
        
        # 规则2: "done了几个" 模式 → 统计查询
        if "done" in user_input and ("几个" in user_input or "多少" in user_input):
            if intent_type == "jira_statistics":
                return 0.3  # 强加成
            elif intent_type == "jira_write":
                return -0.2  # 负加成，降低写操作的可能性
        
        # 规则3: "完成了" + 数量词 → 统计查询
        if ("完成了" in user_input or "做了" in user_input) and any(word in user_input for word in ["几个", "多少"]):
            if intent_type == "jira_statistics":
                return 0.3
            elif intent_type == "jira_write":
                return -0.2
        
        # 规则4: "完成了哪些子任务" → 查询而非统计
        if ("完成了" in user_input or "做了" in user_input) and has_subtask_keyword and "哪些" in user_input:
            if intent_type == "jira_query":
                return 0.4  # 强加成，确保识别为查询
            elif intent_type == "jira_statistics":
                return -0.3  # 负加成，降低统计的可能性
        
        # 规则5: 时间词 + done/完成 → 统计查询
        time_words = ["今天", "昨天", "本周", "上周", "本月", "上月"]
        if any(time_word in user_input for time_word in time_words) and ("done" in user_input or "完成" in user_input):
            if intent_type == "jira_statistics":
                return 0.25
            elif intent_type == "jira_write":
                return -0.15
        
        # 规则6: 明确的写操作模式
        write_patterns = [r"把.*改成", r"将.*设为", r".*状态改为", r"给.*添加"]
        for pattern in write_patterns:
            if re.search(pattern, user_input):
                if intent_type == "jira_write":
                    return 0.3
                else:
                    return -0.1
        
        # 规则7: "add"开头但不是创建子任务的指令
        if (user_input.lower().startswith('add ') or 
            user_input.startswith('添加') or 
            user_input.startswith('新增') or 
            user_input.startswith('增加')) and not has_jira_key and not has_subtask_keyword:
            # 检查是否包含服务或项目关键词
            if any(keyword in user_input.lower() for keyword in ['service', 'shopee-', '服务', '项目']):
                if intent_type == "jira_write":
                    return -0.5  # 强负加成，降低写操作的可能性
            
        return 0.0  # 无特殊调整


class GroupInfoExtractor:
    """群组信息提取器"""
    
    def __init__(self):
        # 标准JIRA单号模式
        self.jira_pattern = r'\b[A-Z]{2,}-\d+\b'
        # SP开头的JIRA单号模式
        self.sp_pattern = r'\bSP[A-Z]+-\d+\b'
        # 方括号中的JIRA单号模式（如[SPCB-51921]）
        self.bracketed_pattern = r'\[([A-Z]{2,}-\d+)\]'
    
    def extract_from_group_title(self, group_title: str) -> Dict:
        """
        从群标题中提取信息
        
        Args:
            group_title: 群标题
            
        Returns:
            提取的信息
        """
        import logging
        logger = logging.getLogger(__name__)
        
        logger.info(f"🔍 从群标题中提取信息: '{group_title}'")
        
        if not group_title:
            logger.warning("⚠️ 群标题为空")
            return {}
        
        info = {
            'group_title': group_title,
            'jira_keys': [],
            'project_info': {},
            'keywords': []
        }
        
        # 提取JIRA单号
        sp_keys = re.findall(self.sp_pattern, group_title, re.IGNORECASE)
        logger.info(f"🔍 从群标题中提取的SP格式JIRA单号: {sp_keys}")
        
        general_keys = re.findall(self.jira_pattern, group_title, re.IGNORECASE)
        logger.info(f"🔍 从群标题中提取的一般格式JIRA单号: {general_keys}")
        
        # 提取方括号中的JIRA单号
        bracketed_keys = re.findall(self.bracketed_pattern, group_title)
        logger.info(f"🔍 从群标题中提取的方括号格式JIRA单号: {bracketed_keys}")
        
        all_keys = list(set([key.upper() for key in sp_keys + general_keys + bracketed_keys]))
        info['jira_keys'] = all_keys
        logger.info(f"🔍 合并后的JIRA单号: {all_keys}")
        
        # 提取项目信息
        if all_keys:
            projects = list(set([key.split('-')[0] for key in all_keys]))
            info['project_info'] = {'projects': projects}
            logger.info(f"🔍 提取的项目信息: {projects}")
        
        # 提取关键词
        keywords = []
        if '开发' in group_title or 'dev' in group_title.lower():
            keywords.append('开发')
        if '测试' in group_title or 'test' in group_title.lower():
            keywords.append('测试')
        if '产品' in group_title or 'product' in group_title.lower():
            keywords.append('产品')
        
        info['keywords'] = keywords
        logger.info(f"🔍 提取的关键词: {keywords}")
        logger.info(f"🔍 最终提取的群组信息: {info}")
        
        return info 