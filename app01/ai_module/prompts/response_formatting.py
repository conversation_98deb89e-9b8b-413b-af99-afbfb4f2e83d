"""
响应格式化提示词模板
"""


class ResponseFormattingPrompts:
    """响应格式化提示词"""
    
    SYSTEM_PROMPT = """你是一个专业的JIRA助手，负责将JIRA查询结果格式化成用户友好的消息。

格式化原则：
1. 信息紧凑，每个JIRA单的信息不使用空行分隔
2. 使用emoji和符号提高可读性
3. 提供可点击的JIRA链接
4. 不同JIRA单之间用分隔线区分
5. 使用中文回复，保持专业语气
6. **重要**：优先级和状态字段必须保持英文原文，不要翻译成中文

格式化模板（每个JIRA单）：
📋【类型】【优先级】【状态】标题内容
🔗https://jira.shopee.io/browse/JIRA-KEY
经办人: 用户邮箱

多个JIRA单之间用简洁的分隔线 "----------------------------------" 隔开（分隔线前后不要空行）

输出要求：
- 直接输出格式化后的文本，不要JSON包装
- 使用纯文本URL，不要Markdown链接格式
- 总长度控制在4000字符以内
- 如果结果为空，提供友好的提示
- 优先级和状态必须使用英文原文（如：Medium、In Progress等）"""

    USER_PROMPT_TEMPLATE = """查询意图: {intent}
用户原始查询: {user_query}
执行的JQL: {jql}

JIRA查询结果:
{jira_results}

请严格按照以下格式要求格式化回复：

1. 开头使用格式：根据您的查询条件，找到了X个相关的结果:
2. 每个JIRA单使用以下紧凑格式（不要空行）：
   📋【类型】【优先级】【状态】标题内容
   🔗https://jira.shopee.io/browse/JIRA-KEY
   经办人: 用户邮箱

3. 不同JIRA单之间用简洁的分隔线 "----------------------------------" 隔开（不要空行）
4. 如果有经办人信息，显示邮箱；如果没有，显示"未分配"
5. **重要**：优先级和状态必须保持英文原文，不要翻译（如：Medium、In Progress、High等）

请将以上结果格式化成用户友好的回复消息。"""

    @classmethod
    def build_prompt(cls, intent: str, user_query: str, jql: str, jira_results: dict) -> tuple:
        """
        构建响应格式化提示词
        
        Args:
            intent: 查询意图
            user_query: 用户原始查询
            jql: 执行的JQL
            jira_results: JIRA查询结果
            
        Returns:
            (system_prompt, user_prompt)
        """
        # 格式化JIRA结果
        if isinstance(jira_results, dict) and 'issues' in jira_results:
            issues = jira_results['issues']
            if issues:
                results_text = f"共{len(issues)}个结果：\n\n"
                for i, issue in enumerate(issues[:20], 1):  # 最多显示20个
                    fields = issue.get('fields', {})
                    results_text += f"{i}. {issue.get('key', 'N/A')}: {fields.get('summary', 'N/A')}\n"
                    results_text += f"   状态: {fields.get('status', {}).get('name', 'N/A')}"
                    if fields.get('priority'):
                        results_text += f" | 优先级: {fields.get('priority', {}).get('name', 'N/A')}"
                    if fields.get('assignee'):
                        results_text += f" | 处理人: {fields.get('assignee', {}).get('displayName', 'N/A')}"
                    results_text += f"\n   JIRA单号: {issue.get('key', '')}\n\n"
                
                if len(issues) > 20:
                    results_text += f"... 还有{len(issues) - 20}个结果未显示\n"
            else:
                results_text = "查询结果为空"
        else:
            results_text = str(jira_results)
        
        user_prompt = cls.USER_PROMPT_TEMPLATE.format(
            intent=intent,
            user_query=user_query,
            jql=jql,
            jira_results=results_text
        )
        
        return cls.SYSTEM_PROMPT, user_prompt
    
    @classmethod
    def get_format_templates(cls) -> dict:
        """获取不同意图的格式化模板"""
        return {
            'query_issues': {
                'title': '查询结果',
                'empty_message': '未找到符合条件的issue',
                'show_fields': ['status', 'priority', 'assignee', 'created']
            },
            'query_status': {
                'title': 'Issue状态',
                'empty_message': '未找到指定的issue',
                'show_fields': ['status', 'assignee', 'updated', 'progress']
            },
            'query_epic': {
                'title': 'Epic信息',
                'empty_message': '未找到Epic信息',
                'show_fields': ['status', 'assignee', 'components', 'fixVersions']
            },
            'query_timeline': {
                'title': '时间线信息',
                'empty_message': '未找到时间线信息',
                'show_fields': ['status', 'created', 'updated', 'duedate']
            }
        }
    
    @classmethod
    def format_quick_response(cls, intent: str, issues: list, is_group_chat: bool = False) -> str:
        """快速格式化响应(不使用LLM)"""
        if not issues:
            return "经查询，没有符合查询条件的结果。"

        # 检查是否为子任务查询
        if len(issues) > 0 and issues[0].get('fields', {}).get('issuetype', {}).get('name') == 'Sub-task':
            return cls._format_subtask_response(issues)

        if len(issues) == 1:
            issue = issues[0]
            fields = issue.get('fields', {})
            key = issue.get('key', 'N/A')
            issuetype = issue.get('fields', {}).get('issuetype', {}).get('name', 'N/A')
            status = fields.get('status', {}).get('name', 'N/A')
            priority = fields.get('priority', {}).get('name', 'N/A') if fields.get('priority') else 'N/A'
            assignee = fields.get('assignee', {}).get('emailAddress', '未分配') if fields.get('assignee') else '未分配'

            response = f"根据您的查询条件，找到了1个相关的结果:\n\n"
            response += f"📋【{issuetype}】【{priority}】【{status}】{fields.get('summary', 'N/A')}\n"
            response += f"🔗https://jira.shopee.io/browse/{key}\n"

            # 处理经办人显示：群聊使用@提醒，私聊显示邮箱
            if assignee != '未分配':
                if is_group_chat:
                    response += f"经办人: <mention-tag target=\"seatalk://user?email={assignee}\"/>"
                else:
                    response += f"经办人: {assignee}"
            else:
                response += f"经办人: {assignee}"

            return response

        else:
            response = f"根据您的查询条件，找到了{len(issues)}个相关的结果:\n\n"

            for i, issue in enumerate(issues[:10], 1):
                fields = issue.get('fields', {})
                key = issue.get('key', 'N/A')
                issuetype = issue.get('fields', {}).get('issuetype', {}).get('name', 'N/A')
                status = fields.get('status', {}).get('name', 'N/A')
                priority = fields.get('priority', {}).get('name', 'N/A')
                assignee = fields.get('assignee', {}).get('emailAddress', '未分配') if fields.get('assignee') else '未分配'

                response += f"📋【{issuetype}】【{priority}】【{status}】{fields.get('summary', 'N/A')}\n"
                response += f"🔗https://jira.shopee.io/browse/{key}\n"

                # 处理经办人显示：群聊使用@提醒，私聊显示邮箱
                if assignee != '未分配':
                    if is_group_chat:
                        response += f"经办人: <mention-tag target=\"seatalk://user?email={assignee}\"/>"
                    else:
                        response += f"经办人: {assignee}"
                else:
                    response += f"经办人: {assignee}"

                # 添加分隔线（除了最后一个），保持紧凑格式
                if i < min(len(issues), 10):
                    response += "\n----------------------------------\n"

            if len(issues) > 10:
                response += f"\n... 还有{len(issues) - 10}个结果未显示"

            return response
    
    @classmethod 
    def _format_subtask_response(cls, issues: list) -> str:
        """专门格式化子任务查询结果"""
        if not issues:
            return "抱歉，未找到符合条件的子任务。"
        
        response = f"📋 **子任务查询结果** (共{len(issues)}个):\n\n"
        
        for i, issue in enumerate(issues[:15], 1):  # 子任务最多显示15个
            fields = issue.get('fields', {})
            key = issue.get('key', 'N/A')
            summary = fields.get('summary', 'N/A')
            status = fields.get('status', {}).get('name', 'N/A')
            assignee = fields.get('assignee', {}).get('emailAddress', '未分配') if fields.get('assignee') else '未分配'
            
            # 获取计划日期
            planned_start = cls._format_date_field(fields.get('customfield_16300'))
            planned_due = cls._format_date_field(fields.get('customfield_16301'))
            
            # 获取Story Points
            story_points = fields.get('customfield_10100') or 'N/A'
            
            response += f"🎯 **{key}** | {status} | https://jira.shopee.io/browse/{key}\n"
            response += f"📌 {summary}\n"
            response += f"👤 经办人: {assignee}\n"
            response += f"📅 计划开始: {planned_start or '未设置'}\n"
            response += f"📅 计划完成: {planned_due or '未设置'}\n"
            response += f"⚡ Story Points: {story_points}\n"
            
            # 添加分隔线（除了最后一个）
            if i < min(len(issues), 15):
                response += "\n\n----------------------------------\n"
            else:
                response += "\n"
        
        if len(issues) > 15:
            response += f"\n... 还有{len(issues) - 15}个子任务未显示"
        
        # 添加统计信息
        total_story_points = 0
        valid_points_count = 0
        for issue in issues:
            fields = issue.get('fields', {})
            points = fields.get('customfield_10100') or fields.get('customfield_10004')
            if points and isinstance(points, (int, float)):
                total_story_points += points
                valid_points_count += 1
        
        if valid_points_count > 0:
            response += f"\n📊 **工作量统计**: 总计 {total_story_points} Story Points ({valid_points_count}/{len(issues)} 个子任务有估算)"
        
        return response

    @classmethod
    def _format_date_field(cls, date_value) -> str:
        """格式化日期字段"""
        if not date_value:
            return None
        
        try:
            # 如果是字符串格式的日期
            if isinstance(date_value, str):
                if 'T' in date_value:
                    # ISO格式日期时间
                    from datetime import datetime
                    dt = datetime.fromisoformat(date_value.replace('Z', '+00:00'))
                    return dt.strftime('%Y-%m-%d')
                else:
                    # 纯日期格式
                    return date_value
            else:
                return str(date_value)
        except Exception:
            return str(date_value) if date_value else None 