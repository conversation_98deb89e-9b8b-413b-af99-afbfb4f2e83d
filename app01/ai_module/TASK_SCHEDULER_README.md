# 定时任务调度器使用说明

## 📋 概述

ChatbotAR 定时任务调度器是一个强大的用户定时任务管理系统，支持用户创建和管理个人定时查询任务，包括JIRA查询任务和提醒任务。

## 🏗️ 文件结构

```
chatbot-ar-be/
├── task_scheduler.sh                        # 🎯 统一管理脚本（主要使用）
├── app01/management/commands/
│   └── run_scheduled_tasks.py              # Django管理命令（核心逻辑）
├── app01/ai_module/
│   ├── task_scheduler.py                   # 任务调度器核心类
│   ├── advanced_task_manager.py            # 高级任务管理器
│   └── TASK_SCHEDULER_README.md            # 本文档
├── start.sh                                 # 整体服务启动脚本
├── stop.sh                                  # 整体服务停止脚本
└── logs/
    ├── task_scheduler.log                   # 主日志文件
    └── task_scheduler_daemon.log            # 管理日志文件
```

## 🚀 快速使用

### 基本命令

```bash
# 启动调度器
./task_scheduler.sh start

# 停止调度器
./task_scheduler.sh stop

# 重启调度器
./task_scheduler.sh restart

# 查看状态
./task_scheduler.sh status
```

### 日志查看

```bash
# 实时查看主日志
tail -f logs/task_scheduler.log

# 查看管理日志
tail -f logs/task_scheduler_daemon.log

# 查看最近的错误
grep "ERROR" logs/task_scheduler.log | tail -10
```

## 📖 功能特性

### 1. 智能通知功能

智能通知系统支持多种通知方式，能够根据JIRA任务的assignee智能选择通知对象。

#### 统一管理脚本（推荐）
```bash
# 启动调度器
./task_scheduler.sh start

# 停止调度器
./task_scheduler.sh stop

# 重启调度器
./task_scheduler.sh restart

# 查看状态
./task_scheduler.sh status

# 查看日志
tail -f logs/task_scheduler.log
```

#### 手动启动（用于调试）
```bash
# 手动启动调度器
python manage.py run_scheduled_tasks --interval 60

# 或者后台启动
nohup python manage.py run_scheduled_tasks --interval 60 > logs/task_scheduler.log 2>&1 &
```

### 2. 任务类型支持

- **JIRA查询任务**: 定期执行JIRA查询并发送结果
- **提醒任务**: 纯文本提醒，不涉及查询
- **智能通知**: 根据assignee智能路由通知

### 3. 调度频率支持

- **每天**: 指定时间每天执行
- **每周**: 指定工作日执行（如周一到周五）
- **每月**: 指定日期每月执行

### 4. 通知方式

- **私聊通知**: 发送给任务创建人
- **群聊通知**: 发送到指定群组
- **智能通知**: 根据assignee动态选择通知对象
- **多重通知**: 组合多种通知方式

## 🔧 使用场景

### 开发调试

```bash
# 查看调度器状态
./task_scheduler.sh status

# 重启调度器（应用代码更改）
./task_scheduler.sh restart

# 查看实时日志
tail -f logs/task_scheduler.log
```

### 生产部署

```bash
# 启动所有服务
./start.sh

# 停止所有服务
./stop.sh

# 单独重启调度器
./task_scheduler.sh restart
```

### 故障排查

```bash
# 检查调度器状态
./task_scheduler.sh status

# 查看错误日志
grep "ERROR" logs/task_scheduler.log | tail -20

# 强制重启
./task_scheduler.sh stop
./task_scheduler.sh start
```

## 🛠️ 统一管理脚本详解

### 脚本功能

`task_scheduler.sh` 提供以下功能：

- **自动环境检测**：自动检测并激活虚拟环境
- **进程管理**：智能检测和清理遗留进程
- **状态监控**：实时检查调度器运行状态
- **日志管理**：统一的日志输出和查看
- **错误处理**：完善的错误处理和恢复机制

### 脚本优势

1. **智能进程管理**：自动检测和清理遗留进程
2. **环境自适应**：自动检测虚拟环境
3. **完善错误处理**：优雅的启动、停止和错误恢复
4. **彩色输出**：清晰的状态显示
5. **详细日志**：分离的管理日志和运行日志
6. **状态检查**：实时进程状态监控

## 🛠️ 故障处理

### 常见问题

1. **调度器启动失败**
   ```bash
   # 检查Python环境
   which python3
   
   # 检查虚拟环境
   ls -la venv/
   
   # 查看详细错误
   ./task_scheduler.sh start
   ```

2. **进程遗留问题**
   ```bash
   # 查看所有相关进程
   ps aux | grep run_scheduled_tasks
   
   # 强制清理
   pkill -f "run_scheduled_tasks"
   
   # 重新启动
   ./task_scheduler.sh start
   ```

3. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x task_scheduler.sh
   
   # 确保日志目录可写
   chmod -R 755 logs/
   ```

### 紧急处理

如果遇到严重问题，可以使用以下命令强制清理：

```bash
# 强制杀死所有相关进程
pkill -9 -f "run_scheduled_tasks"

# 清理PID文件
rm -f task_scheduler.pid

# 重新启动
./task_scheduler.sh start
```

## 📊 监控指标

### 关键日志关键词

- `✅ 调度器启动成功` - 启动成功
- `❌ 调度器启动失败` - 启动失败
- `执行了 X 个任务，成功 Y 个` - 任务执行摘要
- `ERROR` - 错误信息
- `WARNING` - 警告信息

### 性能监控

```bash
# 查看内存使用
ps aux | grep run_scheduled_tasks

# 查看CPU使用
top -p $(cat task_scheduler.pid)

# 查看日志大小
ls -lh logs/task_scheduler.log
```

## 🔄 迁移说明

### 从旧版本迁移

如果你之前使用的是多个脚本文件，现在只需要：

1. **停止旧版本调度器**
   ```bash
   # 停止所有相关进程
   pkill -f "run_scheduled_tasks"
   ```

2. **使用新的统一脚本**
   ```bash
   ./task_scheduler.sh start
   ```

3. **验证运行状态**
   ```bash
   ./task_scheduler.sh status
   ```

### 删除的文件

以下文件已被删除，功能整合到 `task_scheduler.sh`：

- ~~`start_scheduler.py`~~ - Python启动包装器（已删除）
- ~~`start_task_scheduler_daemon.sh`~~ - Bash守护进程启动脚本（已删除）
- ~~`stop_task_scheduler.sh`~~ - 独立停止脚本（已删除）

## 📝 最佳实践

1. **使用统一脚本**：始终使用 `./task_scheduler.sh` 管理调度器
2. **定期检查状态**：使用 `./task_scheduler.sh status` 检查运行状态
3. **监控日志**：定期查看 `logs/task_scheduler.log` 中的错误信息
4. **优雅重启**：代码更新后使用 `./task_scheduler.sh restart` 重启
5. **完整停止**：维护时使用 `./stop.sh` 停止所有服务

## 🏗️ 系统架构

### 核心组件

1. **TaskScheduler** (`app01/ai_module/task_scheduler.py`)
   - 核心调度逻辑
   - 任务创建、执行、管理
   - 异步任务处理

2. **AdvancedTaskManager** (`app01/ai_module/advanced_task_manager.py`)
   - 高级任务管理功能
   - 智能通知路由
   - 权限管理

3. **Django管理命令** (`app01/management/commands/run_scheduled_tasks.py`)
   - 命令行接口
   - 循环调度逻辑
   - 异常处理

### 数据模型

- **UserScheduledTask**: 用户定时任务
- **TaskExecutionLog**: 任务执行日志
- **智能通知配置**: 动态通知路由配置

## 🔒 系统服务配置（可选）

为了让调度器在系统重启后自动启动，可以创建系统服务：

### Ubuntu/Debian 系统服务
```bash
# 创建服务文件
sudo vim /etc/systemd/system/chatbot-scheduler.service
```

服务文件内容：
```ini
[Unit]
Description=ChatbotAR Task Scheduler
After=network.target

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/chatbot-ar-be
ExecStart=/usr/bin/python3 manage.py run_scheduled_tasks --interval 60
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable chatbot-scheduler
sudo systemctl start chatbot-scheduler
sudo systemctl status chatbot-scheduler
```

## 📞 技术支持

如有问题，请查看：
1. 日志文件：`logs/task_scheduler.log`
2. 管理日志：`logs/task_scheduler_daemon.log`
3. 使用 `./task_scheduler.sh status` 检查状态
4. 参考故障处理章节进行排查
