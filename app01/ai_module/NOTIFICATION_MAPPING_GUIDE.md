# 通知方式映射统一化指南

## 🎯 问题解决

**问题**：通知方式映射混乱，`smart`、`private`、`both`等概念模糊
**解决**：统一映射到直观的四种发送方式

## 📋 标准化映射关系

### ✅ 界面显示 ↔ 存储值

| 界面显示 | 前端值 | 存储值 | 说明 |
|---------|--------|--------|------|
| 发送给任务创建人 | `creator` | `creator` | 直接对应 |
| 发送给查询结果的assignee | `assignee` | `assignee` | 直接对应，原`smart` |
| 发送到指定群 | `group` | `group` | 直接对应 |
| 发送到查询结果所在群 | `auto_group` | `auto_group` | 直接对应 |

### 🔄 多选处理

```python
# notification_methods 存储多选值
notification_methods = ["creator", "assignee"]

# notification_type 存储主要类型
if len(notification_methods) > 1:
    notification_type = "multi"  # 多选标记
else:
    notification_type = notification_methods[0]  # 单选时直接使用
```

### 🆔 旧数据兼容

```python
legacy_mapping = {
    'private': ['creator'],      # 私聊 → 创建人
    'smart': ['assignee'],       # 智能通知 → assignee  
    'both': ['creator', 'group'], # 私聊+群聊 → 创建人+指定群
    'group': ['group'],          # 群聊 → 指定群
    'auto_group': ['auto_group'] # 结果所在群 → 查询结果所在群
}
```

## 🔧 实现要点

### 1. 数据库字段设计

```python
# 主字段 - 向后兼容
notification_type = models.CharField(
    choices=[
        ('creator', '发送给任务创建人'),
        ('assignee', '发送给查询结果的assignee'),
        ('group', '发送到指定群'),
        ('auto_group', '发送到查询结果所在群'),
        ('multi', '多种发送方式'),
    ]
)

# 新字段 - 多选支持
notification_methods = models.JSONField(
    default=list,
    help_text='["creator", "assignee", "group", "auto_group"]'
)
```

### 2. API处理逻辑

```python
# 创建/编辑时
notification_methods = request_data.get('notification_types', ['creator'])
if len(notification_methods) > 1:
    notification_type = 'multi'
else:
    notification_type = notification_methods[0]

# 查询时
methods = task.notification_methods or []
if not methods:
    # 兼容旧数据
    methods = legacy_mapping.get(task.notification_type, ['creator'])
```

### 3. 前端回显逻辑

```javascript
// 编辑时设置选中状态
let notificationTypes = task.notification_methods || [];
if (!notificationTypes.length) {
    // 处理旧数据
    const mapping = {
        'private': ['creator'],
        'smart': ['assignee'],
        'both': ['creator', 'group']
    };
    notificationTypes = mapping[task.notification_type] || ['creator'];
}
setNotificationTypes(notificationTypes);
```

## 🚀 优化效果

### ✅ 概念清晰化

- ❌ **优化前**：`smart` (含义模糊)
- ✅ **优化后**：`assignee` (明确指向查询结果的assignee)

### ✅ 映射一致性

- ❌ **优化前**：前端用`creator`，后端存`private`
- ✅ **优化后**：前后端统一使用`creator`

### ✅ 多选支持

- ❌ **优化前**：只能单选或硬编码的组合(`both`)
- ✅ **优化后**：灵活多选，任意组合

### ✅ 兼容性保证

- ✅ 旧数据自动转换
- ✅ 新旧系统平滑过渡
- ✅ 所有创建方式(页面/自然语言/指令)统一

## 📚 使用示例

### 页面创建
```javascript
// 用户选择：创建人 + 指定群
notification_types: ["creator", "group"]
target_group_keyword: "SPCB-1234"
```

### 自然语言创建
```python
# "发送给assignee和创建人"
notification_methods = ["assignee", "creator"]
notification_type = "multi"
```

### API返回
```json
{
    "notification_type": "multi",
    "notification_methods": ["creator", "assignee"],
    "display": "创建人 + Assignee"
}
```

## 🎯 结果

现在所有创建方式都使用相同的标准化通知类型，概念清晰，映射一致，完全兼容！
