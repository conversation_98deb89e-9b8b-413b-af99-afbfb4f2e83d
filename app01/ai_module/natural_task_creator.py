"""
自然语言任务创建器
支持通过自然语言和多轮对话创建定时任务
"""

import json
import re
import logging
from typing import Dict, List, Optional, Tuple
from django.utils import timezone

from .multi_llm_client import multi_llm_client
from .conversation_manager import conversation_manager

logger = logging.getLogger(__name__)


class NaturalTaskCreator:
    """自然语言任务创建器"""
    
    def __init__(self):
        self.max_retries = 2
        
    async def process_natural_request(self, user_query: str, user_id: str, 
                                    user_email: str, session_id: str, group_id: str = None, group_title: str = None) -> Dict:
        """
        处理自然语言任务创建请求
        
        Args:
            user_query: 用户的自然语言请求
            user_id: 用户ID
            user_email: 用户邮箱
            session_id: 会话ID
            
        Returns:
            处理结果
        """
        try:
            logger.info(f"🌟 开始自然语言任务创建 - 用户: {user_id}, 查询: '{user_query}'")
            
            # 1. 解析用户意图，提取已有信息
            parse_result = await self._parse_user_intent(user_query, user_email)
            logger.info(f"🔍 解析意图结果: success={parse_result.get('success')}, task_config={parse_result.get('task_config')}, missing_info={parse_result.get('missing_info')}")
            
            if not parse_result['success']:
                logger.error(f"❌ 意图解析失败: {parse_result.get('error')}")
                return parse_result
            
            task_config = parse_result['task_config']
            missing_info = parse_result['missing_info']
            
            logger.info(f"🔍 意图解析结果: 配置={task_config}, 缺失信息={missing_info}")
            
            # 2. 如果信息完整，解析通知目标并创建任务
            if not missing_info:
                # 解析通知目标
                resolved_config = await self._resolve_notification_target(task_config, group_id, group_title)
                if not resolved_config.get('success', True):  # 如果解析失败，resolved_config会包含error
                    return resolved_config
                
                return await self._create_task_directly(resolved_config, user_id, user_email, group_id, group_title)
            
            # 3. 如果信息不完整，生成确认问题
            logger.info(f"🔄 信息不完整，需要澄清。缺失信息: {missing_info}")
            confirmation_result = await self._generate_confirmation_questions(
                task_config, missing_info, user_query
            )
            
            if not confirmation_result['success']:
                logger.error(f"❌ 生成确认问题失败: {confirmation_result.get('error')}")
                return confirmation_result
            
            # 4. 保存中间状态到会话
            await self._save_intermediate_state(
                session_id, task_config, missing_info, user_query
            )
            
            return {
                'success': True,
                'needs_clarification': True,
                'clarification': confirmation_result['questions'],
                'intent': 'natural_task_creation',
                'intermediate_state': {
                    'task_config': task_config,
                    'missing_info': missing_info,
                    'original_query': user_query
                }
            }
            
        except Exception as e:
            logger.error(f"自然语言任务创建异常: {str(e)}")
            return {
                'success': False,
                'error': f"自然语言任务创建异常: {str(e)}"
            }
    
    async def process_clarification_response(self, user_response: str, session_id: str,
                                           user_id: str, user_email: str) -> Dict:
        """
        处理用户的澄清回复
        
        Args:
            user_response: 用户的回复
            session_id: 会话ID
            user_id: 用户ID
            user_email: 用户邮箱
            
        Returns:
            处理结果
        """
        try:
            # 1. 获取中间状态
            intermediate_state = await self._get_intermediate_state(session_id)
            
            if not intermediate_state:
                return {
                    'success': False,
                    'error': "未找到任务创建的中间状态，请重新开始"
                }
            
            task_config = intermediate_state['task_config']
            missing_info = intermediate_state['missing_info']
            original_query = intermediate_state['original_query']
            
            # 2. 解析用户回复，补全配置
            update_result = await self._update_config_from_response(
                user_response, task_config, missing_info
            )
            
            if not update_result['success']:
                return update_result
            
            updated_config = update_result['updated_config']
            remaining_missing = update_result['remaining_missing']
            
            logger.info(f"🔄 配置更新结果: 更新后配置={updated_config}, 剩余缺失={remaining_missing}")
            
            # 3. 如果还有缺失信息，继续询问
            if remaining_missing:
                confirmation_result = await self._generate_confirmation_questions(
                    updated_config, remaining_missing, f"{original_query} -> {user_response}"
                )
                
                if not confirmation_result['success']:
                    return confirmation_result
                
                # 更新中间状态
                await self._save_intermediate_state(
                    session_id, updated_config, remaining_missing, original_query
                )
                
                return {
                    'success': True,
                    'needs_clarification': True,
                    'clarification': confirmation_result['questions'],
                    'intent': 'natural_task_creation_continue',
                    'intermediate_state': {
                        'task_config': updated_config,
                        'missing_info': remaining_missing,
                        'original_query': original_query
                    }
                }
            
            # 4. 信息完整，解析通知目标并创建任务
            # 解析通知目标
            resolved_config = await self._resolve_notification_target(updated_config, None, None)  # 澄清时暂时不传群信息
            if not resolved_config.get('success', True):
                return resolved_config
            
            return await self._create_task_directly(resolved_config, user_id, user_email)
            
        except Exception as e:
            logger.error(f"处理澄清回复异常: {str(e)}")
            return {
                'success': False,
                'error': f"处理澄清回复异常: {str(e)}"
            }
    
    async def _parse_user_intent(self, user_query: str, user_email: str) -> Dict:
        """解析用户意图，提取任务配置信息"""
        
        # 动态替换currentUser()
        processed_query = self._replace_current_user(user_query, user_email)
        
        system_prompt = """你是一个专业的任务创建助手，能够理解用户的自然语言请求并提取定时任务的配置信息。

## 任务类型：

### 1. JIRA查询任务 (task_type: "jira_query")
用于查询JIRA系统中的单据信息，如bug、任务等

### 2. 提醒任务 (task_type: "reminder") 
用于发送纯文本提醒，不涉及JIRA查询，如"提醒喝水"、"提醒查看邮件"等

## 任务配置信息包括：

### 必需信息：
1. **task_type** (任务类型): "jira_query" 或 "reminder"
2. **task_name** (任务名称): 简洁描述任务目的
3. **schedule_expression** (调度表达式): 执行频率和时间

### JIRA查询任务额外必需：
4. **jql_query** (JQL查询): 具体的JIRA查询语句

### 提醒任务额外必需：
4. **reminder_message** (提醒内容): 具体的提醒消息内容

### 可选信息：
5. **notification_type** (通知类型): private(私聊), group(群聊), both(私聊+群聊)
6. **notification_target** (通知目标): 具体的群ID或用户，支持"本群"、"当前群"、"这个群"等指代
7. **message_template** (消息模板): 自定义通知消息格式

## JQL查询生成规则：

### 人员处理：
- "我的任务" → assignee = currentUser()
- "分配给我的" → assignee = currentUser()  
- "liang.tang的任务" → assignee = "<EMAIL>"
- "某人的bug" → assignee = "某人@shopee.com" AND issuetype = Bug

### 时间处理：
- "今天" → created >= startOfDay()
- "本周" → created >= startOfWeek()
- "上周" → created >= startOfWeek(-1w) AND created < startOfWeek()
- "最近7天" → created >= -7d

### 状态和类型：
- "未完成的" → status not in (Closed, Done, Icebox)
- "未解决的" → status not in (Closed, Done, Icebox)
- "要关注的" → status not in (Closed, Done, Icebox)
- "要解决的" → status not in (Closed, Done, Icebox)
- "需要处理的" → status not in (Closed, Done, Icebox)
- "进行中的" → status not in (Closed, Done, Icebox)
- "bug" → issuetype = Bug
- "任务" → issuetype = Task
- "需求" → issuetype = Epic
- "子任务" → issuetype = sub-task
- "工单" → issuetype = sub-task

### 项目处理：
- 自动从JIRA单号提取项目前缀 (如SPCB-123 → project = SPCB)

## 调度表达式格式：
- "每天上午10点" → "daily 10:00"
- "每周一上午9点" → "weekly 09:00 1"
- "每月1号上午8点" → "monthly 1 08:00"
- "工作日下午5点" → "weekly 17:00 1 2 3 4 5"
- "每个工作日11:20" → "weekly 11:20 1 2 3 4 5"
- "每个工作日18:52" → "weekly 18:52 1 2 3 4 5"

## 时间表达式理解增强：
- 理解各种表达周期的方式，如"每个工作日"、"每周一到周五"、"工作日"、"每天"等
- 能够从上下文中推断出完整的调度信息，即使用户表达不完整
- 对于"每个工作日"，应理解为周一到周五（1-5）

## 任务类型识别规则：

### JIRA查询任务关键词：
- 包含JIRA相关词汇：bug、任务、单据、工单、issue、assignee等
- 包含查询词汇：查询、查看、检查、统计等（在JIRA上下文中）
- 包含状态词汇：未完成、进行中、已关闭等

### 提醒任务关键词：
- 纯提醒词汇：提醒、提示、通知等
- 生活/工作提醒：喝水、休息、会议、邮件、咖啡等
- 不涉及查询操作，只是简单提醒
- **重要识别规则**：当用户说"提醒我：XXX"或"提醒我XXX"时，通常是提醒任务

### 提醒任务配置生成规则：
1. **task_name生成**：根据提醒内容和频率生成描述性名称
   - "每天XXX" → "每日XXX提醒"  
   - "工作日XXX" → "工作日XXX提醒"
   - "每周X XXX" → "每周XXX提醒"

2. **reminder_message生成**：将提醒内容转换为友好的提醒消息
   - "检查邮件" → "📧 该检查邮件了！记得处理重要邮件哦~\nhttps://mail.google.com/"
   - "喝水" → "💧 该喝水了！保持健康很重要~"
   - "休息" → "🌙 该休息一下了！起来走动一下，活动一下颈椎，劳逸结合更高效~"
   - "会议" → "📅 会议时间到了！准备参加会议吧~\nhttps://calendar.google.com/calendar/u/0/r"
   - "咖啡" → "☕ 下午茶时间到了！来杯咖啡提提神吧~"
   - "上班" → "💼 上班时间到了！准备开始一天的工作吧~"
   - "下班" → "🌙 下班时间到了！准备回家吧~"

3. **时间表达式识别**：
   - "每个工作日 HH:MM" → "weekly HH:MM 1 2 3 4 5"
   - "工作日 HH:MM" → "weekly HH:MM 1 2 3 4 5"
   - "每天 HH:MM" → "daily HH:MM"
   - "每周X HH:MM" → "weekly HH:MM X"（X为周几的数字）

## 重要：对于明确的定时任务请求，应该尽可能完整解析，避免不必要的澄清

### 明确的任务请求示例：
用户输入: "每个工作日 11:33提醒我有哪些未完成的子任务"
这应该被完整解析为：
```json
{
  "task_config": {
    "task_type": "jira_query",
    "task_name": "工作日未完成子任务提醒",
    "jql_query": "assignee = currentUser() AND issuetype = sub-task AND status not in (Closed, Done, Icebox)",
    "schedule_expression": "weekly 11:33 1 2 3 4 5",
    "notification_type": "private"
  },
  "missing_info": []
}
```

用户输入: "每个工作日 11:33提醒我查看邮件"
这应该被完整解析为：
```json
{
  "task_config": {
    "task_type": "reminder",
    "task_name": "工作日邮件检查提醒",
    "reminder_message": "📧 该检查邮件了！记得处理重要邮件哦~\nhttps://mail.google.com/",
    "schedule_expression": "weekly 11:33 1 2 3 4 5",
    "notification_type": "private"
  },
  "missing_info": []
}
```

## 示例分析：

### JIRA查询任务示例：
用户输入: "每天早上10点提醒我查看分配给我的未完成bug"
输出:
```json
{
  "task_config": {
    "task_type": "jira_query",
    "task_name": "每日未完成bug提醒",
    "jql_query": "assignee = currentUser() AND issuetype = Bug AND status not in (Closed, Done, Icebox)",
    "schedule_expression": "daily 10:00",
    "notification_type": "private"
  },
  "missing_info": []
}
```

### 提醒任务示例：
用户输入: "每天早上9:30提醒我查看邮件"
输出:
```json
{
  "task_config": {
    "task_type": "reminder",
    "task_name": "每日查看邮件提醒",
    "reminder_message": "📧 该查看邮件了！记得处理重要邮件哦~\nhttps://mail.google.com/",
    "schedule_expression": "daily 09:30",
    "notification_type": "private"
  },
  "missing_info": []
}
```

用户输入: "工作日下午3点提醒我喝杯咖啡"
输出:
```json
{
  "task_config": {
    "task_type": "reminder", 
    "task_name": "工作日咖啡时间提醒",
    "reminder_message": "☕ 下午茶时间到了！来杯咖啡提提神吧~",
    "schedule_expression": "weekly 15:00 1 2 3 4 5",
    "notification_type": "private"
  },
  "missing_info": []
}
```

用户输入: "每个工作日 18:52 提醒我：检查邮件"
输出:
```json
{
  "task_config": {
    "task_type": "reminder",
    "task_name": "工作日邮件检查提醒", 
    "reminder_message": "📧 该检查邮件了！记得处理重要邮件哦~~\nhttps://mail.google.com/",
    "schedule_expression": "weekly 18:52 1 2 3 4 5",
    "notification_type": "private"
  },
  "missing_info": []
}
```

用户输入: "每个工作日的 18:53 提醒下班，提醒到这个群"
输出:
```json
{
  "task_config": {
    "task_type": "reminder",
    "task_name": "工作日下班提醒", 
    "reminder_message": "🌙 下班时间到了！准备回家吧~",
    "schedule_expression": "weekly 18:53 1 2 3 4 5",
    "notification_type": "group",
    "notification_target": "current_group"
  },
  "missing_info": []
}
```

用户输入: "每周一 9:30 提醒我参加团队会议"
输出:
```json
{
  "task_config": {
    "task_type": "reminder",
    "task_name": "每周团队会议提醒",
    "reminder_message": "📅 团队会议时间到了！准备参加会议吧~",
    "schedule_expression": "weekly 09:30 1",
    "notification_type": "private"
  },
  "missing_info": []
}
```

用户输入: "每个工作日的11:20，查询：issue in linkedIssues(\"SPCB-54865\", \"is blocked by\") AND type = bug 的内容提醒到本群"
输出:
```json
{
  "task_config": {
    "task_name": "SPCB-54865阻塞bug提醒",
    "jql_query": "issue in linkedIssues(\"SPCB-54865\", \"is blocked by\") AND type = bug",
    "schedule_expression": "weekly 11:20 1 2 3 4 5",
    "notification_type": "group",
    "notification_target": "current_group"
  },
  "missing_info": []
}
```

## 通知目标处理规则：
### 群聊通知目标识别：
- "发送到本群"、"提醒到当前群"、"通知这个群" → notification_target: "current_group"
- "发送到SPCB-54697的群" → notification_target: "group:SPCB-54697"（表示搜索群名包含此关键字的群）
- "发送到测试群"、"提醒到开发群" → notification_target: "group:测试" 或 "group:开发"

用户输入: "帮我设置一个定时任务查看SPCB项目的bug"
输出:
```json
{
  "task_config": {
    "task_name": "SPCB项目bug查询",
    "jql_query": "project = SPCB AND issuetype = Bug"
  },
  "missing_info": ["schedule_expression", "notification_type"]
}
```

## 输出格式：
请返回JSON格式，包含两个字段：
- task_config: 已提取的配置信息
- missing_info: 缺失的必需信息列表

如果无法理解用户意图，返回错误信息。"""

        user_prompt = f"""用户请求: {processed_query}

请分析用户的自然语言请求，提取定时任务的配置信息。

注意事项：
1. 仔细分析用户的具体需求
2. 生成准确的JQL查询语句
3. 识别调度时间要求
4. 判断通知方式偏好
5. 标识缺失的必需信息
6. **重要**：对于包含明确时间表达式的请求，应该尽可能完整解析，避免不必要的澄清

请按JSON格式返回分析结果。"""

        try:
            result = await multi_llm_client.generate_with_retry(
                user_prompt, system_prompt, max_retries=self.max_retries
            )
            
            if not result['success']:
                return {
                    'success': False,
                    'error': f"意图解析失败: {result['error']}"
                }
            
            # 解析LLM返回的JSON
            try:
                # 更强健的markdown格式清理
                content = result['content'].strip()
                
                # 移除可能的LLM标识符
                if '🤖' in content and '回答:' in content:
                    # 查找JSON部分
                    json_start = content.find('```json')
                    if json_start != -1:
                        content = content[json_start:]
                    else:
                        json_start = content.find('{')
                        if json_start != -1:
                            content = content[json_start:]
                
                # 清理markdown代码块
                if content.startswith('```json'):
                    content = content[7:]
                elif content.startswith('```'):
                    content = content[3:]
                
                if content.endswith('```'):
                    content = content[:-3]
                
                content = content.strip()
                
                # 确保内容以{开头，}结尾
                if not content.startswith('{'):
                    json_start = content.find('{')
                    if json_start != -1:
                        content = content[json_start:]
                    else:
                        raise ValueError("未找到有效的JSON内容")
                
                if not content.endswith('}'):
                    json_end = content.rfind('}')
                    if json_end != -1:
                        content = content[:json_end + 1]
                    else:
                        raise ValueError("JSON格式不完整")
                
                logger.info(f"🔍 清理后的JSON内容: {content[:200]}...")
                
                response_data = json.loads(content)
                
                # 验证必需字段
                if 'task_config' not in response_data or 'missing_info' not in response_data:
                    raise ValueError("返回数据格式不正确")
                
                return {
                    'success': True,
                    'task_config': response_data['task_config'],
                    'missing_info': response_data['missing_info']
                }
                
            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"解析LLM返回JSON失败: {str(e)}, 原始内容: {result['content'][:500]}...")
                
                # 尝试从文本中提取基本信息作为fallback
                fallback_result = self._extract_fallback_config(user_query, result['content'])
                if fallback_result['success']:
                    logger.info(f"🔄 使用fallback配置: {fallback_result}")
                    return fallback_result
                
                return {
                    'success': False,
                    'error': f"意图解析结果格式错误: {str(e)}"
                }
                
        except Exception as e:
            logger.error(f"意图解析异常: {str(e)}")
            return {
                'success': False,
                'error': f"意图解析异常: {str(e)}"
            }
    
    async def _generate_confirmation_questions(self, task_config: Dict, 
                                             missing_info: List[str], 
                                             user_query: str) -> Dict:
        """生成确认问题"""
        
        system_prompt = """你是一个友好的任务创建助手，需要向用户询问缺失的任务配置信息。

## 需要询问的信息类型：

### schedule_expression (调度表达式)
问题示例：
- "您希望这个任务什么时候执行呢？比如每天上午10点，或者每周一上午9点？"
- "请告诉我执行频率，例如：每天、每周、每月，以及具体时间"

### notification_type (通知类型)  
问题示例：
- "您希望通过什么方式接收通知？\n1. 私聊通知\n2. 群聊通知\n3. 私聊+群聊\n4. 智能通知（自动选择最合适的方式）"

### task_name (任务名称)
问题示例：
- "请为这个任务起个名字，方便后续管理"

### jql_query (JQL查询)
问题示例：
- "请提供更具体的查询条件，比如要查询哪个项目的什么类型的单据？"

## 生成原则：
1. 语言友好自然，避免技术术语
2. 提供具体的示例选项
3. 一次询问1-2个最重要的信息
4. 根据已有配置智能调整问题

## 输出格式：
返回JSON格式：
```json
{
  "questions": "友好的询问文本",
  "suggested_options": ["选项1", "选项2", "选项3"],
  "priority_info": ["最优先需要的信息类型"]
}
```"""

        user_prompt = f"""用户原始请求: {user_query}

当前任务配置: {json.dumps(task_config, ensure_ascii=False, indent=2)}

缺失信息: {missing_info}

请生成友好的确认问题，帮助用户补全任务配置。

注意：
1. 优先询问最重要的缺失信息
2. 提供清晰的选项和示例
3. 保持友好的语调
4. 一次不要询问太多信息

请按JSON格式返回问题。"""

        try:
            result = await multi_llm_client.generate_with_retry(
                user_prompt, system_prompt, max_retries=self.max_retries
            )
            
            if not result['success']:
                return {
                    'success': False,
                    'error': f"生成确认问题失败: {result['error']}"
                }
            
            # 解析返回的JSON
            try:
                # 更强健的markdown格式清理
                content = result['content'].strip()
                
                # 移除可能的LLM标识符
                if '🤖' in content and '回答:' in content:
                    # 查找JSON部分
                    json_start = content.find('```json')
                    if json_start != -1:
                        content = content[json_start:]
                    else:
                        json_start = content.find('{')
                        if json_start != -1:
                            content = content[json_start:]
                
                # 清理markdown代码块
                if content.startswith('```json'):
                    content = content[7:]
                elif content.startswith('```'):
                    content = content[3:]
                
                if content.endswith('```'):
                    content = content[:-3]
                
                content = content.strip()
                
                # 确保内容以{开头，}结尾
                if not content.startswith('{'):
                    json_start = content.find('{')
                    if json_start != -1:
                        content = content[json_start:]
                    else:
                        raise ValueError("未找到有效的JSON内容")
                
                if not content.endswith('}'):
                    json_end = content.rfind('}')
                    if json_end != -1:
                        content = content[:json_end + 1]
                    else:
                        raise ValueError("JSON格式不完整")
                
                logger.info(f"🔍 清理后的确认问题JSON: {content[:200]}...")
                
                questions_data = json.loads(content)
                return {
                    'success': True,
                    'questions': questions_data
                }
                
            except json.JSONDecodeError as e:
                logger.warning(f"生成确认问题JSON解析失败: {str(e)}, 原始内容: {result['content'][:300]}...")
                # 如果JSON解析失败，生成简单的确认问题
                return self._generate_simple_confirmation_questions(missing_info, task_config)
                
        except Exception as e:
            logger.error(f"生成确认问题异常: {str(e)}")
            return {
                'success': False,
                'error': f"生成确认问题异常: {str(e)}"
            }
    
    def _generate_simple_confirmation_questions(self, missing_info: List[str], task_config: Dict) -> Dict:
        """生成简单的确认问题（fallback方法）"""
        
        questions = []
        suggested_options = []
        task_type = task_config.get('task_type', 'unknown')
        
        # 根据任务类型生成更精准的澄清问题
        if task_type == 'reminder':
            # 提醒任务的澄清逻辑
            if 'schedule_expression' in missing_info:
                questions.append("⏰ 请告诉我提醒的具体时间，例如：\n• 每天上午10点\n• 每个工作日下午6点\n• 每周一上午9点")
                suggested_options.extend(["每天上午10点", "每个工作日下午6点", "每周一上午9点"])
            
            if 'reminder_message' in missing_info:
                questions.append("💬 请告诉我提醒的具体内容，例如：\n• 检查邮件\n• 喝水休息\n• 准备会议")
                suggested_options.extend(["检查邮件", "喝水休息", "准备会议"])
            
            if 'task_name' in missing_info:
                questions.append("📝 请为这个提醒任务起个名字，例如：\n• 每日邮件提醒\n• 工作日休息提醒")
                suggested_options.extend(["每日邮件提醒", "工作日休息提醒"])
                
        elif task_type == 'jira_query':
            # JIRA查询任务的澄清逻辑
            if 'schedule_expression' in missing_info:
                questions.append("⏰ 请告诉我查询的执行时间，例如：\n• 每天上午10点\n• 每周一上午9点\n• 工作日下午5点")
                suggested_options.extend(["每天上午10点", "每周一上午9点", "工作日下午5点"])
            
            if 'jql_query' in missing_info:
                questions.append("🔍 请提供更具体的查询条件，比如：\n• 查询我的未完成任务\n• 查询SPCB项目的bug\n• 查询高优先级的需求")
                suggested_options.extend(["查询我的未完成任务", "查询SPCB项目的bug", "查询高优先级的需求"])
            
            if 'task_name' in missing_info:
                questions.append("📝 请为这个查询任务起个名字，方便后续管理")
        else:
            # 通用澄清逻辑
            for info_type in missing_info:
                if info_type == 'schedule_expression':
                    questions.append("⏰ 请告诉我任务的执行时间，例如：\n• 每天上午10点\n• 每周一上午9点\n• 工作日下午5点")
                    suggested_options.extend(["每天上午10点", "每周一上午9点", "工作日下午5点"])
                
                elif info_type == 'notification_type':
                    questions.append("📢 请选择通知方式：\n1. 私聊通知\n2. 群聊通知")
                    suggested_options.extend(["私聊通知", "群聊通知"])
                
                elif info_type == 'task_name':
                    questions.append("📝 请为这个任务起个名字，方便后续管理")
                
                elif info_type == 'jql_query':
                    questions.append("🔍 请提供更具体的查询条件，比如要查询哪个项目的什么类型的单据？")
                
                elif info_type == 'reminder_message':
                    questions.append("💬 请告诉我具体的提醒内容")
        
        # 添加更友好的引导信息
        if task_type == 'reminder':
            header = f"🔔 **设置提醒任务**\n\n我理解您想要设置一个提醒任务"
            if task_config.get('reminder_message'):
                header += f"：{task_config['reminder_message']}"
            header += "。\n\n为了完成设置，还需要以下信息：\n\n"
        elif task_type == 'jira_query':
            header = f"📋 **设置JIRA查询任务**\n\n我理解您想要设置一个定时查询任务。\n\n为了完成设置，还需要以下信息：\n\n"
        else:
            header = f"⚙️ **设置定时任务**\n\n为了完成任务设置，还需要以下信息：\n\n"
        
        question_text = header + "\n\n".join(questions)
        
        # 添加底部提示
        footer = "\n\n💡 **提示**: 您可以直接回复相关信息，我会自动识别并完成任务设置。"
        question_text += footer
        
        return {
            'success': True,
            'questions': {
                'questions': question_text,
                'suggested_options': suggested_options,
                'priority_info': missing_info,
                'task_type': task_type
            }
        }
    
    async def _update_config_from_response(self, user_response: str, 
                                         current_config: Dict, 
                                         missing_info: List[str]) -> Dict:
        """根据用户回复更新配置"""
        
        system_prompt = """你是一个配置更新助手，需要根据用户的回复更新任务配置。

## 更新规则：

### schedule_expression (调度表达式)
用户回复示例 → 标准格式：
- "每天上午10点" → "daily 10:00"
- "每天早上9点半" → "daily 09:30"
- "每周一上午9点" → "weekly monday 09:00"
- "每月1号上午8点" → "monthly 1 08:00"
- "工作日下午5点" → "weekdays 17:00"
- "每周五下午6点" → "weekly friday 18:00"

### notification_type (通知类型)
用户回复示例 → 标准值：
- "私聊" / "私信" / "1" → "private"
- "群聊" / "群里" / "2" → "group"  
- "都要" / "两种" / "3" → "both"


### task_name (任务名称)
直接使用用户提供的名称，适当优化格式

### jql_query (JQL查询)
根据用户描述生成或完善JQL查询

## 输出格式：
```json
{
  "updated_config": {
    // 更新后的完整配置
  },
  "remaining_missing": [
    // 仍然缺失的信息
  ],
  "update_summary": "更新说明"
}
```"""

        user_prompt = f"""用户回复: {user_response}

当前配置: {json.dumps(current_config, ensure_ascii=False, indent=2)}

缺失信息: {missing_info}

请根据用户回复更新任务配置，并标识仍然缺失的信息。

注意：
1. 准确解析用户的回复意图
2. 将用户回复转换为标准格式
3. 保留原有的正确配置
4. 标识仍需要询问的信息

请按JSON格式返回更新结果。"""

        try:
            result = await multi_llm_client.generate_with_retry(
                user_prompt, system_prompt, max_retries=self.max_retries
            )
            
            if not result['success']:
                return {
                    'success': False,
                    'error': f"配置更新失败: {result['error']}"
                }
            
            # 解析返回的JSON
            try:
                # 清理可能的markdown格式
                content = result['content'].strip()
                if content.startswith('```json'):
                    content = content[7:]
                if content.startswith('```'):
                    content = content[3:]
                if content.endswith('```'):
                    content = content[:-3]
                content = content.strip()
                
                update_data = json.loads(content)
                
                return {
                    'success': True,
                    'updated_config': update_data.get('updated_config', current_config),
                    'remaining_missing': update_data.get('remaining_missing', missing_info),
                    'update_summary': update_data.get('update_summary', '配置已更新')
                }
                
            except json.JSONDecodeError as e:
                logger.error(f"解析配置更新JSON失败: {str(e)}")
                # 提供简单的fallback处理
                fallback_config = self._simple_config_update(user_response, current_config, missing_info)
                logger.info(f"🔄 使用简单配置更新fallback: {fallback_config}")
                return fallback_config
                
        except Exception as e:
            logger.error(f"配置更新异常: {str(e)}")
            return {
                'success': False,
                'error': f"配置更新异常: {str(e)}"
            }
    
    async def _resolve_notification_target(self, task_config: Dict, group_id: str = None, group_title: str = None) -> Dict:
        """
        解析和处理通知目标
        
        Args:
            task_config: 任务配置
            group_id: 当前群组ID
            group_title: 当前群组标题
            
        Returns:
            处理后的任务配置
        """
        notification_target = task_config.get('notification_target')
        notification_type = task_config.get('notification_type', 'private')
        
        if not notification_target or notification_type not in ['group', 'both']:
            return task_config
        
        try:
            # 处理"当前群"的指代
            if notification_target == 'current_group':
                if group_id:
                    task_config['target_group_id'] = group_id
                    logger.info(f"✅ 解析当前群: {group_id}")
                else:
                    logger.warning("⚠️ 用户指定发送到当前群，但无法获取当前群ID")
                    return {
                        'success': False,
                        'error': "无法获取当前群信息，请在群聊中发送此指令或明确指定群名"
                    }
            
            # 处理群名搜索（格式：group:关键字）
            elif notification_target.startswith('group:'):
                keyword = notification_target[6:]  # 移除"group:"前缀
                target_group_id = await self._search_group_by_keyword(keyword)
                
                if target_group_id:
                    task_config['target_group_id'] = target_group_id
                    logger.info(f"✅ 根据关键字'{keyword}'找到群: {target_group_id}")
                else:
                    logger.warning(f"⚠️ 未找到包含关键字'{keyword}'的群")
                    return {
                        'success': False,
                        'error': f"未找到包含关键字'{keyword}'的群，请检查群名或使用具体的群ID"
                    }
            
            # 处理自动发送到JIRA ticket所在群的模式
            elif notification_target in ['auto_group', 'jira_group', 'ticket_group']:
                task_config['notification_type'] = 'auto_group'
                task_config['target_group_id'] = 'AUTO_DETECT'  # 特殊标记
                logger.info(f"✅ 设置为自动发送到JIRA ticket所在群")
            
            # 移除notification_target，因为已经转换为target_group_id
            task_config.pop('notification_target', None)
            
        except Exception as e:
            logger.error(f"解析通知目标异常: {str(e)}")
            return {
                'success': False,
                'error': f"解析通知目标异常: {str(e)}"
            }
        
        return task_config
    
    async def _search_group_by_keyword(self, keyword: str) -> str:
        """
        根据关键字搜索群组
        
        Args:
            keyword: 搜索关键字（可能是JIRA单号、群名片段等）
            
        Returns:
            群组ID，如果未找到则返回None
        """
        try:
            from app01.models import SeatalkGroup
            from django.db.models import Q
            
            # 搜索群名包含关键字的群组
            groups = SeatalkGroup.objects.filter(
                Q(group_name__icontains=keyword)
            ).order_by('-id')[:5]  # 限制返回数量，优先返回最近创建的
            
            if groups:
                # 如果关键字是JIRA单号格式，优先返回项目匹配的群
                if '-' in keyword and keyword.replace('-', '').replace(' ', '').isalnum():
                    project_prefix = keyword.split('-')[0].upper()
                    for group in groups:
                        if project_prefix in (group.group_name or '').upper():
                            logger.info(f"✅ 找到项目匹配的群: {group.group_name} ({group.group_id})")
                            return group.group_id
                
                # 返回第一个匹配的群
                first_group = groups[0]
                logger.info(f"✅ 找到匹配的群: {first_group.group_name} ({first_group.group_id})")
                return first_group.group_id
            
            logger.warning(f"⚠️ 未找到包含关键字'{keyword}'的群")
            return None
            
        except Exception as e:
            logger.error(f"搜索群组异常: {str(e)}")
            return None
    
    async def _get_group_info_by_id(self, group_id: str) -> Optional[Dict]:
        """
        根据群组ID获取群组信息
        
        Args:
            group_id: 群组ID
            
        Returns:
            群组信息字典，如果未找到则返回None
        """
        try:
            from app01.models import SeatalkGroup
            
            group = SeatalkGroup.objects.filter(group_id=group_id).first()
            if group:
                return {
                    'group_id': group.group_id,
                    'group_name': group.group_name,
                    'group_title': group.group_title
                }
            
            return None
            
        except Exception as e:
            logger.error(f"获取群组信息异常: {str(e)}")
            return None

    async def _create_task_directly(self, task_config: Dict, user_id: str, user_email: str, group_id: str = None, group_title: str = None) -> Dict:
        """直接创建任务"""
        try:
            from .task_scheduler import task_scheduler
            
            # 获取任务类型，默认为JIRA查询任务
            task_type = task_config.get('task_type', 'jira_query')
            
            # 根据任务类型验证必需字段
            if task_type == 'jira_query':
                required_fields = ['task_name', 'jql_query', 'schedule_expression']
            elif task_type == 'reminder':
                required_fields = ['task_name', 'reminder_message', 'schedule_expression']
            else:
                return {
                    'success': False,
                    'error': f"不支持的任务类型: {task_type}"
                }
            
            missing_required = [field for field in required_fields if not task_config.get(field)]
            
            if missing_required:
                return {
                    'success': False,
                    'error': f"缺少必需字段: {', '.join(missing_required)}"
                }
            
            # 解析调度表达式
            schedule_info = task_scheduler.parse_schedule_expression(task_config['schedule_expression'])
            if 'error' in schedule_info:
                return {
                    'success': False,
                    'error': f"调度表达式解析失败: {schedule_info['error']}"
                }
            
            # 设置默认值 - 使用标准化的通知类型
            if group_id:
                # 在群聊中创建任务，默认发送到当前群
                notification_type = task_config.get('notification_type', 'group')
            else:
                # 在私聊中创建任务，默认发送给创建人
                notification_type = task_config.get('notification_type', 'creator')
            
            # 创建智能通知任务
            from .ai_assistant import AIAssistant
            ai_assistant = AIAssistant()
            
            # 根据任务类型选择创建方式
            if task_type == 'reminder':
                # 直接创建提醒任务
                # 设置target_group_id
                target_group_id = task_config.get('target_group_id') or group_id
                
                result = await task_scheduler.create_task(
                    user_id=user_id,
                    user_email=user_email,
                    employee_code=None,
                    task_name=task_config['task_name'],
                    query_text=None,  # 提醒任务不需要JQL查询
                    schedule_time=schedule_info['schedule_time'],
                    frequency=schedule_info['frequency'],
                    schedule_days=schedule_info['schedule_days'],
                    notification_type=notification_type,
                    target_group_id=target_group_id,
                    group_title=group_title,
                    task_type='reminder',
                    reminder_message=task_config['reminder_message']
                )
            else:
                # 创建JIRA查询任务
                # 设置target_group_id
                target_group_id = task_config.get('target_group_id') or group_id
                
                result = await ai_assistant._create_smart_notification_task(
                    user_id=user_id,
                    user_email=user_email,
                    task_name=task_config['task_name'],
                    query_text=task_config['jql_query'],
                    schedule_info=schedule_info,
                    user_query=f"自然语言创建: {task_config['task_name']}",
                    matches=[notification_type],  # 通知类型配置
                    group_title=group_title,
                    target_group_id=target_group_id  # 传递群组ID
                )
            
            if result['success']:
                # 检查任务ID是否正确返回
                task_id = result.get('task_id')
                if not task_id:
                    # 尝试从响应文本中提取任务ID作为备用方案
                    import re
                    response_text = result.get('response', '')
                    id_match = re.search(r'任务ID\*\*:\s*(\d+)', response_text)
                    if id_match:
                        task_id = int(id_match.group(1))
                        logger.info(f"🔧 从响应文本中提取到任务ID: {task_id}")
                        result['task_id'] = task_id  # 补充到结果中
                    else:
                        logger.error(f"❌ 任务创建返回成功但task_id为空: {result}")
                        return {
                            'success': False,
                            'error': "任务创建失败：未能获取任务ID，请检查数据库连接"
                        }
                
                logger.info(f"✅ 任务创建成功，task_id: {task_id}")
                
                # 生成详细的成功消息，包含任务确认信息
                success_msg = f"🎉 **任务创建成功！**\n\n"
                success_msg += f"📋 **任务名称**: {task_config['task_name']}\n"
                success_msg += f"🆔 **任务ID**: {task_id}\n"
                
                # 根据任务类型显示不同内容
                if task_type == 'reminder':
                    success_msg += f"🔔 **提醒内容**: {task_config['reminder_message']}\n"
                    success_msg += f"📝 **任务类型**: 定时提醒\n"
                else:
                    success_msg += f"🔍 **JQL查询**: `{task_config['jql_query']}`\n"
                    success_msg += f"📝 **任务类型**: JIRA查询\n"
                
                success_msg += f"⏰ **执行频率**: {self._format_schedule_display(task_config['schedule_expression'])}\n"
                
                # 显示通知类型的友好名称
                notification_display = {
                    'private': '私聊通知',
                    'group': '群聊通知', 
                    'both': '私聊+群聊通知'
                }.get(notification_type, notification_type)
                success_msg += f"📬 **通知方式**: {notification_display}"
                
                # 如果是群聊通知，显示具体的群组信息
                if notification_type in ['group', 'both'] and task_config.get('target_group_id'):
                    group_info = await self._get_group_info_by_id(task_config['target_group_id'])
                    if group_info:
                        success_msg += f" (群ID: {task_config['target_group_id']}, 群名: {group_info.get('group_name', '未知')})"
                    else:
                        success_msg += f" (群ID: {task_config['target_group_id']})"
                
                success_msg += "\n\n"
                
                # 添加下次执行时间（如果有的话）
                if result.get('next_execution'):
                    next_exec = result['next_execution']
                    if isinstance(next_exec, str):
                        # 格式化时间显示
                        formatted_time = next_exec.replace('T', ' ').split('.')[0]
                        success_msg += f"⏰ **下次执行**: {formatted_time}\n"
                
                # 添加任务状态
                success_msg += f"🟢 **任务状态**: 已激活\n\n"
                
                # 智能通知的特殊说明
                if notification_type == 'smart':
                    success_msg += "🧠 **智能通知说明**:\n"
                    success_msg += "• 系统会自动根据查询结果中的assignee分组\n"
                    success_msg += "• 每个人只会收到分配给自己的ticket通知\n"
                    success_msg += "• 如果没有assignee，会通过私聊通知您\n\n"
                
                # 操作提示
                success_msg += "🔧 **管理任务**:\n"
                success_msg += f"• 查看所有任务: `chedule list`\n"
                success_msg += f"• 暂停此任务: `schedule pause {task_id}`\n"
                success_msg += f"• 删除此任务: `schedule delete {task_id}`\n\n"
                
                success_msg += "✅ 任务已开始运行，请确认以上配置是否符合您的需求。"
                
                result['response'] = success_msg
                result['intent'] = 'natural_task_created'
                result['task_id'] = task_id  # 确保task_id被正确传递
            
            return result
            
        except Exception as e:
            logger.error(f"直接创建任务异常: {str(e)}")
            return {
                'success': False,
                'error': f"创建任务异常: {str(e)}"
            }
    
    async def _save_intermediate_state(self, session_id: str, task_config: Dict, 
                                     missing_info: List[str], original_query: str) -> None:
        """保存中间状态到会话"""
        try:
            intermediate_data = {
                'type': 'natural_task_creation',
                'task_config': task_config,
                'missing_info': missing_info,
                'original_query': original_query,
                'timestamp': timezone.now().isoformat()
            }
            
            # 修复会话管理调用 - 使用正确的参数
            await conversation_manager.update_session_context(
                session_id, 
                {
                    'intermediate_state': intermediate_data,
                    'last_action': 'natural_task_creation_pending'
                }
            )
            
            logger.info(f"✅ 中间状态已保存到会话: {session_id}")
            
        except Exception as e:
            logger.error(f"保存中间状态失败: {str(e)}")
            # 不抛出异常，允许流程继续
    
    async def _get_intermediate_state(self, session_id: str) -> Optional[Dict]:
        """获取中间状态"""
        try:
            # 获取会话上下文
            session_result = await conversation_manager.get_session_context(session_id)
            
            if not session_result['success']:
                logger.warning(f"获取会话上下文失败: {session_result.get('error')}")
                return None
            
            context = session_result.get('context', {})
            intermediate_state = context.get('intermediate_state')
            
            if not intermediate_state:
                logger.warning(f"会话 {session_id} 中未找到中间状态")
                return None
            
            # 验证中间状态类型
            if intermediate_state.get('type') != 'natural_task_creation':
                logger.warning(f"中间状态类型不匹配: {intermediate_state.get('type')}")
                return None
            
            logger.info(f"✅ 成功获取中间状态: {session_id}")
            return intermediate_state
            
        except Exception as e:
            logger.error(f"获取中间状态异常: {str(e)}")
            return None
    
    def _replace_current_user(self, query: str, user_email: str) -> str:
        """替换查询中的currentUser()为实际用户邮箱"""
        if not user_email:
            return query
        
        # 替换各种currentUser的表述
        patterns = [
            r'currentUser\(\)',
            r'currentuser\(\)',
            r'current_user\(\)',
            r'current user',
            r'当前用户',
            r'我的',
            r'我',
            r'我自己的'
        ]
        
        result = query
        for pattern in patterns:
            result = re.sub(pattern, user_email, result, flags=re.IGNORECASE)
        
        return result
    
    def _format_schedule_display(self, schedule_expression: str) -> str:
        """格式化调度表达式为友好显示"""
        try:
            # 首先尝试使用大模型解析
            from app01.ai_module.multi_llm_client import multi_llm_client
            import asyncio
            
            prompt = f"""
            请将以下调度表达式转换为用户友好的中文表达：
            
            调度表达式: {schedule_expression}
            
            请直接返回转换后的中文表达，不要包含其他文本。
            
            转换规则：
            - "daily 10:00" → "每天 10:00"
            - "weekly 11:20 1 2 3 4 5" → "每个工作日 11:20"
            - "weekly 14:00 1" → "每周一 14:00"
            - "weekly 15:30 2 4" → "每周二、四 15:30"
            - "monthly 1 08:00" → "每月1日 08:00"
            """
            
            # 使用安全的异步调用方式
            try:
                import concurrent.futures
                
                # 在新线程中运行异步代码，避免与现有事件循环冲突
                def run_async_llm():
                    try:
                        new_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(new_loop)
                        try:
                            result = new_loop.run_until_complete(
                                multi_llm_client.generate_with_retry(prompt, max_retries=2, temperature=0)
                            )
                            return result
                        finally:
                            new_loop.close()
                    except Exception as e:
                        logger.error(f"异步LLM调用失败: {str(e)}")
                        return {'success': False, 'error': str(e)}
                
                # 在线程池中执行
                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(run_async_llm)
                    result = future.result(timeout=10)  # 10秒超时
                
                if result.get('success', False):
                    response = result['content']
                    # 清理响应，去除可能的引号和多余空格
                    cleaned_response = response.strip().strip('"\'').strip()
                    if cleaned_response:
                        return cleaned_response
                else:
                    logger.error(f"大模型调用失败: {result.get('error', '未知错误')}")
            except Exception as e:
                logger.error(f"使用大模型格式化调度表达式失败: {str(e)}")
            
            # 如果大模型解析失败，回退到传统解析方法
            logger.info("大模型格式化失败，回退到传统解析方法")
            
            # 解析调度表达式
            parts = schedule_expression.split()
            if len(parts) < 2:
                return schedule_expression  # 无法解析，直接返回原始表达式
            
            frequency = parts[0]
            time_part = parts[1]
            
            # 处理时间部分
            try:
                hour, minute = map(int, time_part.split(':'))
                time_str = f"{hour:02d}:{minute:02d}"
            except:
                time_str = time_part
            
            # 处理频率部分
            if frequency == 'daily':
                return f"每天 {time_str}"
            elif frequency == 'weekly':
                if len(parts) > 2:
                    days = parts[2:]
                    day_names = []
                    for day in days:
                        try:
                            day_num = int(day)
                            day_name = {
                                1: '周一',
                                2: '周二',
                                3: '周三',
                                4: '周四',
                                5: '周五',
                                6: '周六',
                                7: '周日'
                            }.get(day_num, str(day_num))
                            day_names.append(day_name)
                        except:
                            day_names.append(day)
                    
                    if len(day_names) == 5 and '周一' in day_names and '周五' in day_names:
                        return f"每个工作日 {time_str}"
                    else:
                        return f"每周{' '.join(day_names)} {time_str}"
                else:
                    return f"每周 {time_str}"
            elif frequency == 'monthly':
                if len(parts) > 2:
                    days = parts[2:]
                    return f"每月{' '.join(days)}日 {time_str}"
                else:
                    return f"每月1日 {time_str}"
            else:
                return schedule_expression
        except Exception as e:
            logger.error(f"格式化调度表达式异常: {str(e)}")
            return schedule_expression
    
    def is_natural_language_request(self, user_query: str) -> bool:
        """
        判断是否为自然语言请求（而非完整的命令格式）
        
        Args:
            user_query: 用户查询
            
        Returns:
            True if 自然语言请求, False if 完整命令
        """
        # 检查是否为完整的schedule create命令格式
        if re.match(r'^schedule\s+create\s+"[^"]+"\s+"[^"]+"\s+"[^"]+"', user_query.strip()):
            return False
        
        # 检查是否包含自然语言创建任务的关键词
        natural_keywords = [
            '帮我', '请', '设置', '创建', '每天', '每周', '每月',
            '提醒', '通知', '查看', '监控', '定时', '自动', '发送', '推送',
            '工作日', '每个工作日', '每日', '每周一', '每周二', '每周三', '每周四', '每周五'
        ]
        
        return any(keyword in user_query for keyword in natural_keywords)

    def _extract_fallback_config(self, user_query: str, content: str) -> Dict:
        """从文本中提取基本信息作为fallback"""
        try:
            # 基于用户查询的关键词分析
            query_lower = user_query.lower()
            
            # 基本任务配置
            task_config = {}
            missing_info = []
            
            # 尝试提取任务名称
            if any(keyword in query_lower for keyword in ['创建', '设置', '帮我']):
                task_config['task_name'] = f"自然语言任务 - {user_query[:20]}..."
            else:
                missing_info.append('task_name')
            
            # 尝试提取JQL查询
            if any(keyword in query_lower for keyword in ['我的', '分配给我']):
                task_config['jql_query'] = 'assignee = currentUser()'
            elif any(keyword in query_lower for keyword in ['bug', '缺陷']):
                task_config['jql_query'] = 'issuetype = Bug'
            elif any(keyword in query_lower for keyword in ['任务', 'task']):
                task_config['jql_query'] = 'issuetype = Task'
            else:
                missing_info.append('jql_query')
            
            # 尝试提取调度信息
            if any(keyword in query_lower for keyword in ['每天', '每日']):
                task_config['schedule_expression'] = 'daily 09:00'
            elif any(keyword in query_lower for keyword in ['每周']):
                task_config['schedule_expression'] = 'weekly monday 09:00'
            else:
                missing_info.append('schedule_expression')
            
            # 设置默认通知方式
            task_config['notification_type'] = 'private'
            
            # 如果缺失太多关键信息，返回失败
            if len(missing_info) > 2:
                return {
                    'success': False,
                    'error': f"无法从查询中提取足够信息: {missing_info}"
                }
            
            return {
                'success': True,
                'task_config': task_config,
                'missing_info': missing_info
            }
            
        except Exception as e:
            logger.error(f"提取fallback配置失败: {str(e)}")
            return {
                'success': False,
                'error': f"提取fallback配置失败: {str(e)}"
            }
    
    def _simple_config_update(self, user_response: str, current_config: Dict, missing_info: List[str]) -> Dict:
        """简单的配置更新fallback"""
        try:
            updated_config = current_config.copy()
            remaining_missing = missing_info.copy()
            response_lower = user_response.lower()
            
            # 尝试更新调度表达式
            if 'schedule_expression' in missing_info:
                if any(keyword in response_lower for keyword in ['每天', '每日']):
                    # 尝试提取时间
                    import re
                    time_match = re.search(r'(\d{1,2})[点时:](\d{0,2})', user_response)
                    if time_match:
                        hour = time_match.group(1).zfill(2)
                        minute = time_match.group(2).zfill(2) if time_match.group(2) else '00'
                        updated_config['schedule_expression'] = f'daily {hour}:{minute}'
                    else:
                        updated_config['schedule_expression'] = 'daily 09:00'
                    remaining_missing.remove('schedule_expression')
                elif any(keyword in response_lower for keyword in ['每周']):
                    updated_config['schedule_expression'] = 'weekly monday 09:00'
                    remaining_missing.remove('schedule_expression')
            
            # 尝试更新通知类型
            if 'notification_type' in missing_info:
                if any(keyword in response_lower for keyword in ['私聊', '私信', '1']):
                    updated_config['notification_type'] = 'private'
                    remaining_missing.remove('notification_type')
                elif any(keyword in response_lower for keyword in ['群聊', '群里', '2']):
                    updated_config['notification_type'] = 'group'
                    remaining_missing.remove('notification_type')
                elif any(keyword in response_lower for keyword in ['两者', '都要', '3']):
                    updated_config['notification_type'] = 'both'
                    remaining_missing.remove('notification_type')
            
            # 尝试更新任务名称
            if 'task_name' in missing_info and len(user_response.strip()) > 0:
                updated_config['task_name'] = user_response.strip()[:50]  # 限制长度
                remaining_missing.remove('task_name')
            
            return {
                'success': True,
                'updated_config': updated_config,
                'remaining_missing': remaining_missing,
                'update_summary': '使用简单规则更新配置'
            }
            
        except Exception as e:
            logger.error(f"简单配置更新失败: {str(e)}")
            return {
                'success': False,
                'error': f"配置更新失败: {str(e)}"
            }


# 全局实例
natural_task_creator = NaturalTaskCreator() 