"""
LLM 客户端模块
提供与公司内部LLM平台的兼容接口（OpenAI格式）
"""

import asyncio
import aiohttp
import json
import time
import logging
from typing import Dict, List, Optional
from django.conf import settings

logger = logging.getLogger(__name__)

# 导入AI配置
try:
    from app01.ai_config import LLM_CONFIG
except ImportError:
    # 如果配置文件不存在，使用默认配置
    LLM_CONFIG = {
        'API_BASE_URL': 'https://compass.llm.shopee.io/compass-api/v1',
        'API_KEY': 'your-api-key-here',
        'MODEL_NAME': 'gpt-3.5-turbo',
        'MAX_TOKENS': 1500,
        'TEMPERATURE': 0.7,
        'TIMEOUT': 12,
        'MAX_RETRIES': 3,
        'RETRY_DELAY': 1,
    }


class LLMConfig:
    """LLM配置类"""
    
    @classmethod
    def get_config(cls, key: str, default=None):
        """获取配置值"""
        return LLM_CONFIG.get(key.upper(), default)
    
    @classmethod
    def get_api_base_url(cls):
        return cls.get_config('API_BASE_URL')
    
    @classmethod
    def get_api_key(cls):
        return cls.get_config('API_KEY')
    
    @classmethod
    def get_model_name(cls):
        return cls.get_config('MODEL_NAME')
    
    @classmethod
    def get_max_tokens(cls):
        return cls.get_config('MAX_TOKENS', 1500)
    
    @classmethod
    def get_temperature(cls):
        return cls.get_config('TEMPERATURE', 0.7)
    
    @classmethod
    def get_timeout(cls):
        return cls.get_config('TIMEOUT', 12)


class LLMClient:
    """LLM 客户端"""
    
    def __init__(self):
        self.base_url = LLMConfig.get_api_base_url()
        self.api_key = LLMConfig.get_api_key()
        self.model_name = LLMConfig.get_model_name()
        self.max_tokens = LLMConfig.get_max_tokens()
        self.temperature = LLMConfig.get_temperature()
        self.timeout = LLMConfig.get_timeout()
        
    async def chat_completion(self, messages: List[Dict], **kwargs) -> Dict:
        """
        发送聊天完成请求
        
        Args:
            messages: 消息列表
            **kwargs: 其他参数
            
        Returns:
            LLM响应结果
        """
        try:
            start_time = time.time()
            
            # 构建请求数据（兼容不同模型的token参数名）
            try:
                from app01.ai_config import MODEL_POOL
                token_param_key = MODEL_POOL.get(self.model_name, {}).get('TOKEN_PARAM_KEY', 'max_tokens')
            except Exception:
                token_param_key = 'max_tokens'
            token_value = kwargs.get('max_tokens', self.max_tokens)
            if 'max_completion_tokens' in kwargs:
                token_param_key = 'max_completion_tokens'
                token_value = kwargs['max_completion_tokens']

            data = {
                "model": kwargs.get('model', self.model_name),
                "messages": messages,
                token_param_key: token_value,
                "stream": False
            }
            # 兼容不支持 temperature 的模型
            try:
                from app01.ai_config import MODEL_POOL
                allow_temperature = MODEL_POOL.get(self.model_name, {}).get('ALLOW_TEMPERATURE', True)
            except Exception:
                allow_temperature = True
            req_temperature = kwargs.get('temperature', self.temperature)
            if allow_temperature and (req_temperature is not None):
                data["temperature"] = req_temperature
            
            # 发送请求
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    json=data,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        # 记录响应时间
                        response_time = time.time() - start_time
                        logger.info(f"LLM请求成功，耗时: {response_time:.2f}秒")
                        
                        return {
                            'success': True,
                            'data': result,
                            'response_time': response_time
                        }
                    else:
                        error_text = await response.text()
                        logger.error(f"LLM请求失败: {response.status} - {error_text}")
                        logger.debug(f"请求payload: {json.dumps(data)[:500]}")
                        return {
                            'success': False,
                            'error': f"HTTP {response.status}: {error_text}",
                            'response_time': time.time() - start_time
                        }
                        
        except asyncio.TimeoutError:
            logger.error(f"LLM请求超时 (>{self.timeout}秒)")
            return {
                'success': False,
                'error': f"请求超时 (>{self.timeout}秒)",
                'response_time': self.timeout
            }
        except Exception as e:
            logger.error(f"LLM请求异常: {str(e)}")
            return {
                'success': False,
                'error': f"请求异常: {str(e)}",
                'response_time': time.time() - start_time if 'start_time' in locals() else 0
            }
    
    async def generate_text(self, prompt: str, system_prompt: str = None, **kwargs) -> Dict:
        """
        生成文本
        
        Args:
            prompt: 用户提示
            system_prompt: 系统提示
            **kwargs: 其他参数
            
        Returns:
            生成结果
        """
        messages = []
        
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        messages.append({"role": "user", "content": prompt})
        
        result = await self.chat_completion(messages, **kwargs)
        
        if result['success']:
            try:
                content = result['data']['choices'][0]['message']['content']
                return {
                    'success': True,
                    'content': content.strip(),
                    'response_time': result['response_time']
                }
            except (KeyError, IndexError) as e:
                logger.error(f"解析LLM响应失败: {str(e)}")
                return {
                    'success': False,
                    'error': f"解析响应失败: {str(e)}",
                    'response_time': result['response_time']
                }
        else:
            return result
    
    async def generate_with_retry(self, prompt: str, system_prompt: str = None, 
                                 max_retries: int = 2, **kwargs) -> Dict:
        """
        带重试的文本生成
        
        Args:
            prompt: 用户提示
            system_prompt: 系统提示
            max_retries: 最大重试次数
            **kwargs: 其他参数
            
        Returns:
            生成结果
        """
        for attempt in range(max_retries + 1):
            result = await self.generate_text(prompt, system_prompt, **kwargs)
            
            if result['success']:
                return result
            
            if attempt < max_retries:
                wait_time = 2 ** attempt  # 指数退避
                logger.warning(f"LLM请求失败，{wait_time}秒后重试 (第{attempt + 1}次)")
                await asyncio.sleep(wait_time)
            else:
                logger.error(f"LLM请求最终失败，已重试{max_retries}次")
        
        return result
    
    def test_connection(self) -> Dict:
        """
        测试连接
        
        Returns:
            测试结果
        """
        try:
            # 简单的同步测试
            import requests
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model_name,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                json=data,
                headers=headers,
                timeout=5
            )
            
            if response.status_code == 200:
                return {'success': True, 'message': '连接测试成功'}
            else:
                return {'success': False, 'message': f'连接测试失败: HTTP {response.status_code}'}
                
        except Exception as e:
            return {'success': False, 'message': f'连接测试异常: {str(e)}'}


# 全局LLM客户端实例
llm_client = LLMClient() 