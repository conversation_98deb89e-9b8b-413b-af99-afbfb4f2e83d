import requests
import json
import time
import asyncio
from typing import List, Dict, Set
from icecream import ic
from datetime import datetime, timedelta
from django.utils import timezone
from jira import JIRA
from cachetools import TTLCache
import os
import sys
import locale
import threading
from app01.models import SeatalkGroup
from app01.statistics.decorators import track_cronjob_execution

# 设置环境变量，确保使用正确的字符编码
os.environ['PYTHONIOENCODING'] = 'utf-8'
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')
locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')

# Workee PJ 相关日志群配置
WORKEE_PJ_LOG_GROUP_ID = "ODY1NTY4NTMwMjEw"



# 翻译配置 - 支持多项目配置
TRANSLATION_CONFIG = {
    'enabled': True,  # 全局翻译开关
    'target_language': 'English',  # 目标语言
    'group_patterns': [
        'SPCT',  # SPCT 项目
        'SPB'
        # 'SPCB',  # 可以添加其他项目
        # 'OTHER-PROJECT',  # 示例：其他需要翻译的项目
    ]
}

# 线程回复上下文管理器
class ThreadReplyContext:
    """
    线程回复上下文管理器，用于在SeaTalk回调和消息发送之间传递线程ID和消息ID
    使用threading.local()确保线程安全
    """
    _thread_context = threading.local()
    _enabled = True  # 全局开关，控制是否启用自动线程回复
    
    @classmethod
    def set_thread_context(cls, group_id, thread_id=None, message_id=None):
        """
        设置线程上下文，记录当前群组ID、线程ID和消息ID
        
        Args:
            group_id: 群组ID
            thread_id: 线程ID，可选
            message_id: 消息ID，可选，用于引用回复
        """
        if not cls._enabled:
            return
        cls._thread_context.group_id = group_id
        cls._thread_context.thread_id = thread_id
        cls._thread_context.message_id = message_id
        ic(f"设置线程上下文: 群组={group_id}, 线程={thread_id}, 消息ID={message_id}")
    
    @classmethod
    def get_thread_context(cls, group_id=None):
        """
        获取线程上下文
        如果提供了group_id，则只在匹配时返回线程ID和消息ID
        
        Returns:
            tuple: (group_id, thread_id, message_id)
        """
        if not cls._enabled:
            return None, None, None
            
        context_group_id = getattr(cls._thread_context, 'group_id', None)
        context_thread_id = getattr(cls._thread_context, 'thread_id', None)
        context_message_id = getattr(cls._thread_context, 'message_id', None)
        
        # 如果提供了group_id且与上下文不匹配，则不返回任何ID
        if group_id and context_group_id != group_id:
            return None, None, None
            
        return context_group_id, context_thread_id, context_message_id
    
    @classmethod
    def clear_thread_context(cls):
        """清理线程上下文，避免上下文泄漏"""
        if hasattr(cls._thread_context, 'group_id'):
            delattr(cls._thread_context, 'group_id')
        if hasattr(cls._thread_context, 'thread_id'):
            delattr(cls._thread_context, 'thread_id')
        if hasattr(cls._thread_context, 'message_id'):
            delattr(cls._thread_context, 'message_id')
        ic("已清理线程上下文")
    
    @classmethod
    def enable(cls):
        """启用自动线程回复功能"""
        cls._enabled = True
        ic("已启用自动线程回复功能")
        
    @classmethod
    def disable(cls):
        """禁用自动线程回复功能"""
        cls._enabled = False
        ic("已禁用自动线程回复功能")
    
    @classmethod
    def is_enabled(cls):
        """检查自动线程回复功能是否启用"""
        return cls._enabled

def simplify_jira_error(error_message: str) -> str:
    """
    简化JIRA错误信息，提取关键内容
    
    Args:
        error_message: 原始错误信息
        
    Returns:
        str: 简化后的错误信息
    """
    error_str = str(error_message)
    
    # 检查是否是HTTP 401错误
    if "HTTP 401" in error_str:
        if "Unauthorized" in error_str:
            return "JIRA认证失败 (HTTP 401): Token可能已过期或无效，请检查JIRA_TOKEN配置"
        else:
            return "JIRA认证失败 (HTTP 401): 未授权访问"
    
    # 检查是否是HTTP 403错误
    elif "HTTP 403" in error_str:
        return "JIRA权限不足 (HTTP 403): 当前用户没有访问该资源的权限"
    
    # 检查是否是HTTP 404错误
    elif "HTTP 404" in error_str:
        return "JIRA资源未找到 (HTTP 404): 请检查JIRA单号或项目是否存在"
    
    # 检查是否是HTTP 500错误
    elif "HTTP 500" in error_str:
        return "JIRA服务器内部错误 (HTTP 500): 服务器暂时无法处理请求"
    
    # 检查是否是HTTP 502错误
    elif "HTTP 502" in error_str:
        return "JIRA网关错误 (HTTP 502): 网关或代理服务器问题，请稍后重试"
    
    # 检查是否是HTTP 503错误
    elif "HTTP 503" in error_str:
        return "JIRA服务不可用 (HTTP 503): 服务器暂时无法使用，请稍后重试"
    
    # 检查是否是连接错误
    elif "Connection" in error_str or "连接" in error_str:
        return "JIRA连接失败: 网络连接问题，请检查网络或JIRA服务状态"
    
    # 检查是否是超时错误
    elif "timeout" in error_str.lower() or "超时" in error_str:
        return "JIRA请求超时: 请求处理时间过长，请稍后重试"
    
    # 检查是否包含JQL错误
    elif "JQL" in error_str or "jql" in error_str:
        return f"JIRA查询语法错误: JQL语句可能有误"
    
    # 如果错误信息很长且包含HTML，只提取前100个字符
    elif len(error_str) > 200 and ("<html>" in error_str.lower() or "<title>" in error_str.lower()):
        return f"JIRA服务错误: 服务器返回HTML页面而非API响应，可能是认证或服务配置问题"
    
    # 如果错误信息超过300字符，截取前200字符
    elif len(error_str) > 300:
        return f"JIRA错误: {error_str[:200]}..."
    
    # 默认返回原始错误信息
    return f"JIRA错误: {error_str}"



# SPCT Bug提醒调试配置
SPCT_BUG_REMINDER_DEBUG_GROUP = "NzQzMzAxODcyMjAy"  # 机器人调试群ID (机器人调试群)

def load_spct_bug_debug_config() -> bool:
    """
    从配置文件加载SPCT Bug提醒调试模式设置
    
    Returns:
        bool: 调试模式状态
    """
    try:
        import json
        from pathlib import Path
        
        config_file = Path(__file__).parent / 'spct_bug_debug_config.json'
        
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                debug_mode = config.get('debug_mode', True)  # 默认调试模式
                return debug_mode
        else:
            # 配置文件不存在，创建默认配置
            default_config = {
                'debug_mode': True,
                'last_updated': str(datetime.now())
            }
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            return True
            
    except Exception as e:
        ic(f"加载SPCT Bug调试配置失败: {e}，使用默认值True")
        return True

# 从配置文件加载调试模式状态
SPCT_BUG_REMINDER_DEBUG = load_spct_bug_debug_config()

# 团队成员和TL关系数据
TEAM_DATA = {
    "ChatSS": {
        "leaders": ["<EMAIL>"],
        "members": [
            "<EMAIL>", "<EMAIL>", "<EMAIL>", 
            "<EMAIL>", "<EMAIL>", "<EMAIL>", 
            "<EMAIL>", "<EMAIL>", "<EMAIL>"
        ],
        "project": "SPCT"
    },
    "Webchat_FE": {
        "leaders": ["<EMAIL>"],
        "members": ["<EMAIL>", "<EMAIL>"],
        "project": "SPCT"
    },
    "Webchat_BE": {
        "leaders": ["<EMAIL>"],
        "members": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
        "project": "SPCT"
    },
    "Chat_QA": {
        "leaders": ["<EMAIL>"],
        "members": [
            "<EMAIL>", "<EMAIL>", "<EMAIL>", 
            "<EMAIL>", "<EMAIL>"
        ],
        "project": "SPCT"
    },
    "Native": {
        "leaders": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
        "members": [
            "<EMAIL>", "<EMAIL>", "<EMAIL>", 
            "<EMAIL>", "<EMAIL>", "<EMAIL>", 
            "<EMAIL>", "<EMAIL>", "<EMAIL>", 
            "<EMAIL>", "<EMAIL>", "<EMAIL>", 
            "<EMAIL>", "<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"
        ],
        "project": "SPCT"
    },
    "Chat_RN": {
        "leaders": ["<EMAIL>"],
        "members": ["<EMAIL>"],
        "project": "SPCT"
    },
    "Chat_DRE": {
        "leaders": ["<EMAIL>"],
        "members": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
        "project": "SPCT"
    },
    "Chatbot_Platform_BE": {
        "leaders": ["<EMAIL>"],
        "members": [
            "<EMAIL>", "<EMAIL>", "<EMAIL>", 
            "<EMAIL>", "<EMAIL>", "<EMAIL>", 
            "<EMAIL>", "<EMAIL>", "<EMAIL>", 
            "<EMAIL>"
        ],
        "project": "SPCB"
    },
    "Chatbot_Function_BE": {
        "leaders": ["<EMAIL>"],
        "members": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
        "project": "SPCB"
    },
    "Chatbot_FE": {
        "leaders": ["<EMAIL>"],
        "members": [
            "<EMAIL>", "<EMAIL>", "<EMAIL>", 
            "<EMAIL>", "<EMAIL>", "<EMAIL>", 
            "<EMAIL>", "<EMAIL>"
        ],
        "project": "SPCB"
    },
    "Chatbot_QA": {
        "leaders": ["<EMAIL>"],
        "members": [
            "<EMAIL>", "<EMAIL>", "<EMAIL>", 
            "<EMAIL>", "<EMAIL>", "<EMAIL>", 
            "<EMAIL>"
        ],
        "project": "SPCB"
    }
}

# 使用 jira_token
jira_token = "NTA4ODQ0NTE5OTQ2OpMq9xXh446Fs5eBqdDx9mM4tLaj"

# 创建一个TTLCache，设置10分钟过期时间，最大容量1000条记录
PROCESSED_CHANGES = TTLCache(maxsize=1000, ttl=600)  # 600秒 = 10分钟

def truncate_group_name(group_name: str, max_bytes: int = 120) -> str:
    """
    截断群名称以符合SeaTalk的长度限制

    Args:
        group_name: 原始群名称
        max_bytes: 最大字节数限制（默认120）

    Returns:
        str: 截断后的群名称
    """
    # 如果群名称的字节长度在限制内，直接返回
    if len(group_name.encode('utf-8')) <= max_bytes:
        return group_name

    # 提取JIRA key和状态部分（这部分必须保留）
    import re
    match = re.match(r'(\[[\w-]+\]\[[\w\s]+\])', group_name)
    if match:
        prefix = match.group(1)
        summary = group_name[len(prefix):]

        # 计算前缀的字节长度
        prefix_bytes = len(prefix.encode('utf-8'))

        # 为省略号预留3个字节
        available_bytes = max_bytes - prefix_bytes - 3

        if available_bytes > 0:
            # 截断summary部分
            truncated_summary = truncate_text_by_bytes(summary, available_bytes)
            return f"{prefix}{truncated_summary}..."
        else:
            # 如果前缀本身就太长，只保留JIRA key部分
            jira_key_match = re.match(r'(\[[\w-]+\])', group_name)
            if jira_key_match:
                jira_key_part = jira_key_match.group(1)
                remaining_bytes = max_bytes - len(jira_key_part.encode('utf-8')) - 3
                if remaining_bytes > 0:
                    rest = group_name[len(jira_key_part):]
                    truncated_rest = truncate_text_by_bytes(rest, remaining_bytes)
                    return f"{jira_key_part}{truncated_rest}..."
                else:
                    return jira_key_part

    # 如果无法解析格式，直接截断
    return truncate_text_by_bytes(group_name, max_bytes - 3) + "..."

def truncate_text_by_bytes(text: str, max_bytes: int) -> str:
    """
    按字节长度截断文本，确保不会在多字节字符中间截断

    Args:
        text: 要截断的文本
        max_bytes: 最大字节数

    Returns:
        str: 截断后的文本
    """
    if len(text.encode('utf-8')) <= max_bytes:
        return text

    # 逐字符检查，确保不在多字节字符中间截断
    result = ""
    for char in text:
        test_result = result + char
        if len(test_result.encode('utf-8')) <= max_bytes:
            result = test_result
        else:
            break

    return result

def validate_group_members(all_emails: List[str], detail_message: str, min_members: int = 2) -> Dict:
    """
    验证群成员的有效性并检查最小成员数量要求

    Args:
        all_emails: 所有成员邮箱列表
        detail_message: 详细信息消息
        min_members: 最小成员数量要求（不包括群主）

    Returns:
        Dict: 验证结果
    """
    # 获取employee codes
    email_to_code = get_employee_codes(all_emails)
    if not email_to_code:
        return {
            "success": False,
            "message": "Failed to get employee codes",
            "detail_message": detail_message
        }

    # 分类成员
    successful_members = []
    failed_members = []

    for email in all_emails:
        if email in email_to_code and email_to_code[email]:
            successful_members.append(email)
        else:
            failed_members.append(email)

    # 更新详细信息
    if failed_members:
        ic(f"无法获取employee code的成员: {failed_members}")
        detail_message += f"\n❌ 无法获取employee code的成员: {', '.join(failed_members)}\n原因: 可能是邮箱不存在或已离职"

    ic(f"成功添加的成员: {successful_members}")
    detail_message += f"\n✅ 成功添加的成员: {', '.join(successful_members)}"

    # 检查最小成员数量（群主会自动包含，所以这里检查的是除群主外的成员数量）
    valid_member_count = len(successful_members) - 1  # 减去群主
    if valid_member_count < min_members:
        error_msg = f"有效群成员数量不足，需要至少{min_members}个成员（不包括群主），当前只有{valid_member_count}个有效成员"
        ic(error_msg)
        detail_message += f"\n❌ {error_msg}"
        return {
            "success": False,
            "message": error_msg,
            "detail_message": detail_message,
            "email_to_code": email_to_code,
            "successful_members": successful_members,
            "failed_members": failed_members
        }

    return {
        "success": True,
        "email_to_code": email_to_code,
        "successful_members": successful_members,
        "failed_members": failed_members,
        "detail_message": detail_message
    }

def create_group_with_retry(param: Dict, headers: Dict, detail_message: str, max_retries: int = 2) -> Dict:
    """
    带重试机制的群组创建函数

    Args:
        param: 创建群组的参数
        headers: 请求头
        detail_message: 详细信息消息
        max_retries: 最大重试次数

    Returns:
        Dict: 创建结果
    """
    url = "https://openapi.seatalk.io/messaging/v2/group_chat/create_group"

    for attempt in range(max_retries + 1):
        try:
            ic(f"Create group attempt {attempt + 1}/{max_retries + 1}, param: {param}")
            response = requests.post(url=url, json=param, headers=headers, timeout=30)
            result = response.json()
            ic(f"Create group response: {result}")

            if result["code"] == 0:
                # 成功创建群组
                return handle_successful_group_creation(result, param, detail_message)
            elif result["code"] == 102:
                # 参数错误，检查是否是群名称长度问题
                validation_errors = result.get('validation_errors', [])
                for error in validation_errors:
                    if error.get('field') == 'GroupName' and 'length' in error.get('reason', ''):
                        # 群名称长度问题，尝试进一步截断
                        original_name = param["group_name"]
                        new_name = truncate_group_name(original_name, max_bytes=100)  # 更保守的长度
                        if new_name != original_name and attempt < max_retries:
                            ic(f"群名称长度超限，尝试进一步截断: {original_name} -> {new_name}")
                            param["group_name"] = new_name
                            detail_message += f"\n⚠️ 群名称已自动截断以符合长度限制"
                            continue  # 重试

                return {
                    "success": False,
                    "message": f"请求参数错误: {validation_errors}.\n\n{detail_message}"
                }
            elif result["code"] == 7002:
                # 群成员不足，无法重试
                return {
                    "success": False,
                    "message": f"Failed to create group: {result}\n\n{detail_message}"
                }
            elif result["code"] == 103:
                return {
                    "success": False,
                    "message": f"code=103,请确保应用程序具有已批准的 API 权限.\n\n{detail_message}"
                }
            else:
                # 其他错误，如果不是最后一次尝试，则重试
                if attempt < max_retries:
                    ic(f"群组创建失败，将在2秒后重试: {result}")
                    time.sleep(2)
                    continue
                else:
                    return {
                        "success": False,
                        "message": f"Failed to create group: {result}\n\n{detail_message}"
                    }

        except requests.exceptions.Timeout:
            if attempt < max_retries:
                ic(f"请求超时，将在3秒后重试")
                time.sleep(3)
                continue
            else:
                return {
                    "success": False,
                    "message": f"请求超时，已重试{max_retries}次\n\n{detail_message}"
                }
        except Exception as e:
            if attempt < max_retries:
                ic(f"请求异常，将在2秒后重试: {str(e)}")
                time.sleep(2)
                continue
            else:
                return {
                    "success": False,
                    "message": f"Exception in create_seatalk_group: {str(e)}\n\n{detail_message}"
                }

    return {
        "success": False,
        "message": f"创建群组失败，已重试{max_retries}次\n\n{detail_message}"
    }

def handle_successful_group_creation(result: Dict, param: Dict, detail_message: str) -> Dict:
    """
    处理成功创建群组的情况

    Args:
        result: API返回结果
        param: 创建群组的参数
        detail_message: 详细信息消息

    Returns:
        Dict: 处理结果
    """
    group_name = param["group_name"]

    # 如果API返回了未能成功添加的用户
    users_not_added = result.get("users_not_added", [])
    if users_not_added:
        ic(f"API返回未能成功添加的成员: {users_not_added}")
        detail_message += f"\n\n❌ API返回未能成功添加的成员: {users_not_added}\n原因: 可能是用户在SeaTalk中未激活或其他权限问题"

    # 保存群组信息到数据库
    try:
        if not SeatalkGroup.objects.filter(group_id=result["group_id"]).exists():
            seatalk_group = SeatalkGroup(group_id=result["group_id"], group_name=group_name)
            seatalk_group.save()
            ic(f"群组信息已保存到数据库: {group_name}")
    except Exception as e:
        ic(f"保存群组信息到数据库失败: {str(e)}")
        # 不影响主流程，继续返回成功

    return {
        "success": True,
        "group_id": result["group_id"],
        "group_name": group_name,
        "users_not_added": users_not_added,
        "detail_message": detail_message
    }

def generate_epic_group_creation_report(current_time, time_range_description, epics_to_create_group, success_count, failed_epics, skipped_epics=None) -> str:
    """
    生成Epic状态变更自动建群的详细执行报告

    Args:
        current_time: 执行时间
        time_range_description: 时间范围描述
        epics_to_create_group: 符合条件的Epic列表
        success_count: 成功创建群组的数量
        failed_epics: 失败的Epic列表
        skipped_epics: 跳过的Epic列表（新增）

    Returns:
        str: 格式化的报告内容
    """
    # 计算统计数据
    total_epics = len(epics_to_create_group)
    failure_count = len(failed_epics)
    skipped_count = len(skipped_epics) if skipped_epics else 0
    success_rate = (success_count / total_epics * 100) if total_epics > 0 else 0

    # 分析失败原因
    failure_reasons = {}
    for failed in failed_epics:
        error = failed['error']
        if 'length must be at most 120 bytes' in error:
            reason = '群名称长度超限'
        elif 'Valid group members are not enough' in error:
            reason = '有效群成员不足'
        elif 'User does not exist' in error:
            reason = '用户不存在'
        elif 'Failed to get employee codes' in error:
            reason = '无法获取员工代码'
        else:
            reason = '其他错误'

        failure_reasons[reason] = failure_reasons.get(reason, 0) + 1

    # 生成报告
    report = f"📊 Epic状态变更自动建群执行报告\n\n"
    report += f"• 执行时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
    report += f"• 检查范围: {time_range_description}内的状态变更\n"
    report += f"• 符合条件Epic: {total_epics}个\n"
    report += f"• 成功创建群组: {success_count}个\n"
    report += f"• 跳过创建: {skipped_count}个（群组已存在）\n"
    report += f"• 失败数量: {failure_count}个\n"

    if total_epics > 0:
        report += f"• 成功率: {success_rate:.1f}%\n"

    # 失败原因统计
    if failure_reasons:
        report += f"\n失败原因统计:\n"
        for reason, count in failure_reasons.items():
            report += f"• {reason}: {count}个\n"

    # Epic列表
    if epics_to_create_group:
        report += f"\n创建群组的Epic列表:\n"
        for epic in epics_to_create_group:
            # 检查Epic的状态
            if skipped_epics and epic['issue_key'] in [s['issue_key'] for s in skipped_epics]:
                status = "⏭️ 跳过（群组已存在）"
            elif epic['issue_key'] in [f['issue_key'] for f in failed_epics]:
                status = "❌ 失败"
            else:
                status = "✅ 成功"
            report += f"• [{epic['issue_key']}] {epic['summary']} - 状态变更: {epic['from_status']} → {epic['to_status']} - {status}\n"

    # 跳过详情
    if skipped_epics:
        report += f"\n跳过详情:\n"
        for skipped in skipped_epics:
            report += f"• [{skipped['issue_key']}] {skipped['summary']} - 原因: {skipped['reason']}\n"
    
    # 失败详情
    if failed_epics:
        report += f"\n失败详情:\n"
        for failed in failed_epics:
            # 截断过长的错误信息
            error_msg = failed['error']
            if len(error_msg) > 200:
                error_msg = error_msg[:200] + "..."
            report += f"• [{failed['issue_key']}] {failed['summary']} - 错误: {error_msg}\n"

    # 添加优化建议
    if failed_epics:
        report += f"\n💡 优化建议:\n"
        if any('length must be at most 120 bytes' in f['error'] for f in failed_epics):
            report += f"• 群名称长度问题已通过自动截断功能优化，如仍有问题请检查JIRA标题长度\n"
        if any('Valid group members are not enough' in f['error'] for f in failed_epics):
            report += f"• 建议检查Epic的assignee和相关人员配置，确保有足够的有效成员\n"
        if any('User does not exist' in f['error'] for f in failed_epics):
            report += f"• 建议清理无效的邮箱地址（如机器人账号、离职员工等）\n"

    return report

# Timeline相关的字段ID和名称映射
TIMELINE_FIELDS = {
    "customfield_11545": "PRD Review Start Date",
    "customfield_11546": "PRD Review End Date",
    "customfield_11520": "Planned Dev Start Date",
    "customfield_11509": "Planned Dev Due Date",
    "customfield_12634": "Planned Integration Start Date",
    "customfield_12635": "Planned Integration End Date",
    "customfield_11521": "Planned QA Start Date",
    "customfield_11510": "Planned QA Due Date",
    "customfield_11522": "Planned UAT Start Date",
    "customfield_11511": "Planned UAT Due Date",
    "customfield_11513": "Planned Release Date",
    # 添加字段名称映射，以防字段ID不匹配
    "Planned UAT Start Date": "Planned UAT Start Date",
    "Planned Integration Start Date": "Planned Integration Start Date",
    "Planned UAT Due Date": "Planned UAT Due Date"
}

# 人员相关的字段ID和名称映射
PEOPLE_FIELDS = {
    "assignee": "Assignee",
    "customfield_10307": "Developer",
    "customfield_37801": "FE List",
    "customfield_37800": "BE List",
    "customfield_10308": "QA",
    "customfield_12202": "QA List",
    "customfield_10306": "Product Manager"
}

def app_access_token():
    """获取SeaTalk的access token，使用统一的缓存和重试机制"""
    from .config import get_seatalk_access_token
    token = get_seatalk_access_token()
    if not token:
        raise Exception("无法获取Seatalk访问令牌")
    return token

def get_employee_codes(emails: List[str]) -> Dict[str, str]:
    """通过邮箱列表获取employee codes"""
    url = "https://openapi.seatalk.io/contacts/v2/get_employee_code_with_email"
    access_token = app_access_token()
    headers = {
        'content-type': "application/json",
        'Authorization': f"Bearer {access_token}"
    }
    
    # 过滤掉空值
    valid_emails = [email for email in emails if email]
    
    param = {
        "emails": valid_emails
    }
    
    try:
        response = requests.post(url=url, json=param, headers=headers)
        result = response.json()
        
        if result["code"] != 0:
            ic(f"Error getting employee codes: {result}")
            return {}
        
        # 创建邮箱到employee_code的映射
        email_to_code = {}
        for employee in result["employees"]:
            if employee["code"] == 0 and employee["employee_status"] == 2:  # 只获取在职员工
                email_to_code[employee["email"]] = employee["employee_code"]
        
        return email_to_code
    except Exception as e:
        ic(f"Exception in get_employee_codes: {str(e)}")
        return {}

async def translate_message(message: str, target_language: str = None) -> str:
    """
    使用 ChatGPT 将消息翻译为目标语言
    
    Args:
        message: 要翻译的消息内容
        target_language: 目标语言，默认使用配置中的语言
        
    Returns:
        str: 翻译后的消息，如果翻译失败则返回原消息
    """
    try:
        # 导入多语言客户端
        from app01.ai_module.multi_llm_client import MultiLLMClient
        
        # 获取目标语言
        if target_language is None:
            target_language = TRANSLATION_CONFIG.get('target_language', 'English')
        
        # 创建客户端实例
        client = MultiLLMClient()
        
        # 构建翻译提示词
        prompt = f"""请将以下消息翻译为{target_language}。保持原文的格式和语气，确保翻译准确、自然。

原始消息:
{message}

请只返回翻译后的{target_language}内容，不要添加任何解释或额外文字。"""

        # 调用 LLM 进行翻译
        result = await client.generate_with_retry(prompt, max_retries=2)
        
        if result.get('success', False):
            translated_message = result.get('content', '').strip()
            ic(f"✅ 群消息翻译成功: '{message[:50]}...' -> '{translated_message[:50]}...'")
            return translated_message
        else:
            ic(f"❌ 群消息翻译失败: {result.get('error', '未知错误')}")
            return message  # 翻译失败时返回原消息
            
    except Exception as e:
        ic(f"❌ 群消息翻译异常: {str(e)}")
        return message  # 出现异常时返回原消息


# 保持向后兼容的别名
async def translate_message_to_english(message: str) -> str:
    """向后兼容的英文翻译函数"""
    return await translate_message(message, "English")


def needs_translation(group_id: str) -> bool:
    """
    检查群组是否需要翻译（支持多项目配置）
    
    Args:
        group_id: 群组ID
        
    Returns:
        bool: 如果群名称包含需要翻译的项目模式则返回 True，否则返回 False
    """
    if not TRANSLATION_CONFIG.get('enabled', False):
        return False
        
    group_name = ""
    
    # 首先尝试从数据库获取群组信息
    try:
        from app01.models import SeatalkGroup
        group = SeatalkGroup.objects.filter(group_id=group_id).first()
        
        if group and group.group_name:
            group_name = group.group_name.upper()
            ic(f"🔍 从数据库获取群组名称: {group_name}")
    except Exception as db_error:
        ic(f"⚠️ 数据库查询群组失败: {str(db_error)}")
    
    # 如果数据库没有获取到群组名称，尝试从 API 获取
    if not group_name:
        try:
            from app01.views import get_group_info
            group_info = get_group_info(group_id)
            
            if group_info and group_info.get('code') == 0:
                group_data = group_info.get('group', {})
                group_name = group_data.get('group_name', '').upper()
                ic(f"🔍 从API获取群组名称: {group_name}")
        except Exception as api_error:
            ic(f"⚠️ API查询群组失败: {str(api_error)}")
    
    # 如果仍然没有获取到群组名称，返回False
    if not group_name:
        ic(f"❌ 无法获取群组 {group_id} 的名称")
        return False
    
    # 检查是否匹配任何需要翻译的项目模式
    for pattern in TRANSLATION_CONFIG.get('group_patterns', []):
        if pattern.upper() in group_name:
            ic(f"✅ 检测到需翻译群组: {group_name} (ID: {group_id}, 模式: {pattern})")
            return True
    
    ic(f"🔍 群组不需要翻译: {group_name} (ID: {group_id})")        
    return False


# 保持向后兼容的别名
def is_spct_group(group_id: str) -> bool:
    """向后兼容的 SPCT 群组检查函数"""
    return needs_translation(group_id)


def send_message_to_group(group_id: str, message: str, thread_id: str = None, quoted_message_id: str = None):
    """
    发送消息到指定的群组（简化版，避免循环调用）
    
    Args:
        group_id: 群组ID
        message: 消息内容
        thread_id: 线程ID，如果提供则作为线程回复发送
        quoted_message_id: 引用消息ID，如果提供则作为引用回复发送
    
    Returns:
        bool: 发送成功返回True，否则返回False
    """
    try:
        # 导入test_for_seatalk_bot函数（翻译逻辑已在其中处理）
        from app01.views import test_for_seatalk_bot
        
        # 如果没有提供thread_id和quoted_message_id，尝试从上下文获取
        if thread_id is None and quoted_message_id is None:
            context_group_id, context_thread_id, context_message_id = ThreadReplyContext.get_thread_context(group_id)
            # 优先使用线程ID
            if context_thread_id:
                thread_id = context_thread_id
                ic(f"从上下文获取线程ID: {thread_id}, 用于群组: {group_id}")
            # 如果没有线程ID但有消息ID，则使用消息ID
            elif context_message_id:
                quoted_message_id = context_message_id
                ic(f"从上下文获取引用消息ID: {quoted_message_id}, 用于群组: {group_id}")
        
        if thread_id:
            ic(f"发送线程回复消息，群组={group_id}, 线程ID={thread_id}")
        elif quoted_message_id:
            ic(f"发送引用回复消息，群组={group_id}, 引用消息ID={quoted_message_id}")
        
        # 调用test_for_seatalk_bot函数（翻译逻辑已在其中处理）
        if thread_id and quoted_message_id:
            ic(f"使用线程ID和引用消息ID回复: {thread_id}, {quoted_message_id}")
            return test_for_seatalk_bot(message, [], group_id, thread_id, quoted_message_id)
        elif thread_id:
            ic(f"使用线程ID回复: {thread_id}")
            return test_for_seatalk_bot(message, [], group_id, thread_id, None)
        elif quoted_message_id:
            ic(f"使用引用消息ID回复: {quoted_message_id}")
            return test_for_seatalk_bot(message, [], group_id, None, quoted_message_id)
        else:
            ic("发送普通消息")
            return test_for_seatalk_bot(message, [], group_id)
    except Exception as e:
        ic(f"Exception in send_message_to_group: {str(e)}")
        return False

def create_seatalk_group(jira_key: str, members_info: Dict[str, List[str]], jira_status: str, jira_summary: str) -> Dict:
    """
    创建SeaTalk群组
    
    Args:
        jira_key: JIRA单号
        members_info: 成员信息字典，包含各角色的邮箱列表
        jira_status: JIRA单状态
        jira_summary: JIRA单标题
        
    Returns:
        Dict: 包含创建结果的字典
    """
    # 设置群主
    if "SPCB" in jira_key:
        owner_email = "<EMAIL>"
    elif "SPCT" in jira_key:
        owner_email = "<EMAIL>"
    else:
        owner_email = "<EMAIL>"
    
    # 收集所有成员邮箱
    all_emails = [owner_email]  # 首先添加群主
    additional_info = []
    
    for role, role_emails in members_info.items():
        if role == "additional_info":
            # 保存额外信息，稍后添加到消息中
            if isinstance(role_emails, list):
                additional_info.extend(role_emails)
            else:
                additional_info.append(role_emails)
            continue
            
        if isinstance(role_emails, list):
            all_emails.extend(role_emails)
        elif isinstance(role_emails, str):
            all_emails.append(role_emails)
    
    # 去重
    all_emails = list(set(all_emails))
    
    # 生成群名称并处理长度限制
    base_group_name = f"[{jira_key}][{jira_status}]{jira_summary}"
    group_name = truncate_group_name(base_group_name)
    
    # 准备详细信息消息
    detail_message = f"群组创建详情：\n\n"
    detail_message += f"群名称: {group_name}\n\n"
    detail_message += f"群主: {owner_email}\n"
    detail_message += f"预期群成员: {', '.join(all_emails)}\n"
    
    # 添加额外信息到详细消息
    if additional_info:
        detail_message += "\n额外信息:\n"
        for info in additional_info:
            detail_message += f"• {info}\n"
    
    # 打印群成员信息
    ic(f"群主: {owner_email}")
    ic(f"预期群成员: {all_emails}")
    
    # 预先验证成员并检查最小群成员数量
    validation_result = validate_group_members(all_emails, detail_message)
    if not validation_result["success"]:
        return validation_result

    email_to_code = validation_result["email_to_code"]
    successful_members = validation_result["successful_members"]
    failed_members = validation_result["failed_members"]
    detail_message = validation_result["detail_message"]

    # 确保群主的employee code存在
    owner_code = email_to_code.get(owner_email)
    if not owner_code:
        return {"success": False, "message": "Failed to get owner employee code", "detail_message": detail_message}

    # 准备群成员列表
    group_members = []
    for email, emp_code in email_to_code.items():
        if emp_code and email != owner_email:  # 排除群主，因为群主会通过另一个参数设置
            group_members.append({
                "employee_code": emp_code,
                "role": 0  # 设置为普通成员
            })
    
    # 创建群组
    access_token = app_access_token()
    headers = {
        'content-type': "application/json",
        'Authorization': f"Bearer {access_token}"
    }

    param = {
        "group_owner": owner_code,
        "group_member_list": group_members,
        "group_name": group_name,
        "group_settings": {
            "chat_history_for_new_members": 2  # 聊天记录设置 0 - "关闭", 1 - "24小时", 2 - "7天"
        }
    }

    # 使用带重试机制的群组创建函数
    return create_group_with_retry(param, headers, detail_message)

def auto_create_group_for_jira(jira_key: str, get_timeline_func=None) -> Dict:
    """
    为JIRA单号自动创建群组的主函数
    
    Args:
        jira_key: JIRA单号
        get_timeline_func: 获取timeline信息的函数（从views.py传入）
        
    Returns:
        Dict: 包含创建结果的字典
    """
    # 创建JIRA连接
    jira_url = "https://jira.shopee.io"
    jira = JIRA(jira_url, token_auth=jira_token)
    
    try:
        # 获取JIRA issue
        epic_issue = jira.issue(jira_key)
        
        # 获取状态和标题
        jira_status = epic_issue.fields.status.name if epic_issue.fields.status else "Unknown"
        jira_summary = epic_issue.fields.summary
        
        # 收集人员信息
        people_info = {}

        # 添加reporter和 assignee
        if hasattr(epic_issue.fields, "reporter") and epic_issue.fields.reporter:
            # 如果reporter是*******************就不添加 reporter
            if epic_issue.fields.reporter.emailAddress != "<EMAIL>":
                people_info["Reporter"] = [epic_issue.fields.reporter.emailAddress]
        if hasattr(epic_issue.fields, "assignee") and epic_issue.fields.assignee:
            people_info["Assignee"] = [epic_issue.fields.assignee.emailAddress]
        
        # 添加开发人员
        if hasattr(epic_issue.fields, "customfield_10307") and epic_issue.fields.customfield_10307:
            people_info["Developer"] = [epic_issue.fields.customfield_10307.emailAddress]
            
        # 添加FE开发
        if hasattr(epic_issue.fields, "customfield_37801") and epic_issue.fields.customfield_37801:
            fe_list = []
            for person in epic_issue.fields.customfield_37801:
                if hasattr(person, 'emailAddress'):
                    fe_list.append(person.emailAddress)
            if fe_list:
                people_info["FE List"] = fe_list
                
        # 添加BE开发
        if hasattr(epic_issue.fields, "customfield_37800") and epic_issue.fields.customfield_37800:
            be_list = []
            for person in epic_issue.fields.customfield_37800:
                if hasattr(person, 'emailAddress'):
                    be_list.append(person.emailAddress)
            if be_list:
                people_info["BE List"] = be_list
            
        # 添加QA
        if hasattr(epic_issue.fields, "customfield_10308") and epic_issue.fields.customfield_10308:
            people_info["QA"] = [epic_issue.fields.customfield_10308.emailAddress]
            
        # 添加QA list
        if hasattr(epic_issue.fields, "customfield_12202") and epic_issue.fields.customfield_12202:
            qa_list = []
            for person in epic_issue.fields.customfield_12202:
                if hasattr(person, 'emailAddress'):
                    qa_list.append(person.emailAddress)
            if qa_list:
                people_info["QA List"] = qa_list
            
        # 添加PM
        if hasattr(epic_issue.fields, "customfield_10306") and epic_issue.fields.customfield_10306:
            people_info["Product Manager"] = [epic_issue.fields.customfield_10306.emailAddress]
        
        # 记录收集到的原始成员
        all_original_members = []
        for role, role_members in people_info.items():
            if isinstance(role_members, list):
                all_original_members.extend(role_members)
            elif isinstance(role_members, str):
                all_original_members.append(role_members)
        
        ic(f"从JIRA获取的原始成员: {all_original_members}")
        
        # 获取将要添加的团队TL
        team_leaders = get_team_leaders_for_members(all_original_members)
        if team_leaders:
            ic(f"将自动添加以下团队TL: {team_leaders}")
            
            # 将团队TL添加到人员信息中
            if "Team Leaders" not in people_info:
                people_info["Team Leaders"] = []
            
            # 过滤掉已经在其他角色列表中的TL
            new_leaders = []
            for leader in team_leaders:
                already_added = False
                for role, members in people_info.items():
                    if role != "Team Leaders" and isinstance(members, list) and leader in members:
                        already_added = True
                        break
                    elif role != "Team Leaders" and isinstance(members, str) and leader == members:
                        already_added = True
                        break
                
                if not already_added:
                    new_leaders.append(leader)
            
            people_info["Team Leaders"] = new_leaders
            
            # 添加团队TL信息到消息中
            if "additional_info" not in people_info:
                people_info["additional_info"] = []
            people_info["additional_info"].append(f"自动添加的团队TL: {', '.join(new_leaders)}")
            
        # 创建群组
        result = create_seatalk_group(jira_key, people_info, jira_status, jira_summary)
        
        # 如果创建成功且需要获取timeline信息
        if result["success"] and get_timeline_func is None:
            try:
                # 在需要时导入get_timeline_of_epic
                from app01.views import get_timeline_of_epic
                timeline_info = get_timeline_of_epic(jira_key)
                if isinstance(timeline_info, str):  # 如果返回的是字符串，说明是消息内容
                    send_message_to_group(result["group_id"], timeline_info)
            except Exception as e:
                ic(f"Exception in sending timeline info: {str(e)}")
        elif result["success"] and get_timeline_func:
            try:
                timeline_info = get_timeline_func(jira_key)
                if isinstance(timeline_info, str):
                    send_message_to_group(result["group_id"], timeline_info)
            except Exception as e:
                ic(f"Exception in sending timeline info: {str(e)}")
        
        return result
        
    except Exception as e:
        # 使用简化的错误信息
        simplified_error = simplify_jira_error(str(e))
        ic(f"Exception in auto_create_group_for_jira: {simplified_error}")
        return {"success": False, "message": f"Error getting JIRA information: {simplified_error}"}

# 给用户发送私聊消息的标准方法，可以发送文本、图片、文件、markdown消息
def send_message_to_user(employee_code: str, message_content: str, message_type: str = "text", format: int = 1, file_content: str = None, file_name: str = None) -> Dict:
    """
    给用户发送私聊消息
    
    Args:
        employee_code: 用户的employee_code
        message_content: 消息内容。如果是文本消息，则为文本内容；如果是图片/文件消息，则为base64编码的内容
        message_type: 消息类型，支持 "text"/"image"/"file"/"interactive_message"/"markdown"
        format: 文本消息的格式，1为支持markdown格式，2为纯文本
        file_content: 文件内容（base64编码）
        file_name: 文件名（带扩展名）
    
    Returns:
        Dict: 包含发送结果的字典
    """
    # 私聊消息长度限制（与异步版本保持一致）
    MAX_PRIVATE_MESSAGE_LENGTH = 3500
    
    # 对于文本和markdown消息，检查长度限制
    if message_type in ["text", "markdown"] and len(message_content) > MAX_PRIVATE_MESSAGE_LENGTH:
        return _send_long_text_message(employee_code, message_content, message_type, format, MAX_PRIVATE_MESSAGE_LENGTH)
    
    # 发送单条消息
    return _send_single_user_message(employee_code, message_content, message_type, format, file_content, file_name)


def _send_single_user_message(employee_code: str, message_content: str, message_type: str = "text", format: int = 1, file_content: str = None, file_name: str = None) -> Dict:
    """
    发送单条私聊消息的内部方法
    """
    url = "https://openapi.seatalk.io/messaging/v2/single_chat"
    access_token = app_access_token()
    headers = {
        'content-type': "application/json",
        'Authorization': f"Bearer {access_token}"
    }
    
    # 根据消息类型构建不同的消息体
    if message_type == "text":
        message = {
            "tag": "text",
            "text": {
                "format": format,
                "content": message_content
            }
        }
    elif message_type == "image":
        message = {
            "tag": "image",
            "image": {
                "content": message_content  # base64编码的图片内容
            }
        }
    elif message_type == "file":
        if not file_name or not file_content:
            return {"code": 1, "message": "File name and content are required for file messages"}
        message = {
            "tag": "file",
            "file": {
                "filename": file_name,
                "content": file_content  # base64编码的文件内容
            }
        }
    elif message_type == "markdown":
        message = {
            "tag": "markdown",
            "markdown": {
                "content": message_content
            }
        }
    else:
        return {"code": 1, "message": f"Unsupported message type: {message_type}"}
    
    param = {
        "employee_code": employee_code,
        "message": message
    }
    
    try:
        ic(f"Sending message to user {employee_code}")
        response = requests.post(url=url, json=param, headers=headers)
        result = response.json()
        ic(f"Send message response: {result}")
        
        if result["code"] == 0:
            return {"code": 0, "message": "Success"}
        else:
            return {"code": result["code"], "message": f"Failed to send message: {result}"}
    except Exception as e:
        ic(f"Exception in send_message_to_user: {str(e)}")
        return {"code": 1, "message": f"Exception: {str(e)}"}


def _send_long_text_message(employee_code: str, content: str, message_type: str, format: int, max_length: int) -> Dict:
    """
    发送长文本消息的内部方法，自动分段处理
    """
    ic(f"消息长度 {len(content)} 超过限制 {max_length}，开始分段发送到: {employee_code}")
    
    # 按行分割文本
    lines = content.split('\n')
    current_chunk = ""
    segment_count = 1
    last_result = None
    
    for line in lines:
        # 如果当前块加上新行不超过限制，就添加到当前块
        if len(current_chunk) + len(line) + 1 <= max_length:
            if current_chunk:
                current_chunk += '\n'
            current_chunk += line
        else:
            # 发送当前块
            if current_chunk:
                ic(f"发送第 {segment_count} 段私聊消息，长度: {len(current_chunk)}")
                result = _send_single_user_message(employee_code, current_chunk, message_type, format)
                if result.get("code") != 0:
                    ic(f"第 {segment_count} 段消息发送失败: {result.get('message')}")
                    return result
                last_result = result
                segment_count += 1
            # 开始新的块
            current_chunk = line
    
    # 发送最后一段消息，并添加截断提示
    if current_chunk:
        # 在最后一段消息末尾添加截断提示
        truncate_notice = "\n\n⚠️ 由于消息过长，已自动截断显示。如需查看完整内容，请重新提问或分段询问。"
        if len(current_chunk) + len(truncate_notice) <= max_length:
            current_chunk += truncate_notice
        else:
            # 如果添加提示后超长，先发送当前块，再发送提示
            ic(f"发送第 {segment_count} 段私聊消息，长度: {len(current_chunk)}")
            result = _send_single_user_message(employee_code, current_chunk, message_type, format)
            if result.get("code") != 0:
                ic(f"第 {segment_count} 段消息发送失败: {result.get('message')}")
                return result
            segment_count += 1
            current_chunk = truncate_notice
        
        ic(f"发送最后一段（第 {segment_count} 段）私聊消息，长度: {len(current_chunk)}")
        result = _send_single_user_message(employee_code, current_chunk, message_type, format)
        if result.get("code") != 0:
            ic(f"最后一段消息发送失败: {result.get('message')}")
            return result
        last_result = result
    
    ic(f"长消息分 {segment_count} 段发送完成到: {employee_code}")
    return last_result or {"code": 0, "message": "分段发送完成"}

def sync_timeline_changes(jira_key: str, old_timeline: Dict = None, new_timeline: Dict = None, old_people: Dict = None, new_people: Dict = None) -> Dict:
    """
    同步排期变更和人员变更信息到需求群
    
    Args:
        jira_key: JIRA单号
        old_timeline: 旧的timeline信息，包含各个阶段的日期
        new_timeline: 新的timeline信息，包含各个阶段的日期
        old_people: 旧的人员信息
        new_people: 新的人员信息
    
    Returns:
        Dict: 包含同步结果的字典
    """
    try:
        ic(f"开始同步 {jira_key} 的Timeline和人员变更")
        
        # 在需要时导入get_timeline_of_epic
        from app01.views import get_timeline_of_epic
        
        # 获取完整的timeline消息
        try:
            timeline_message = get_timeline_of_epic(jira_key)
            ic(f"{jira_key} - 成功获取Timeline信息")
        except Exception as e:
            # 使用简化的错误信息
            simplified_error = simplify_jira_error(str(e))
            ic(f"{jira_key} - 获取Timeline信息失败: {simplified_error}")
            raise Exception(f"获取Timeline信息失败: {simplified_error}")
            
        if isinstance(timeline_message, str) and "JIRA登录失败" in timeline_message:
            ic(f"{jira_key} - JIRA登录失败")
            raise Exception(timeline_message)
            
        # 添加变更信息到消息末尾
        changes_message = ""
        
        # 处理Timeline变更
        if old_timeline and new_timeline:
            timeline_changes_found = False
            for field, old_value in old_timeline.items():
                if field in new_timeline and old_value != new_timeline[field]:
                    if not timeline_changes_found:
                        changes_message += "\n\n⚠️ **检测到以下Timeline变更**\n"
                        changes_message += "━━━━━━━━━━━━━━━━━━━━━━\n"
                        timeline_changes_found = True
                    changes_message += f"• {field}: **{old_value or '未设置'}** → **{new_timeline[field] or '未设置'}**\n"
            
            if timeline_changes_found:
                ic(f"{jira_key} - 已生成Timeline变更信息")
        
        # 处理人员变更
        if old_people and new_people:
            people_changes_found = False
            for field, old_value in old_people.items():
                if field in new_people and old_value != new_people[field]:
                    if not people_changes_found:
                        changes_message += "\n\n👥 **检测到以下人员变更**\n"
                        changes_message += "━━━━━━━━━━━━━━━━━━━━━━\n"
                        people_changes_found = True
                    changes_message += f"• {field}: **{old_value or '未设置'}** → **{new_people[field] or '未设置'}**\n"
            
            if people_changes_found:
                ic(f"{jira_key} - 已生成人员变更信息")
        
        # 如果有任何变更，添加到消息中
        if changes_message:
            timeline_message += changes_message
        
        # 从数据库查找包含该JIRA号的群组
        try:
            groups = SeatalkGroup.objects.filter(group_name__contains=jira_key)
            ic(f"{jira_key} - 找到 {groups.count()} 个相关群组")
        except Exception as e:
            ic(f"{jira_key} - 查询群组失败: {str(e)}")
            raise Exception(f"查询群组失败: {str(e)}")
        
        if not groups.exists():
            ic(f"{jira_key} - 未找到相关群组")
            raise Exception(f"未找到包含 {jira_key} 的群组")
        
        # 向所有找到的群发送消息
        success_count = 0
        failed_groups = []
        
        for group in groups:
            try:
                ic(f"{jira_key} - 正在发送消息到群组: {group.group_name}")
                send_result = send_message_to_group(group.group_id, timeline_message)
                if send_result:
                    success_count += 1
                    ic(f"{jira_key} - 成功发送消息到群组: {group.group_name}")
                else:
                    ic(f"{jira_key} - 发送消息失败: {group.group_name}")
                    failed_groups.append({
                        "group_id": group.group_id,
                        "group_name": group.group_name
                    })
            except Exception as e:
                ic(f"{jira_key} - 发送消息异常: {group.group_name} - {str(e)}")
                failed_groups.append({
                    "group_id": group.group_id,
                    "group_name": group.group_name,
                    "error": str(e)
                })
        
        # 发送详细的执行报告到Workee PJ日志群（包含成功和失败信息）
        log_message = f"📋 **Timeline变更消息发送报告** 📋\n\n"
        log_message += f"📋 **JIRA单号**: {jira_key}\n"
        log_message += f"📊 **发送统计**: {success_count}/{groups.count()} 个群组成功\n\n"
        
        # 添加成功发送的群组详情
        if success_count > 0:
            log_message += "✅ **发送成功的群组**:\n"
            success_groups = [group for group in groups if group not in [fg.get('group_name') for fg in failed_groups]]
            for group in groups:
                if not any(fg.get('group_name') == group.group_name or fg.get('group_id') == group.group_id for fg in failed_groups):
                    log_message += f"• {group.group_name} (ID: {group.group_id})\n"
            log_message += "\n"
        
        # 添加失败的群组详情
        if failed_groups:
            log_message += "❌ **发送失败的群组**:\n"
            for group in failed_groups:
                log_message += f"• {group['group_name']} (ID: {group['group_id']})\n"
                if 'error' in group:
                    simplified_error = simplify_jira_error(group['error'])
                    log_message += f"  错误: {simplified_error}\n"
            log_message += "\n"
        
        # 添加消息预览（截取前200字符）
        if timeline_message:
            preview = timeline_message[:200] + "..." if len(timeline_message) > 200 else timeline_message
            log_message += f"📄 **消息预览**: {preview}"
        
        try:
            # 发送详细报告到Workee PJ日志群
            ic(f"{jira_key} - 正在发送详细执行报告到Workee PJ日志群")
            send_message_to_group(WORKEE_PJ_LOG_GROUP_ID, log_message)
        except Exception as e:
            ic(f"{jira_key} - 发送执行报告到Workee PJ日志群失败: {str(e)}")
        
        return {
            "success": success_count > 0,
            "message": f"成功发送到 {success_count}/{groups.count()} 个群组",
            "failed_groups": failed_groups if failed_groups else None
        }
        
    except Exception as e:
        ic(f"{jira_key} - Timeline变更处理异常: {str(e)}")
        
        # 简化错误信息
        simplified_error = simplify_jira_error(str(e))
        
        # 直接发送异常信息到Workee PJ日志群
        error_message = f"❌ **Timeline变更处理异常** ❌\n\n"
        error_message += f"📋 **JIRA单号**: {jira_key}\n"
        error_message += f"❌ **错误信息**: {simplified_error}\n"
        error_message += f"⏰ **时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        try:
            send_message_to_group(WORKEE_PJ_LOG_GROUP_ID, error_message)
            ic(f"{jira_key} - 已发送Timeline处理异常通知到Workee PJ日志群")
        except Exception as notify_error:
            ic(f"{jira_key} - 发送Timeline异常通知失败: {str(notify_error)}")
        
        return {
            "success": False,
            "message": str(e)
        }

def process_timeline_changes(issue_key: str, history_items: list, change_id: str, change_time: datetime) -> Dict:
    """
    处理 Timeline 变更和人员信息变更的方法
    
    Args:
        issue_key: JIRA 单号
        history_items: 变更历史记录项
        change_id: 变更记录的唯一标识
        change_time: 变更时间
    
    Returns:
        Dict: 处理结果
    """
    try:
        # 获取所有Timeline相关字段的变更
        timeline_changes = []
        old_timeline = {}
        new_timeline = {}
        
        # 获取所有人员相关字段的变更
        people_changes = []
        old_people = {}
        new_people = {}
        
        for item in history_items:
            # 处理Timeline字段变更
            field_key = item.field if item.field in TIMELINE_FIELDS else None
            if field_key or item.field in TIMELINE_FIELDS.values():
                field_name = TIMELINE_FIELDS.get(field_key or item.field, item.field)
                change = {
                    'field': field_name,
                    'from': item.fromString,
                    'to': item.toString,
                    'time': change_time
                }
                timeline_changes.append(change)
                # 同时更新新旧timeline数据
                old_timeline[field_name] = item.fromString
                new_timeline[field_name] = item.toString
                ic(f"{issue_key} - Timeline变更: {field_name} - 从 {item.fromString or '未设置'} → {item.toString or '未设置'}")
            
            # 处理人员字段变更
            people_field_key = item.field if item.field in PEOPLE_FIELDS else None
            if people_field_key or item.field in PEOPLE_FIELDS.values():
                field_name = PEOPLE_FIELDS.get(people_field_key or item.field, item.field)
                change = {
                    'field': field_name,
                    'from': item.fromString,
                    'to': item.toString,
                    'time': change_time
                }
                people_changes.append(change)
                # 同时更新新旧人员数据
                old_people[field_name] = item.fromString
                new_people[field_name] = item.toString
                ic(f"{issue_key} - 人员变更: {field_name} - 从 {item.fromString or '未设置'} → {item.toString or '未设置'}")
        
        if timeline_changes or people_changes:
            # 检查是否已经处理过这个变更
            if change_id in PROCESSED_CHANGES:
                ic(f"{issue_key} - 变更 {change_id} 已经处理过，跳过")
                return {"success": True, "message": "Change already processed"}
            
            # 同步到群组（现在支持Timeline和人员变更）
            result = sync_timeline_changes(issue_key, old_timeline, new_timeline, old_people, new_people)
            
            if result.get("success"):
                # 记录已处理的变更
                PROCESSED_CHANGES[change_id] = change_time
                ic(f"{issue_key} - 变更已同步到群组")
                return {"success": True, "message": "Timeline and people changes synced successfully"}
            else:
                ic(f"{issue_key} - 同步变更失败: {result.get('message')}")
                return {"success": False, "message": result.get('message')}
        
        return {"success": True, "message": "No timeline or people changes detected"}
            
    except Exception as e:
        ic(f"{issue_key} - 处理出错: {str(e)}")
        return {"success": False, "message": f"Error processing timeline changes: {str(e)}"}

@track_cronjob_execution('check_timeline_changes')
def check_timeline_changes() -> None:
    """
    定时检查timeline变更的函数
    每60分钟运行一次，使用内存缓存确保不重复发送
    """
    try:
        # 创建JIRA连接
        jira_url = "https://jira.shopee.io"
        jira = JIRA(jira_url, token_auth=jira_token)
        
        # 查询最近6分钟内更新的JIRA单（60分钟间隔+1分钟容错）,注意这里的 60 分钟需要保持和计划任务的配置一致
        jql = 'project in (SPCB, SPCT) AND issuetype = Epic AND updated >= -61m  AND status != closed'
        issues = jira.search_issues(jql, expand='changelog')
        ic(f"找到 {len(issues)} 个最近更新的 JIRA 单")
        
        # 获取61分钟前的时间点
        cronjob_minutes_ago = datetime.now(timezone.utc) - timedelta(minutes=61)
        
        # 用于存储每个JIRA单的所有变更
        issue_changes = {}
        
        for issue in issues:
            try:
                # 获取这个issue的所有历史记录
                changelog = issue.changelog
                
                # 遍历每个变更历史
                for history in changelog.histories:
                    # 检查变更时间
                    change_time = datetime.strptime(history.created, '%Y-%m-%dT%H:%M:%S.%f%z')
                    
                    # 只处理最近6分钟内的变更
                    if change_time < cronjob_minutes_ago:
                        continue
                    
                    # 生成变更记录的唯一标识
                    change_id = f"{issue.key}_{history.id}"
                    
                    # 检查这个变更是否已经处理过
                    if change_id in PROCESSED_CHANGES:
                        ic(f"跳过已处理的变更: {change_id}, 处理时间: {PROCESSED_CHANGES[change_id]}")
                        continue
                    
                    # 检查是否包含Timeline相关字段的变更
                    has_timeline_changes = False
                    for item in history.items:
                        field_key = item.field if item.field in TIMELINE_FIELDS else None
                        if field_key or item.field in TIMELINE_FIELDS.values():
                            has_timeline_changes = True
                            break
                    
                    # 检查是否包含人员相关字段的变更
                    has_people_changes = False
                    for item in history.items:
                        people_field_key = item.field if item.field in PEOPLE_FIELDS else None
                        if people_field_key or item.field in PEOPLE_FIELDS.values():
                            has_people_changes = True
                            break
                    
                    if has_timeline_changes or has_people_changes:
                        # 将变更添加到对应JIRA单的变更列表中
                        if issue.key not in issue_changes:
                            issue_changes[issue.key] = {
                                'changes': [],
                                'change_ids': set(),
                                'latest_time': change_time
                            }
                        
                        issue_changes[issue.key]['changes'].extend(history.items)
                        issue_changes[issue.key]['change_ids'].add(change_id)
                        issue_changes[issue.key]['latest_time'] = max(issue_changes[issue.key]['latest_time'], change_time)
                
            except Exception as e:
                # 使用简化的错误信息
                simplified_error = simplify_jira_error(str(e))
                ic(f"{issue.key} - 处理出错: {simplified_error}")
                continue
        
        # 处理每个JIRA单的所有变更
        for issue_key, change_data in issue_changes.items():
            try:
                # 处理合并后的变更
                result = process_timeline_changes(
                    issue_key=issue_key,
                    history_items=change_data['changes'],
                    change_id=f"{issue_key}_merged_{int(change_data['latest_time'].timestamp())}",
                    change_time=change_data['latest_time']
                )
                
                if result.get("success"):
                    # 记录所有已处理的变更ID
                    for change_id in change_data['change_ids']:
                        PROCESSED_CHANGES[change_id] = change_data['latest_time']
                        ic(f"变更 {change_id} 处理成功，已加入缓存")
                
            except Exception as e:
                # 使用简化的错误信息
                simplified_error = simplify_jira_error(str(e))
                ic(f"{issue_key} - 处理合并变更出错: {simplified_error}")
                continue
        
    except Exception as e:
        # 使用简化的错误信息
        simplified_error = simplify_jira_error(str(e))
        ic(f"Timeline 检查出错: {simplified_error}")
        
    ic("Timeline 检查完成\n\n") 

def get_team_leaders_for_members(member_emails: List[str]) -> Set[str]:
    """
    根据成员邮箱获取对应的团队TL邮箱
    
    Args:
        member_emails: 成员邮箱列表
    
    Returns:
        Set[str]: 对应的团队TL邮箱集合
    """
    leaders = set()
    
    # 将输入的邮箱清理和标准化
    member_emails = [email.strip().lower() for email in member_emails if email]
    
    # 先检查成员是否已经是TL
    for subteam, data in TEAM_DATA.items():
        team_leaders = [leader.strip().lower() for leader in data["leaders"] if leader]
        for email in member_emails:
            if email in team_leaders:
                leaders.add(email)
    
    # 为每个成员查找对应的TL
    for email in member_emails:
        for subteam, data in TEAM_DATA.items():
            team_members = [member.strip().lower() for member in data["members"] if member]
            team_leaders = [leader.strip().lower() for leader in data["leaders"] if leader]
            
            if email in team_members:
                # 添加该团队的所有TL
                leaders.update(team_leaders)
    
    # 返回去重后的TL邮箱集合
    return leaders

def test_team_leaders_feature(test_members: List[str] = None) -> None:
    """
    测试团队TL自动添加功能
    
    Args:
        test_members: 测试成员列表，如果为None则使用默认测试数据
    """
    if test_members is None:
        # 默认测试数据，包含不同团队的成员
        test_members = [
            "<EMAIL>",        # ChatSS 成员
            "<EMAIL>",         # Webchat_FE 成员
            "<EMAIL>",      # Webchat_BE 成员
            "<EMAIL>",      # Chat_QA 成员
            "<EMAIL>",        # Native 成员
            "<EMAIL>", # Chat_DRE 成员
            "<EMAIL>",      # Chatbot_Platform_BE 成员
            "<EMAIL>",       # Chatbot_Function_BE 成员
            "<EMAIL>",        # Chatbot_FE 成员
            "<EMAIL>"         # Chatbot_QA 成员
        ]
    
    ic("===== 测试团队TL自动添加功能 =====")
    ic(f"测试成员: {test_members}")
    
    # 获取团队TL
    leaders = get_team_leaders_for_members(test_members)
    ic(f"自动添加的团队TL: {leaders}")
    
    # 模拟创建群组的成员信息数据结构
    members_info = {
        "Test Members": test_members
    }
    
    # 打印预期的完整成员列表
    all_members = test_members.copy()
    all_members.extend(leader for leader in leaders if leader not in test_members)
    ic(f"最终成员列表: {all_members}")
    
    return {
        "original_members": test_members,
        "added_leaders": leaders,
        "final_members": all_members
    }

# 可以通过在命令行运行此模块来测试团队TL功能
if __name__ == "__main__":
    test_team_leaders_feature()

@track_cronjob_execution('check_jira_assignee_changes')
def check_jira_assignee_changes() -> None:
    """
    定时检查JIRA单Assignee变更的函数
    每5分钟运行一次，当Bug被分配给某人时发送私信通知
    
    要求:
    1. 只通知SPCB, SPCT项目的Bug
    2. 状态不为closed、icebox和Done的Bug
    3. 只通知5分钟内刚修改的assignee，历史的不用管
    4. 只有在发送失败时，才发送错误消息到调试群"NzQzMzAxODcyMjAy"
    5. 对于多次修改assignee的情况，只发送给最后一个assignee
    """
    try:
        # 创建JIRA连接
        jira_url = "https://jira.shopee.io"
        jira = JIRA(jira_url, token_auth=jira_token)
        
        # 查询最近6分钟内更新的JIRA单（5分钟间隔+1分钟容错）
        # 扩展支持SPUAT项目的指定模块
        jql = '''(
            (project in (SPCB,SPCT) AND issuetype = Bug) OR 
            (project = "SPUAT" AND "Product Line (UAT)" in ("shop chatbot","Chat","Chat - ChatSS","Chat - WebChat","Chatboost", "Chatbot Data","Chatbot Foundation","Chatbot Intention","Chatbot Knowledge","Chatbot Skill") AND issuetype = Bug)
        ) AND updated >= -6m AND status not in (Closed, Icebox, Done)'''
        issues = jira.search_issues(jql, expand='changelog')
        ic(f"找到 {len(issues)} 个最近更新的 Bug 类型 JIRA 单")
        
        # 获取6分钟前的时间点
        six_minutes_ago = datetime.now(timezone.utc) - timedelta(minutes=6)
        
        # 用于记录处理结果的列表，失败时将发送到调试群
        process_results = []
        
        # 用于记录每个issue最新的assignee变更
        latest_assignee_changes = {}
        
        # 第一步：收集最近6分钟内所有issue的最新assignee变更
        for issue in issues:
            try:
                issue_key = issue.key
                current_assignee = issue.fields.assignee
                current_assignee_email = current_assignee.emailAddress if current_assignee else None
                
                # 获取这个issue的所有历史记录
                changelog = issue.changelog
                
                # 记录最新的assignee变更
                latest_change_time = None
                latest_change_id = None
                latest_old_assignee = None
                latest_new_assignee = None
                
                # 遍历每个变更历史
                for history in changelog.histories:
                    # 检查变更时间
                    change_time = datetime.strptime(history.created, '%Y-%m-%dT%H:%M:%S.%f%z')
                    
                    # 只处理最近6分钟内的变更
                    if change_time < six_minutes_ago:
                        continue
                    
                    # 检查是否有Assignee字段的变更
                    for item in history.items:
                        if item.field == 'assignee':
                            # 记录变更信息，如果有多次变更，只保留最新的
                            if latest_change_time is None or change_time > latest_change_time:
                                latest_change_time = change_time
                                latest_change_id = f"{issue_key}_assignee_{history.id}"
                                latest_old_assignee = item.fromString
                                latest_new_assignee = item.toString
                
                # 如果找到了assignee变更，记录到字典中
                if latest_change_time:
                    # 获取SLA信息（仅对SPUAT项目）
                    sla_info = None
                    if issue_key.startswith('SPUAT-'):
                        sla_info = get_real_sla_info(issue)
                    
                    latest_assignee_changes[issue_key] = {
                        "change_time": latest_change_time,
                        "change_id": latest_change_id,
                        "old_assignee": latest_old_assignee,
                        "new_assignee": latest_new_assignee,
                        "current_assignee": current_assignee,
                        "current_assignee_email": current_assignee_email,
                        "issue_summary": issue.fields.summary,
                        "issue_status": issue.fields.status.name,
                        "issue_priority": issue.fields.priority.name,
                        "created_time": issue.fields.created,
                        "sla_info": sla_info
                    }
            except Exception as e:
                ic(f"{issue.key} - 收集assignee变更信息时出错: {str(e)}")
        
        # 处理新建的bug
        for issue in issues:
            try:
                issue_key = issue.key
                # 如果这个issue已经在处理列表中，跳过
                if issue_key in latest_assignee_changes:
                    continue
            
                # 检查是否是新建的bug
                created_time = datetime.strptime(issue.fields.created, '%Y-%m-%dT%H:%M:%S.%f%z')
                if created_time < six_minutes_ago:
                    continue  # 不是最近创建的，跳过
            
                # 检查是否有assignee
                current_assignee = issue.fields.assignee
                if not current_assignee:
                    continue  # 没有assignee，跳过
            
                # 为新建的bug构建一个特殊的变更ID
                change_id = f"{issue_key}_new_created_{int(created_time.timestamp())}"
            
                # 检查是否已经处理过
                if change_id in PROCESSED_CHANGES:
                    continue
            
                # 获取SLA信息（仅对SPUAT项目）
                sla_info = None
                if issue_key.startswith('SPUAT-'):
                    sla_info = get_real_sla_info(issue)
                
                # 添加到处理列表
                latest_assignee_changes[issue_key] = {
                    "change_time": created_time,
                    "change_id": change_id,
                    "old_assignee": None,
                    "new_assignee": current_assignee.displayName,
                    "current_assignee": current_assignee,
                    "current_assignee_email": current_assignee.emailAddress,
                    "issue_summary": issue.fields.summary,
                    "issue_status": issue.fields.status.name,
                    "issue_priority": issue.fields.priority.name,
                    "created_time": issue.fields.created,
                    "sla_info": sla_info,
                    "is_new_bug": True  # 标记这是一个新建的bug
                }
            except Exception as e:
                ic(f"{issue.key} - 处理新建bug时出错: {str(e)}")
        
        # 第二步：处理每个issue的最新assignee变更
        for issue_key, change_info in latest_assignee_changes.items():
            try:
                issue_url = f"{jira_url}/browse/{issue_key}"
                
                # 检查这个变更是否已经处理过
                if change_info["change_id"] in PROCESSED_CHANGES:
                    ic(f"跳过已处理的assignee变更: {change_info['change_id']}, 处理时间: {PROCESSED_CHANGES[change_info['change_id']]}")
                    continue
                
                # 如果有当前的受理人，发送私信
                if change_info["current_assignee_email"]:
                    # 获取受理人的employee code
                    employee_codes = get_employee_codes([change_info["current_assignee_email"]])
                    if change_info["current_assignee_email"] in employee_codes:
                        employee_code = employee_codes[change_info["current_assignee_email"]]
                        
                        # 准备消息内容
                        message = f"🐞 **您有一个新的【{change_info['issue_priority']}】Bug需要处理**\n"
                        message += f"• JIRA: {issue_url}\n"
                        message += f"• 标题: {change_info['issue_summary']}\n"
                        message += f"• 状态: {change_info['issue_status']}\n"
                        
                        # 添加创建时间
                        try:
                            created_time_str = change_info.get('created_time')
                            if created_time_str:
                                created_time = datetime.strptime(created_time_str, '%Y-%m-%dT%H:%M:%S.%f%z')
                                # 转换为新加坡时间显示
                                singapore_tz = timezone(timedelta(hours=8))
                                singapore_time = created_time.astimezone(singapore_tz)
                                message += f"• 创建时间: {singapore_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                        except Exception as time_e:
                            ic(f"格式化创建时间出错: {str(time_e)}")
                            # 如果格式化失败，不影响其他信息的显示
                        
                        # 添加SLA信息（仅对SPUAT项目）
                        if issue_key.startswith('SPUAT-'):
                            sla_info = change_info.get('sla_info')
                            if sla_info and not sla_info.get('error'):
                                # 使用真实的JIRA SLA API数据
                                time_to_remind = sla_info.get('time_to_remind')
                                time_to_resolution = sla_info.get('time_to_resolution')
                                
                                if time_to_remind:
                                    remind_text = format_sla_info(time_to_remind)
                                    message += f"• {remind_text}\n"
                                
                                if time_to_resolution:
                                    resolution_text = format_sla_info(time_to_resolution)
                                    message += f"• {resolution_text}\n"
                                
                                if not time_to_remind and not time_to_resolution:
                                    message += f"• SLA: 此Issue未配置SLA\n"
                            elif sla_info and sla_info.get('error'):
                                # SLA API调用失败
                                error_msg = sla_info.get('error')
                                if '未配置SLA' in error_msg:
                                    message += f"• SLA: 未配置SLA\n"
                                else:
                                    ic(f"SLA API调用失败: {error_msg}")
                                    message += f"• SLA: 获取失败\n"
                            else:
                                # 回退到计算方式
                                try:
                                    created_time_str = change_info.get('created_time')
                                    if created_time_str:
                                        created_time = datetime.strptime(created_time_str, '%Y-%m-%dT%H:%M:%S.%f%z')
                                        sla_deadline = calculate_sla_deadline(created_time, change_info['issue_priority'])
                                        current_time = datetime.now(timezone.utc)
                                        time_remaining = sla_deadline - current_time
                                        
                                        if time_remaining.total_seconds() > 0:
                                            days_remaining = time_remaining.days
                                            hours_remaining = int(time_remaining.seconds / 3600)
                                            message += f"• SLA截止: {sla_deadline.strftime('%Y-%m-%d %H:%M:%S')} (剩余 {days_remaining}天{hours_remaining}小时)\n"
                                        else:
                                            overdue_time = current_time - sla_deadline
                                            days_overdue = overdue_time.days
                                            hours_overdue = int(overdue_time.seconds / 3600)
                                            message += f"• SLA截止: {sla_deadline.strftime('%Y-%m-%d %H:%M:%S')} (已超期 {days_overdue}天{hours_overdue}小时) 🚨\n"
                                    else:
                                        message += f"• SLA: 无法获取创建时间\n"
                                except Exception as sla_e:
                                    ic(f"计算SLA信息出错: {str(sla_e)}")
                                    message += f"• SLA: 计算失败\n"
                        # 发送私信
                        send_result = send_message_to_user(employee_code, message, format=1)
                        
                        # 记录处理结果
                        process_result = {
                            "issue_key": issue_key,
                            "summary": change_info["issue_summary"],
                            "assignee": change_info["current_assignee"].displayName,
                            "email": change_info["current_assignee_email"],
                            "success": send_result.get("code") == 0,
                            "message": send_result.get("message", "Unknown error")
                        }
                        process_results.append(process_result)
                        
                        # 如果发送成功，记录已处理的变更
                        if send_result.get("code") == 0:
                            PROCESSED_CHANGES[change_info["change_id"]] = change_info["change_time"]
                            ic(f"{issue_key} - 已发送Assignee变更通知给 {change_info['current_assignee_email']}")
                        else:
                            ic(f"{issue_key} - 发送通知失败: {send_result.get('message')}")
                    else:
                        ic(f"{issue_key} - 无法获取 {change_info['current_assignee_email']} 的employee code")
                        process_results.append({
                            "issue_key": issue_key,
                            "summary": change_info["issue_summary"],
                            "assignee": change_info["current_assignee"].displayName,
                            "email": change_info["current_assignee_email"],
                            "success": False,
                            "message": f"无法获取 {change_info['current_assignee_email']} 的employee code"
                        })
            except Exception as e:
                ic(f"{issue_key} - 处理出错: {str(e)}")
                process_results.append({
                    "issue_key": issue_key,
                    "summary": change_info.get("issue_summary", "Unknown"),
                    "assignee": change_info.get("current_assignee", {}).displayName if change_info.get("current_assignee") else "Unknown",
                    "email": change_info.get("current_assignee_email", "Unknown"),
                    "success": False,
                    "message": f"处理异常: {str(e)}"
                })
        
        # 只有在发生错误时才发送到调试群
        if process_results:
            # 检查是否有失败的处理结果
            failed_results = [result for result in process_results if not result["success"]]
            
            if failed_results:
                # 构建错误消息
                summary_message = f"❌ **Bug Assignee变更通知失败报告** ❌\n\n"
                summary_message += f"📊 **处理失败**: {len(failed_results)}个Bug\n"
                summary_message += f"⏰ **时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                summary_message += "❌ **失败详情**:\n"
                
                # 只添加失败的处理结果，并简化错误信息
                for result in failed_results:
                    simplified_error = simplify_jira_error(result['message'])
                    summary_message += f"• [{result['issue_key']}] {result['assignee']} ({result['email']}) - {simplified_error}\n"
                
                # 发送消息到Workee PJ日志群
                send_message_to_group(WORKEE_PJ_LOG_GROUP_ID, summary_message)
                ic(f"已发送Bug Assignee变更失败通知到Workee PJ日志群")
            else:
                ic(f"所有{len(process_results)}个Bug通知都发送成功，无需发送到日志群")
        
    except Exception as e:
        ic(f"Assignee变更检查出错: {str(e)}")
        # 使用简化的错误信息，避免冗长的HTML内容
        simplified_error = simplify_jira_error(str(e))
        
        # 尝试将错误信息发送到Workee PJ日志群
        try:
            error_message = f"❌ **Bug Assignee变更检查出错** ❌\n\n"
            error_message += f"❌ **错误信息**: {simplified_error}\n"
            error_message += f"⏰ **时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            send_message_to_group(WORKEE_PJ_LOG_GROUP_ID, error_message)
            ic("已发送Bug Assignee检查错误通知到Workee PJ日志群")
        except Exception as inner_e:
            ic(f"发送Bug Assignee错误通知失败: {str(inner_e)}")

# ========== SPUAT监控使用说明 ==========
# 
# 1. 计划任务配置（推荐每5分钟运行一次）：
#    在 djangoProject/settings.py 的 CRONJOBS 列表中添加：
#    ('*/5 * * * *', 'app01.seatalk_group_manager.check_spuat_chat_tickets', f'>> {os.path.join(LOGS_DIR, "check_spuat_chat_tickets.log")} 2>&1'),
# 
#    然后执行以下命令更新计划任务：
#    python manage.py crontab add
#    python manage.py crontab show  # 查看已添加的计划任务
# 
# 2. 通知规则：
#    - 所有问题：创建后立即开始发送通知（计划任务5分钟内必定发现新问题）
#    - High级别：每60分钟发送一次（7x24小时不停）
#    - Medium级别：每120分钟发送一次（仅工作时间 周一-周五 9:30-19:00）
#    - Low级别：每天上午10点发送一次
# 
# 3. 重启安全：
#    - 不使用缓存机制，重启Django项目不会影响通知逻辑
#    - 纯基于时间计算，可靠性高
# 
# 4. 测试方法：
#    - 取消注释下面的check_spuat_chat_tickets()来测试
#    - 或者在Django shell中运行：
#      python manage.py shell
#      from app01.seatalk_group_manager import check_spuat_chat_tickets
#      check_spuat_chat_tickets()

# 可以通过在命令行运行此模块来测试SPUAT监控功能
if __name__ == "__main__":
    # 取消注释下面的行来测试不同功能
    # test_team_leaders_feature()
    # check_spuat_chat_tickets()
    pass

# SPUAT Chat UAT问题监控配置
# 计划任务建议：每5分钟运行一次 (*/5 * * * *)

def is_working_hours() -> bool:
    """
    判断当前是否为工作时间（新加坡时间）
    工作时间定义：周一到周五，上午9点到下午6点
    
    Returns:
        bool: True表示工作时间，False表示非工作时间
    """
    try:
        # 获取新加坡当前时间 
        now_utc = datetime.now(timezone.utc)
        singapore_time = now_utc + timedelta(hours=8)
        
        # 检查是否为工作日（周一到周五）
        weekday = singapore_time.weekday()  # 0=Monday, 6=Sunday
        if weekday > 4:  # 周六、周日
            return False
        
        # 检查是否为工作时间（9:30-19   :00)
        current_hour = singapore_time.hour
        if current_hour < 9 or current_hour >= 19:
            return False
        
        return True
    except Exception as e:
        ic(f"判断工作时间出错: {str(e)}")
        return True  # 出错时默认为工作时间，避免漏发通知

def get_real_sla_info(issue) -> Dict:
    """
    获取JIRA真实的SLA信息 - 通过Service Desk API
    
    Args:
        issue: JIRA issue对象
    
    Returns:
        Dict: SLA信息字典，包含提醒时间和解决时间
    """
    sla_info = {
        'time_to_remind': None,
        'time_to_resolution': None,
        'sla_data': None,
        'error': None
    }
    
    try:
        import requests
        
        issue_key = issue.key
        # 构建Service Desk SLA API URL
        sla_url = f"https://jira.shopee.io/rest/servicedesk/1/servicedesk/sla/issue/{issue_key}"
        
        # 使用JIRA token进行认证
        headers = {
            'Authorization': f'Bearer {jira_token}',
            'Accept': 'application/json',
            'User-Agent': 'ChatbotAR-SLA-Client/1.0'
        }
        
        # 发送请求
        response = requests.get(sla_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            sla_data = response.json()
            sla_info['sla_data'] = sla_data
            
            # 解析goalViews中的SLA信息
            goal_views = sla_data.get('goalViews', [])
            
            for goal in goal_views:
                metric_name = goal.get('metricName', '')
                
                if 'Time to remind' in metric_name:
                    sla_info['time_to_remind'] = {
                        'metric_name': metric_name,
                        'remaining_time': goal.get('remainingTime'),
                        'remaining_time_readable': goal.get('remainingTimeHumanReadable'),
                        'goal_time': goal.get('goalTime'),
                        'goal_time_readable': goal.get('goalTimeHumanReadable'),
                        'emergency_level': goal.get('emergencyLevel'),
                        'failed': goal.get('failed', False),
                        'active': goal.get('active', False),
                        'breach_time': goal.get('breachTime')
                    }
                
                elif 'Time to resolution' in metric_name and 'L3' in metric_name:
                    sla_info['time_to_resolution'] = {
                        'metric_name': metric_name,
                        'remaining_time': goal.get('remainingTime'),
                        'remaining_time_readable': goal.get('remainingTimeHumanReadable'),
                        'goal_time': goal.get('goalTime'),
                        'goal_time_readable': goal.get('goalTimeHumanReadable'),
                        'emergency_level': goal.get('emergencyLevel'),
                        'failed': goal.get('failed', False),
                        'active': goal.get('active', False),
                        'breach_time': goal.get('breachTime')
                    }
        
        elif response.status_code == 404:
            # 该Issue可能没有SLA配置
            sla_info['error'] = f"Issue {issue_key} 未配置SLA"
        else:
            sla_info['error'] = f"获取SLA失败: HTTP {response.status_code}"
            
    except requests.exceptions.Timeout:
        sla_info['error'] = "SLA API请求超时"
    except requests.exceptions.RequestException as e:
        sla_info['error'] = f"SLA API请求异常: {str(e)}"
    except Exception as e:
        ic(f"获取SLA信息出错: {str(e)}")
        sla_info['error'] = f"获取SLA信息异常: {str(e)}"
    
    return sla_info

def format_sla_info(sla_data: Dict) -> str:
    """
    格式化SLA信息为用户友好的显示
    
    Args:
        sla_data: SLA数据字典，包含metric_name, remaining_time_readable等
    
    Returns:
        str: 格式化的SLA信息字符串
    """
    if not sla_data:
        return "未设置"
    
    try:
        metric_name = sla_data.get('metric_name', '未知SLA')
        remaining_readable = sla_data.get('remaining_time_readable', '')
        goal_time_readable = sla_data.get('goal_time_readable', '')
        emergency_level = sla_data.get('emergency_level', 'normal')
        failed = sla_data.get('failed', False)
        
        # 根据紧急程度选择图标
        if emergency_level == 'breached' or failed:
            icon = "🚨"
            status = "已超期"
        elif emergency_level == 'warning':
            icon = "⚠️"
            status = "即将超期"
        else:
            icon = "✅"
            status = "正常"
        
        # 简化SLA名称显示
        if "Time to remind" in metric_name:
            sla_name = "提醒SLA"
        elif "Time to resolution" in metric_name:
            sla_name = "解决SLA"
        else:
            sla_name = metric_name
        
        # 构建消息
        result = f"{icon} {sla_name}: "
        
        if goal_time_readable:
            result += f"目标{goal_time_readable}"
        
        if remaining_readable:
            # 转换时间表述，使其更清晰
            readable_time = convert_time_readable(remaining_readable)
            
            if remaining_readable.startswith('-'):
                # 超期情况
                overdue_time = readable_time[1:] if readable_time.startswith('-') else readable_time  # 去掉负号
                result += f", 已超期{overdue_time}"
            else:
                # 正常情况
                result += f", 剩余{readable_time}"
        
        return result
        
    except Exception as e:
        ic(f"格式化SLA信息出错: {str(e)}")
        return "SLA信息解析失败"


def convert_time_readable(time_str: str) -> str:
    """
    转换时间字符串为更清晰的中文表述
    
    Args:
        time_str: 原始时间字符串，如 "2mo", "-1537:26", "24h"
    
    Returns:
        str: 转换后的时间字符串
    """
    if not time_str:
        return time_str
    
    try:
        # 处理负号
        negative = time_str.startswith('-')
        work_str = time_str[1:] if negative else time_str
        
        # 转换 mo -> 月
        if 'mo' in work_str:
            work_str = work_str.replace('mo', '月')
        
        # 转换 d -> 天
        if work_str.endswith('d'):
            work_str = work_str.replace('d', '天')
        
        # 转换 h -> 小时
        if work_str.endswith('h'):
            work_str = work_str.replace('h', '小时')
        
        # 转换 m -> 分钟 (但要避免mo的情况)
        if work_str.endswith('m') and not work_str.endswith('月'):
            work_str = work_str.replace('m', '分钟')
        
        # 处理带有冒号的时间格式 (如 "1537:26" -> "1537小时26分钟")
        if ':' in work_str and not '月' in work_str and not '天' in work_str:
            parts = work_str.split(':')
            if len(parts) == 2:
                hours = parts[0]
                minutes = parts[1]
                work_str = f"{hours}小时{minutes}分钟"
        
        return f"-{work_str}" if negative else work_str
        
    except Exception as e:
        ic(f"转换时间表述出错: {str(e)}")
        return time_str

def format_sla_time(remaining_time_ms) -> str:
    """
    格式化SLA剩余时间（从毫秒转换为可读格式） - 保留向后兼容
    
    Args:
        remaining_time_ms: 剩余时间（毫秒）
    
    Returns:
        str: 格式化的时间字符串
    """
    if not remaining_time_ms:
        return "未设置"
    
    try:
        # 转换毫秒为秒
        total_seconds = remaining_time_ms / 1000
        
        if total_seconds < 0:
            # 已超期
            total_seconds = abs(total_seconds)
            days = int(total_seconds // 86400)
            hours = int((total_seconds % 86400) // 3600)
            minutes = int((total_seconds % 3600) // 60)
            
            if days > 0:
                return f"已超期 {days}天{hours}小时 🚨"
            elif hours > 0:
                return f"已超期 {hours}小时{minutes}分钟 🚨"
            else:
                return f"已超期 {minutes}分钟 🚨"
        else:
            # 剩余时间
            days = int(total_seconds // 86400)
            hours = int((total_seconds % 86400) // 3600)
            minutes = int((total_seconds % 3600) // 60)
            
            if days > 0:
                return f"剩余 {days}天{hours}小时"
            elif hours > 0:
                return f"剩余 {hours}小时{minutes}分钟"
            else:
                return f"剩余 {minutes}分钟"
    except Exception as e:
        ic(f"格式化SLA时间出错: {str(e)}")
        return "格式化失败"

def calculate_sla_deadline(created_time: datetime, priority: str) -> datetime:
    """
    根据优先级计算SLA截止时间（备用方法，当无法获取真实SLA时使用）
    
    Args:
        created_time: JIRA创建时间
        priority: 优先级（High/Medium/Low）
    
    Returns:
        datetime: SLA截止时间
    """
    try:
        if priority.lower() == 'high':
            # High级别：24小时内解决
            return created_time + timedelta(hours=24)
        elif priority.lower() == 'medium':
            # Medium级别：3天内解决
            return created_time + timedelta(days=3)
        elif priority.lower() == 'low':
            # Low级别：5天内解决
            return created_time + timedelta(days=5)
        else:
            # 默认按Medium处理
            return created_time + timedelta(days=3)
    except Exception as e:
        ic(f"计算SLA截止时间出错: {str(e)}")
        # 出错时返回3天后
        return created_time + timedelta(days=3)

def should_send_spuat_notification(issue_key: str, created_time: datetime, priority: str, current_time: datetime) -> bool:
    """
    判断是否应该发送SPUAT通知（基于纯时间计算，不使用缓存）
    
    Args:
        issue_key: JIRA单号
        created_time: 创建时间
        priority: 优先级
        current_time: 当前时间
    
    Returns:
        bool: 是否应该发送通知
    """
    try:
        # 计算从创建到现在的时间差（分钟）
        minutes_since_created = (current_time - created_time).total_seconds() / 60
        
        # 所有优先级的问题都在创建5分钟内立即发送通知（不考虑工作日）
        if minutes_since_created < 5:
            return True
        
        # 根据优先级决定后续通知频率
        if priority.lower() == 'high':
            # High级别：每60分钟发送一次（7x24小时不停）
            # 发送时间点：立即、60分钟后、120分钟后、180分钟后...
            # 使用5分钟窗口容错（计划任务每5分钟运行一次）
            return minutes_since_created % 60 < 5
            
        elif priority.lower() == 'medium':
            # Medium级别：每120分钟发送一次（仅工作时间）
            # 发送时间点：立即、120分钟后、240分钟后...
            if not is_working_hours():
                return False
            return minutes_since_created % 120 < 5
            
        elif priority.lower() == 'low':
            # Low级别：每个工作日上午10点发送一次
            singapore_time = current_time + timedelta(hours=8)  # 转换为新加坡时间
            current_hour = singapore_time.hour  
            current_minute = singapore_time.minute
            weekday = singapore_time.weekday()  # 0=Monday, 6=Sunday
            
            # 检查是否为工作日（周一到周五）
            if weekday > 4:  # 周六、周日
                return False
            
            # 在工作日的上午10点左右（9:57-10:03）发送
            if current_hour == 9 and current_minute >= 57:
                return True
            elif current_hour == 10 and current_minute <= 3:
                return True
            else:
                return False
        
        return False
    except Exception as e:
        ic(f"判断是否发送通知出错: {str(e)}")
        return False
@track_cronjob_execution('check_spuat_chat_tickets')
def check_spuat_chat_tickets() -> None:
    """
    检查SPUAT项目Chat产品线的UAT问题，根据SLA发送通知
    监控范围：project = "Shopee Product UAT Board" AND "Product Line (UAT)" = Chat AND status not in (closed,done,deployed)
    
    通知规则（基于纯时间计算，不使用缓存）：
    - 所有级别：创建后5分钟内立即发送通知（不考虑工作日）
    - High级别：SLA 24小时，后续每1小时提醒一次（7x24小时）
    - Medium级别：SLA 3天，后续每2小时提醒一次（仅工作时间）
    - Low级别：SLA 5天，后续每个工作日上午10点提醒一次
    
    计划任务建议：每5分钟运行一次 (*/5 * * * *)
    """
    try:
        ic("开始检查SPUAT Chat产品线UAT问题")
        
        # 创建JIRA连接
        jira_url = "https://jira.shopee.io"
        jira = JIRA(jira_url, token_auth=jira_token)
        
        # 查询JQL - SPUAT项目Chat产品线的非完成状态问题
        jql = 'project in ("SPUAT") AND "Product Line (UAT)" in("Chat","Chat - ChatSS","Chat - WebChat","Chatboost","Chatbot Data","Chatbot Foundation","Chatbot Intention","Chatbot Knowledge","Chatbot Skill","shop chatbot") AND status not in (closed,done,deployed,reviewing)'
        try:
            issues = jira.search_issues(jql, maxResults=200)  # 限制最多200个结果
            ic(f"找到 {len(issues)} 个SPUAT Chat UAT问题")
        except Exception as jql_error:
            # 使用simplify_jira_error函数简化JQL查询错误
            error_msg = simplify_jira_error(str(jql_error))
            ic(f"SPUAT JQL查询失败: {error_msg}")
            # 发送错误通知到调试群
            debug_group_id = "NzQzMzAxODcyMjAy"
            error_message = f"❌ **SPUAT Chat UAT问题JQL查询失败**\n\n"
            error_message += f"• **错误信息**: {error_msg}\n"
            error_message += f"• **时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            error_message += f"• **JQL**: {jql}\n"
            send_message_to_group(debug_group_id, error_message)
            return  # 如果JQL查询失败，直接返回
        
        current_time = datetime.now(timezone.utc)
        debug_group_id = "NzQzMzAxODcyMjAy"  # 调试群ID
        
        # 统计变量
        total_processed = 0
        notifications_sent = 0
        failed_notifications = []
        skipped_notifications = []  # 记录跳过通知的问题
        
        for issue in issues:
            try:
                issue_key = issue.key
                issue_summary = issue.fields.summary
                issue_status = issue.fields.status.name
                issue_priority = issue.fields.priority.name if issue.fields.priority else "Medium"
                created_time = datetime.strptime(issue.fields.created, '%Y-%m-%dT%H:%M:%S.%f%z')
                
                # 获取assignee信息
                current_assignee = issue.fields.assignee
                assignee_email = current_assignee.emailAddress if current_assignee else None
                assignee_name = current_assignee.displayName if current_assignee else "未分配"
                
                total_processed += 1
                
                # 判断是否需要发送通知
                should_send = should_send_spuat_notification(issue_key, created_time, issue_priority, current_time)
                if should_send:
                    # 计算SLA截止时间
                    sla_deadline = calculate_sla_deadline(created_time, issue_priority)
                    
                    # 计算剩余时间
                    time_remaining = sla_deadline - current_time
                    if time_remaining.total_seconds() > 0:
                        days_remaining = time_remaining.days
                        hours_remaining = int(time_remaining.seconds / 3600)
                        status_text = f"剩余 {days_remaining}天{hours_remaining}小时"
                        urgency_icon = "⏰"
                    else:
                        # 已经超期
                        overdue_time = current_time - sla_deadline
                        days_overdue = overdue_time.days
                        hours_overdue = int(overdue_time.seconds / 3600)
                        status_text = f"已超期 {days_overdue}天{hours_overdue}小时"
                        urgency_icon = "🚨"
                    
                    # 构建通知消息
                    issue_url = f"{jira_url}/browse/{issue_key}"
                    message = f"{urgency_icon} **SPUAT Chat UAT问题提醒**\n\n"
                    message += f"• **JIRA**: [{issue_key}]({issue_url})\n"
                    message += f"• **标题**: {issue_summary}\n"
                    message += f"• **优先级**: {issue_priority}\n"
                    message += f"• **状态**: {issue_status}\n"
                    message += f"• **负责人**: {assignee_name}\n"
                    message += f"• **创建时间**: {created_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                    message += f"• **SLA截止时间**: {sla_deadline.strftime('%Y-%m-%d %H:%M:%S')}\n"
                    message += f"• **完成状态**: {status_text}\n\n"
                    
                    # 根据优先级添加不同的提醒文案
                    if issue_priority.lower() == 'high':
                        message += "⚠️ **HIGH级别问题需要在24小时内解决，请尽快处理！**"
                    elif issue_priority.lower() == 'medium':
                        message += "📋 **MEDIUM级别问题需要在3天内解决，请及时跟进。**"
                    elif issue_priority.lower() == 'low':
                        message += "📝 **LOW级别问题需要在5天内解决，请安排时间处理。**"
                    
                    # 发送私信给assignee（如果有）
                    assignee_notification_success = True
                    if assignee_email:
                        try:
                            employee_codes = get_employee_codes([assignee_email])
                            if assignee_email in employee_codes:
                                employee_code = employee_codes[assignee_email]
                                send_result = send_message_to_user(employee_code, message, format=1)
                                if send_result.get("code") != 0:
                                    assignee_notification_success = False
                                    ic(f"{issue_key} - 发送私信给{assignee_email}失败: {send_result.get('message')}")
                                else:
                                    ic(f"{issue_key} - 已发送私信通知给 {assignee_email}")
                            else:
                                assignee_notification_success = False
                                ic(f"{issue_key} - 无法获取 {assignee_email} 的employee code")
                        except Exception as e:
                            assignee_notification_success = False
                            ic(f"{issue_key} - 发送私信异常: {str(e)}")
                    else:
                        ic(f"{issue_key} - 无assignee，跳过私信通知")
                    
                    # 发送消息到调试群
                    group_notification_success = True
                    try:
                        # 直接发送消息，不重复添加标题
                        send_result = send_message_to_group(debug_group_id, message)
                        if not send_result:
                            group_notification_success = False
                            ic(f"{issue_key} - 发送群消息失败")
                        else:
                            ic(f"{issue_key} - 已发送群通知")
                    except Exception as e:
                        group_notification_success = False
                        ic(f"{issue_key} - 发送群消息异常: {str(e)}")
                    
                    # 记录通知结果
                    if assignee_notification_success or group_notification_success:
                        notifications_sent += 1
                        ic(f"{issue_key} - 通知发送成功")
                    else:
                        failed_notifications.append({
                            "issue_key": issue_key,
                            "summary": issue_summary,
                            "priority": issue_priority,
                            "assignee": assignee_name,
                            "error": "私信和群消息都发送失败"
                        })
                else:
                    # 记录跳过通知的问题及原因
                    minutes_since_created = (current_time - created_time).total_seconds() / 60
                    skip_reason = f"优先级:{issue_priority}, 创建时间:{created_time.strftime('%Y-%m-%d %H:%M:%S')}, 距今:{int(minutes_since_created)}分钟"
                    
                    if issue_priority.lower() == 'medium' and not is_working_hours():
                        skip_reason += ", 原因:Medium级别且非工作时间"
                    elif issue_priority.lower() == 'high' and minutes_since_created % 60 >= 5:
                        skip_reason += f", 原因:High级别但不在60分钟通知窗口内(余数:{int(minutes_since_created % 60)})"
                    elif issue_priority.lower() == 'medium' and minutes_since_created % 120 >= 5:
                        skip_reason += f", 原因:Medium级别但不在120分钟通知窗口内(余数:{int(minutes_since_created % 120)})"
                    elif issue_priority.lower() == 'low':
                        singapore_time = current_time + timedelta(hours=8)
                        current_hour = singapore_time.hour
                        current_minute = singapore_time.minute
                        weekday = singapore_time.weekday()  # 0=Monday, 6=Sunday
                        weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                        
                        if minutes_since_created < 5:
                            skip_reason += f", 原因:应该发送新建通知但被其他条件跳过"
                        elif weekday > 4:  # 周六、周日
                            skip_reason += f", 原因:Low级别但今天是{weekday_names[weekday]}(非工作日)"
                        else:
                            skip_reason += f", 原因:Low级别但不在工作日10点通知时间(当前:{current_hour:02d}:{current_minute:02d})"
                    else:
                        skip_reason += ", 原因:其他条件不满足"
                    
                    skipped_notifications.append({
                        "issue_key": issue_key,
                        "summary": issue_summary,
                        "priority": issue_priority,
                        "assignee": assignee_name,
                        "reason": skip_reason
                    })
                
            except Exception as e:
                ic(f"{issue.key} - 处理SPUAT问题出错: {str(e)}")
                # 使用simplify_jira_error函数简化错误信息
                error_msg = simplify_jira_error(str(e))
                failed_notifications.append({
                    "issue_key": issue.key,
                    "summary": getattr(issue.fields, 'summary', 'Unknown'),
                    "priority": getattr(issue.fields.priority, 'name', 'Unknown') if issue.fields.priority else 'Unknown',
                    "assignee": "Unknown",
                    "error": f"处理异常: {error_msg}"
                })
        
        # 只有在发生错误时才发送汇总到调试群
        if failed_notifications:
            summary_message = f"❌ **SPUAT Chat UAT问题监控错误报告**\n\n"
            summary_message += f"• **执行时间**: {current_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            summary_message += f"• **处理总数**: {total_processed}个问题\n" 
            summary_message += f"• **发送通知**: {notifications_sent}个\n"
            summary_message += f"• **跳过通知**: {len(skipped_notifications)}个\n"
            summary_message += f"• **失败数量**: {len(failed_notifications)}个\n"
            
            summary_message += f"\n❌ **失败详情**:\n"
            for failed in failed_notifications[:10]:  # 最多显示10个失败记录
                summary_message += f"• [{failed['issue_key']}] {failed['priority']} - {failed['assignee']} - {failed['error']}\n"
            
            if len(failed_notifications) > 10:
                summary_message += f"• ... 还有{len(failed_notifications)-10}个失败记录\n"
            
            # 发送错误报告到调试群
            try:
                send_message_to_group(debug_group_id, summary_message)
                ic("已发送SPUAT监控错误报告到调试群")
            except Exception as e:
                ic(f"发送SPUAT监控错误报告失败: {str(e)}")
        else:
            ic("SPUAT监控执行正常，无错误发生，跳过发送汇总消息")
        
        ic(f"SPUAT Chat UAT问题检查完成 - 处理{total_processed}个，通知{notifications_sent}个，跳过{len(skipped_notifications)}个，失败{len(failed_notifications)}个")
        
    except Exception as e:
        ic(f"SPUAT Chat UAT问题检查出错: {str(e)}")
        # 使用simplify_jira_error函数简化错误信息
        error_msg = simplify_jira_error(str(e))
        # 发送错误通知到调试群
        try:
            debug_group_id = "NzQzMzAxODcyMjAy"
            error_message = f"❌ **SPUAT Chat UAT问题检查出错**\n\n"
            error_message += f"• **错误信息**: {error_msg}\n"
            error_message += f"• **时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            send_message_to_group(debug_group_id, error_message)
            ic("已发送SPUAT检查错误通知到调试群")
        except Exception as inner_e:
            ic(f"发送SPUAT错误通知失败: {str(inner_e)}")

# 可以通过在命令行运行此模块来测试不同功能
if __name__ == "__main__":
    # 取消注释下面的行来测试不同功能
    # test_team_leaders_feature()
    # check_spuat_chat_tickets()
    # 测试线程回复功能，需要提供群组ID和线程ID
    # test_thread_reply_feature("YOUR_GROUP_ID", "YOUR_THREAD_ID", "这是一条线程回复测试消息")
    # 测试普通消息发送功能，只需提供群组ID
    # test_thread_reply_feature("YOUR_GROUP_ID", message="这是一条普通消息测试")
    pass


def get_thread_by_id(group_id: str, thread_id: str) -> Dict:
    """
    通过线程ID获取线程信息
    
    Args:
        group_id: 群组ID
        thread_id: 线程ID
    
    Returns:
        Dict: 包含线程信息的字典
    """
    url = "https://openapi.seatalk.io/messaging/v2/group_chat/thread"
    access_token = app_access_token()
    headers = {
        'content-type': "application/json",
        'Authorization': f"Bearer {access_token}"
    }
    
    param = {
        "group_id": group_id,
        "thread_id": thread_id
    }
    
    try:
        response = requests.get(url=url, params=param, headers=headers)
        result = response.json()
        ic(f"Get thread response: {result}")
        
        if result["code"] == 0:
            return {
                "success": True,
                "thread": result.get("thread", {}),
                "replies": result.get("replies", [])
            }
        else:
            return {
                "success": False,
                "message": f"Failed to get thread: {result}"
            }
    except Exception as e:
        ic(f"Exception in get_thread_by_id: {str(e)}")
        return {
            "success": False,
            "message": f"Exception in get_thread_by_id: {str(e)}"
        }


def reply_to_thread(group_id: str, thread_id: str, message: str) -> bool:
    """
    回复线程的便捷函数
    
    Args:
        group_id: 群组ID
        thread_id: 线程ID
        message: 回复消息内容
    
    Returns:
        bool: 回复成功返回True，否则返回False
    """
    return send_message_to_group(group_id, message, thread_id)

def test_thread_reply_feature(group_id: str, thread_id: str = None, message: str = None, quoted_message_id: str = None) -> Dict:
    """
    测试线程回复功能
    
    Args:
        group_id: 群组ID
        thread_id: 线程ID，如果为None则发送普通消息
        message: 消息内容，如果为None则使用默认测试消息
        quoted_message_id: 引用消息ID，如果提供则作为引用回复
    
    Returns:
        Dict: 包含测试结果的字典
    """
    if not message:
        message = "这是一条测试消息 - " + datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    try:
        # 导入test_for_seatalk_bot函数
        from app01.views import test_for_seatalk_bot
        
        if thread_id and quoted_message_id:
            # 发送线程回复并引用消息
            ic(f"发送线程回复并引用消息，群组={group_id}, 线程ID={thread_id}, 引用消息ID={quoted_message_id}")
            result = test_for_seatalk_bot(message, [], group_id, thread_id, quoted_message_id)
            if result:
                return {
                    "success": True,
                    "message": f"成功发送线程回复并引用消息，线程ID: {thread_id}, 引用消息ID: {quoted_message_id}",
                    "thread_id": thread_id,
                    "quoted_message_id": quoted_message_id,
                    "content": message
                }
            else:
                return {
                    "success": False,
                    "message": "发送线程回复并引用消息失败"
                }
        elif quoted_message_id:
            # 发送引用回复
            ic(f"发送引用回复消息，群组={group_id}, 引用消息ID={quoted_message_id}")
            result = test_for_seatalk_bot(message, [], group_id, None, quoted_message_id)
            if result:
                return {
                    "success": True,
                    "message": f"成功发送引用回复到消息 {quoted_message_id}",
                    "quoted_message_id": quoted_message_id,
                    "content": message
                }
            else:
                return {
                    "success": False,
                    "message": "发送引用回复失败"
                }
        elif thread_id:
            # 发送线程回复
            ic(f"发送线程回复消息，群组={group_id}, 线程ID={thread_id}")
            result = test_for_seatalk_bot(message, [], group_id, thread_id)
            if result:
                return {
                    "success": True,
                    "message": f"成功发送线程回复到线程 {thread_id}",
                    "thread_id": thread_id,
                    "content": message
                }
            else:
                return {
                    "success": False,
                    "message": "发送线程回复失败"
                }
        else:
            # 发送普通消息
            ic(f"正在发送普通消息到群组 {group_id}")
            result = test_for_seatalk_bot(message, [], group_id)
            if result:
                return {
                    "success": True,
                    "message": "成功发送普通消息",
                    "content": message
                }
            else:
                return {
                    "success": False,
                    "message": "发送普通消息失败"
                }
    except Exception as e:
        ic(f"测试线程回复功能出错: {str(e)}")
        return {
            "success": False,
            "message": f"测试异常: {str(e)}"
        }
def set_spct_bug_reminder_debug_mode(debug: bool) -> None:
    """
    设置SPCT Bug提醒调试模式，并持久化到配置文件
    
    Args:
        debug: True=调试模式（发送到调试群），False=正式模式（发送到对应群组）
    """
    global SPCT_BUG_REMINDER_DEBUG
    SPCT_BUG_REMINDER_DEBUG = debug
    
    # 持久化到配置文件
    try:
        import json
        from pathlib import Path
        
        config_file = Path(__file__).parent / 'spct_bug_debug_config.json'
        config = {
            'debug_mode': debug,
            'last_updated': str(datetime.now())
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
            
        ic(f"SPCT Bug提醒调试模式已设置为: {debug}，已保存到配置文件")
    except Exception as e:
        ic(f"保存调试模式配置失败: {e}")
    
    ic(f"SPCT Bug提醒调试模式已设置为: {debug}")


def get_spct_bug_reminder_debug_status() -> Dict[str, any]:
    """
    获取SPCT Bug提醒调试模式状态，实时从配置文件读取
    
    Returns:
        Dict: 包含调试模式状态的字典
    """
    # 实时从配置文件读取最新状态
    current_debug_mode = load_spct_bug_debug_config()
    
    return {
        'debug_mode': current_debug_mode,
        'debug_group_id': SPCT_BUG_REMINDER_DEBUG_GROUP,
        'status': '调试模式（发送到调试群）' if current_debug_mode else '正式模式（发送到对应群组）'
    }


def get_assignee_and_tl(assignee_email: str) -> Dict[str, str]:
    """
    根据assignee邮箱获取对应的TL信息
    
    Args:
        assignee_email: assignee的邮箱
        
    Returns:
        Dict: 包含assignee和tl信息的字典
    """
    result = {
        'assignee': assignee_email,
        'tl': '<EMAIL>'  # 默认TL
    }
    
    # 在TEAM_DATA中查找assignee对应的TL
    for team_name, team_info in TEAM_DATA.items():
        if assignee_email in team_info.get('members', []):
            leaders = team_info.get('leaders', [])
            if leaders:
                result['tl'] = leaders[0]  # 取第一个leader
            break
    
    return result


def get_epic_groups_from_db() -> Dict[str, str]:
    """
    从数据库获取SPCB/SPCT项目的群组信息
    
    Returns:
        Dict: Epic key到群组ID的映射
    """
    from app01.models import SeatalkGroup
    
    epic_groups = {}
    
    try:
        # 获取所有群组
        groups = SeatalkGroup.objects.all()
        
        for group in groups:
            group_name = group.group_name or ""
            # 提取群名中的Epic key (格式: [EPIC_KEY][状态]标题)，包含EPIC_KEY即可，不要求是开头

            if '[SPCB-' in group_name or '[SPCT-' in group_name:
                # 提取Epic key
                end_bracket = group_name.find(']', 1)
                if end_bracket > 0:
                    epic_key = group_name[1:end_bracket]
                    epic_groups[epic_key] = group.group_id
                    
        ic(f"从数据库获取到 {len(epic_groups)} 个SPCB/SPCT群组")
        return epic_groups
        
    except Exception as e:
        ic(f"获取群组信息失败: {e}")
        return {}


def filter_bugs_by_priority_and_time(bugs: List[Dict], current_time: datetime) -> Dict[str, List[Dict]]:
    """
    根据优先级和时间过滤需要提醒的bug
    
    Args:
        bugs: bug列表
        current_time: 当前时间
        
    Returns:
        Dict: 按提醒类型分组的bug列表
    """
    reminder_bugs = {
        'p0_p1_bugs': [],  # P0/high优先级1天未解决的bug
        'p1_medium_bugs': [],  # P1/medium优先级3天未解决的bug
        'overdue_bugs': []  # 超过测试完成时间的bug
    }
    
    for bug in bugs:
        try:
            created_str = bug.get('created', '')
            if not created_str:
                continue
                
            # 解析创建时间，期望格式为“2025-01-15 10:30:45”
            # 先尝试用新格式解析，否则回退到原有格式
            try:
                created_time = datetime.strptime(created_str, '%Y-%m-%d %H:%M:%S')
                created_time = timezone.make_aware(created_time, timezone.get_current_timezone())
            except Exception:
                # 兼容旧格式（如2025-01-15T10:30:45.000+0800等）
                if '+' in created_str or 'Z' in created_str:
                    from dateutil import parser
                    created_time = parser.parse(created_str)
                else:
                    created_time = datetime.strptime(created_str.split('.')[0], '%Y-%m-%dT%H:%M:%S')
                    created_time = timezone.make_aware(created_time, timezone.get_current_timezone())
            
            # 计算未解决天数
            days_unresolved = (current_time - created_time).days
            
            priority = bug.get('priority', '').lower()
            
            ic(f"Bug {bug.get('key', 'unknown')}: priority={priority}, days_unresolved={days_unresolved}, created={created_str}")
            
            # P0/highest/high 优先级，1天未解决
            if priority in ['p0', 'highest', 'high'] and days_unresolved >= 1:
                reminder_bugs['p0_p1_bugs'].append(bug)
                ic(f"  -> 添加到P0/High提醒列表")
            
            # P1/medium 优先级，3天未解决
            elif priority in ['p1', 'medium'] and days_unresolved >= 3:
                reminder_bugs['p1_medium_bugs'].append(bug)
                ic(f"  -> 添加到P1/Medium提醒列表")
            else:
                ic(f"  -> 不符合提醒条件")
            
            # TODO: 添加超过测试完成时间的逻辑
            # 这里需要根据Epic的测试完成时间字段进行判断
            
        except Exception as e:
            ic(f"处理bug时间过滤失败: {e}, bug: {bug.get('key', 'unknown')}")
            continue
    
    return reminder_bugs


def send_consolidated_bug_reminder(group_id: str, epic_keys: List[str], bugs: List[Dict], reminder_type: str) -> bool:
    """
    发送整合的bug提醒消息到群组（支持多个Epic）
    
    Args:
        group_id: 群组ID
        epic_keys: Epic key列表
        bugs: bug列表（每个bug包含epic_key字段）
        reminder_type: 提醒类型
        
    Returns:
        bool: 发送是否成功
    """
    if not bugs:
        return True
    
    try:
        # 构建消息标题
        if reminder_type == 'p0_p1_bugs':
            title = f"🚨 SPCT/SPCB P0/High优先级Bug提醒（超过1天未解决）"
        elif reminder_type == 'p1_medium_bugs':
            title = f"⚠️ SPCT/SPCB P1/Medium优先级Bug提醒（超过3天未解决）"
        else:
            title = f"📅 SPCT/SPCB 测试超期Bug提醒"
        
        # 调试模式下添加前缀信息
        if SPCT_BUG_REMINDER_DEBUG:
            message = f"🐛 [调试模式] SPCT Bug提醒\n"
            message += f"原始目标群组: {group_id}\n"
            message += f"涉及Epic: {', '.join(epic_keys)}\n\n"
            message += f"{title}\n\n"
        else:
            message = f"{title}\n"
            if len(epic_keys) > 1:
                message += f"涉及Epic: {', '.join(epic_keys)}\n"
            message += "\n"
        
        # 按Epic分组显示Bug
        bugs_by_epic = {}
        for bug in bugs:
            epic_key = bug.get('epic_key', 'Unknown')
            if epic_key not in bugs_by_epic:
                bugs_by_epic[epic_key] = []
            bugs_by_epic[epic_key].append(bug)
        
        bug_count = 0
        for epic_key, epic_bugs in bugs_by_epic.items():
            if len(epic_keys) > 1:
                message += f"📋 Epic: {epic_key}\n"
            
            for bug in epic_bugs:
                bug_count += 1
                assignee_info = get_assignee_and_tl(bug.get('assignee', ''))
                
                message += f"{bug_count}. 【{bug.get('status', 'Unknown')}】【{bug.get('priority', 'Unknown')}】{bug.get('summary', 'No title')}\n"
                message += f"🔗 {bug.get('key', '')}: https://jira.shopee.io/browse/{bug.get('key', '')}\n"
                message += f"处理人: <mention-tag target=\"seatalk://user?email={assignee_info['assignee']}\"/>\n"
                message += f"TL/PJM: <mention-tag target=\"seatalk://user?email={assignee_info['tl']}\"/>\n"
                message += f"创建时间: {bug.get('created', 'Unknown')}\n"
                message += "------------------------------------------------\n"
            
            if len(epic_keys) > 1:
                message += "\n"
        
        if SPCT_BUG_REMINDER_DEBUG:
            message += f"\n🔧 调试信息:\n"
            message += f"- 调试模式: 开启\n"
            message += f"- 原始群组ID: {group_id}\n"
            message += f"- 当前发送到调试群\n"
            message += f"- 提醒类型: {reminder_type}\n"
            message += f"- 涉及Epic数量: {len(epic_keys)}\n"
            message += f"- Bug总数: {len(bugs)}\n\n"
            message += f"✅ 调试完成后，请设置 SPCT_BUG_REMINDER_DEBUG = False 启用正式模式"
        else:
            message += f"\n请相关同学及时处理以上Bug。"
        
        # 根据调试模式选择发送目标
        target_group_id = SPCT_BUG_REMINDER_DEBUG_GROUP if SPCT_BUG_REMINDER_DEBUG else group_id
        
        ic(f"发送整合Bug提醒消息: 调试模式={SPCT_BUG_REMINDER_DEBUG}, 目标群组={target_group_id}, Epics={epic_keys}")
        
        # 发送消息
        return send_message_to_group(target_group_id, message)
        
    except Exception as e:
        ic(f"发送整合Bug提醒消息失败: {e}")
        return False


def send_bug_reminder_message(group_id: str, epic_key: str, bugs: List[Dict], reminder_type: str) -> bool:
    """
    发送bug提醒消息到群组
    
    Args:
        group_id: 群组ID
        epic_key: Epic key
        bugs: bug列表
        reminder_type: 提醒类型
        
    Returns:
        bool: 发送是否成功
    """
    if not bugs:
        return True
    
    try:
        # 构建消息内容
        if reminder_type == 'p0_p1_bugs':
            title = f"🚨 {epic_key} - P0/High优先级Bug提醒（超过1天未解决）"
        elif reminder_type == 'p1_medium_bugs':
            title = f"⚠️ {epic_key} - P1/Medium优先级Bug提醒（超过3天未解决）"
        else:
            title = f"📅 {epic_key} - 测试超期Bug提醒"
        
        # 调试模式下添加前缀信息
        if SPCT_BUG_REMINDER_DEBUG:
            message = f"🐛 [调试模式] SPCT Bug提醒\n"
            message += f"原始目标群组: {group_id}\n"
            message += f"Epic: {epic_key}\n\n"
            message += f"{title}\n\n"
        else:
            message = f"{title}\n\n"
        
        for i, bug in enumerate(bugs, 1):
            assignee_info = get_assignee_and_tl(bug.get('assignee', ''))
            
            message += f"{i}. 【{bug.get('status', 'Unknown')}】【{bug.get('priority', 'Unknown')}】{bug.get('summary', 'No title')}\n"
            message += f"🔗 {bug.get('key', '')}: https://jira.shopee.io/browse/{bug.get('key', '')}\n"
            message += f"处理人: <mention-tag target=\"seatalk://user?email={assignee_info['assignee']}\"/>\n"
            message += f"TL: <mention-tag target=\"seatalk://user?email={assignee_info['tl']}\"/>\n"
            message += f"创建时间: {bug.get('created', 'Unknown')}\n"
            message += "------------------------------------------------\n"
        
        if SPCT_BUG_REMINDER_DEBUG:
            message += f"\n🔧 调试信息:\n"
            message += f"- 调试模式: 开启\n"
            message += f"- 原始群组ID: {group_id}\n"
            message += f"- 当前发送到调试群\n"
            message += f"- 提醒类型: {reminder_type}\n\n"
            message += f"✅ 调试完成后，请设置 SPCT_BUG_REMINDER_DEBUG = False 启用正式模式"
        else:
            message += f"\n请相关同学及时处理以上Bug。"
        
        # 根据调试模式选择发送目标
        target_group_id = SPCT_BUG_REMINDER_DEBUG_GROUP if SPCT_BUG_REMINDER_DEBUG else group_id
        
        ic(f"发送Bug提醒消息: 调试模式={SPCT_BUG_REMINDER_DEBUG}, 目标群组={target_group_id}, Epic={epic_key}")
        
        # 发送消息
        return send_message_to_group(target_group_id, message)
        
    except Exception as e:
        ic(f"发送Bug提醒消息失败: {e}")
        return False


@track_cronjob_execution('check_spct_test_bugs')
def check_spct_test_bugs() -> None:
    """
    SPCT/SPCB测试环境bug提醒定时任务
    
    根据bug等级进行升级提醒：
    - P0/highest/high的bug一天未解决 cue 到assignee和他的TL
    - P1/medium的bug 超过3天未解决 cue 到assignee和他的TL
    - 已经超过测试完成时间，还有bug未解决每天@assignee的TL
    
    实现思路：
    1. 查询数据库中的SPCB/SPCT群组信息
    2. 查询符合条件的Epic
    3. 查询这些Epic的blocking bugs
    4. 按优先级和时间规则发送提醒
    """
    try:
        ic("开始执行SPCT/SPCB测试环境Bug提醒任务")
        
        # 显示调试模式状态
        debug_status = get_spct_bug_reminder_debug_status()
        ic(f"当前模式: {debug_status['status']}")
        if debug_status['debug_mode']:
            ic(f"调试群组ID: {debug_status['debug_group_id']}")
        
        # 更新全局变量以确保一致性
        global SPCT_BUG_REMINDER_DEBUG
        SPCT_BUG_REMINDER_DEBUG = debug_status['debug_mode']
        
        current_time = datetime.now(timezone.get_current_timezone())
        
        # 1. 获取数据库中的群组信息
        epic_groups = get_epic_groups_from_db()
        if not epic_groups:
            ic("没有找到SPCB/SPCT群组，跳过Bug提醒")
            return
        
        # 2. 创建JIRA连接
        jira_url = "https://jira.shopee.io"
        from app01.config import JIRA_TOKEN
        jira = JIRA(jira_url, token_auth=JIRA_TOKEN)
        
        # 3. 查询符合条件的Epic
        epic_keys_str = ','.join([f'"{key}"' for key in epic_groups.keys()])
        epic_jql = f'''project in (SPCB, SPCT) AND 
                      issuetype = Epic AND 
                      resolution = Unresolved AND 
                      "Project Type" = "Feature Project" AND 
                      status not in (Done, Closed, Waiting, Icebox) AND 
                      createdDate >= 2025-1-1 AND 
                      "Planned Integration End Date" >= startOfMonth(-1) AND
                      key in ({epic_keys_str})'''
        
        valid_epics = jira.search_issues(epic_jql, maxResults=100)
        ic(f"找到 {len(valid_epics)} 个符合条件的Epic")
        
        if not valid_epics:
            ic("没有找到符合条件的Epic，跳过Bug提醒")
            return
        
        # 检查Epic和群组的匹配情况
        epics_with_groups = []
        epics_without_groups = []
        for epic in valid_epics:
            if epic.key in epic_groups:
                epics_with_groups.append(epic.key)
            else:
                epics_without_groups.append(epic.key)
        
        ic(f"有群组的Epic ({len(epics_with_groups)}): {epics_with_groups}")
        ic(f"无群组的Epic ({len(epics_without_groups)}): {epics_without_groups}")
        
        if not epics_with_groups:
            ic("所有符合条件的Epic都没有对应的群组，跳过Bug提醒")
            return
        
        # 4. 查询这些Epic的blocking bugs
        valid_epic_keys = [epic.key for epic in valid_epics]
        ic(f"有效Epic列表: {valid_epic_keys}")
        
        epic_keys_for_bug_query = ','.join([f'"{key}"' for key in valid_epic_keys])
        
        # 构建查询blocks这些Epic的Bug的JQL
        blocks_conditions = []
        for epic_key in valid_epic_keys:
            blocks_conditions.append(f'issue in linkedIssues("{epic_key}", "is blocked by")')
        
        bug_jql = f'''project in (SPCB, SPCT) AND 
                     issuetype = Bug AND 
                     priority IN (P0, P1, highest, high, medium) AND 
                     status not in (Done, Closed, Icebox) AND
                     ({' OR '.join(blocks_conditions)})'''
        
        ic(f"Bug查询JQL: {bug_jql}")
        
        # 需要的字段
        fields = ['key', 'summary', 'priority', 'assignee', 'created', 'status', 'customfield_10008']  # customfield_10008 是Epic Link
        
        bugs = jira.search_issues(bug_jql, fields=fields, maxResults=200)
        ic(f"找到 {len(bugs)} 个相关Bug")
        
        if not bugs:
            ic("没有找到相关Bug，跳过提醒")
            return
        
        # 5. 查询每个Bug blocks哪些Epic，然后分组处理
        bugs_by_epic = {}
        
        for bug in bugs:
            ic(f"处理Bug {bug.key}: 查询blocks关系")
            
            bug_info = {
                'key': bug.key,
                'summary': bug.fields.summary,
                'priority': bug.fields.priority.name if bug.fields.priority else 'Unknown',
                'assignee': bug.fields.assignee.emailAddress if bug.fields.assignee else 'Unassigned',
                'created': bug.fields.created,
                'status': bug.fields.status.name
            }
            
            # 查询这个Bug blocks哪些Epic
            try:
                epic_keys_str = ",".join([f'"{k}"' for k in valid_epic_keys])
                blocked_issues_jql = f'issue in linkedIssues("{bug.key}", "blocks") AND key in ({epic_keys_str})'
                blocked_issues = jira.search_issues(blocked_issues_jql, fields=['key'], maxResults=50)
                
                blocked_epic_keys = [issue.key for issue in blocked_issues]
                ic(f"Bug {bug.key} blocks这些Epic: {blocked_epic_keys}")
                
                if not blocked_epic_keys:
                    ic(f"Bug {bug.key} 没有block任何有效的Epic，跳过")
                    continue
                
                # 将这个Bug添加到它blocks的所有Epic中
                for epic_key in blocked_epic_keys:
                    if epic_key not in bugs_by_epic:
                        bugs_by_epic[epic_key] = []
                    # 添加epic_key信息到bug_info中，用于消息整合
                    bug_with_epic = bug_info.copy()
                    bug_with_epic['epic_key'] = epic_key
                    bugs_by_epic[epic_key].append(bug_with_epic)
                    ic(f"添加Bug {bug.key} 到Epic {epic_key}: {bug_info['priority']} - {bug_info['summary']}")
                    
            except Exception as e:
                ic(f"查询Bug {bug.key} 的blocks关系失败: {e}")
                continue
        
        ic(f"按Epic分组的Bug统计: {[(k, len(v)) for k, v in bugs_by_epic.items()]}")
        
        # 6. 按群组整合消息（一个群组可能对应多个Epic）
        messages_by_group = {}
        
        for epic_key, epic_bugs in bugs_by_epic.items():
            group_id = epic_groups.get(epic_key)
            if not group_id:
                ic(f"Epic {epic_key} 没有对应的群组，跳过")
                continue
            
            ic(f"处理Epic {epic_key} (群组: {group_id}): {len(epic_bugs)} 个Bug")
            
            # 过滤需要提醒的bug
            reminder_bugs = filter_bugs_by_priority_and_time(epic_bugs, current_time)
            
            ic(f"Epic {epic_key} 过滤后的Bug数量: P0/High={len(reminder_bugs['p0_p1_bugs'])}, P1/Medium={len(reminder_bugs['p1_medium_bugs'])}, Overdue={len(reminder_bugs['overdue_bugs'])}")
            
            # 如果该群组已经有消息，则整合
            if group_id not in messages_by_group:
                messages_by_group[group_id] = {
                    'epics': [],
                    'total_bugs': {'p0_p1_bugs': [], 'p1_medium_bugs': [], 'overdue_bugs': []}
                }
            
            # 添加Epic信息
            messages_by_group[group_id]['epics'].append(epic_key)
            
            # 整合Bug信息
            for reminder_type, bug_list in reminder_bugs.items():
                if bug_list:
                    # 确保每个bug都有epic_key标识（已在前面步骤添加）
                    messages_by_group[group_id]['total_bugs'][reminder_type].extend(bug_list)
        
        # 7. 发送整合后的消息
        processed_count = 0
        for group_id, group_data in messages_by_group.items():
            epics = group_data['epics']
            total_bugs = group_data['total_bugs']
            
            ic(f"准备发送消息到群组 {group_id}: Epics={epics}")
            
            # 发送不同类型的提醒
            for reminder_type, bug_list in total_bugs.items():
                if bug_list:
                    success = send_consolidated_bug_reminder(group_id, epics, bug_list, reminder_type)
                    if success:
                        processed_count += 1
                        ic(f"已发送{reminder_type}整合提醒到群组 {group_id}, Epics: {epics}, Bug数量: {len(bug_list)}")
        
        ic(f"SPCT/SPCB Bug提醒任务完成，处理了 {processed_count} 个提醒")
        
    except Exception as e:
        ic(f"SPCT/SPCB Bug提醒任务执行失败: {e}")
        import traceback
        traceback.print_exc()


@track_cronjob_execution('check_epic_status_changes_for_group_creation')
def check_epic_status_changes_for_group_creation() -> None:
    """
    检查Epic状态变更并自动创建群组
    
    每个工作日18:00执行，检查时间范围根据当前日期动态调整：
    - 周一：检查过去的3天（包括周五、周六、周日）
    - 周二到周五：检查过去的24小时
    
    符合条件的Epic：从WAITING、REQ.GATHERING、FEASIBILITY STUDY、PLANNED、PRD
    切换到TECH DESIGN、PLANNING、DEVELOPING，且当前状态不是ICEBOX、CLOSED、DONE
    """
    try:
        ic("开始检查Epic状态变更并自动创建群组")
        
        # 创建JIRA连接
        jira_url = "https://jira.shopee.io"
        jira = JIRA(jira_url, token_auth=jira_token)
        
        # 当前时间
        current_time = datetime.now(timezone.get_current_timezone())
        
        # 确定当前是星期几（0=周一，6=周日）
        weekday = current_time.weekday()
        
        # 根据星期几确定检查的时间范围
        if weekday == 0:  # 周一
            # 检查过去3天的变更（包括周五、周六、周日）
            hours_to_check = 72
            time_range_description = "过去3天（包括周末）"
        else:  # 周二到周五
            # 检查过去24小时的变更
            hours_to_check = 24
            time_range_description = "过去24小时"
        
        # 构建JQL查询
        # 查询条件：
        # 1. 项目为SPCB或SPCT
        # 2. 类型为Epic
        # 3. 当前状态为TECH DESIGN、PLANNING、DEVELOPING
        # 4. 当前状态不是ICEBOX、CLOSED、DONE
        # 5. 在指定时间范围内更新过
        jql = f'project in (SPCB, SPCT) AND issuetype = Epic AND status in ("TECH DESIGN", PLANNING, DEVELOPING) AND status not in (ICEBOX, CLOSED, DONE) AND updated >= -{hours_to_check}h'
        
        # 执行JQL查询
        issues = jira.search_issues(jql, expand='changelog', maxResults=100)
        ic(f"找到 {len(issues)} 个可能符合条件的Epic")
        
        # 记录符合条件的Epic
        epics_to_create_group = []
        
        # 遍历每个Epic
        for issue in issues:
            try:
                issue_key = issue.key
                issue_summary = issue.fields.summary
                current_status = issue.fields.status.name
                
                # 获取状态变更历史
                changelog = issue.changelog
                
                # 检查是否有符合条件的状态变更
                status_changed = False
                from_status = None
                to_status = None
                change_time = None
                
                # 源状态列表
                source_statuses = ["WAITING", "REQ.GATHERING", "FEASIBILITY STUDY", "PLANNED", "PRD"]
                # 目标状态列表
                target_statuses = ["TECH DESIGN", "PLANNING", "DEVELOPING"]
                
                # 遍历变更历史
                for history in changelog.histories:
                    # 检查变更时间
                    history_time = datetime.strptime(history.created, '%Y-%m-%dT%H:%M:%S.%f%z')
                    
                    # 只检查指定时间范围内的变更
                    if (current_time - history_time).total_seconds() > hours_to_check * 3600:  # 转换为秒
                        continue
                    
                    # 检查是否有状态变更
                    for item in history.items:
                        if item.field == 'status':
                            old_status = item.fromString
                            new_status = item.toString
                            
                            # 检查是否符合条件：从源状态列表切换到目标状态列表
                            if old_status in source_statuses and new_status in target_statuses:
                                status_changed = True
                                from_status = old_status
                                to_status = new_status
                                change_time = history_time
                                break
                    
                    if status_changed:
                        break
                
                # 如果有符合条件的状态变更，记录该Epic
                if status_changed:
                    ic(f"找到符合条件的Epic: {issue_key} - {issue_summary}")
                    ic(f"状态变更: {from_status} -> {to_status}, 变更时间: {change_time}")
                    
                    epics_to_create_group.append({
                        "issue_key": issue_key,
                        "summary": issue_summary,
                        "from_status": from_status,
                        "to_status": to_status,
                        "change_time": change_time
                    })
            
            except Exception as e:
                ic(f"处理Epic {issue.key} 时出错: {str(e)}")
        
        # 为符合条件的Epic创建群组
        success_count = 0
        failed_epics = []
        skipped_epics = []  # 新增：记录跳过的Epic
        
        for epic in epics_to_create_group:
            try:
                ic(f"为Epic {epic['issue_key']} 创建群组")
                
                # 检查群组是否已存在（重复建群检测）
                try:
                    existing_groups = SeatalkGroup.objects.filter(group_name__contains=epic['issue_key'])
                    if existing_groups.exists():
                        ic(f"Epic {epic['issue_key']} 已存在相关群组，跳过创建")
                        # 记录跳过信息
                        skipped_epics.append({
                            "issue_key": epic['issue_key'],
                            "summary": epic['summary'],
                            "reason": "群组已存在"
                        })
                        continue
                except Exception as e:
                    ic(f"检查现有群组失败: {str(e)}")
                    # 检查失败时继续创建，避免因数据库问题阻止建群
                
                # 调用现有的建群函数
                result = auto_create_group_for_jira(epic['issue_key'])
                
                if result["success"]:
                    success_count += 1
                    ic(f"成功为Epic {epic['issue_key']} 创建群组: {result.get('group_id')}")
                else:
                    ic(f"为Epic {epic['issue_key']} 创建群组失败: {result.get('message')}")
                    failed_epics.append({
                        "issue_key": epic['issue_key'],
                        "summary": epic['summary'],
                        "error": result.get('message')
                    })
            except Exception as e:
                ic(f"为Epic {epic['issue_key']} 创建群组时出错: {str(e)}")
                failed_epics.append({
                    "issue_key": epic['issue_key'],
                    "summary": epic['summary'],
                    "error": str(e)
                })
        
        # 发送执行结果到Workee PJ日志群
        summary_message = generate_epic_group_creation_report(
            current_time, time_range_description, epics_to_create_group,
            success_count, failed_epics, skipped_epics
        )
        
        # 发送执行报告到Workee PJ日志群
        try:
            send_message_to_group(WORKEE_PJ_LOG_GROUP_ID, summary_message)
            ic("已发送Epic状态变更自动建群执行报告到Workee PJ日志群")
        except Exception as e:
            ic(f"发送执行报告失败: {str(e)}")
        
        ic(f"Epic状态变更自动建群检查完成 - 符合条件:{len(epics_to_create_group)}个，成功:{success_count}个，跳过:{len(skipped_epics)}个，失败:{len(failed_epics)}个")
        
    except Exception as e:
        ic(f"Epic状态变更自动建群检查出错: {str(e)}")
        # 发送错误通知到Workee PJ日志群
        try:
            error_message = f"❌ **Epic状态变更自动建群检查出错** ❌\n\n"
            error_message += f"❌ **错误信息**: {str(e)}\n"
            error_message += f"⏰ **时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            send_message_to_group(WORKEE_PJ_LOG_GROUP_ID, error_message)
            ic("已发送错误通知到Workee PJ日志群")
        except Exception as inner_e:
            ic(f"发送错误通知失败: {str(inner_e)}")



# 注意：daily_bug_summary 功能已移除
# 请使用现有的定时任务系统替代，详见 ENHANCED_TASK_SYSTEM_SUMMARY.md
