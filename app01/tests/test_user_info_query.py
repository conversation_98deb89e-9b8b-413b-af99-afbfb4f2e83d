"""
用户信息查询功能测试
"""

import asyncio
import pytest
import json
from unittest.mock import AsyncMock, patch, MagicMock
from app01.ai_module.user_info_client import UserInfoClient, user_info_client
from app01.ai_module.ai_assistant import AIAssistant


class TestUserInfoClient:
    """测试用户信息查询客户端"""
    
    def setup_method(self):
        """测试前准备"""
        self.client = UserInfoClient()
    
    @pytest.mark.asyncio
    async def test_query_account_info_success(self):
        """测试账户信息查询成功"""
        mock_response_data = {
            "data": [
                {
                    "username": "qiqi.49",
                    "phone": "**********",
                    "SPC_EC": "MTIzNDU2Nzg5MDEyMzQ1NtMOH2DIaCUXTnUz7p4GGE8ILqxCMthZxg1l51gMClUFkFUvO8gvP8+jOYHmBcwqZ+jNRCujsJdUtsNZZzqrJmAzX2DAG9BXbWMZy+nVVqfSAyVzkiAiHpENTV8R0BBXVmnZ+t9YRYqPXj8z24dllCM=",
                    "env": "TEST",
                    "SPC_ST": ".MTIzNDU2Nzg5MDEyMzQ1NgNSKuUDmtIV+NzRTAGsu5oAC/k3O+zwv5KqEFgEmJHGGMYFCFqDF1qu7apnD5yEi/26BFj2PM+Q0cvGXIEkGfivdZGoJXNrAj0qiOs1e79PgUxdeBFKAGUXSgShNIzzuNPtlVvlDd01XvlK7v1FpBjfgGXcyJTyaurO1L+O3SCwrgeXGbw+UOMoeR3SZKw6DA==",
                    "country": "ID",
                    "password": "123456",
                    "userid": **********
                }
            ]
        }
        
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_response_data)
            
            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
            
            result = await self.client.query_account_info(username="qiqi.49")
            
            assert result['success'] is True
            assert result['source'] == 'account_api'
            assert result['data'] == mock_response_data
    
    @pytest.mark.asyncio
    async def test_query_user_info_success(self):
        """测试用户信息查询成功"""
        mock_response_data = {
            "username": "qiqi.49",
            "userid": **********,
            "system": "",
            "shopid": **********,
            "phone": "**********",
            "deviceid": "",
            "app_version": "",
            "email": ""
        }
        
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_response_data)
            
            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
            
            result = await self.client.query_user_info(country="ID", env="TEST", username="qiqi.49")
            
            assert result['success'] is True
            assert result['source'] == 'user_info_api'
            assert result['data'] == mock_response_data
    
    @pytest.mark.asyncio
    async def test_query_user_comprehensive_success(self):
        """测试综合查询成功"""
        account_data = {
            "data": [
                {
                    "username": "qiqi.49",
                    "phone": "**********",
                    "SPC_EC": "sensitive_data",
                    "env": "TEST",
                    "SPC_ST": "sensitive_data",
                    "country": "ID",
                    "password": "123456",
                    "userid": **********
                }
            ]
        }
        
        user_info_data = {
            "username": "qiqi.49",
            "userid": **********,
            "system": "",
            "shopid": **********,
            "phone": "**********",
            "deviceid": "",
            "app_version": "",
            "email": "<EMAIL>"
        }
        
        with patch.object(self.client, 'query_account_info', return_value={'success': True, 'data': account_data}), \
             patch.object(self.client, 'query_user_info', return_value={'success': True, 'data': user_info_data}):
            
            result = await self.client.query_user_comprehensive(username="qiqi.49")
            
            assert result['success'] is True
            assert len(result['users']) == 1
            
            user = result['users'][0]
            assert user['username'] == "qiqi.49"
            assert user['userid'] == **********
            assert user['email'] == "<EMAIL>"
            # 确保敏感字段被过滤
            assert 'SPC_EC' not in user
            assert 'SPC_ST' not in user
    
    def test_clean_sensitive_data(self):
        """测试敏感数据清理"""
        data = {
            "username": "test",
            "SPC_EC": "sensitive",
            "SPC_ST": "sensitive",
            "normal_field": "normal"
        }
        
        cleaned = self.client._clean_sensitive_data(data)
        
        assert 'username' in cleaned
        assert 'normal_field' in cleaned
        assert 'SPC_EC' not in cleaned
        assert 'SPC_ST' not in cleaned
    
    def test_format_user_info(self):
        """测试用户信息格式化"""
        users = [
            {
                "username": "qiqi.49",
                "userid": **********,
                "phone": "**********",
                "email": "<EMAIL>",
                "env": "TEST",
                "country": "ID"
            }
        ]
        
        formatted = self.client.format_user_info(users)
        
        assert "找到 1 个用户" in formatted
        assert "qiqi.49" in formatted
        assert "**********" in formatted
        assert "**********" in formatted
        assert "<EMAIL>" in formatted
    
    def test_get_help_message(self):
        """测试帮助信息"""
        help_msg = self.client.get_help_message()
        
        assert "用户信息查询帮助" in help_msg
        assert "user username" in help_msg
        assert "user id" in help_msg
        assert "示例" in help_msg


class TestAIAssistantUserInfo:
    """测试AI助手的用户信息查询功能"""
    
    def setup_method(self):
        """测试前准备"""
        self.ai_assistant = AIAssistant()
    
    @pytest.mark.asyncio
    async def test_handle_user_info_command_help(self):
        """测试用户信息命令帮助"""
        result = await self.ai_assistant._handle_user_info_command("user", "test_user", "<EMAIL>")
        
        assert result['success'] is True
        assert result['intent'] == 'user_info_help'
        assert "用户信息查询帮助" in result['response']
    
    @pytest.mark.asyncio
    async def test_handle_user_info_command_username(self):
        """测试按用户名查询"""
        mock_result = {
            'success': True,
            'users': [
                {
                    'username': 'qiqi.49',
                    'userid': **********,
                    'phone': '**********',
                    'email': '<EMAIL>',
                    'env': 'TEST',
                    'country': 'ID'
                }
            ]
        }
        
        with patch('app01.ai_module.user_info_client.user_info_client.query_user_comprehensive', return_value=mock_result), \
             patch('app01.ai_module.user_info_client.user_info_client.format_user_info', return_value="格式化的用户信息"):
            
            result = await self.ai_assistant._handle_user_info_command("user username qiqi.49", "test_user", "<EMAIL>")
            
            assert result['success'] is True
            assert result['intent'] == 'user_info_query'
            assert result['user_count'] == 1
            assert result['query_params']['username'] == 'qiqi.49'
    
    @pytest.mark.asyncio
    async def test_handle_user_info_command_userid(self):
        """测试按用户ID查询"""
        mock_result = {
            'success': True,
            'users': [
                {
                    'username': 'qiqi.49',
                    'userid': **********,
                    'phone': '**********',
                    'email': '<EMAIL>',
                    'env': 'TEST',
                    'country': 'ID'
                }
            ]
        }
        
        with patch('app01.ai_module.user_info_client.user_info_client.query_user_comprehensive', return_value=mock_result), \
             patch('app01.ai_module.user_info_client.user_info_client.format_user_info', return_value="格式化的用户信息"):
            
            result = await self.ai_assistant._handle_user_info_command("user id **********", "test_user", "<EMAIL>")
            
            assert result['success'] is True
            assert result['intent'] == 'user_info_query'
            assert result['user_count'] == 1
            assert result['query_params']['userid'] == **********
    
    @pytest.mark.asyncio
    async def test_handle_user_info_command_auto_detect(self):
        """测试自动识别类型查询"""
        mock_result = {
            'success': True,
            'users': [
                {
                    'username': 'test',
                    'userid': 123456,
                    'phone': '1234567890',
                    'email': '<EMAIL>',
                    'env': 'TEST',
                    'country': 'ID'
                }
            ]
        }
        
        with patch('app01.ai_module.user_info_client.user_info_client.query_user_comprehensive', return_value=mock_result), \
             patch('app01.ai_module.user_info_client.user_info_client.format_user_info', return_value="格式化的用户信息"):
            
            # 测试数字ID自动识别
            result = await self.ai_assistant._handle_user_info_command("user 123456", "test_user", "<EMAIL>")
            assert result['success'] is True
            assert result['query_params']['userid'] == 123456
            
            # 测试用户名自动识别
            result = await self.ai_assistant._handle_user_info_command("user test", "test_user", "<EMAIL>")
            assert result['success'] is True
            assert result['query_params']['username'] == 'test'
    
    @pytest.mark.asyncio
    async def test_handle_user_info_command_not_found(self):
        """测试用户未找到"""
        mock_result = {
            'success': True,
            'users': []
        }
        
        with patch('app01.ai_module.user_info_client.user_info_client.query_user_comprehensive', return_value=mock_result):
            
            result = await self.ai_assistant._handle_user_info_command("user notfound", "test_user", "<EMAIL>")
            
            assert result['success'] is True
            assert result['intent'] == 'user_info_not_found'
            assert "未找到匹配的用户信息" in result['response']
    
    @pytest.mark.asyncio
    async def test_handle_user_info_command_error(self):
        """测试查询错误"""
        mock_result = {
            'success': False,
            'error': 'API调用失败'
        }
        
        with patch('app01.ai_module.user_info_client.user_info_client.query_user_comprehensive', return_value=mock_result):
            
            result = await self.ai_assistant._handle_user_info_command("user test", "test_user", "<EMAIL>")
            
            assert result['success'] is False
            assert result['intent'] == 'user_info_error'
            assert "API调用失败" in result['response']


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
