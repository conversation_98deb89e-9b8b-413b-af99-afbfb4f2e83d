#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统计API URL配置
"""

from django.urls import path
from . import views

app_name = 'statistics'

urlpatterns = [
    # 统计面板页面
    path('dashboard/', views.statistics_dashboard_page, name='statistics_dashboard_page'),
    
    # 定时任务管理页面
    path('task-management/', views.task_management_page, name='task_management_page'),

    # 实时监控API
    path('realtime/dashboard/', views.realtime_dashboard, name='realtime_dashboard'),
    path('realtime/command-trends/', views.command_trends, name='command_trends'),
    path('realtime/user-activity/', views.user_activity_stats, name='user_activity_stats'),
    path('realtime/activity-logs/', views.activity_logs, name='activity_logs'),

    # 性能分析API
    path('performance/metrics/', views.performance_metrics, name='performance_metrics'),
    path('performance/slow-queries/', views.slow_queries_analysis, name='slow_queries_analysis'),

    # 定时任务监控API
    path('cronjobs/status/', views.cronjob_status, name='cronjob_status'),
    path('cronjobs/comprehensive/', views.comprehensive_cronjob_status, name='comprehensive_cronjob_status'),
    path('cronjobs/health/', views.cronjob_health_report, name='cronjob_health_report'),

    # 报表API
    path('reports/daily/', views.daily_report, name='daily_report'),
    path('reports/weekly/', views.weekly_report, name='weekly_report'),
    path('reports/alerts/', views.alert_report, name='alert_report'),

    # 数据查询API
    path('data/command-records/', views.command_execution_records, name='command_execution_records'),
    path('data/system-health/', views.system_health_status, name='system_health_status'),
    path('data/command-statistics/', views.get_command_statistics, name='get_command_statistics'),
    path('data/user-statistics/', views.get_user_statistics, name='get_user_statistics'),
    path('data/performance-statistics/', views.get_performance_statistics, name='get_performance_statistics'),
    path('data/user-scheduled-tasks/', views.get_user_scheduled_tasks, name='get_user_scheduled_tasks'),
    
    # 定时任务管理API
    path('api/tasks/', views.list_user_tasks_api, name='list_user_tasks_api'),
    path('api/tasks/create/', views.create_task_api, name='create_task_api'),
    path('api/tasks/<int:task_id>/toggle/', views.toggle_task_api, name='toggle_task_api'),
    path('api/tasks/<int:task_id>/delete/', views.delete_task_api, name='delete_task_api'),
    path('api/tasks/<int:task_id>/update/', views.update_task_api, name='update_task_api'),
    path('api/search-groups/', views.search_groups_api, name='search_groups_api'),
]
