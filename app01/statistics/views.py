#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统计API视图
提供统计数据的REST API接口
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.conf import settings
from django.core.paginator import Paginator
from django.shortcuts import render, redirect
from .services import (
    realtime_stats_service,
    performance_analysis_service,
    cronjob_monitoring_service
)
from .reports import (
    daily_report_generator,
    weekly_report_generator,
    alert_report_generator
)

logger = logging.getLogger(__name__)


def api_response(data: Any = None, error: str = None, status: int = 200) -> JsonResponse:
    """统一的API响应格式"""
    response_data = {
        'timestamp': timezone.now().isoformat(),
        'success': error is None
    }
    
    if error:
        response_data['error'] = error
        status = 400 if status == 200 else status
    else:
        response_data['data'] = data
    
    return JsonResponse(response_data, status=status)


@require_http_methods(["GET"])
def realtime_dashboard(request):
    """实时监控面板数据API"""
    try:
        data = realtime_stats_service.get_realtime_dashboard_data()
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except Exception as e:
        logger.error(f"Failed to get realtime dashboard data: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def command_trends(request):
    """指令执行趋势API"""
    try:
        days = int(request.GET.get('days', 7))
        if days < 1 or days > 90:
            return api_response(error="Days parameter must be between 1 and 90")
        
        data = realtime_stats_service.get_command_trends(days=days)
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except ValueError:
        return api_response(error="Invalid days parameter")
    except Exception as e:
        logger.error(f"Failed to get command trends: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def user_activity_stats(request):
    """用户活动统计API"""
    try:
        days = int(request.GET.get('days', 7))
        if days < 1 or days > 90:
            return api_response(error="Days parameter must be between 1 and 90")

        data = realtime_stats_service.get_user_activity_stats(days=days)

        if 'error' in data:
            return api_response(error=data['error'])

        return api_response(data)

    except ValueError:
        return api_response(error="Invalid days parameter")
    except Exception as e:
        logger.error(f"Failed to get user activity stats: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def activity_logs(request):
    """实时活动日志API"""
    try:
        limit = int(request.GET.get('limit', 20))  # 默认改为20条
        if limit < 1 or limit > 50:
            return api_response(error="Limit parameter must be between 1 and 50")

        data = realtime_stats_service.get_activity_logs(limit=limit)

        if 'error' in data:
            return api_response(error=data['error'])

        return api_response(data)

    except ValueError:
        return api_response(error="Invalid limit parameter")
    except Exception as e:
        logger.error(f"Failed to get activity logs: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def cronjob_list(request):
    """定时任务列表API"""
    try:
        data = realtime_stats_service.get_cronjob_list()

        if 'error' in data:
            return api_response(error=data['error'])

        return api_response(data)

    except Exception as e:
        logger.error(f"Failed to get cronjob list: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def comprehensive_cronjob_status(request):
    """综合定时任务状态API（包含用户任务）"""
    try:
        hours = int(request.GET.get('hours', 24))
        if hours < 1 or hours > 168:  # 最多7天
            return api_response(error="Hours parameter must be between 1 and 168")

        from app01.statistics.services import cronjob_monitoring_service
        data = cronjob_monitoring_service.get_comprehensive_cronjob_status(hours=hours)

        if 'error' in data:
            return api_response(error=data['error'])

        return api_response(data)

    except ValueError:
        return api_response(error="Invalid hours parameter")
    except Exception as e:
        logger.error(f"Failed to get comprehensive cronjob status: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def performance_metrics(request):
    """性能指标API"""
    try:
        hours = int(request.GET.get('hours', 24))
        if hours < 1 or hours > 168:  # 最多7天
            return api_response(error="Hours parameter must be between 1 and 168")
        
        data = performance_analysis_service.get_performance_metrics(hours=hours)
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except ValueError:
        return api_response(error="Invalid hours parameter")
    except Exception as e:
        logger.error(f"Failed to get performance metrics: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def slow_queries_analysis(request):
    """慢查询分析API"""
    try:
        threshold = float(request.GET.get('threshold', 1.0))
        if threshold < 0.1 or threshold > 60:
            return api_response(error="Threshold must be between 0.1 and 60 seconds")
        
        data = performance_analysis_service.get_slow_queries_analysis(threshold_seconds=threshold)
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except ValueError:
        return api_response(error="Invalid threshold parameter")
    except Exception as e:
        logger.error(f"Failed to analyze slow queries: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def cronjob_status(request):
    """定时任务状态API"""
    try:
        hours = int(request.GET.get('hours', 24))
        if hours < 1 or hours > 168:
            return api_response(error="Hours parameter must be between 1 and 168")
        
        data = cronjob_monitoring_service.get_cronjob_status(hours=hours)
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except ValueError:
        return api_response(error="Invalid hours parameter")
    except Exception as e:
        logger.error(f"Failed to get cronjob status: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def cronjob_health_report(request):
    """定时任务健康报告API"""
    try:
        data = cronjob_monitoring_service.get_job_health_report()
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except Exception as e:
        logger.error(f"Failed to get cronjob health report: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def daily_report(request):
    """日报API"""
    try:
        date_str = request.GET.get('date')
        date = None
        
        if date_str:
            try:
                date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                return api_response(error="Invalid date format. Use YYYY-MM-DD")
        
        format_type = request.GET.get('format', 'json')
        
        if format_type == 'html':
            html_content = daily_report_generator.generate_daily_report_html(date)
            return HttpResponse(html_content, content_type='text/html')
        else:
            data = daily_report_generator.generate_daily_report(date)
            
            if 'error' in data:
                return api_response(error=data['error'])
            
            return api_response(data)
        
    except Exception as e:
        logger.error(f"Failed to generate daily report: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def weekly_report(request):
    """周报API"""
    try:
        week_start_str = request.GET.get('week_start')
        week_start = None
        
        if week_start_str:
            try:
                week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()
            except ValueError:
                return api_response(error="Invalid week_start format. Use YYYY-MM-DD")
        
        data = weekly_report_generator.generate_weekly_report(week_start)
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except Exception as e:
        logger.error(f"Failed to generate weekly report: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def alert_report(request):
    """告警报告API"""
    try:
        data = alert_report_generator.generate_alert_report()
        
        if 'error' in data:
            return api_response(error=data['error'])
        
        return api_response(data)
        
    except Exception as e:
        logger.error(f"Failed to generate alert report: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def command_execution_records(request):
    """指令执行记录查询API"""
    try:
        from app01.models import CommandExecutionRecord
        
        # 查询参数
        user_id = request.GET.get('user_id')
        command_type = request.GET.get('command_type')
        success = request.GET.get('success')
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        
        # 构建查询
        queryset = CommandExecutionRecord.objects.all()
        
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        if command_type:
            queryset = queryset.filter(command_type=command_type)
        
        if success is not None:
            success_bool = success.lower() in ['true', '1', 'yes']
            queryset = queryset.filter(success=success_bool)
        
        if start_date:
            try:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                queryset = queryset.filter(created_at__gte=start_dt)
            except ValueError:
                return api_response(error="Invalid start_date format. Use YYYY-MM-DD")
        
        if end_date:
            try:
                end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                queryset = queryset.filter(created_at__lt=end_dt)
            except ValueError:
                return api_response(error="Invalid end_date format. Use YYYY-MM-DD")
        
        # 分页
        paginator = Paginator(queryset.order_by('-created_at'), page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化数据
        records = []
        for record in page_obj:
            records.append({
                'execution_id': str(record.execution_id),
                'user_id': record.user_id,
                'user_email': record.user_email,
                'command_type': record.command_type,
                'raw_input': record.raw_input[:200],  # 限制长度
                'success': record.success,
                'processing_time': record.processing_time,
                'created_at': record.created_at.isoformat(),
                'error_message': record.error_message[:200] if record.error_message else None
            })
        
        return api_response({
            'records': records,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_pages': paginator.num_pages,
                'total_count': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        })
        
    except ValueError as e:
        return api_response(error=f"Invalid parameter: {e}")
    except Exception as e:
        logger.error(f"Failed to get command execution records: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def system_health_status(request):
    """系统健康状态API"""
    try:
        from app01.models import SystemHealthSnapshot
        
        # 获取最新的健康快照
        latest_snapshot = SystemHealthSnapshot.objects.first()
        
        if not latest_snapshot:
            return api_response(error="No health snapshot available")
        
        data = {
            'snapshot_time': latest_snapshot.snapshot_time.isoformat(),
            'overall_status': latest_snapshot.overall_status,
            'total_users_today': latest_snapshot.total_users_today,
            'active_users_now': latest_snapshot.active_users_now,
            'total_commands_today': latest_snapshot.total_commands_today,
            'success_rate_today': latest_snapshot.success_rate_today,
            'avg_response_time': latest_snapshot.avg_response_time,
            'system_load': latest_snapshot.system_load,
            'memory_usage': latest_snapshot.memory_usage,
            'cpu_usage': latest_snapshot.cpu_usage,
            'database_status': latest_snapshot.database_status,
            'redis_status': latest_snapshot.redis_status,
            'external_services_status': latest_snapshot.external_services_status,
            'error_count_last_hour': latest_snapshot.error_count_last_hour,
            'cronjobs_running': latest_snapshot.cronjobs_running,
            'cronjobs_failed_today': latest_snapshot.cronjobs_failed_today,
            'alerts': latest_snapshot.alerts
        }
        
        return api_response(data)
        
    except Exception as e:
        logger.error(f"Failed to get system health status: {e}")
        return api_response(error=str(e))


@require_http_methods(["GET"])
def statistics_dashboard_page(request):
    """统计监控面板页面"""
    try:
        # 获取基础统计数据
        context = {
            'title': 'ChatBot AutoRelease 统计监控面板',
            'current_time': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
        }

        return render(request, 'statistics/dashboard.html', context)

    except Exception as e:
        logger.error(f"Failed to render statistics dashboard: {e}")
        return HttpResponse(f"页面加载失败: {str(e)}", status=500)


@require_http_methods(["GET", "POST"])
def task_management_page(request):
    """定时任务管理页面"""
    try:
        # 简单的密码保护
        password = "zhimakaimenba"  # 与dashboard相同的密码
        
        if request.method == "POST":
            entered_password = request.POST.get('password')
            if entered_password == password:
                request.session['task_management_authenticated'] = True
                return redirect('/api/statistics/task-management/')
            else:
                return render(request, 'statistics/task_login.html', {'error': '密码错误'})
        
        # 检查是否已经认证
        if not request.session.get('task_management_authenticated'):
            return render(request, 'statistics/task_login.html')
        
        context = {
            'title': 'ChatBot AutoRelease 定时任务管理',
            'current_time': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
        }
        return render(request, 'statistics/task_management.html', context)
    except Exception as e:
        logger.error(f"Failed to render task management page: {e}")
        return HttpResponse(f"页面加载失败: {str(e)}", status=500)


# 修复指令统计页面的数据查询逻辑
def get_command_statistics(request):
    """获取指令统计数据"""
    try:
        from django.db.models import Count, Q
        from app01.models import CommandExecutionRecord
        from datetime import datetime, timedelta

        # 获取时间范围参数
        days = int(request.GET.get('days', 7))
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 修复指令类型分布查询
        command_type_stats = CommandExecutionRecord.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).values('command_type').annotate(
            count=Count('id')
        ).order_by('-count')

        # 修复成功率趋势查询
        success_rate_data = []
        for i in range(days):
            date = start_date + timedelta(days=i)
            date_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
            date_end = date_start + timedelta(days=1)

            total = CommandExecutionRecord.objects.filter(
                created_at__gte=date_start,
                created_at__lt=date_end
            ).count()

            success = CommandExecutionRecord.objects.filter(
                created_at__gte=date_start,
                created_at__lt=date_end,
                success=True
            ).count()

            success_rate = (success / total * 100) if total > 0 else 0
            success_rate_data.append({
                'date': date.strftime('%Y-%m-%d'),
                'success_rate': round(success_rate, 2),
                'total': total,
                'success': success
            })

        return JsonResponse({
            'success': True,
            'data': {
                'command_type_distribution': list(command_type_stats),
                'success_rate_trend': success_rate_data
            }
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


def get_user_statistics(request):
    """获取用户统计数据"""
    try:
        from django.db.models import Count, Q
        from app01.models import CommandExecutionRecord, BotAccessEvent, SeatalkGroup
        from datetime import datetime, timedelta

        # 获取时间范围
        days = int(request.GET.get('days', 7))
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 总用户数 - 所有进入过机器人页面的用户（基于BotAccessEvent）
        try:
            total_users = BotAccessEvent.objects.values('user_id').distinct().count()
        except:
            # 如果BotAccessEvent表查询失败，使用CommandExecutionRecord作为备选
            total_users = CommandExecutionRecord.objects.values('user_id').distinct().count()

        # 活跃用户数 - 最近指定天数有指令执行活动的用户
        active_users = CommandExecutionRecord.objects.filter(
            created_at__gte=start_date
        ).values('user_id').distinct().count()

        # 新增服务群数量统计
        total_groups = SeatalkGroup.objects.count()

        # 用户活跃度 - 平均每用户指令数
        if active_users > 0:
            total_commands = CommandExecutionRecord.objects.filter(
                created_at__gte=start_date
            ).count()
            avg_commands_per_user = round(total_commands / active_users, 2)
        else:
            avg_commands_per_user = 0

        # 平均指令数
        avg_daily_commands = CommandExecutionRecord.objects.filter(
            created_at__gte=start_date
        ).count() / days if days > 0 else 0

        # 活跃用户排行榜
        user_rankings = CommandExecutionRecord.objects.filter(
            created_at__gte=start_date
        ).values('user_id', 'user_email').annotate(
            command_count=Count('id'),
            success_count=Count('id', filter=Q(success=True)),
        ).order_by('-command_count')[:10]

        # 计算成功率
        for user in user_rankings:
            user['success_rate'] = round(
                (user['success_count'] / user['command_count'] * 100) if user['command_count'] > 0 else 0,
                2
            )
            # 获取最后活动时间
            last_activity = CommandExecutionRecord.objects.filter(
                user_id=user['user_id']
            ).order_by('-created_at').first()
            user['last_activity'] = last_activity.created_at if last_activity else None

        # 用户活跃度趋势
        user_activity_trend = []
        for i in range(days):
            date = start_date + timedelta(days=i)
            date_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
            date_end = date_start + timedelta(days=1)

            active_users_day = CommandExecutionRecord.objects.filter(
                created_at__gte=date_start,
                created_at__lt=date_end
            ).values('user_id').distinct().count()

            # 统计新用户 - 使用进入聊天室事件作为新用户指标
            new_users_day = BotAccessEvent.objects.filter(
                event_time__gte=date_start,
                event_time__lt=date_end,
                event_type__in=['user_enter_chatroom_with_bot', 'user_start_chat_with_bot']
            ).values('user_id').distinct().count()
            
            user_activity_trend.append({
                'date': date.strftime('%Y-%m-%d'),
                'active_users': active_users_day,
                'new_users': new_users_day
            })
        
        return JsonResponse({
            'success': True,
            'data': {
                'total_users': total_users,
                'active_users': active_users,
                'total_groups': total_groups,  # 新增服务群数量
                'user_activity_rate': round((active_users / total_users * 100) if total_users > 0 else 0, 2),
                'avg_commands_per_user': avg_commands_per_user,
                'user_rankings': list(user_rankings),
                'user_activity_trend': user_activity_trend
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


def get_performance_statistics(request):
    """获取性能统计数据"""
    try:
        from django.db.models import Avg
        from app01.models import CommandExecutionRecord
        from datetime import datetime, timedelta
        import logging

        logger = logging.getLogger(__name__)

        # 获取系统资源使用情况
        try:
            import psutil
            logger.info("psutil模块导入成功")

            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            logger.info(f"CPU使用率: {cpu_percent}%")

            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            logger.info(f"内存使用率: {memory_percent}%")

            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            logger.info(f"磁盘使用率: {disk_percent}%")

        except ImportError as e:
            logger.error(f"psutil模块导入失败: {e}")
            # 如果无法导入psutil，使用模拟数据
            cpu_percent = 15.5
            memory_percent = 45.2
            disk_percent = 60.8
        except Exception as e:
            logger.error(f"获取系统资源失败: {e}")
            # 如果无法获取系统资源，使用模拟数据
            cpu_percent = 15.5
            memory_percent = 45.2
            disk_percent = 60.8

        # 获取时间范围
        days = int(request.GET.get('days', 7))
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # API响应时间统计
        avg_response_time = CommandExecutionRecord.objects.filter(
            created_at__gte=start_date,
            processing_time__isnull=False
        ).aggregate(avg_time=Avg('processing_time'))['avg_time'] or 0
        
        # 内存使用率（模拟数据，实际应该从监控系统获取）
        memory_usage_rate = memory_percent
        
        # 数据库状态
        db_status = 'normal'  # 可以添加数据库连接检查逻辑
        
        # API响应时间统计 - 基于真实的CommandExecutionRecord数据
        endpoint_performance = []

        # 从CommandExecutionRecord中统计不同类型指令的响应时间
        from django.db.models import Avg, Max, Count
        command_performance = CommandExecutionRecord.objects.filter(
            created_at__gte=start_date,
            processing_time__isnull=False
        ).values('command_type').annotate(
            avg_time=Avg('processing_time'),
            max_time=Max('processing_time'),
            count=Count('id')
        ).order_by('-count')[:6]  # 取前6个最常用的指令类型

        for cmd in command_performance:
            endpoint_performance.append({
                'endpoint': f"指令类型: {cmd['command_type']}",
                'avg_response_time': round(cmd['avg_time'] * 1000, 2) if cmd['avg_time'] else 0,  # 转换为毫秒
                'max_response_time': round(cmd['max_time'] * 1000, 2) if cmd['max_time'] else 0,   # 转换为毫秒
                'request_count': cmd['count']
            })
        
        return JsonResponse({
            'success': True,
            'data': {
                'avg_response_time': round(avg_response_time, 2),
                'memory_usage_rate': round(memory_usage_rate, 2),
                'cpu_usage_rate': round(cpu_percent, 2),
                'disk_usage_rate': round(disk_percent, 2),
                'database_status': db_status,
                'endpoint_performance': endpoint_performance
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


def get_user_scheduled_tasks(request):
    """获取用户定时任务数据"""
    try:
        from django.db.models import Count, Q
        from django.core.paginator import Paginator
        from datetime import datetime, timedelta

        # 获取分页参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))

        # 获取时间范围
        days = int(request.GET.get('days', 7))
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 查询用户定时任务
        try:
            # 直接使用SQL查询，避免模型导入问题
            from django.db import connection

            with connection.cursor() as cursor:
                # 查询任务列表（分页）
                offset = (page - 1) * page_size
                cursor.execute("""
                    SELECT id, user_id, user_email, task_name, task_type, frequency,
                           status, is_active, total_executions, successful_executions,
                           last_execution, next_execution, created_at
                    FROM user_scheduled_task
                    ORDER BY created_at DESC
                    LIMIT %s OFFSET %s
                """, [page_size, offset])

                tasks_data = cursor.fetchall()

                # 查询总数
                cursor.execute("SELECT COUNT(*) FROM user_scheduled_task")
                total_count = cursor.fetchone()[0]

                # 查询统计数据
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_tasks,
                        COUNT(CASE WHEN is_active=1 THEN 1 END) as active_tasks,
                        COUNT(CASE WHEN last_execution >= %s AND last_execution <= %s AND status='success' THEN 1 END) as success_today,
                        COUNT(CASE WHEN last_execution >= %s AND last_execution <= %s AND status='failed' THEN 1 END) as failed_today
                    FROM user_scheduled_task
                """, [start_date, end_date, start_date, end_date])

                stats = cursor.fetchone()

                # 查询任务类型分布
                cursor.execute("""
                    SELECT task_type, COUNT(*) as count
                    FROM user_scheduled_task
                    GROUP BY task_type
                    ORDER BY count DESC
                """)

                type_distribution = cursor.fetchall()

            # 处理任务列表数据
            tasks_list = []
            for task in tasks_data:
                # 处理用户显示
                user_display = task[2].split('@')[0] if task[2] and '@' in task[2] else task[1]

                # 计算成功率
                success_rate = 0
                if task[8] > 0:  # total_executions
                    success_rate = round((task[9] / task[8]) * 100, 1)  # successful_executions

                tasks_list.append({
                    'id': task[0],
                    'user': user_display,
                    'user_email': task[2],
                    'task_name': task[3],
                    'task_type': task[4],
                    'frequency': task[5],
                    'status': task[6],
                    'is_active': bool(task[7]),
                    'total_executions': task[8],
                    'successful_executions': task[9],
                    'success_rate': success_rate,
                    'last_execution': task[10].isoformat() if task[10] else None,
                    'next_execution': task[11].isoformat() if task[11] else None,
                    'created_at': task[12].isoformat() if task[12] else None
                })

            # 计算分页信息
            total_pages = (total_count + page_size - 1) // page_size
            has_next = page < total_pages
            has_previous = page > 1

            # 处理任务类型分布
            task_type_dist = [{'task_type': row[0], 'count': row[1]} for row in type_distribution]

            return JsonResponse({
                'success': True,
                'data': {
                    'tasks': tasks_list,
                    'pagination': {
                        'current_page': page,
                        'total_pages': total_pages,
                        'total_count': total_count,
                        'has_next': has_next,
                        'has_previous': has_previous,
                        'page_size': page_size
                    },
                    'summary': {
                        'total_tasks': stats[0],
                        'active_tasks': stats[1],
                        'success_today': stats[2],
                        'failed_today': stats[3],
                        'success_rate': round((stats[2] / (stats[2] + stats[3]) * 100), 1) if (stats[2] + stats[3]) > 0 else 0
                    },
                    'task_type_distribution': task_type_dist
                }
            })

        except Exception as db_error:
            # 如果数据库查询失败，返回空数据
            return JsonResponse({
                'success': True,
                'data': {
                    'tasks': [],
                    'pagination': {
                        'current_page': 1,
                        'total_pages': 0,
                        'total_count': 0,
                        'has_next': False,
                        'has_previous': False,
                        'page_size': page_size
                    },
                    'summary': {
                        'total_tasks': 0,
                        'active_tasks': 0,
                        'success_today': 0,
                        'failed_today': 0,
                        'success_rate': 0
                    },
                    'task_type_distribution': []
                }
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


# ==================== 定时任务管理API ====================

@require_http_methods(["GET"])
def list_user_tasks_api(request):
    """获取用户定时任务列表API"""
    try:
        from app01.models import UserScheduledTask
        
        # 获取查询参数
        user_email = request.GET.get('user_email', '')
        admin_mode = request.GET.get('admin_mode', 'false').lower() == 'true'
        project_filter = request.GET.get('project_filter', '')
        sort_field = request.GET.get('sort_field', '')
        sort_direction = request.GET.get('sort_direction', 'asc')

        # 直接从数据库查询任务
        query = UserScheduledTask.objects.all()

        # 如果不是管理员模式且指定了用户邮箱，只显示该用户的任务
        if not admin_mode and user_email:
            query = query.filter(user_email=user_email)

        # 处理排序
        if sort_field:
            # 字段映射
            field_mapping = {
                'id': 'id',
                'task_name': 'task_name',
                'user_email': 'user_email',
                'task_type': 'task_type',
                'frequency': 'frequency',
                'schedule_time': 'schedule_time',
                'is_active': 'is_active',
                'next_execution_time': 'next_execution',
                'last_run_time': 'last_execution'
            }

            db_field = field_mapping.get(sort_field)
            if db_field:
                order_prefix = '-' if sort_direction == 'desc' else ''
                tasks = query.order_by(f'{order_prefix}{db_field}')
            else:
                # 默认排序
                tasks = query.order_by('-created_at')
        else:
            # 默认按创建时间倒序排列
            tasks = query.order_by('-created_at')
        
        # 构建返回数据
        task_list = []
        for task in tasks:
            # 解析群关键词
            target_group_keyword = ''
            if task.target_group_id:
                if task.target_group_id.startswith('keyword:'):
                    target_group_keyword = task.target_group_id.replace('keyword:', '')
                elif task.target_group_id.startswith('group_id:'):
                    # 尝试从数据库获取群名称
                    group_id = task.target_group_id.replace('group_id:', '')
                    try:
                        from app01.models import SeatalkGroup
                        group = SeatalkGroup.objects.filter(group_id=group_id).first()
                        if group:
                            target_group_keyword = group.group_name
                        else:
                            target_group_keyword = f"群ID:{group_id}"
                    except:
                        target_group_keyword = f"群ID:{group_id}"
            
            # 处理通知方式，优先使用新的notification_methods字段（如果存在）
            notification_methods = []
            try:
                notification_methods = getattr(task, 'notification_methods', None) or []
            except AttributeError:
                # 字段还不存在，使用默认值
                notification_methods = []
            if not notification_methods:
                # 为老数据兼容，从notification_type转换
                if task.notification_type == 'multi':
                    notification_methods = ['creator']  # 默认回退到创建人
                elif task.notification_type in ['creator', 'assignee', 'group', 'auto_group']:
                    notification_methods = [task.notification_type]
                else:
                    # 处理遗留的老值
                    legacy_mapping = {
                        'private': ['creator'],
                        'smart': ['assignee'], 
                        'both': ['creator', 'group']
                    }
                    notification_methods = legacy_mapping.get(task.notification_type, ['creator'])
                    
            task_info = {
                'id': task.id,
                'task_name': task.task_name,
                'user_email': task.user_email,
                'task_type': getattr(task, 'task_type', 'jira_query') or 'jira_query',
                'query_text': task.query_text,
                'frequency': task.frequency,
                'schedule_time': task.schedule_time.strftime('%H:%M') if task.schedule_time else '',
                'schedule_days': task.schedule_days,
                'notification_type': task.notification_type,
                'notification_methods': notification_methods,
                'target_group_id': task.target_group_id,
                'target_group_keyword': target_group_keyword,
                'reminder_message': task.reminder_message,
                'is_active': task.is_active,
                'created_at': task.created_at.isoformat() if task.created_at else None,
                'last_run_time': task.last_execution.isoformat() if task.last_execution else None,
                'next_execution_time': task.next_execution.isoformat() if task.next_execution else None,
                'status': 'active' if task.is_active else 'inactive'
            }
            task_list.append(task_info)
        
        return JsonResponse({
            'success': True,
            'tasks': task_list,
            'total_count': len(task_list),
            'admin_mode': admin_mode,
            'user_role': 'admin' if admin_mode else 'user'
        })
        
    except Exception as e:
        logger.error(f"获取用户任务列表失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f"获取任务列表失败: {str(e)}"
        })


@require_http_methods(["POST"])
def create_task_api(request):
    """创建定时任务API"""
    try:
        from app01.ai_module.task_scheduler import task_scheduler
        from app01.models import UserScheduledTask
        import asyncio
        import json
        
        # 解析请求数据
        data = json.loads(request.body)
        
        # 必需字段验证
        required_fields = ['task_name', 'query_text', 'schedule_time', 'frequency', 'user_email']
        for field in required_fields:
            if not data.get(field):
                return JsonResponse({
                    'success': False,
                    'error': f"缺少必需字段: {field}"
                })
        
        # 提取参数
        task_name = data['task_name']
        query_text = data['query_text']
        schedule_time = data['schedule_time']
        frequency = data['frequency']
        user_email = data['user_email']
        
        # 可选参数
        schedule_days = data.get('schedule_days', [])
        notification_types = data.get('notification_types', ['creator'])
        target_group_keyword = data.get('target_group_keyword')
        task_type = data.get('task_type', 'jira_query')
        reminder_message = data.get('reminder_message')

        # 间隔执行配置
        interval_config = None
        if frequency == 'interval':
            interval_config = {
                'start_time': data.get('interval_start_time', '09:00'),
                'end_time': data.get('interval_end_time', '18:00'),
                'interval_minutes': data.get('interval_minutes', 30)
            }
        
        # 处理通知类型 - 统一使用标准化的值
        notification_methods = notification_types if notification_types else ['creator']
        
        # 设置主要通知类型 - 优先使用第一个选择的方式，多选时使用'multi'
        if len(notification_methods) > 1:
            notification_type = 'multi'
        elif notification_methods:
            notification_type = notification_methods[0]
        else:
            notification_type = 'creator'
        
        # 处理群组关键词
        target_group_id = None
        if target_group_keyword and 'group' in notification_types:
            # 支持新的group_id格式和原有的keyword格式
            if target_group_keyword.startswith('group_id:'):
                target_group_id = target_group_keyword
            else:
                target_group_id = f"keyword:{target_group_keyword}"
        
        # 使用task_scheduler创建任务，确保通知功能正常
        try:
            # 获取用户的employee_code
            from app01.seatalk_group_manager import get_employee_codes
            email_to_code = get_employee_codes([user_email])
            employee_code = email_to_code.get(user_email)

            if not employee_code:
                return JsonResponse({
                    'success': False,
                    'error': f"无法获取用户 {user_email} 的employee_code，请确认邮箱是否正确或用户是否已离职"
                })

            # 生成web用户ID
            web_user_id = f"web_user_{employee_code}"

            # 使用task_scheduler创建任务，确保通知功能正常
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            result = loop.run_until_complete(
                task_scheduler.create_task(
                    user_id=web_user_id,
                    user_email=user_email,
                    employee_code=employee_code,
                    task_name=task_name,
                    query_text=query_text,
                    schedule_time=schedule_time,
                    frequency=frequency,
                    schedule_days=schedule_days,
                    notification_type=notification_type,
                    target_group_id=target_group_id,
                    task_type=task_type,
                    reminder_message=reminder_message,
                    notification_methods=notification_methods
                )
            )

            loop.close()

            if result['success']:
                # 如果是间隔执行，需要单独设置interval_config
                if frequency == 'interval' and interval_config:
                    try:
                        # 获取刚创建的任务并设置间隔配置
                        task_id = result.get('task_id')
                        if task_id:
                            task = UserScheduledTask.objects.get(id=task_id)
                            task.interval_config = interval_config
                            task.calculate_next_execution()  # 重新计算执行时间
                            task.save()
                    except Exception as e:
                        logger.warning(f"设置间隔配置失败: {str(e)}")

                return JsonResponse({
                    'success': True,
                    'message': f"任务 '{task_name}' 创建成功",
                    'task_id': result.get('task_id')
                })
            else:
                return JsonResponse({
                    'success': False,
                    'error': result.get('error', '创建任务失败')
                })

        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': f"创建任务失败: {str(e)}"
            })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': "请求数据格式错误"
        })
    except Exception as e:
        logger.error(f"创建任务失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f"创建任务失败: {str(e)}"
        })


@require_http_methods(["POST"])
def toggle_task_api(request, task_id):
    """切换任务状态API（启用/暂停）"""
    try:
        from app01.models import UserScheduledTask
        
        task = UserScheduledTask.objects.get(id=task_id)
        task.is_active = not task.is_active
        task.save()
        
        return JsonResponse({
            'success': True,
            'message': f"任务已{'启用' if task.is_active else '暂停'}",
            'is_active': task.is_active
        })
        
    except UserScheduledTask.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': "任务不存在"
        })
    except Exception as e:
        logger.error(f"切换任务状态失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f"操作失败: {str(e)}"
        })


@require_http_methods(["DELETE"])
def delete_task_api(request, task_id):
    """删除任务API"""
    try:
        from app01.models import UserScheduledTask
        
        task = UserScheduledTask.objects.get(id=task_id)
        task_name = task.task_name
        task.delete()
        
        return JsonResponse({
            'success': True,
            'message': f"任务 '{task_name}' 已删除"
        })
        
    except UserScheduledTask.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': "任务不存在"
        })
    except Exception as e:
        logger.error(f"删除任务失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f"删除失败: {str(e)}"
        })


@require_http_methods(["GET"])
def search_groups_api(request):
    """搜索群组API"""
    try:
        keyword = request.GET.get('keyword', '').strip()
        if not keyword:
            return JsonResponse({
                'success': True,
                'groups': []
            })
        
        from app01.models import SeatalkGroup
        from django.db.models import Q
        
        # 搜索群名包含关键字的群组
        groups = SeatalkGroup.objects.filter(
            Q(group_name__icontains=keyword)
        ).order_by('-id')[:10]  # 返回最多10个结果
        
        group_list = []
        for group in groups:
            group_list.append({
                'group_id': group.group_id,
                'group_name': group.group_name,
                'display_name': f"{group.group_name} ({group.group_id})"
            })
        
        return JsonResponse({
            'success': True,
            'groups': group_list
        })
        
    except Exception as e:
        logger.error(f"搜索群组失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f"搜索失败: {str(e)}"
        })


@require_http_methods(["PUT"])
def update_task_api(request, task_id):
    """更新任务API"""
    try:
        from app01.models import UserScheduledTask
        import json
        
        # 解析请求数据
        data = json.loads(request.body)
        
        task = UserScheduledTask.objects.get(id=task_id)
        
        # 更新字段
        if 'task_name' in data:
            task.task_name = data['task_name']
        if 'query_text' in data:
            task.query_text = data['query_text']
        if 'schedule_time' in data:
            # 对于间隔执行，不更新schedule_time，因为它使用interval_config中的时间
            if data.get('frequency') != 'interval':
                from datetime import datetime
                try:
                    # 将时间字符串转换为time对象
                    time_str = data['schedule_time']
                    if isinstance(time_str, str):
                        time_obj = datetime.strptime(time_str, '%H:%M').time()
                        task.schedule_time = time_obj
                    else:
                        task.schedule_time = time_str
                except ValueError as e:
                    logger.error(f"时间格式错误: {data['schedule_time']}, 错误: {str(e)}")
                    # 保持原有时间不变
        if 'frequency' in data:
            task.frequency = data['frequency']
        if 'schedule_days' in data:
            task.schedule_days = ','.join(map(str, data['schedule_days'])) if data['schedule_days'] else ''
        if 'reminder_message' in data:
            task.reminder_message = data['reminder_message']

        # 处理间隔执行配置
        if data.get('frequency') == 'interval':
            interval_config = {
                'start_time': data.get('interval_start_time', '09:00'),
                'end_time': data.get('interval_end_time', '18:00'),
                'interval_minutes': data.get('interval_minutes', 30)
            }
            task.interval_config = interval_config
        elif 'frequency' in data and data['frequency'] != 'interval':
            # 如果频率改为非间隔执行，清空间隔配置
            task.interval_config = None
        
        # 只有当用户邮箱真的改变时才更新user_email，不更新user_id以保持原有值
        if 'user_email' in data and data['user_email'] != task.user_email:
            task.user_email = data['user_email']
            # 注意：不更新user_id，保持原有的user_id不变
        
        # 处理通知类型和群组关键词
        if 'notification_types' in data:
            notification_types = data['notification_types']
            
            # 保存新的多选发送方式（如果字段存在）
            try:
                # 检查字段是否存在
                UserScheduledTask._meta.get_field('notification_methods')
                task.notification_methods = notification_types
                logger.info(f"🔍 成功保存notification_methods: {notification_types}")
            except Exception as e:
                # 字段不存在，跳过这个设置
                logger.warning(f"🔍 notification_methods字段不存在或保存失败: {str(e)}")
                pass
            
            # 设置主要通知类型 - 优先使用第一个选择的方式，多选时使用'multi'
            if len(notification_types) > 1:
                task.notification_type = 'multi'
            elif notification_types:
                task.notification_type = notification_types[0]
            else:
                task.notification_type = 'creator'
            
            # 根据通知类型处理群组设置
            if 'group' in notification_types and 'target_group_keyword' in data:
                target_group_keyword = data['target_group_keyword']
                if target_group_keyword:
                    # 支持新的group_id格式和原有的keyword格式
                    if target_group_keyword.startswith('group_id:'):
                        task.target_group_id = target_group_keyword
                    else:
                        task.target_group_id = f"keyword:{target_group_keyword}"
                else:
                    task.target_group_id = None
            elif 'auto_group' in notification_types:
                # 发送到查询结果所在群，设置特殊标记
                task.target_group_id = 'AUTO_DETECT'
            elif 'target_group_keyword' in data and not ('group' in notification_types):
                # 如果没有选择指定群，清空群组设置
                task.target_group_id = None
        
        # 如果更新了执行时间、频率或调度日期，重新计算下次执行时间
        if any(key in data for key in ['schedule_time', 'frequency', 'schedule_days', 'interval_start_time', 'interval_end_time', 'interval_minutes']):
            try:
                # 先保存任务以确保所有字段都已更新
                task.save()
                # 然后重新计算下次执行时间
                task.calculate_next_execution()
                task.save()  # 再次保存以更新next_execution字段
                logger.info(f"任务 {task.id} 下次执行时间已重新计算: {task.next_execution}")
            except Exception as e:
                logger.error(f"重新计算任务 {task.id} 下次执行时间失败: {str(e)}")
                # 即使计算失败，也要保存其他更新
                task.save()
        else:
            # 没有更新时间相关字段，直接保存
            task.save()

        return JsonResponse({
            'success': True,
            'message': f"任务 '{task.task_name}' 已更新"
        }, json_dumps_params={'ensure_ascii': False})
        
    except UserScheduledTask.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': "任务不存在"
        })
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': "请求数据格式错误"
        })
    except Exception as e:
        logger.error(f"更新任务失败: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f"更新失败: {str(e)}"
        })
