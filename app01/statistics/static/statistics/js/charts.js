// 修复指令类型分布图表
function renderCommandTypeChart(data) {
    const ctx = document.getElementById('commandTypeChart');
    if (!ctx) return;
    
    // 检查数据是否为空
    if (!data || data.length === 0) {
        ctx.getContext('2d').clearRect(0, 0, ctx.width, ctx.height);
        const context = ctx.getContext('2d');
        context.font = '16px Arial';
        context.fillStyle = '#666';
        context.textAlign = 'center';
        context.fillText('暂无数据', ctx.width / 2, ctx.height / 2);
        return;
    }
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: data.map(item => item.command_type || '未知'),
            datasets: [{
                data: data.map(item => item.count),
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// 修复成功率趋势图表
function renderSuccessRateChart(data) {
    const ctx = document.getElementById('successRateChart');
    if (!ctx) return;
    
    if (!data || data.length === 0) {
        ctx.getContext('2d').clearRect(0, 0, ctx.width, ctx.height);
        const context = ctx.getContext('2d');
        context.font = '16px Arial';
        context.fillStyle = '#666';
        context.textAlign = 'center';
        context.fillText('暂无数据', ctx.width / 2, ctx.height / 2);
        return;
    }
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(item => item.date),
            datasets: [{
                label: '成功率 (%)',
                data: data.map(item => item.success_rate),
                borderColor: '#36A2EB',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `成功率: ${context.parsed.y}%`;
                        }
                    }
                }
            }
        }
    });
}