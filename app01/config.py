import os
from pathlib import Path

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent

# 部署相关的路径配置
DEPLOY_PATHS = {
    # vue-admin项目路径
    'VUE_ADMIN_DIR': os.getenv('VUE_ADMIN_DIR', '/data/chatbot-ar-fe'),
    
    # autodeploy项目路径
    'AUTODEPLOY_DIR': os.getenv('AUTODEPLOY_DIR', '/data/chatbot-ar-be'),
}

# 创建必要的目录
def ensure_directories():
    """确保所有必要的目录都存在"""
    for path in DEPLOY_PATHS.values():
        if path:
            os.makedirs(path, exist_ok=True)
            app01_path = os.path.join(path, 'app01')
            os.makedirs(app01_path, exist_ok=True)

class Config:
    #   基础配置
    branch = {
        "test": "origin/test",
        'master': "origin/master",
        "uat": "origin/uat",
        "release": "origin/release"
    }

    cids = {
        "single": "SG",
        "all": "ID,MY,PH,SG,TW,TH,VN,BR,MX,CO,CL"
    }

    mrMapChannel = {
        #Channel 仓库路径和项目ID对应表
        "web-chatbot-csat": "48901",
        "web-csat-rn": "85497",
        "channel-config": "32285",
        "webform": "26240",
        "webform-client": "38853",
        "case-tracking-rn": "26236",
        "service-portal": "60031",
        "help-center-agent": "27371",
        "help-center": "26239",
        "help-center-node": "61965",
        "csat-client": "61877",
        "channel-email": "63362",
        "channel-form": "70970",
        "channel-call": "70972",
        "call": "33520",
        "channel-socialmedia": "77227",
        "socialmedia": "30505",
        "casetracking": "33361",
        "comment": "43928",
        "helpcenter": "25887",
        "chat": "25889",
        "chatbot-chat": "44700",
        "eventfactory": "58903",
        "web-chatbot": "9674",
        "cs-chat": "19635",
        "live-chat": "70469",
    }

    srvMap = {
        #仓库地址和服务对应表
        "https://git.garena.com/shopee/chatbot/knowledge-platform": ["shopee-knowledgeplatform-admin-test","shopee-knowledgeplatform-api-test","shopee-knowledgeplatform-qa_tools-test","shopee-knowledgeplatform-offline-test"],
        "https://git.garena.com/shopee/chatbot/platform": ["shopee-chatbotcommon-shopconsole-test",
                                                           "shopee-chatbotcommon-rulebaseservice-test",
                                                           "shopee-chatbot-rulebaseservice-test",
                                                           "shopee-chatbotcommon-productrecommend-test","shopee-chatbot-websocketgwy-test",
                                                           "shopee-chatbotcommon-logic-test","shopee-chatbotcommon-msgdetection-test"
                                                           ],
        "https://git.garena.com/shopee/chatbot/web-microfe-tmc": ["shopee-chatbotcommon-tmcsaasstatic-test",
                                                                  "shopee-chatbot-tmcstatic-test"],
        "https://git.garena.com/shopee/chatbot/operation-analysis-service": ["shopee-chatbot-opanalysis-test"],
        "https://git.garena.com/shopee/chatbot/message-service": ["shopee-chatbot-messageasynctask-test",
                                                                  "shopee-chatbot-messageservice-test",
                                                                  "shopee-chatbot-messageverification-test"],
        "https://git.garena.com/shopee/chatbot/annotation-saas": ["shopee-chatbotcommon-apasynctask-test",
                                                                  "shopee-chatbotcommon-apdataproxy-test",
                                                                  "shopee-chatbotcommon-aptimetask-test",
                                                                  "shopee-chatbotcommon-apadmin-test"],
        "https://git.garena.com/shopee/chatbot/web-microfe-annotation-saas": [
            "shopee-chatbotcommon-apadminsaasstatic-test"],
        "https://git.garena.com/shopee/chatbot/dashboard-web": ["shopee-chatbot-dashboardstatic-test"],
        "https://git.garena.com/shopee/chatbot/web-ssar": ["shopee-csdata-ssarfe-test"],
        "https://git.garena.com/shopee/chatbot/web-microfe-operation-portal": [
            "shopee-csdata-operationadminstatic-test"],
        "https://git.garena.com/shopee/chatbot/web-chatbot-admin-saas": [
            "shopee-chatbotcommon-admincommonsaasstatic-test", "shopee-chatbotcommon-adminsaasstatic-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-admin": ["shopee-chatbotcommon-adminservice-test",
                                                                "shopee-chatbot-adminservice-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-adminasynctask": ["shopee-chatbot-adminasynctask-test",
                                                                         "shopee-chatbotcommon-adminasynctask-test"],
        "https://git.garena.com/shopee/marketing/chatbot": ["shopee-chatbot-api-test"],
        "https://git.garena.com/shopee/chatbot/mp-chatbot-admin":["shopee-chatbot-admin-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-asynctask": ["shopee-chatbot-asynctask-test",
                                                                    "shopee-chatbotcommon-asynctask-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-botapi": ["shopee-chatbot-botapi-test",
                                                                 "shopee-chatbotcommon-botapi-test"],
        "https://git.garena.com/shopee/chatbot/dialogue-management": ["shopee-chatbot-dm-test",
                                                                      "shopee-chatbotcommon-dm-test"],
        "https://git.garena.com/shopee/chatbot/intent-service": ["shopee-chatbot-intent-test",
                                                                 "shopee-chatbotcommon-intent-test"],
        "https://git.garena.com/shopee/chatbot/annotation": ["shopee-annotation-admin-test",

                                                             "shopee-annotation-asynctask-test",
                                                             "shopee-chatbotcommon-annotationasynctask-test",
                                                             "shopee-annotation-dataproxy-test",
                                                             "shopee-chatbotcommon-annotationdataproxy-test",
                                                             "shopee-annotation-timetask-test",
                                                             "shopee-chatbotcommon-annotationtimetask-test"],
        "https://git.garena.com/shopee/chatbot/kb/knowledge-base": ["shopee-knowledgebase-admin-test",
                                                                    "shopee-chatbotcommon-kbadmin-test",
                                                                    "shopee-knowledgebase-api-test",
                                                                    "shopee-chatbotcommon-kbapi-test",
                                                                    "shopee-knowledgebase-asynctask-test",
                                                                    "shopee-chatbotcommon-kbasynctask-test"],
        "https://git.garena.com/shopee/chatbot/task-flow": [
            "shopee-taskflow-apiproxy-test",
            "shopee-chatbotcommon-tfapiproxy-test",
            "shopee-taskflow-editor-test",
            "shopee-chatbotcommon-tfvariateserving-test",
            "shopee-chatbotcommon-tfeditor-test",
            "shopee-taskflow-taskflowserving-test",
            "shopee-chatbotcommon-tfserving-test",
            "shopee-taskflow-variateserving-test",
            "shopee-taskflow-taskflowsop-test",
            "shopee-taskflow-apiflowserving-test"],
        "https://git.garena.com/shopee/chatbot/intent-clarification": ["shopee-chatbot-intentclarification-test"],
        "https://git.garena.com/shopee/chatbot/liveagent-control": ["shopee-chatbot-agentcontrol-test",
                                                                    "shopee-chatbotcommon-agentcontrol-test"],
        "https://git.garena.com/shopee/chatbot/auto-training/auto-training": ["shopee-chatbot-autotraining-test"],
        "https://git.garena.com/shopee/chatbot/feature-center": ["shopee-chatbot-featurecenter-test",
                                                                 "shopee-chatbotcommon-featurecenter-test"],
        "https://git.garena.com/shopee/chatbot/audit-log/audit-log": ["shopee-chatbot-auditlog-test"],
        "https://git.garena.com/shopee/chatbot/admin-config-service": ["shopee-chatbotcommon-adminconfigservice-test",
                                                                       "shopee-chatbot-adminconfigservice-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-context": ["shopee-chatbot-context-test",
                                                                  "shopee-chatbotcommon-context-test"],
        "https://git.garena.com/shopee/marketing/web-chatbot": ["shopee-chatbot-chatbotstatic-test",
                                                                "shopee-chatbotcommon-static-test"],
        "https://git.garena.com/shopee/chatbot/web-chatbot-rn": ["shopee-chatbot-chatbotrnstatic-test"],
        "https://git.garena.com/shopee/marketing/web-chatbot-admin": ["shopee-chatbot-adminstatic-test",
                                                                      "shopee-chatbotcommon-adminstatic-test"],
        "https://git.garena.com/shopee/seller-fe/cs-chat": ["shopee-cschat-h5-test"],
        "https://git.garena.com/shopee/chatbot/web-microfe-annotation-portal": ["shopee-annotation-adminstatic-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-chat": ["shopee-cschat-chat-test", "shopee-cschat-wschat-test"],
        "https://git.garena.com/shopee/chatbot/kb/web-microfe-knowledge-base": [
            "shopee-knowledgebase-adminstatic-test", "shopee-chatbotcommon-kbadminsaasstatic-test",
            "shopee-chatbotcommon-kbadmincommonsaasstatic-test"],
        "https://git.garena.com/shopee/chatbot/chatflow-editor": ["shopee-chatbotcommon-tfeadminsaasstatic-test",
                                                                  "shopee-taskflow-adminstatic-test",
                                                                  "shopee-chatbotcommon-tfeadmincommonsaasstatic-test"],
        "https://git.garena.com/shopee/chatbot/web-chatbot-csat": ["shopee-chatbot-csatstatic-test"],
        "https://git.garena.com/shopee/chatbot/auto-training-portal": ["shopee-autotrainingportal-adminstatic-test"],
        "https://git.garena.com/shopee/chatbot/metric-service": ["shopee-csdata-alert-test",
                                                                 "shopee-csdata-interface-test",
                                                                 "shopee-csdata-realtimedashboard-test",
                                                                 "shopee-csdata-metricservice-test",
                                                                 "shopee-csdata-dataplatform-test"],
        "https://git.garena.com/shopee/chatbot/data-service": ["shopee-csdata-dataservice-test"],
        "https://git.garena.com/shopee/chatbot/report-service": ["shopee-chatbot-report-test",
                                                                 "shopee-chatbot-reportasynctask-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-ordercard": ["shopee-chatbot-ordercard-test"],
        "https://git.garena.com/shopee/chatbot/nlu-service": ["shopee-chatbot-nlu-test",
                                                              "shopee-chatbotcommon-nlu-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-prompt":["shopee-chatbotcommon-promptservice-test","shopee-chatbotcommon-promptmanagements-test"],
        "https://git.garena.com/shopee/chatbot/insights":["shopee-chatbot-insights-test","shopee-chatbotcommon-insightssaasstatic-test"],
        "https://git.garena.com/shopee/chatbot/api-store":["shopee-chatbot-apiadmin-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-platform-portal":["shopee-knowledgeplatform-adminstatic-test"],

    }

    srvMap_failmsg_chatbot = {
        "https://git.garena.com/shopee/chatbot/knowledge-platform": ["shopee-knowledgeplatform-admin-test","shopee-knowledgeplatform-api-test","shopee-knowledgeplatform-qa_tools-test","shopee-knowledgeplatform-offline-test"],
        "https://git.garena.com/shopee/chatbot/message-service": ["shopee-chatbot-messageasynctask-test",
                                                                  "shopee-chatbot-messageservice-test",
                                                                  "shopee-chatbot-messageverification-test"],
        "https://git.garena.com/shopee/chatbot/dashboard-web": ["shopee-chatbot-dashboardstatic-test"],
        "https://git.garena.com/shopee/chatbot/web-chatbot-admin-saas": [
            "shopee-chatbotcommon-admincommonsaasstatic-test", "shopee-chatbotcommon-adminsaasstatic-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-admin": ["shopee-chatbotcommon-adminservice-test",
                                                                "shopee-chatbot-adminservice-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-adminasynctask": ["shopee-chatbot-adminasynctask-test",
                                                                         "shopee-chatbotcommon-adminasynctask-test"],
        "https://git.garena.com/shopee/marketing/chatbot": ["shopee-chatbot-api-test"],
        "https://git.garena.com/shopee/chatbot/mp-chatbot-admin":["shopee-chatbot-admin-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-asynctask": ["shopee-chatbot-asynctask-test",
                                                                    "shopee-chatbotcommon-asynctask-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-botapi": ["shopee-chatbot-botapi-test",
                                                                 "shopee-chatbotcommon-botapi-test"],
        "https://git.garena.com/shopee/chatbot/dialogue-management": ["shopee-chatbot-dm-test",
                                                                      "shopee-chatbotcommon-dm-test"],
        "https://git.garena.com/shopee/chatbot/intent-service": ["shopee-chatbot-intent-test",
                                                                 "shopee-chatbotcommon-intent-test"],
        "https://git.garena.com/shopee/chatbot/annotation": ["shopee-annotation-admin-test",

                                                             "shopee-annotation-asynctask-test",
                                                             "shopee-chatbotcommon-annotationasynctask-test",
                                                             "shopee-annotation-dataproxy-test",

                                                             "shopee-annotation-timetask-test",
                                                             "shopee-chatbotcommon-annotationtimetask-test"],
        "https://git.garena.com/shopee/chatbot/kb/knowledge-base": ["shopee-knowledgebase-admin-test",
                                                                    "shopee-chatbotcommon-kbadmin-test",
                                                                    "shopee-knowledgebase-api-test",
                                                                    "shopee-chatbotcommon-kbapi-test",
                                                                    "shopee-knowledgebase-asynctask-test",
                                                                    "shopee-chatbotcommon-kbasynctask-test"],
        "https://git.garena.com/shopee/chatbot/task-flow": [
            "shopee-taskflow-apiproxy-test",
            "shopee-chatbotcommon-tfapiproxy-test",
            "shopee-taskflow-editor-test",
            "shopee-chatbotcommon-tfvariateserving-test",
            "shopee-chatbotcommon-tfeditor-test",
            "shopee-taskflow-taskflowserving-test",
            "shopee-chatbotcommon-tfserving-test",
            "shopee-taskflow-variateserving-test", 
            "shopee-taskflow-taskflowsop-test",
            "shopee-taskflow-apiflowserving-test"],
        "https://git.garena.com/shopee/chatbot/intent-clarification": ["shopee-chatbot-intentclarification-test"],
        "https://git.garena.com/shopee/chatbot/liveagent-control": ["shopee-chatbot-agentcontrol-test",
                                                                    "shopee-chatbotcommon-agentcontrol-test"],
        "https://git.garena.com/shopee/chatbot/auto-training/auto-training": ["shopee-chatbot-autotraining-test"],
        "https://git.garena.com/shopee/chatbot/feature-center": ["shopee-chatbot-featurecenter-test",
                                                                 "shopee-chatbotcommon-featurecenter-test"],
        "https://git.garena.com/shopee/chatbot/audit-log/audit-log": ["shopee-chatbot-auditlog-test"],
        "https://git.garena.com/shopee/chatbot/admin-config-service": ["shopee-chatbotcommon-adminconfigservice-test",
                                                                       "shopee-chatbot-adminconfigservice-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-context": ["shopee-chatbot-context-test",
                                                                  "shopee-chatbotcommon-context-test"],
        "https://git.garena.com/shopee/marketing/web-chatbot": ["shopee-chatbot-chatbotstatic-test"],
        "https://git.garena.com/shopee/chatbot/web-chatbot-rn": ["shopee-chatbot-chatbotrnstatic-test"],
        "https://git.garena.com/shopee/marketing/web-chatbot-admin": ["shopee-chatbot-adminstatic-test",
                                                                      "shopee-chatbotcommon-adminstatic-test"],
        "https://git.garena.com/shopee/seller-fe/cs-chat": ["shopee-cschat-h5-test"],
        "https://git.garena.com/shopee/chatbot/web-microfe-annotation-portal": ["shopee-annotation-adminstatic-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-chat": ["shopee-cschat-chat-test", "shopee-cschat-wschat-test"],
        "https://git.garena.com/shopee/chatbot/kb/web-microfe-knowledge-base": [
            "shopee-knowledgebase-adminstatic-test", "shopee-chatbotcommon-kbadminsaasstatic-test",
            "shopee-chatbotcommon-kbadmincommonsaasstatic-test"],
        "https://git.garena.com/shopee/chatbot/chatflow-editor": ["shopee-chatbotcommon-tfeadminsaasstatic-test",
                                                                  "shopee-chatbotcommon-tfeadminstatic-test",
                                                                  "shopee-taskflow-adminstatic-test",
                                                                  "shopee-chatbotcommon-tfeadmincommonsaasstatic-test"],
        "https://git.garena.com/shopee/chatbot/web-chatbot-csat": ["shopee-chatbot-csatstatic-test"],
        "https://git.garena.com/shopee/chatbot/auto-training-portal": ["shopee-autotrainingportal-adminstatic-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-ordercard": ["shopee-chatbot-ordercard-test"],
        "https://git.garena.com/shopee/chatbot/nlu-service": ["shopee-chatbot-nlu-test",
                                                              "shopee-chatbotcommon-nlu-test"],
        "https://git.garena.com/shopee/chatbot/operation-analysis-service": ["shopee-chatbot-opanalysis-test"],
        #"https://git.garena.com/shopee/chatbot/insights":["shopee-chatbot-insights-test", "shopee-chatbotcommon-insightssaasstatic-test"],
        "https://git.garena.com/shopee/chatbot/api-store":["shopee-chatbot-apiadmin-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-platform-portal":["shopee-knowledgeplatform-adminstatic-test"],
        "https://git.garena.com/shopee/chatbot/chatbot-shared-rc":["shopee-chatbot-h5mmfchatbotsharedrcstatic-test"],
        "https://git.garena.com/shopee/chatbot/mmf-chabot-console":["shopee-chatbot-mmfchatbotconsole-test"]
    }

    autoMap = {
        "https://git.garena.com/shopee/chatbot/chatbot-adminasynctask": ["chatbot_admin"
                                                                         ],
        "https://git.garena.com/shopee/chatbot/admin-config-service": ["chatbot_admin", "chatbot_botapi", "chatbot_api",
                                                                       "chatbot_ds"],
        "https://git.garena.com/shopee/chatbot/annotation": ["chatbot_ap"],

        "https://git.garena.com/shopee/chatbot/chatbot-botapi": ["chatbot_botapi", "chatbot_api", "chatbot_ds"],

        "https://git.garena.com/shopee/chatbot/chatbot-chat": ["chatbot_chat", "chatbot_botapi", "chatbot_api"
                                                               ],
        "https://git.garena.com/shopee/chatbot/kb/knowledge-base": ["chatbot_kb"],
        "https://git.garena.com/shopee/chatbot/task-flow": ["chatbot_tfe"],

        "https://git.garena.com/shopee/chatbot/chatbot-admin": ["chatbot_admin"],

        "https://git.garena.com/shopee/marketing/chatbot": ["chatbot_admin"],
        "https://git.garena.com/shopee/chatbot/chatbot-asynctask": ["chatbot_botapi", "chatbot_api", "chatbot_ds"],
        "https://git.garena.com/shopee/chatbot/dialogue-management": ["chatbot_botapi", "chatbot_api", "chatbot_ds"],
        "https://git.garena.com/shopee/chatbot/intent-service": ["chatbot_botapi", "chatbot_api", "chatbot_ds"],
        "https://git.garena.com/shopee/chatbot/intent-clarification": ["chatbot_botapi", "chatbot_api", "chatbot_ds"],
        "https://git.garena.com/shopee/chatbot/liveagent-control": ["chatbot_botapi", "chatbot_api", "chatbot_ds"],
        "https://git.garena.com/shopee/chatbot/auto-training/auto-training": ["chatbot_admin"],
        "https://git.garena.com/shopee/chatbot/feature-center": ["chatbot_botapi", "chatbot_api", "chatbot_ds"],
        "https://git.garena.com/shopee/chatbot/audit-log/audit-log": ["chatbot_admin"],
        "https://git.garena.com/shopee/chatbot/chatbot-context": ["chatbot_botapi", "chatbot_api", "chatbot_ds"],
        "https://git.garena.com/shopee/chatbot/chatflow-editor": ["chatbot_tfe"],

        "https://git.garena.com/shopee/chatbot/auto-training-portal": ["chatbot_admin"],
        "https://git.garena.com/shopee/chatbot/metric-service": ["csalert", "chatbot_admin/tmc",
                                                                 "chatbot_admin/self_defined_topics",
                                                                 "chatbot_admin/chat_transcript"],
        "https://git.garena.com/shopee/chatbot/api-store":["api-store"],
        "https://git.garena.com/shopee/chatbot/chatbot-platform-portal":["chatbot-platform-portal"]
    }

    mrMapdata = {
         #Data 仓库路径和项目ID对应表
        "metric-service": "61932",
        "web-microfe-operation-portal ": "64764",
        "report-service": "46755",
        "web-ssar": "74385",
        "web-dashboard": "75883",
        "data-service": "43562",
        "web-microfe-insights": "66531",
    }

    mrMap = {
         #Chatbot 仓库路径和项目ID对应表
        "operation-analysis-client": "82481",
        "operation-analysis-service": "82480",
        "platform": "75599",
        "message-service": "85616",
        "web-chatbot-admin-saas": "70768",
        "web-microfe-tmc": "70311",
        "web-microfe-operation-portal": "64764",
        "chatbot-pilot-api": "19408",
        "chatbot-context": "73247",
        "chatbot": "8530",
        "mp-chatbot-admin":"97938",
        "web-chatbot-admin": "11552",
        'chatbot-asynctask': "60418",
        "chatbot-botapi": "49709",
        "dialogue-management": "49691",
        "intent-service": "49700",
        "web-chatbot": "9674",
        "chatbot-chat": "44700",
        "seller-fe/cs-chat": "19635",
        "web-microfe-annotation-portal": "43201",
        "annotation": "40024",
        "web-microfe-knowledge-base": "42033",
        "knowledge-base": "42364",
        "chatflow-editor": "48914",
        "task-flow": "49345",
        "chatbot-model": "53653",
        "intent-clarification": "52653",
        "report-service": "46755",
        "data-service": "43562",
        "web-chatbot-csat": "48901",
        "alert": "46457",
        "metric-service": "61932",
        "liveagent-control": "59586",
        "auto-training-portal": "64150",
        "auto-training": "64453",
        "feature-center": "60270",
        "audit-log": "67371",
        "admin-config-service": "70863",
        "seller-server/cs/cs": "63066",
        "chatbot-ordercard": "56776",
        "adminservice": "70143",
        "adminasynctask": "49862",
        "seller-server/pilot/api": "19408",
        "nlu-service": "80604",
        "web-microfe-annotation-saas": "79462",
        "annotation-saas": "79926",
        "chatbot-qa-cicd": "71019",
        "chatbot-prompt":"101799",
        "knowledge-platform": "106652",
        "api-store":"111377",
        "chatbot-platform-portal":"109186"
    }
    srv2id = {
        #Chatbot服务和和项目ID对应表
        "shopee-chatbotcommon-intentclarification":"52653",
        "shopee-chatbotcommon-productrecommend": "75599",
        "shopee-chatbotcommon-rulebaseservice": "75599",
        "shopee-chatbot-rulebaseservice": "75599",
        "shopee-chatbotcommon-shopconsole": "75599",
        "shopee-chatbotcommon-modelgw": "63066",
        "shopee-chatbot-modelgw": "63066",
        "shopee-chatbot-recallmanager": "63066",
        "shopee-chatbot-recallservice": "63066",
        "shopee-chatbot-opanalysis": "82480",
        "shopee-csdata-dataservice": "43562",
        "shopee-csdata-alert": "61932",
        "shopee-csdata-insurancerealtimedashboard": "61932",
        "shopee-csdata-interface": "61932",
        "shopee-csdata-metricservice": "61932",
        "shopee-csdata-realtimedashboard": "61932",
        "shopee-csdata-report": "46755",
        "shopee-csdata-reportasynctask": "46755",
        "shopee-csdata-webdashboard": "75883",
        "shopee-chatbot-insights": "66531",
        "shopee-chatbot-featureapiproxy": "63066",
        "shopee-chatbot-messageverification": "85616",
        "shopee-chatbot-messageservice": "85616",
        "shopee-chatbot-messageasynctask": "85616",
        "shopee-chatbot-dashboardstatic": "82465",
        "shopee-chatbot-nlu": "80604",
        "shopee-chatbotcommon-nlu": "80604",
        "shopee-csdata-ssarfe": "74385",
        "shopee-chatbotcommon-admincommonsaasstatic": "70768",
        "shopee-chatbotcommon-adminsaasstatic": "70768",
        "shopee-chatbotcommon-adminservice": "70143",
        "shopee-chatbot-adminservice": "70143",
        "shopee-chatbot-adminasynctask": "49862",
        "shopee-chatbotcommon-adminasynctask": "49862",
        "shopee-chatbot-admin": "97938",
        "shopee-chatbotcommon-apadminsaasstatic": "79462",
        "shopee-chatbotcommon-admin": "8530",
        "shopee-chatbot-api": "8530",
        "shopee-chatbot-asynctask": "60418",
        "shopee-chatbotcommon-asynctask": "60418",
        "shopee-chatbot-botapi": "49709",
        "shopee-chatbotcommon-botapi": "49709",
        "shopee-chatbot-dm": "49691",
        "shopee-chatbotcommon-dm": "49691",
        "shopee-chatbot-intent": "49700",
        "shopee-chatbotcommon-intent": "49700",
        "shopee-annotation-admin": "40024",
        "shopee-chatbotcommon-annotationadmin": "40024",
        "shopee-annotation-asynctask": "40024",
        "shopee-chatbotcommon-annotationasynctask": "40024",
        "shopee-annotation-dataproxy": "40024",
        "shopee-chatbotcommon-annotationdataproxy": "40024",
        "shopee-annotation-timetask": "40024",
        "shopee-csdata-operationadminstatic": "64764",
        "shopee-chatbotcommon-annotationtimetask": "40024",
        "shopee-knowledgebase-admin": "42364",
        "shopee-chatbotcommon-kbadmin": "42364",
        "shopee-knowledgebase-api": "42364",
        "shopee-chatbotcommon-kbapi": "42364",
        "shopee-knowledgebase-asynctask": "42364",
        "shopee-chatbotcommon-kbasynctask": "42364",
        "shopee-taskflow-apiproxy": "49345",
        "shopee-chatbotcommon-tfapiproxy": "49345",
        "shopee-taskflow-editor": "49345",
        "shopee-chatbotcommon-tfvariateserving": "49345",
        "shopee-chatbotcommon-tfeditor": "49345",
        "shopee-taskflow-taskflowserving": "49345",
        "shopee-taskflow-taskflowsop": "49345",
        "shopee-chatbotcommon-tfserving": "49345",
        "shopee-taskflow-variateserving": "49345",
        "shopee-chatbot-intentclarification": "52653",
        "shopee-chatbot-agentcontrol": "59586",
        "shopee-chatbotcommon-agentcontrol": "59586",
        "shopee-chatbot-autotraining": "64453",
        "shopee-chatbot-featurecenter": "60270",
        "shopee-chatbotcommon-featurecenter": "60270",
        "shopee-chatbot-auditlog": "67371",
        "shopee-chatbotcommon-adminconfigservice": "70863",
        "shopee-chatbot-adminconfigservice": "70863",
        "shopee-chatbot-context": "73247",
        "shopee-chatbotcommon-context": "73247",
        "shopee-chatbot-chatbotstatic": "91319",
        "shopee-chatbot-chatbotrnstatic": "9674",
        "shopee-chatbotcommon-static": "9674",
        "shopee-chatbot-adminstatic": "11552",
        "shopee-cschat-h5": "19635",
        "shopee-annotation-adminstatic": "43201",
        "shopee-chatbotcommon-adminstatic": "11552",
        "shopee-cschat-chat": "44700",
        "shopee-cschat-wschat": "44700",
        "shopee-knowledgebase-adminstatic": "42033",
        "shopee-taskflow-adminstatic": "48914",
        "shopee-chatbot-csatstatic": "48901",
        "shopee-autotrainingportal-adminstatic": "64150",
        "shopee-csdata-dataplatform": "61932",
        "shopee-csinfra-dataservice": "43562",
        "shopee-chatbot-report": "46755",
        "shopee-chatbot-reportasynctask": "46755",
        "shopee-chatbot-ordercard": "56776",
        "shopee-chatbotcommon-kbadmincommonsaasstatic": "42033",
        "shopee-chatbotcommon-kbadminsaasstatic": "42033",
        "shopee-chatbotcommon-kbadminstatic": "42033",
        "shopee-chatbotcommon-tfeadmincommonsaasstatic": "48914",
        "shopee-chatbotcommon-tfeadminsaasstatic": "48914",
        "shopee-chatbotcommon-tfeadminstatic": "48914",
        "shopee-chatbotcommon-apasynctask": "79926",
        "shopee-chatbotcommon-apdataproxy": "79926",
        "shopee-chatbotcommon-aptimetask": "79926",
        "shopee-chatbotcommon-apadmin": "79926",
        "shopee-chatbot-tmcstatic": "70311",
        "shopee-chatbotcommon-tmcsaasstatic": "70311",
        "shopee-chatbotcommon-promptmanagements":"101799",
        "shopee-chatbotcommon-promptservice":"101799",
        "shopee-knowledgeplatform-admin": "106652",
        "shopee-knowledgeplatform-api": "106652",
        "shopee-knowledgeplatform-qa_tools": "106652",
        "shopee-chatbot-apiadmin":"111377",
        "shopee-chatbot-shared-rc":"112042"

    }
    #消息黑名单
    msgBlackList = ["shopee-csdata-dataplatform-staging", "shopee-chatbotcommon-adminsaasstatic-test",
                    "shopee-chatbotcommon-annotationadmin-test", "shopee-chatbot-report-staging",
                    "shopee-chatbot-reportasynctask-staging","shopee-csdata-alert-staging",
                    "shopee-csdata-interface-staging", 
                    "shopee-chatbot-adminservice-staging",
                    "shopee-chatbotcommon-agentcontrol-test","shopee-chatbotcommon-agentcontrol-uat","shopee-chatbotcommon-agentcontrol-staging"]

# JIRA配置
JIRA_TOKEN = "NTA4ODQ0NTE5OTQ2OpMq9xXh446Fs5eBqdDx9mM4tLaj"  # liang.tang's token

# SeaTalk配置
import threading
import time
from typing import Optional

# 令牌缓存相关配置
_seatalk_token_cache = {
    'token': None,
    'expires_at': 0,
    'lock': threading.Lock()
}
TOKEN_CACHE_DURATION = 3600  # 1小时缓存时间
TOKEN_RETRY_DELAY = 1  # 重试延时（秒）

def get_seatalk_access_token(max_retries: int = 3) -> Optional[str]:
    """
    动态获取SeaTalk访问令牌，支持缓存和重试机制
    
    Args:
        max_retries: 最大重试次数
    
    Returns:
        str: 访问令牌，失败返回None
    """
    import requests
    import json
    from icecream import ic
    
    current_time = time.time()
    
    # 检查缓存
    with _seatalk_token_cache['lock']:
        if (_seatalk_token_cache['token'] and 
            current_time < _seatalk_token_cache['expires_at']):
            ic("使用缓存的Seatalk访问令牌")
            return _seatalk_token_cache['token']
    
    # 获取新令牌
    token_url = "https://openapi.seatalk.io/auth/app_access_token"
    headers = {
        'content-type': "application/json",
    }
    param = {
        "app_id": "NzM5MzEzODYyNzk5",
        "app_secret": "3dcplTo_Q3zIQCJLOs_FLzKZNJASevOT"
    }
    
    for attempt in range(max_retries):
        try:
            ic(f"获取Seatalk访问令牌 (尝试 {attempt + 1}/{max_retries})")
            r = requests.post(url=token_url, json=param, headers=headers, timeout=10)
            r.raise_for_status()  # 检查HTTP状态码
            
            results = json.loads(r.text)
            
            # 检查API响应
            if 'app_access_token' not in results:
                error_msg = f"API响应中缺少app_access_token字段: {results}"
                ic(error_msg)
                if attempt < max_retries - 1:
                    time.sleep(TOKEN_RETRY_DELAY * (attempt + 1))  # 递增延时
                    continue
                return None
            
            token = results["app_access_token"]
            
            # 更新缓存
            with _seatalk_token_cache['lock']:
                _seatalk_token_cache['token'] = token
                _seatalk_token_cache['expires_at'] = current_time + TOKEN_CACHE_DURATION
            
            ic("Seatalk访问令牌获取成功并已缓存")
            return token
            
        except requests.exceptions.RequestException as e:
            ic(f"网络请求失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
        except json.JSONDecodeError as e:
            ic(f"JSON解析失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
        except Exception as e:
            ic(f"获取SeaTalk访问令牌失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
        
        # 重试延时
        if attempt < max_retries - 1:
            sleep_time = TOKEN_RETRY_DELAY * (attempt + 1)
            ic(f"等待 {sleep_time}秒 后重试...")
            time.sleep(sleep_time)
    
    ic("所有重试尝试都失败，无法获取Seatalk访问令牌")
    return None

# 为了兼容AI模块的静态导入，提供一个默认值
SEATALK_ACCESS_TOKEN = None  # 将在运行时动态获取
