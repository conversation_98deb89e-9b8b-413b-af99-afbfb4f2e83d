<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workee 统计面板</title>
    <!-- 引入Element Plus样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- 引入ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #303133;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #409eff;
        }
        .header .refresh-btn {
            padding: 8px 16px;
            background-color: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .header .refresh-btn:hover {
            background-color: #66b1ff;
        }

        .date-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .date-selector label {
            color: #606266;
            font-weight: 500;
        }

        .date-selector select {
            padding: 6px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background-color: white;
            font-size: 14px;
            cursor: pointer;
        }

        .date-selector select:focus {
            outline: none;
            border-color: #409eff;
        }

        /* Tooltip样式 */
        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip .tooltip-text {
            visibility: hidden;
            width: 300px;
            background-color: #333;
            color: #fff;
            text-align: left;
            border-radius: 6px;
            padding: 8px 12px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            line-height: 1.4;
            word-wrap: break-word;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }

        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        /* 错误信息截断样式 */
        .error-message {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .error-message.full {
            max-width: none;
            white-space: normal;
            word-wrap: break-word;
        }

        /* 任务类型标识样式 */
        .task-type-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .task-type-badge.system-task {
            background-color: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }

        .task-type-badge.user-task {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        /* 状态标识样式 */
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-badge.error {
            background-color: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }

        .status-badge.success {
            background-color: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .metrics-cards, .metrics-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        /* 响应式布局 */
        @media (max-width: 1200px) {
            .metrics-cards, .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .metrics-cards, .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
        .metric-card {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
            text-align: center;
            min-height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .metric-value {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
            line-height: 1.2;
        }
        .metric-label {
            font-size: 14px;
            color: #909399;
        }
        .success { color: #67c23a; }
        .warning { color: #e6a23c; }
        .danger { color: #f56c6c; }
        .info { color: #409eff; }
        
        .charts-section {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        .chart-card {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .chart-title {
            font-size: 16px;
            margin-top: 0;
            margin-bottom: 15px;
            color: #303133;
        }
        .chart-container {
            height: 300px;
        }
        
        .data-tables {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        .table-card {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .table-title {
            font-size: 16px;
            margin-top: 0;
            margin-bottom: 15px;
            color: #303133;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ebeef5;
        }
        th {
            font-weight: bold;
            color: #606266;
            background-color: #f5f7fa;
        }
        .activity-log {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .log-title {
            font-size: 16px;
            margin-top: 0;
            margin-bottom: 15px;
            color: #303133;
        }
        .log-entry {
            padding: 10px;
            border-bottom: 1px solid #ebeef5;
        }
        .log-entry:last-child {
            border-bottom: none;
        }
        .log-time {
            font-size: 12px;
            color: #909399;
        }
        .log-message {
            margin-top: 5px;
        }
        .tabs {
            margin-bottom: 20px;
        }
        .tab {
            display: inline-block;
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom-color: #409eff;
            color: #409eff;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }

        /* 密码保护样式 */
        .password-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .password-dialog {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            text-align: center;
            min-width: 300px;
        }

        .password-dialog h2 {
            margin-bottom: 20px;
            color: #303133;
        }

        .password-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 16px;
            margin-bottom: 20px;
            box-sizing: border-box;
        }

        .password-input:focus {
            outline: none;
            border-color: #409eff;
        }

        .password-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin-right: 10px;
        }

        .password-button:hover {
            background: #66b1ff;
        }

        .password-error {
            color: #f56c6c;
            margin-top: 10px;
            font-size: 14px;
        }

        /* 分页组件样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            gap: 10px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            background: white;
            color: #606266;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .pagination button:hover:not(:disabled) {
            background: #f5f7fa;
            border-color: #c0c4cc;
        }

        .pagination button:disabled {
            background: #f5f7fa;
            color: #c0c4cc;
            cursor: not-allowed;
        }

        .pagination button.active {
            background: #409eff;
            color: white;
            border-color: #409eff;
        }

        .pagination .page-info {
            color: #606266;
            font-size: 14px;
            margin: 0 10px;
        }

        /* 原始输入单元格样式 */
        .raw-input-cell {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 改善表格tooltip显示 */
        table td[title] {
            position: relative;
        }

        table td[title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            max-width: 300px;
            word-wrap: break-word;
            white-space: normal;
        }

        table td[title]:hover::before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%) translateY(100%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }
        .footer {
            text-align: center;
            padding: 20px 0;
            color: #909399;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- 密码保护层 -->
    <div id="password-overlay" class="password-overlay">
        <div class="password-dialog">
            <h2>🔐 访问验证</h2>
            <p>请输入访问密码：</p>
            <input type="password" id="password-input" class="password-input" placeholder="请输入密码" />
            <br>
            <button onclick="checkPassword()" class="password-button">确认</button>
            <div id="password-error" class="password-error" style="display: none;"></div>
        </div>
    </div>

    <div class="container" id="main-content" style="display: none;">
        <div class="header">
            <h1>Workee 统计面板</h1>
            <div style="display: flex; align-items: center; gap: 10px;">
                <div class="date-selector">
                    <label for="date-range">查看时间范围:</label>
                    <select id="date-range" onchange="changeDateRange()">
                        <option value="7">最近7天</option>
                        <option value="30">最近30天</option>
                        <option value="90">最近90天</option>
                    </select>
                </div>
                <div id="data-status" style="padding: 5px 10px; border-radius: 4px; font-size: 12px; background: #f0f0f0; color: #666;">
                    数据加载中...
                </div>
                <button class="refresh-btn" onclick="refreshData()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M23 4v6h-6"></path>
                        <path d="M1 20v-6h6"></path>
                        <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
                        <path d="M20.49 15a9 9 0 0 1-14.85 3.36L1 14"></path>
                    </svg>
                    刷新数据
                </button>
            </div>
        </div>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('dashboard')">实时监控</div>
            <div class="tab" onclick="switchTab('commands')">指令统计</div>
            <div class="tab" onclick="switchTab('users')">用户分析</div>
            <div class="tab" onclick="switchTab('performance')">性能监控</div>
            <div class="tab" onclick="switchTab('system-cronjobs')">系统定时任务</div>
            <div class="tab" onclick="switchTab('user-cronjobs')">用户定时任务</div>
        </div>
        
        <!-- 实时监控面板 -->
        <div id="dashboard" class="tab-content active">
            <div class="metrics-cards" style="grid-template-columns: repeat(4, 1fr);">
                <div class="metric-card">
                    <div class="metric-value info" id="active-users">--</div>
                    <div class="metric-label">当前活跃用户 <small>(30分钟内)</small></div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="today-commands">--</div>
                    <div class="metric-label">今日指令数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="success-rate">--</div>
                    <div class="metric-label">成功率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="avg-response-time">--</div>
                    <div class="metric-label">平均响应时间</div>
                </div>
            </div>
            
            <div class="charts-section">
                <div class="chart-card">
                    <h3 class="chart-title">指令执行趋势</h3>
                    <div class="chart-container" id="command-trend-chart"></div>
                </div>
                <div class="chart-card">
                    <h3 class="chart-title">用户活跃度</h3>
                    <div class="chart-container" id="user-activity-chart"></div>
                </div>
            </div>
            
            <div class="activity-log">
                <h3 class="log-title">实时活动日志</h3>
                <div class="log-controls" style="margin-bottom: 10px;">
                    <input type="text" id="activity-filter" placeholder="筛选活动内容..." 
                           style="padding: 5px; border: 1px solid #ddd; border-radius: 3px; margin-right: 10px;">
                    <select id="activity-type-filter" style="padding: 5px;">
                        <option value="">所有类型</option>
                        <option value="access">访问事件</option>
                        <option value="command">指令执行</option>
                        <option value="cronjob">定时任务</option>
                    </select>
                </div>
                <div id="activity-logs">
                    <div class="log-entry">
                        <div class="log-time">--</div>
                        <div class="log-message">正在加载活动日志...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 指令统计面板 -->
        <div id="commands" class="tab-content">
            <div class="charts-section">
                <div class="chart-card">
                    <h3 class="chart-title">指令类型分布</h3>
                    <div class="chart-container" id="command-type-chart"></div>
                </div>
                <div class="chart-card">
                    <h3 class="chart-title">指令成功率趋势</h3>
                    <div class="chart-container" id="success-rate-chart"></div>
                </div>
            </div>
            
            <div class="table-card">
                <h3 class="table-title">指令执行记录</h3>
                <div class="table-controls" style="margin-bottom: 10px;">
                    <input type="text" id="command-records-filter" placeholder="筛选指令内容..." 
                           style="padding: 5px; border: 1px solid #ddd; border-radius: 3px; margin-right: 10px;">
                    <select id="command-records-type-filter" style="padding: 5px; margin-right: 10px;">
                        <option value="">所有类型</option>
                        <option value="ai_query">AI查询</option>
                        <option value="jira_query">JIRA查询</option>
                        <option value="system_cmd">系统指令</option>
                    </select>
                    <select id="command-records-status-filter" style="padding: 5px;">
                        <option value="">所有状态</option>
                        <option value="true">成功</option>
                        <option value="false">失败</option>
                    </select>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th style="cursor: pointer;" onclick="sortCommandRecords('time')">时间 ↕</th>
                            <th>用户</th>
                            <th style="cursor: pointer;" onclick="sortCommandRecords('type')">指令类型 ↕</th>
                            <th>原始输入</th>
                            <th style="cursor: pointer;" onclick="sortCommandRecords('status')">状态 ↕</th>
                            <th style="cursor: pointer;" onclick="sortCommandRecords('duration')">响应时间 ↕</th>
                        </tr>
                    </thead>
                    <tbody id="command-records">
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                                正在加载指令执行记录...
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div id="command-records-pagination" class="pagination" style="display: none;">
                    <!-- 分页组件将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
        
        <!-- 用户分析面板 -->
        <div id="users" class="tab-content">
            <div class="metrics-cards" style="grid-template-columns: repeat(5, 1fr);">
                <div class="metric-card">
                    <div class="metric-value info" id="users-total-users">--</div>
                    <div class="metric-label">总用户数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="users-active-users-count">--</div>
                    <div class="metric-label">活跃用户数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="users-activity-rate">--</div>
                    <div class="metric-label">用户活跃度</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="users-avg-commands-per-user">--</div>
                    <div class="metric-label">平均指令数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="total-groups-count">--</div>
                    <div class="metric-label">服务群数量</div>
                </div>
            </div>

            <div class="charts-section">
                <div class="chart-card">
                    <h3 class="chart-title">活跃用户排行榜</h3>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>用户ID</th>
                                    <th>指令数</th>
                                    <th>成功率</th>
                                    <th>最后活动</th>
                                </tr>
                            </thead>
                            <tbody id="top-users-table">
                                <tr>
                                    <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                                        正在加载用户排行榜...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="chart-card">
                    <h3 class="chart-title">用户行为分析</h3>
                    <div class="chart-container" id="user-behavior-chart"></div>
                </div>
            </div>
        </div>
        
        <!-- 性能监控面板 -->
        <div id="performance" class="tab-content">
            <div class="metrics-cards">
                <div class="metric-card">
                    <div class="metric-value info" id="perf-avg-response-time">--</div>
                    <div class="metric-label">平均响应时间</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="perf-memory-usage">--</div>
                    <div class="metric-label">内存使用率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="perf-cpu-usage">--</div>
                    <div class="metric-label">CPU使用率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="perf-db-status">--</div>
                    <div class="metric-label">数据库状态</div>
                </div>
            </div>

            <div class="charts-section">
                <div class="chart-card">
                    <h3 class="chart-title">API响应时间趋势</h3>
                    <div class="chart-container" id="api-response-time-chart"></div>
                </div>
                <div class="chart-card">
                    <h3 class="chart-title">系统资源使用情况</h3>
                    <div class="chart-container" id="system-resources-chart"></div>
                </div>
            </div>

            <div class="table-card">
                <h3 class="table-title">慢查询分析</h3>
                <div class="table-controls" style="margin-bottom: 10px;">
                    <input type="text" id="slow-queries-filter" placeholder="筛选查询内容..." 
                           style="padding: 5px; border: 1px solid #ddd; border-radius: 3px; margin-right: 10px;">
                    <select id="slow-queries-type-filter" style="padding: 5px; margin-right: 10px;">
                        <option value="">所有类型</option>
                        <option value="ai_query">AI查询</option>
                        <option value="jira_query">JIRA查询</option>
                        <option value="system_cmd">系统指令</option>
                    </select>
                    <select id="slow-queries-time-filter" style="padding: 5px;">
                        <option value="">所有时间</option>
                        <option value="5">5秒以上</option>
                        <option value="10">10秒以上</option>
                        <option value="30">30秒以上</option>
                    </select>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th style="cursor: pointer;" onclick="sortSlowQueries('time')">时间 ↕</th>
                            <th style="cursor: pointer;" onclick="sortSlowQueries('type')">查询类型 ↕</th>
                            <th style="cursor: pointer;" onclick="sortSlowQueries('duration')">执行时间 ↕</th>
                            <th>查询内容</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="slow-queries-table">
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                                正在加载慢查询数据...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 系统定时任务监控面板 -->
        <div id="system-cronjobs" class="tab-content">
            <div class="metrics-cards">
                <div class="metric-card">
                    <div class="metric-value info" id="cronjobs-running">--</div>
                    <div class="metric-label">运行中任务</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value success" id="cronjobs-success-today">--</div>
                    <div class="metric-label">今日成功</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value danger" id="cronjobs-failed-today">--</div>
                    <div class="metric-label">今日失败</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="cronjobs-success-rate">--</div>
                    <div class="metric-label">成功率</div>
                </div>
            </div>

            <div class="charts-section">
                <div class="chart-card">
                    <h3 class="chart-title">任务执行状态分布</h3>
                    <div class="chart-container" id="cronjob-status-chart"></div>
                </div>
                <div class="chart-card">
                    <h3 class="chart-title">任务执行时长趋势</h3>
                    <div class="chart-container" id="cronjob-duration-chart"></div>
                </div>
            </div>

            <div class="table-card">
                <h3 class="table-title">最近任务执行记录</h3>
                <table>
                    <thead>
                        <tr>
                            <th>任务名称</th>
                            <th>执行时间</th>
                            <th>状态</th>
                            <th>执行时长</th>
                            <th>错误信息</th>
                        </tr>
                    </thead>
                    <tbody id="cronjob-records-table">
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                                正在加载定时任务记录...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 用户定时任务监控面板 -->
        <div id="user-cronjobs" class="tab-content">
            <div class="metrics-cards">
                <div class="metric-card">
                    <div class="metric-value info" id="user-tasks-active">--</div>
                    <div class="metric-label">活跃用户任务</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value success" id="user-tasks-success-today">--</div>
                    <div class="metric-label">今日成功执行</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value danger" id="user-tasks-failed-today">--</div>
                    <div class="metric-label">今日执行失败</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value info" id="user-tasks-success-rate">--</div>
                    <div class="metric-label">成功率</div>
                </div>
            </div>

            <div class="charts-grid">
                <div class="chart-card">
                    <h3 class="chart-title">用户任务类型分布</h3>
                    <div class="chart-container" id="user-task-type-chart"></div>
                </div>
                <div class="chart-card">
                    <h3 class="chart-title">用户任务执行趋势</h3>
                    <div class="chart-container" id="user-task-trend-chart"></div>
                </div>
            </div>

            <div class="table-card">
                <h3 class="table-title">最近用户任务执行记录</h3>
                <table>
                    <thead>
                        <tr>
                            <th>执行时间</th>
                            <th>用户</th>
                            <th>任务类型</th>
                            <th>任务内容</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="user-task-records-table">
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                                正在加载用户任务记录...
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div id="user-task-records-pagination" class="pagination" style="display: none;">
                    <!-- 分页组件将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <div class="footer">
            <p>Workee 统计系统 &copy; 2025</p>
        </div>
    </div>
    
    <script>
        // 密码保护功能
        const CORRECT_PASSWORD = 'zhimakaimenba';

        function checkPassword() {
            const inputPassword = document.getElementById('password-input').value;
            const errorDiv = document.getElementById('password-error');

            if (inputPassword === CORRECT_PASSWORD) {
                // 密码正确，隐藏密码层，显示主内容
                document.getElementById('password-overlay').style.display = 'none';
                document.getElementById('main-content').style.display = 'block';

                // 保存密码状态到sessionStorage
                sessionStorage.setItem('workee_auth', 'true');

                // 初始化页面
                initializePage();
            } else {
                // 密码错误，显示错误信息
                errorDiv.textContent = '密码错误，请重试';
                errorDiv.style.display = 'block';
                document.getElementById('password-input').value = '';
                document.getElementById('password-input').focus();
            }
        }

        // 检查是否已经验证过密码
        function checkAuthStatus() {
            const isAuthenticated = sessionStorage.getItem('workee_auth') === 'true';
            if (isAuthenticated) {
                document.getElementById('password-overlay').style.display = 'none';
                document.getElementById('main-content').style.display = 'block';
                initializePage();
            } else {
                // 聚焦到密码输入框
                setTimeout(() => {
                    document.getElementById('password-input').focus();
                }, 100);
            }
        }

        // 支持回车键提交密码
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('password-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    checkPassword();
                }
            });

            // 检查认证状态
            checkAuthStatus();
        });

        // 初始化页面内容
        function initializePage() {
            refreshData();
            initFilters();

            // 窗口大小改变时重新调整图表
            window.addEventListener('resize', function() {
                if (window.commandTrendChart) window.commandTrendChart.resize();
                if (window.userActivityChart) window.userActivityChart.resize();
                if (window.commandTypeChart) window.commandTypeChart.resize();
                if (window.successRateChart) window.successRateChart.resize();
            });
        }

        // 处理API错误的通用函数
        function handleApiError(elementId, errorMessage) {
            console.error(`❌ ${errorMessage}`);
            
            // 显示错误状态
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = 'Error';
                element.className = element.className.replace(/success|warning|info/g, 'danger');
            }
            
            // 更新状态指示器
            const statusEl = document.getElementById('data-status');
            if (statusEl) {
                statusEl.textContent = '❌ 连接失败';
                statusEl.style.background = '#fff2f0';
                statusEl.style.color = '#ff4d4f';
            }
        }

        // 生成分页组件
        function createPagination(containerId, pagination, onPageChange) {
            const container = document.getElementById(containerId);
            if (!container || !pagination) return;

            const { current_page, total_pages, total_count, has_next, has_previous } = pagination;

            if (total_pages <= 1) {
                container.style.display = 'none';
                return;
            }

            container.style.display = 'flex';
            container.innerHTML = '';

            // 上一页按钮
            const prevBtn = document.createElement('button');
            prevBtn.textContent = '上一页';
            prevBtn.disabled = !has_previous;
            prevBtn.onclick = () => has_previous && onPageChange(current_page - 1);
            container.appendChild(prevBtn);

            // 页码按钮
            const startPage = Math.max(1, current_page - 2);
            const endPage = Math.min(total_pages, current_page + 2);

            if (startPage > 1) {
                const firstBtn = document.createElement('button');
                firstBtn.textContent = '1';
                firstBtn.onclick = () => onPageChange(1);
                container.appendChild(firstBtn);

                if (startPage > 2) {
                    const ellipsis = document.createElement('span');
                    ellipsis.textContent = '...';
                    ellipsis.className = 'page-info';
                    container.appendChild(ellipsis);
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                pageBtn.className = i === current_page ? 'active' : '';
                pageBtn.onclick = () => onPageChange(i);
                container.appendChild(pageBtn);
            }

            if (endPage < total_pages) {
                if (endPage < total_pages - 1) {
                    const ellipsis = document.createElement('span');
                    ellipsis.textContent = '...';
                    ellipsis.className = 'page-info';
                    container.appendChild(ellipsis);
                }

                const lastBtn = document.createElement('button');
                lastBtn.textContent = total_pages;
                lastBtn.onclick = () => onPageChange(total_pages);
                container.appendChild(lastBtn);
            }

            // 下一页按钮
            const nextBtn = document.createElement('button');
            nextBtn.textContent = '下一页';
            nextBtn.disabled = !has_next;
            nextBtn.onclick = () => onPageChange(current_page + 1);
            container.appendChild(nextBtn);

            // 页面信息
            const pageInfo = document.createElement('span');
            pageInfo.className = 'page-info';
            pageInfo.textContent = `第 ${current_page} 页，共 ${total_pages} 页 (总计 ${total_count} 条记录)`;
            container.appendChild(pageInfo);
        }

        // 刷新数据
        async function refreshData() {
            console.log('正在从API获取实时数据...');

            // 更新状态指示器为加载中
            const statusEl = document.getElementById('data-status');
            statusEl.textContent = '🔄 正在加载...';
            statusEl.style.background = '#f0f9ff';
            statusEl.style.color = '#409eff';

            try {
                // 从后端API获取实时数据
                const response = await fetch('/api/statistics/realtime/dashboard/');
                
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
                }
                
                const result = await response.json();

                if (result.success && result.data) {
                    const data = result.data;

                    // 更新实时指标
                    updateMetricValue('active-users', data.active_users || 0);
                    updateMetricValue('today-commands', (data.today_commands || 0).toLocaleString());
                    updateMetricValue('success-rate', (data.success_rate || 0).toFixed(1) + '%');
                    updateMetricValue('avg-response-time', Math.round(data.avg_response_time || 0) + 'ms');

                    // 更新成功率颜色
                    const successRate = data.success_rate || 0;
                    const successRateElement = document.getElementById('success-rate');

                    if (successRate >= 95) {
                        successRateElement.className = 'metric-value success';
                    } else if (successRate >= 90) {
                        successRateElement.className = 'metric-value warning';
                    } else {
                        successRateElement.className = 'metric-value danger';
                    }

                    console.log('✅ 实时数据更新成功', data);

                    // 更新状态指示器
                    const hasData = data.today_commands > 0 || data.active_users > 0;
                    if (hasData) {
                        statusEl.textContent = '✅ 显示真实数据';
                        statusEl.style.background = '#e7f5e7';
                        statusEl.style.color = '#52c41a';
                    } else {
                        statusEl.textContent = '📊 暂无数据';
                        statusEl.style.background = '#fff7e6';
                        statusEl.style.color = '#fa8c16';
                    }
                } else {
                    throw new Error(result.error || 'API返回错误数据');
                }
            } catch (error) {
                console.error('❌ 获取数据失败:', error);
                
                // 显示错误状态
                updateMetricValue('active-users', '--');
                updateMetricValue('today-commands', '--');
                updateMetricValue('success-rate', '--');
                updateMetricValue('avg-response-time', '--');

                // 更新状态指示器
                statusEl.textContent = `❌ ${error.message}`;
                statusEl.style.background = '#fff2f0';
                statusEl.style.color = '#ff4d4f';
            }

            // 并行加载其他数据
            await Promise.allSettled([
                loadChartData(),
                loadActivityLogs(),
                loadCommandRecords()
            ]);

            // 根据当前活跃的标签页刷新对应数据
            const activeTab = document.querySelector('.tab-content.active');
            if (activeTab) {
                const tabId = activeTab.id;
                console.log(`🔄 刷新当前活跃标签页数据: ${tabId}`);

                switch(tabId) {
                    case 'commands':
                        await initCommandStatsCharts();
                        break;
                    case 'users':
                        await loadUserAnalysisData();
                        break;
                    case 'performance':
                        await loadPerformanceData();
                        break;
                    case 'cronjobs':
                        await loadCronjobData();
                        break;
                }
            }
        }

        // 更新指标值的辅助函数
        function updateMetricValue(elementId, value) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
            }
        }
        
        // 加载图表数据
        async function loadChartData() {
            try {
                // 获取指令趋势数据
                const commandTrendsResponse = await fetch(`/api/statistics/realtime/command-trends/?days=${currentDateRange}`);
                const commandTrendsResult = await commandTrendsResponse.json();

                // 获取用户活动数据
                const userActivityResponse = await fetch(`/api/statistics/realtime/user-activity/?days=${currentDateRange}`);
                const userActivityResult = await userActivityResponse.json();

                // 初始化图表
                initCharts(
                    commandTrendsResult.success ? commandTrendsResult.data : null, 
                    userActivityResult.success ? userActivityResult.data : null
                );

            } catch (error) {
                console.error('❌ 获取图表数据失败:', error);
                // 使用默认数据初始化图表
                initCharts(null, null);
            }
        }

        // 加载活动日志
        async function loadActivityLogs() {
            try {
                const response = await fetch('/api/statistics/realtime/activity-logs/');
                const result = await response.json();

                if (result.success && result.data && result.data.length > 0) {
                    const logsContainer = document.getElementById('activity-logs');
                    logsContainer.innerHTML = '';

                    result.data.forEach(log => {
                        const logEntry = document.createElement('div');
                        logEntry.className = 'log-entry';
                        logEntry.setAttribute('data-type', log.type || '');

                        const logTime = document.createElement('div');
                        logTime.className = 'log-time';
                        logTime.textContent = log.time;

                        const logMessage = document.createElement('div');
                        logMessage.className = 'log-message';
                        logMessage.innerHTML = log.message;

                        logEntry.appendChild(logTime);
                        logEntry.appendChild(logMessage);
                        logsContainer.appendChild(logEntry);
                    });

                    console.log('✅ 活动日志加载成功');
                } else {
                    // 显示无数据状态
                    const logsContainer = document.getElementById('activity-logs');
                    logsContainer.innerHTML = `
                        <div class="log-entry">
                            <div class="log-time">--</div>
                            <div class="log-message">暂无活动记录</div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ 加载活动日志失败:', error);
                const logsContainer = document.getElementById('activity-logs');
                logsContainer.innerHTML = `
                    <div class="log-entry">
                        <div class="log-time">--</div>
                        <div class="log-message">加载失败</div>
                    </div>
                `;
            }
        }

        // 加载指令执行记录
        async function loadCommandRecords() {
            try {
                const response = await fetch('/api/statistics/data/command-records/?page_size=20');
                const result = await response.json();

                if (result.success && result.data && result.data.records && result.data.records.length > 0) {
                    const recordsContainer = document.getElementById('command-records');
                    recordsContainer.innerHTML = '';

                    result.data.records.forEach(record => {
                        const row = document.createElement('tr');

                        const statusClass = record.success ? 'success' : 'error';
                        const statusText = record.success ? '成功' : '失败';
                        const responseTime = record.processing_time ? Math.round(record.processing_time * 1000) + 'ms' : '--';

                        // 格式化时间
                        const createdAt = new Date(record.created_at).toLocaleString('zh-CN');

                        // 处理用户显示 - 优先显示邮箱前缀，否则显示用户ID
                        let userDisplay = record.user_id;
                        if (record.user_email && record.user_email.includes('@')) {
                            userDisplay = record.user_email.split('@')[0];
                        } else if (record.user_id && record.user_id.includes('@')) {
                            userDisplay = record.user_id.split('@')[0];
                        }

                        // 优化指令类型显示
                        let commandTypeDisplay = record.command_type;
                        const commandTypeMap = {
                            'ai_query': 'AI查询',
                            'jira_query': 'JIRA查询',
                            'jira_write': 'JIRA写操作',
                            'mr_check': 'MR检查',
                            'schedule_management': '定时任务',
                            'traditional_command': '传统指令',
                            'unknown': '未知指令'
                        };
                        if (commandTypeMap[record.command_type]) {
                            commandTypeDisplay = commandTypeMap[record.command_type];
                        }

                        // 处理原始输入显示 - 添加完整内容的tooltip
                        const rawInputDisplay = record.raw_input.length > 30 ?
                            record.raw_input.substring(0, 30) + '...' :
                            record.raw_input;

                        // 添加data属性用于筛选和排序
                        row.setAttribute('data-record', 'true');
                        row.setAttribute('data-time', record.created_at);
                        row.setAttribute('data-type', record.command_type);
                        row.setAttribute('data-input', record.raw_input);
                        row.setAttribute('data-status', record.success.toString());
                        row.setAttribute('data-duration', record.processing_time || '0');

                        row.innerHTML = `
                            <td>${createdAt}</td>
                            <td title="${record.user_email || record.user_id}">${userDisplay}</td>
                            <td title="${record.command_type}">${commandTypeDisplay}</td>
                            <td class="raw-input-cell" title="${record.raw_input}" style="cursor: help; position: relative;">
                                ${rawInputDisplay}
                            </td>
                            <td><span class="${statusClass}">${statusText}</span></td>
                            <td>${responseTime}</td>
                        `;

                        recordsContainer.appendChild(row);
                    });

                    console.log('✅ 指令执行记录加载成功');
                } else {
                    // 显示无数据状态
                    const recordsContainer = document.getElementById('command-records');
                    recordsContainer.innerHTML = `
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                                暂无指令执行记录
                            </td>
                        </tr>
                    `;
                }
            } catch (error) {
                console.error('❌ 加载指令执行记录失败:', error);
                const recordsContainer = document.getElementById('command-records');
                recordsContainer.innerHTML = `
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 20px; color: #ff4d4f;">
                            加载失败
                        </td>
                    </tr>
                `;
            }
        }

        // 初始化图表
        function initCharts(commandTrendsData = null, userActivityData = null) {
            // 指令执行趋势图
            const commandTrendChart = echarts.init(document.getElementById('command-trend-chart'));

            // 使用真实数据或默认数据
            const defaultCommandData = {
                dates: ['07-12', '07-13', '07-14', '07-15', '07-16', '07-17', '07-18'],
                total: [0, 0, 0, 0, 0, 0, 0],
                success: [0, 0, 0, 0, 0, 0, 0],
                failed: [0, 0, 0, 0, 0, 0, 0]
            };

            const chartData = commandTrendsData || defaultCommandData;

            commandTrendChart.setOption({
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['总指令数', '成功', '失败']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: chartData.dates
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '总指令数',
                        type: 'line',
                        data: chartData.total
                    },
                    {
                        name: '成功',
                        type: 'line',
                        data: chartData.success
                    },
                    {
                        name: '失败',
                        type: 'line',
                        data: chartData.failed
                    }
                ]
            });
            
            // 用户活跃度图
            const userActivityChart = echarts.init(document.getElementById('user-activity-chart'));

            // 使用真实数据或默认数据
            const defaultUserData = {
                dates: ['07-12', '07-13', '07-14', '07-15', '07-16', '07-17', '07-18'],
                active_users: [0, 0, 0, 0, 0, 0, 0],
                new_users: [0, 0, 0, 0, 0, 0, 0]
            };

            const userData = userActivityData || defaultUserData;

            userActivityChart.setOption({
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['活跃用户', '新用户']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: userData.dates
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '活跃用户',
                        type: 'line',
                        data: userData.active_users
                    },
                    {
                        name: '新用户',
                        type: 'line',
                        data: userData.new_users
                    }
                ]
            });
            
            // 指令类型分布图 - 将在initCommandStatsCharts中初始化
            
            // 成功率趋势图 - 将在initCommandStatsCharts中初始化
        }

        // 初始化指令统计页面的图表
        async function initCommandStatsCharts() {
            try {
                console.log('🔄 开始加载指令统计图表数据...');

                // 获取指令统计数据
                const response = await fetch(`/api/statistics/data/command-statistics/?days=${currentDateRange}`);
                const result = await response.json();

                if (result.success && result.data) {
                    console.log('✅ 指令统计数据获取成功:', result.data);
                    initCommandTypeChart(result.data.command_type_distribution || []);
                    initSuccessRateChart(result.data.success_rate_trend || []);
                } else {
                    console.error('❌ 指令统计数据获取失败:', result.error);
                    initCommandTypeChart([]);
                    initSuccessRateChart([]);
                }
            } catch (error) {
                console.error('❌ 获取指令统计数据失败:', error);
                initCommandTypeChart([]);
                initSuccessRateChart([]);
            }
        }

        // 初始化指令类型分布图表
        function initCommandTypeChart(commandTypes) {
            const chartElement = document.getElementById('command-type-chart');
            if (!chartElement) {
                console.warn('⚠️ 指令类型图表容器未找到');
                return;
            }

            // 检查容器是否可见
            if (chartElement.offsetWidth === 0 || chartElement.offsetHeight === 0) {
                console.warn('⚠️ 指令类型图表容器不可见，延迟初始化');
                setTimeout(() => initCommandTypeChart(commandTypes), 100);
                return;
            }

            const commandTypeChart = echarts.init(chartElement);

            // 准备数据
            const data = commandTypes.length > 0 ?
                commandTypes.map(item => ({
                    name: item.command_type || '未知类型',
                    value: item.count || 0
                })) :
                [{name: '暂无数据', value: 1}];

            console.log('📊 指令类型分布数据:', data);

            commandTypeChart.setOption({
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left'
                },
                series: [
                    {
                        name: '指令类型',
                        type: 'pie',
                        radius: '50%',
                        data: data,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            });

            window.commandTypeChart = commandTypeChart;
        }

        // 初始化成功率趋势图表
        function initSuccessRateChart(successRateData) {
            const chartElement = document.getElementById('success-rate-chart');
            if (!chartElement) {
                console.warn('⚠️ 成功率趋势图表容器未找到');
                return;
            }

            // 检查容器是否可见
            if (chartElement.offsetWidth === 0 || chartElement.offsetHeight === 0) {
                console.warn('⚠️ 成功率趋势图表容器不可见，延迟初始化');
                setTimeout(() => initSuccessRateChart(successRateData), 100);
                return;
            }

            const successRateChart = echarts.init(chartElement);

            // 准备数据
            let dates = [];
            let successRates = [];

            if (successRateData && successRateData.length > 0) {
                dates = successRateData.map(item => item.date);
                successRates = successRateData.map(item => item.success_rate || 0);
            } else {
                dates = ['无数据'];
                successRates = [0];
            }

            console.log('📈 成功率趋势数据:', {dates, successRates});

            successRateChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    formatter: '{b}: {c}%'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: dates
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: [
                    {
                        name: '成功率',
                        type: 'line',
                        data: successRates,
                        smooth: true,
                        lineStyle: {
                            color: '#52c41a'
                        },
                        areaStyle: {
                            color: 'rgba(82, 196, 26, 0.1)'
                        },
                        markLine: {
                            data: [
                                { yAxis: 90, lineStyle: { color: '#E6A23C' }, label: { formatter: '警戒线: 90%' } },
                                { yAxis: 95, lineStyle: { color: '#67C23A' }, label: { formatter: '优秀线: 95%' } }
                            ]
                        }
                    }
                ]
            });

            window.successRateChart = successRateChart;
        }

        // 初始化用户行为分析图表
        function initUserBehaviorChart(userActivityTrend) {
            const chartElement = document.getElementById('user-behavior-chart');
            if (!chartElement) {
                console.warn('⚠️ 用户行为分析图表容器未找到');
                return;
            }

            // 检查容器是否可见
            if (chartElement.offsetWidth === 0 || chartElement.offsetHeight === 0) {
                console.warn('⚠️ 用户行为分析图表容器不可见，延迟初始化');
                setTimeout(() => initUserBehaviorChart(userActivityTrend), 100);
                return;
            }

            const userBehaviorChart = echarts.init(chartElement);

            // 准备数据
            let dates = [];
            let activeUsers = [];
            let newUsers = [];

            if (userActivityTrend && userActivityTrend.length > 0) {
                dates = userActivityTrend.map(item => item.date);
                activeUsers = userActivityTrend.map(item => item.active_users || 0);
                newUsers = userActivityTrend.map(item => item.new_users || 0);
            } else {
                // 生成默认数据
                const today = new Date();
                for (let i = 6; i >= 0; i--) {
                    const date = new Date(today);
                    date.setDate(date.getDate() - i);
                    dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }));
                    activeUsers.push(0);
                    newUsers.push(0);
                }
            }

            console.log('👥 用户行为分析数据:', {dates, activeUsers, newUsers});

            userBehaviorChart.setOption({
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['活跃用户', '新用户']
                },
                xAxis: {
                    type: 'category',
                    data: dates
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '活跃用户',
                        type: 'line',
                        data: activeUsers,
                        smooth: true,
                        itemStyle: { color: '#1890ff' }
                    },
                    {
                        name: '新用户',
                        type: 'line',
                        data: newUsers,
                        smooth: true,
                        itemStyle: { color: '#52c41a' }
                    }
                ]
            });

            window.userBehaviorChart = userBehaviorChart;
        }

        // 加载用户分析数据
        async function loadUserAnalysisData() {
            try {
                const response = await fetch(`/api/statistics/data/user-statistics/?days=${currentDateRange}`);
                const result = await response.json();

                if (result.success && result.data) {
                    const data = result.data;

                    // 更新用户指标
                    document.getElementById('users-total-users').textContent = data.total_users || 0;
                    document.getElementById('users-active-users-count').textContent = data.active_users || 0;
                    document.getElementById('users-activity-rate').textContent = data.user_activity_rate + '%' || '0%';
                    document.getElementById('users-avg-commands-per-user').textContent = data.avg_commands_per_user || 0;

                    // 新增：显示服务群数量
                    const totalGroupsElement = document.getElementById('total-groups-count');
                    if (totalGroupsElement) {
                        totalGroupsElement.textContent = data.total_groups || 0;
                    }

                    // 加载用户排行榜
                    loadTopUsersTable(data.user_rankings || []);

                    // 初始化用户行为分析图表
                    initUserBehaviorChart(data.user_activity_trend || []);

                    console.log('✅ 用户分析数据加载成功');
                } else {
                    console.error('❌ 用户统计数据获取失败:', result.error);
                }
            } catch (error) {
                console.error('❌ 加载用户分析数据失败:', error);
            }
        }

        // 加载用户排行榜
        function loadTopUsersTable(topUsers) {
            const tableContainer = document.getElementById('top-users-table');
            if (!tableContainer) return;

            if (topUsers.length > 0) {
                tableContainer.innerHTML = '';
                topUsers.forEach((user, index) => {
                    const row = document.createElement('tr');
                    const lastActivity = user.last_activity ?
                        new Date(user.last_activity).toLocaleDateString() : '未知';

                    // 处理用户显示 - 优先显示邮箱前缀
                    let userDisplay = '未知用户';
                    if (user.user_email && user.user_email.includes('@')) {
                        userDisplay = user.user_email.split('@')[0];
                    } else if (user.user_id && user.user_id.includes('@')) {
                        userDisplay = user.user_id.split('@')[0];
                    } else if (user.user_id) {
                        userDisplay = user.user_id;
                    }

                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td title="${user.user_email || user.user_id}">${userDisplay}</td>
                        <td>${user.command_count}</td>
                        <td>${user.success_rate}%</td>
                        <td>${lastActivity}</td>
                    `;
                    tableContainer.appendChild(row);
                });
            } else {
                tableContainer.innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                            暂无用户数据
                        </td>
                    </tr>
                `;
            }
        }

        // 加载性能监控数据
        async function loadPerformanceData() {
            try {
                const response = await fetch(`/api/statistics/data/performance-statistics/?days=${currentDateRange}`);
                const result = await response.json();

                if (result.success && result.data) {
                    const data = result.data;

                    // 更新性能指标
                    document.getElementById('perf-avg-response-time').textContent =
                        data.avg_response_time ?
                        Math.round(data.avg_response_time * 1000) + 'ms' : '--';

                    document.getElementById('perf-memory-usage').textContent =
                        data.memory_usage_rate !== undefined ?
                        data.memory_usage_rate.toFixed(1) + '%' : '--';

                    document.getElementById('perf-cpu-usage').textContent =
                        data.cpu_usage_rate !== undefined ?
                        data.cpu_usage_rate.toFixed(1) + '%' : '--';

                    // 数据库状态
                    document.getElementById('perf-db-status').textContent =
                        data.database_status || 'unknown';

                    // 初始化API响应时间图表
                    if (data.endpoint_performance && data.endpoint_performance.length > 0) {
                        initApiResponseTimeChart(data.endpoint_performance);
                    }

                    // 初始化系统资源使用图表
                    initSystemResourcesChart({
                        cpu_usage: data.cpu_usage_rate || 0,
                        memory_usage: data.memory_usage_rate || 0,
                        disk_usage: data.disk_usage_rate || 0
                    });

                    // 加载慢查询数据
                    loadSlowQueriesData();

                    console.log('✅ 性能监控数据加载成功');
                } else {
                    console.error('❌ 性能统计数据获取失败:', result.error);
                    handleApiError('perf-avg-response-time', 'API返回错误: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                handleApiError('perf-avg-response-time', '加载性能监控数据失败: ' + error);
            }
        }
        
        // 加载慢查询数据
        async function loadSlowQueriesData() {
            try {
                const response = await fetch('/api/statistics/performance/slow-queries/');
                const result = await response.json();

                if (result.success && result.data) {
                    const data = result.data;
                    const tableContainer = document.getElementById('slow-queries-table');
                    
                    if (data.slowest_queries && data.slowest_queries.length > 0) {
                        tableContainer.innerHTML = '';
                        
                        data.slowest_queries.forEach(query => {
                            const row = document.createElement('tr');
                            const createdAt = new Date(query.created_at).toLocaleString('zh-CN');
                            const status = query.success ? '成功' : '失败';
                            const statusClass = query.success ? 'success' : 'error';
                            
                            row.innerHTML = `
                                <td>${createdAt}</td>
                                <td>${query.command_type}</td>
                                <td>${(query.processing_time * 1000).toFixed(0)}ms</td>
                                <td>-</td>
                                <td><span class="${statusClass}">${status}</span></td>
                            `;
                            
                            tableContainer.appendChild(row);
                        });
                        
                        console.log('✅ 慢查询数据加载成功');
                    } else {
                        tableContainer.innerHTML = `
                            <tr>
                                <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                                    暂无慢查询数据
                                </td>
                            </tr>
                        `;
                    }
                } else {
                    document.getElementById('slow-queries-table').innerHTML = `
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 20px; color: #ff4d4f;">
                                加载失败: ${result.error || '未知错误'}
                            </td>
                        </tr>
                    `;
                }
            } catch (error) {
                console.error('❌ 加载慢查询数据失败:', error);
                document.getElementById('slow-queries-table').innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px; color: #ff4d4f;">
                            加载失败: ${error}
                        </td>
                    </tr>
                `;
            }
        }
        
        // 初始化API响应时间图表
        function initApiResponseTimeChart(apiPerformance) {
            const chartElement = document.getElementById('api-response-time-chart');
            if (!chartElement) return;
            
            const chart = echarts.init(chartElement);
            
            // 准备数据
            const endpoints = apiPerformance.map(item => item.endpoint || item.api_endpoint);
            const avgTimes = apiPerformance.map(item => item.avg_response_time || 0); // 已经是毫秒
            const maxTimes = apiPerformance.map(item => item.max_response_time || 0); // 已经是毫秒
            
            chart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['平均响应时间', '最大响应时间']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: '{value} ms'
                    }
                },
                yAxis: {
                    type: 'category',
                    data: endpoints,
                    axisLabel: {
                        formatter: function(value) {
                            // 截断长端点名称
                            return value.length > 30 ? value.substring(0, 30) + '...' : value;
                        }
                    }
                },
                series: [
                    {
                        name: '平均响应时间',
                        type: 'bar',
                        data: avgTimes
                    },
                    {
                        name: '最大响应时间',
                        type: 'bar',
                        data: maxTimes
                    }
                ]
            });
        }
        
        // 初始化系统资源使用图表
        function initSystemResourcesChart(systemResources) {
            const chartElement = document.getElementById('system-resources-chart');
            if (!chartElement) return;
            
            const chart = echarts.init(chartElement);
            
            // 准备数据
            const cpuUsage = systemResources && systemResources.cpu_usage !== undefined ? systemResources.cpu_usage : 0;
            const memoryUsage = systemResources && systemResources.memory_usage !== undefined ? systemResources.memory_usage : 0;
            const diskUsage = systemResources && systemResources.disk_usage !== undefined ? systemResources.disk_usage : 0;
            
            chart.setOption({
                tooltip: {
                    formatter: '{a} <br/>{b} : {c}%'
                },
                series: [
                    {
                        name: 'CPU使用率',
                        type: 'gauge',
                        min: 0,
                        max: 100,
                        detail: {formatter: '{value}%'},
                        data: [{value: parseFloat(cpuUsage.toFixed(1)), name: 'CPU'}],
                        center: ['20%', '50%'],
                        radius: '60%'
                    },
                    {
                        name: '内存使用率',
                        type: 'gauge',
                        min: 0,
                        max: 100,
                        detail: {formatter: '{value}%'},
                        data: [{value: parseFloat(memoryUsage.toFixed(1)), name: '内存'}],
                        center: ['50%', '50%'],
                        radius: '60%'
                    },
                    {
                        name: '磁盘使用率',
                        type: 'gauge',
                        min: 0,
                        max: 100,
                        detail: {formatter: '{value}%'},
                        data: [{value: parseFloat(diskUsage.toFixed(1)), name: '磁盘'}],
                        center: ['80%', '50%'],
                        radius: '60%'
                    }
                ]
            });
        }

        // 加载系统定时任务监控数据
        async function loadSystemCronjobData() {
            try {
                // 使用新的综合API，包含用户任务
                const response = await fetch('/api/statistics/cronjobs/comprehensive/');
                const result = await response.json();

                if (result.success && result.data) {
                    const data = result.data;
                    const summary = data.summary || {};

                    // 更新系统定时任务指标
                    document.getElementById('cronjobs-running').textContent = summary.running_jobs || 0;
                    document.getElementById('cronjobs-success-today').textContent = summary.system_success || 0;
                    document.getElementById('cronjobs-failed-today').textContent = summary.system_failed || 0;

                    // 计算系统任务成功率
                    const systemTotal = (summary.system_success || 0) + (summary.system_failed || 0);
                    const systemSuccessRate = systemTotal > 0 ?
                        Math.round((summary.system_success || 0) / systemTotal * 100) : 0;
                    document.getElementById('cronjobs-success-rate').textContent = systemSuccessRate + '%';

                    // 初始化任务状态分布图表
                    initCronjobStatusChart(data);

                    // 初始化任务执行时长趋势图表
                    initCronjobDurationChart(data);

                    // 加载最近任务执行记录（包含完整错误信息）
                    loadCronjobRecords(data);

                    console.log('✅ 系统定时任务监控数据加载成功');
                } else {
                    handleApiError('cronjobs-running', 'API返回错误: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                handleApiError('cronjobs-running', '加载定时任务监控数据失败: ' + error);
            }
        }
        
        // 加载最近任务执行记录
        function loadCronjobRecords(data) {
            const tableContainer = document.getElementById('cronjob-records-table');
            if (!tableContainer) return;
            
            if (data.recent_failures && data.recent_failures.length > 0) {
                tableContainer.innerHTML = '';
                
                data.recent_failures.forEach(job => {
                    const row = document.createElement('tr');
                    const startTime = job.start_time || '未知时间';

                    // 处理错误信息，支持完整显示和tooltip
                    const errorMessage = job.error_message || '无错误信息';
                    const shortError = errorMessage.length > 50 ? errorMessage.substring(0, 50) + '...' : errorMessage;

                    const errorCell = errorMessage.length > 50 ?
                        `<div class="tooltip">
                            <span class="error-message">${shortError}</span>
                            <span class="tooltip-text">${errorMessage.replace(/"/g, '&quot;')}</span>
                         </div>` :
                        `<span class="error-message">${errorMessage}</span>`;

                    // 任务类型标识
                    const taskTypeClass = job.task_type === '用户任务' ? 'user-task' : 'system-task';
                    const taskTypeBadge = job.task_type ? `<br><small><span class="task-type-badge ${taskTypeClass}">${job.task_type}</span></small>` : '';

                    row.innerHTML = `
                        <td>
                            ${job.job_name || '未知任务'}
                            ${taskTypeBadge}
                        </td>
                        <td>${startTime}</td>
                        <td><span class="status-badge error">失败</span></td>
                        <td>-</td>
                        <td>${errorCell}</td>
                    `;

                    tableContainer.appendChild(row);
                });
            } else {
                tableContainer.innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                            暂无失败的定时任务记录
                        </td>
                    </tr>
                `;
            }
        }
        
        // 初始化任务状态分布图表
        function initCronjobStatusChart(data) {
            const chartElement = document.getElementById('cronjob-status-chart');
            if (!chartElement) return;
            
            const chart = echarts.init(chartElement);
            
            // 准备数据 - 使用正确的API数据结构
            const summary = data.summary || {};
            const runningJobs = summary.running_jobs || 0;
            const successJobs = summary.system_success || 0;  // 只显示系统任务
            const failedJobs = summary.system_failed || 0;    // 只显示系统任务

            console.log('📊 任务状态分布数据:', {runningJobs, successJobs, failedJobs});
            
            chart.setOption({
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    data: ['运行中', '成功', '失败']
                },
                series: [
                    {
                        name: '任务状态',
                        type: 'pie',
                        radius: '50%',
                        data: [
                            {value: runningJobs, name: '运行中', itemStyle: {color: '#1890ff'}},
                            {value: successJobs, name: '成功', itemStyle: {color: '#52c41a'}},
                            {value: failedJobs, name: '失败', itemStyle: {color: '#ff4d4f'}}
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            });
        }
        
        // 初始化任务执行时长趋势图表
        function initCronjobDurationChart(data) {
            const chartElement = document.getElementById('cronjob-duration-chart');
            if (!chartElement) return;

            const chart = echarts.init(chartElement);

            // 准备数据 - 从recent_failures中提取任务名称，生成模拟执行时长数据
            const jobNames = [];
            const avgDurations = [];
            const maxDurations = [];

            // 从API数据中获取真实的任务执行时长数据
            if (data.job_stats && data.job_stats.length > 0) {
                // 使用API返回的真实任务统计数据
                const topJobs = data.job_stats.slice(0, 8); // 显示前8个任务

                topJobs.forEach(job => {
                    jobNames.push(job.job_name || '未知任务');
                    avgDurations.push(job.avg_duration || 0);
                    maxDurations.push(job.max_duration || 0);
                });
            } else if (data.recent_failures && data.recent_failures.length > 0) {
                // 如果没有统计数据，从失败记录中提取任务名称，但不显示执行时长
                const uniqueJobs = [...new Set(data.recent_failures.map(f => f.job_name))];
                const topJobs = uniqueJobs.slice(0, 8);

                topJobs.forEach(jobName => {
                    jobNames.push(jobName);
                    avgDurations.push(0); // 无数据时显示0
                    maxDurations.push(0);
                });
            }

            console.log('⏱️ 任务执行时长数据:', {jobNames, avgDurations, maxDurations});
            
            chart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['平均执行时长', '最大执行时长']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: '{value} s'
                    }
                },
                yAxis: {
                    type: 'category',
                    data: jobNames,
                    axisLabel: {
                        formatter: function(value) {
                            // 截断长任务名称
                            return value.length > 20 ? value.substring(0, 20) + '...' : value;
                        }
                    }
                },
                series: [
                    {
                        name: '平均执行时长',
                        type: 'bar',
                        data: avgDurations
                    },
                    {
                        name: '最大执行时长',
                        type: 'bar',
                        data: maxDurations
                    }
                ]
            });
        }

        // 加载用户定时任务监控数据
        async function loadUserCronjobData() {
            try {
                const response = await fetch(`/api/statistics/data/user-scheduled-tasks/?days=${currentDateRange}&page=1&page_size=20`);
                const result = await response.json();

                if (result.success && result.data) {
                    const data = result.data;
                    const summary = data.summary || {};

                    // 更新用户定时任务指标
                    document.getElementById('user-tasks-active').textContent = summary.active_tasks || 0;
                    document.getElementById('user-tasks-success-today').textContent = summary.success_today || 0;
                    document.getElementById('user-tasks-failed-today').textContent = summary.failed_today || 0;
                    document.getElementById('user-tasks-success-rate').textContent = (summary.success_rate || 0) + '%';

                    // 初始化用户任务图表
                    initUserTaskTypeChart(data.task_type_distribution || []);
                    initUserTaskTrendChart(data);

                    // 加载用户任务执行记录
                    loadUserTaskRecords(data.tasks || [], data.pagination || {});

                    console.log('✅ 用户定时任务监控数据加载成功');
                } else {
                    handleApiError('user-tasks-active', 'API返回错误: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                handleApiError('user-tasks-active', '加载用户定时任务监控数据失败: ' + error);
            }
        }

        // 初始化用户任务类型分布图表
        function initUserTaskTypeChart(taskTypeData) {
            const chartElement = document.getElementById('user-task-type-chart');
            if (!chartElement) return;

            const chart = echarts.init(chartElement);

            // 使用真实的任务类型数据
            const taskTypes = taskTypeData.length > 0 ?
                taskTypeData.map(item => ({
                    name: item.task_type || '未知类型',
                    value: item.count || 0
                })) :
                [{name: '暂无数据', value: 1}];

            console.log('📊 用户任务类型分布数据:', taskTypes);

            chart.setOption({
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left'
                },
                series: [
                    {
                        name: '任务类型',
                        type: 'pie',
                        radius: '50%',
                        data: taskTypes,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            });
        }

        // 初始化用户任务执行趋势图表
        function initUserTaskTrendChart(data) {
            const chartElement = document.getElementById('user-task-trend-chart');
            if (!chartElement) return;

            const chart = echarts.init(chartElement);

            // 使用真实的用户任务趋势数据
            const dates = [];
            const successData = [];
            const failedData = [];

            // 如果API提供了趋势数据，使用真实数据
            if (data.task_trend && data.task_trend.length > 0) {
                data.task_trend.forEach(item => {
                    dates.push(item.date);
                    successData.push(item.success_count || 0);
                    failedData.push(item.failed_count || 0);
                });
            } else {
                // 如果没有趋势数据，显示空图表
                for (let i = 6; i >= 0; i--) {
                    const date = new Date();
                    date.setDate(date.getDate() - i);
                    dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }));
                    successData.push(0);
                    failedData.push(0);
                }
            }

            chart.setOption({
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['成功', '失败']
                },
                xAxis: {
                    type: 'category',
                    data: dates
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '成功',
                        type: 'line',
                        data: successData,
                        itemStyle: { color: '#52c41a' }
                    },
                    {
                        name: '失败',
                        type: 'line',
                        data: failedData,
                        itemStyle: { color: '#ff4d4f' }
                    }
                ]
            });
        }

        // 加载用户任务执行记录
        function loadUserTaskRecords(userTasks, pagination) {
            const tableContainer = document.getElementById('user-task-records-table');
            if (!tableContainer) return;

            if (userTasks.length > 0) {
                tableContainer.innerHTML = '';
                userTasks.forEach(task => {
                    const row = document.createElement('tr');

                    // 处理状态显示
                    let statusClass = 'info';
                    let statusText = task.status;
                    if (task.status === 'success') {
                        statusClass = 'success';
                        statusText = '成功';
                    } else if (task.status === 'failed') {
                        statusClass = 'danger';
                        statusText = '失败';
                    } else if (task.status === 'active') {
                        statusClass = 'info';
                        statusText = '活跃';
                    }

                    // 格式化时间
                    const lastExecution = task.last_execution ?
                        new Date(task.last_execution).toLocaleString('zh-CN') : '未执行';

                    row.innerHTML = `
                        <td>${lastExecution}</td>
                        <td title="${task.user_email || ''}">${task.user}</td>
                        <td>${task.task_type}</td>
                        <td title="${task.task_name}">${task.task_name.length > 30 ? task.task_name.substring(0, 30) + '...' : task.task_name}</td>
                        <td><span class="${statusClass}">${statusText}</span></td>
                    `;
                    tableContainer.appendChild(row);
                });

                // 生成分页组件
                createPagination('user-task-records-pagination', pagination, (page) => {
                    loadUserCronjobDataWithPage(page);
                });
            } else {
                tableContainer.innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                            暂无用户任务记录
                        </td>
                    </tr>
                `;
            }
        }

        // 带分页的用户定时任务数据加载
        async function loadUserCronjobDataWithPage(page = 1) {
            try {
                const response = await fetch(`/api/statistics/data/user-scheduled-tasks/?days=${currentDateRange}&page=${page}&page_size=20`);
                const result = await response.json();

                if (result.success && result.data) {
                    const data = result.data;

                    // 只更新表格和分页，不更新指标和图表
                    loadUserTaskRecords(data.tasks || [], data.pagination || {});

                    console.log(`✅ 用户定时任务第${page}页数据加载成功`);
                } else {
                    console.error('❌ 用户定时任务分页数据获取失败:', result.error);
                }
            } catch (error) {
                console.error('❌ 加载用户定时任务分页数据失败:', error);
            }
        }

        // 全局变量存储当前日期范围
        let currentDateRange = 7;

        // 日期范围变更处理
        function changeDateRange() {
            const select = document.getElementById('date-range');
            currentDateRange = parseInt(select.value);

            console.log(`📅 切换到${currentDateRange}天数据范围`);

            // 更新状态指示器
            const statusEl = document.getElementById('data-status');
            statusEl.textContent = '🔄 正在加载数据...';
            statusEl.style.background = '#f0f9ff';
            statusEl.style.color = '#409eff';

            // 重新加载所有数据
            refreshData();
        }

        // 切换标签页时加载对应数据
        function switchTab(tabId) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 取消所有标签的激活状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 激活选中的标签和内容
            document.getElementById(tabId).classList.add('active');
            document.querySelector(`.tab[onclick="switchTab('${tabId}')"]`).classList.add('active');

            // 根据标签页加载对应数据
            switch(tabId) {
                case 'users':
                    loadUserAnalysisData();
                    break;
                case 'performance':
                    loadPerformanceData();
                    break;
                case 'system-cronjobs':
                    loadSystemCronjobData();
                    break;
                case 'user-cronjobs':
                    loadUserCronjobData();
                    break;
                case 'commands':
                    initCommandStatsCharts();
                    break;
            }
        }

        // 初始化筛选功能
        function initFilters() {
            // 活动日志筛选
            const activityFilter = document.getElementById('activity-filter');
            const activityTypeFilter = document.getElementById('activity-type-filter');
            
            if (activityFilter) {
                activityFilter.addEventListener('input', filterActivityLogs);
            }
            if (activityTypeFilter) {
                activityTypeFilter.addEventListener('change', filterActivityLogs);
            }
            
            // 指令记录筛选
            const commandFilter = document.getElementById('command-records-filter');
            const commandTypeFilter = document.getElementById('command-records-type-filter');
            const commandStatusFilter = document.getElementById('command-records-status-filter');
            
            if (commandFilter) {
                commandFilter.addEventListener('input', filterCommandRecords);
            }
            if (commandTypeFilter) {
                commandTypeFilter.addEventListener('change', filterCommandRecords);
            }
            if (commandStatusFilter) {
                commandStatusFilter.addEventListener('change', filterCommandRecords);
            }
            
            // 慢查询筛选
            const slowQueriesFilter = document.getElementById('slow-queries-filter');
            const slowQueriesTypeFilter = document.getElementById('slow-queries-type-filter');
            const slowQueriesTimeFilter = document.getElementById('slow-queries-time-filter');
            
            if (slowQueriesFilter) {
                slowQueriesFilter.addEventListener('input', filterSlowQueries);
            }
            if (slowQueriesTypeFilter) {
                slowQueriesTypeFilter.addEventListener('change', filterSlowQueries);
            }
            if (slowQueriesTimeFilter) {
                slowQueriesTimeFilter.addEventListener('change', filterSlowQueries);
            }
        }
        
        // 筛选活动日志
        function filterActivityLogs() {
            const filterText = document.getElementById('activity-filter')?.value.toLowerCase() || '';
            const filterType = document.getElementById('activity-type-filter')?.value || '';
            const logEntries = document.querySelectorAll('#activity-logs .log-entry');
            
            logEntries.forEach(entry => {
                const message = entry.querySelector('.log-message')?.textContent.toLowerCase() || '';
                const type = entry.getAttribute('data-type') || '';
                
                const matchesText = !filterText || message.includes(filterText);
                const matchesType = !filterType || type === filterType;
                
                if (matchesText && matchesType) {
                    entry.style.display = '';
                } else {
                    entry.style.display = 'none';
                }
            });
        }
        
        // 筛选指令记录
        function filterCommandRecords() {
            const filterText = document.getElementById('command-records-filter')?.value.toLowerCase() || '';
            const filterType = document.getElementById('command-records-type-filter')?.value || '';
            const filterStatus = document.getElementById('command-records-status-filter')?.value || '';
            const tableRows = document.querySelectorAll('#command-records tr[data-record]');
            
            tableRows.forEach(row => {
                const input = row.getAttribute('data-input')?.toLowerCase() || '';
                const type = row.getAttribute('data-type') || '';
                const status = row.getAttribute('data-status') || '';
                
                const matchesText = !filterText || input.includes(filterText);
                const matchesType = !filterType || type === filterType;
                const matchesStatus = !filterStatus || status === filterStatus;
                
                if (matchesText && matchesType && matchesStatus) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        // 排序指令记录
        let commandRecordsSortOrder = {};
        function sortCommandRecords(column) {
            const tableBody = document.getElementById('command-records');
            const rows = Array.from(tableBody.querySelectorAll('tr[data-record]'));
            
            // 切换排序方向
            commandRecordsSortOrder[column] = commandRecordsSortOrder[column] === 'asc' ? 'desc' : 'asc';
            const isAsc = commandRecordsSortOrder[column] === 'asc';
            
            rows.sort((a, b) => {
                let valueA, valueB;
                
                switch(column) {
                    case 'time':
                        valueA = new Date(a.getAttribute('data-time'));
                        valueB = new Date(b.getAttribute('data-time'));
                        break;
                    case 'type':
                        valueA = a.getAttribute('data-type');
                        valueB = b.getAttribute('data-type');
                        break;
                    case 'status':
                        valueA = a.getAttribute('data-status') === 'true' ? 1 : 0;
                        valueB = b.getAttribute('data-status') === 'true' ? 1 : 0;
                        break;
                    case 'duration':
                        valueA = parseFloat(a.getAttribute('data-duration'));
                        valueB = parseFloat(b.getAttribute('data-duration'));
                        break;
                    default:
                        return 0;
                }
                
                if (valueA < valueB) return isAsc ? -1 : 1;
                if (valueA > valueB) return isAsc ? 1 : -1;
                return 0;
            });
            
            // 重新插入排序后的行
            rows.forEach(row => tableBody.appendChild(row));
        }
        
        // 筛选慢查询
        function filterSlowQueries() {
            const filterText = document.getElementById('slow-queries-filter')?.value.toLowerCase() || '';
            const filterType = document.getElementById('slow-queries-type-filter')?.value || '';
            const filterTime = document.getElementById('slow-queries-time-filter')?.value || '';
            const tableRows = document.querySelectorAll('#slow-queries-table tr[data-query]');
            
            tableRows.forEach(row => {
                const input = row.getAttribute('data-input')?.toLowerCase() || '';
                const type = row.getAttribute('data-type') || '';
                const duration = parseFloat(row.getAttribute('data-duration') || '0');
                
                const matchesText = !filterText || input.includes(filterText);
                const matchesType = !filterType || type === filterType;
                const matchesTime = !filterTime || duration >= parseFloat(filterTime);
                
                if (matchesText && matchesType && matchesTime) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        // 排序慢查询
        let slowQueriesSortOrder = {};
        function sortSlowQueries(column) {
            const tableBody = document.getElementById('slow-queries-table');
            const rows = Array.from(tableBody.querySelectorAll('tr[data-query]'));
            
            // 切换排序方向
            slowQueriesSortOrder[column] = slowQueriesSortOrder[column] === 'asc' ? 'desc' : 'asc';
            const isAsc = slowQueriesSortOrder[column] === 'asc';
            
            rows.sort((a, b) => {
                let valueA, valueB;
                
                switch(column) {
                    case 'time':
                        valueA = new Date(a.getAttribute('data-time'));
                        valueB = new Date(b.getAttribute('data-time'));
                        break;
                    case 'type':
                        valueA = a.getAttribute('data-type');
                        valueB = b.getAttribute('data-type');
                        break;
                    case 'duration':
                        valueA = parseFloat(a.getAttribute('data-duration'));
                        valueB = parseFloat(b.getAttribute('data-duration'));
                        break;
                    default:
                        return 0;
                }
                
                if (valueA < valueB) return isAsc ? -1 : 1;
                if (valueA > valueB) return isAsc ? 1 : -1;
                return 0;
            });
            
            // 重新插入排序后的行
            rows.forEach(row => tableBody.appendChild(row));
        }

        // 页面初始化已移动到密码验证后的initializePage函数中
    </script>
</body>
</html>
