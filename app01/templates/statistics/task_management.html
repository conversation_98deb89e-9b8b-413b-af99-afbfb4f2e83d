<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>定时任务管理 - ChatBot AutoRelease</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.4;
            min-height: 100vh;
            font-size: 13px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 12px 16px;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #ddd;
        }

        .header h1 {
            color: #333;
            margin: 0;
            font-size: 16px;
            font-weight: 500;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 400;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 3px;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .main-content {
            background: white;
            padding: 16px;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            border: 1px solid #ddd;
        }

        .filters {
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-group label {
            font-weight: 400;
            color: #666;
            font-size: 12px;
        }

        .filter-group input,
        .filter-group select {
            padding: 6px 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
            font-size: 12px;
        }

        .tasks-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .tasks-table th,
        .tasks-table td {
            padding: 8px 10px;
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 12px;
        }

        .tasks-table th {
            background: #f8f9fa;
            font-weight: 500;
            color: #666;
            position: sticky;
            top: 0;
        }

        .tasks-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            display: inline-block;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .frequency-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            background: #e3f2fd;
            color: #1565c0;
        }

        .task-actions {
            display: flex;
            gap: 4px;
            white-space: nowrap;
            min-width: 150px;
        }

        .task-actions .btn {
            padding: 4px 8px;
            font-size: 11px;
            white-space: nowrap;
        }

        .task-details {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 排序功能样式 */
        .sortable-header {
            cursor: pointer;
            user-select: none;
            position: relative;
            padding-right: 20px;
        }

        .sortable-header:hover {
            background-color: #f3f4f6;
        }

        .sort-indicator {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 10px;
            color: #9ca3af;
        }

        .sort-indicator.asc::after {
            content: '▲';
            color: #3b82f6;
        }

        .sort-indicator.desc::after {
            content: '▼';
            color: #3b82f6;
        }

        .task-id {
            width: 45px;
            text-align: center;
            font-weight: 600;
            color: #666;
        }

        .notification-column {
            min-width: 120px;
            max-width: 150px;
            word-wrap: break-word;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.3;
            cursor: help;
        }

        .task-details .query-text {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            background: #f5f5f5;
            padding: 2px 4px;
            border-radius: 2px;
            margin-top: 4px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ddd;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #999;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 32px;
            border-radius: 8px;
            width: 95%;
            max-width: 900px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 28px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-header h2 {
            margin: 0;
            color: #374151;
            font-size: 18px;
            font-weight: 500;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 400;
            color: #374151;
            font-size: 13px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
            transition: all 0.2s ease;
            background: #ffffff;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3b82f6;
            background: white;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-group textarea {
            height: 60px;
            resize: vertical;
        }

        .form-group small {
            font-size: 11px;
            color: #6b7280;
            margin-top: 2px;
            display: block;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .form-row .form-group {
            margin-bottom: 10px;
        }

        /* 三列布局用于短内容选项 */
        .form-row-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
        }

        .form-row-3 .form-group {
            margin-bottom: 10px;
        }

        .checkbox-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
        }

        .notification-types {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-top: 6px;
        }

        .notification-option {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            transition: all 0.2s;
            cursor: pointer;
        }

        .notification-option:hover {
            border-color: #ee4d2d;
            background: #fef7f6;
        }

        .notification-option input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .notification-option input[type="checkbox"]:checked + label,
        .notification-option:has(input[type="checkbox"]:checked) {
            color: #ee4d2d;
            font-weight: 500;
        }

        .notification-option:has(input[type="checkbox"]:checked) {
            border-color: #ee4d2d;
            background: #fef7f6;
        }

        .notification-option label {
            margin: 0;
            cursor: pointer;
            font-size: 12px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .pagination button:hover:not(:disabled) {
            border-color: #ee4d2d;
            color: #ee4d2d;
        }

        .pagination button.current-page {
            background: #ee4d2d;
            color: white;
            border-color: #ee4d2d;
        }

        .pagination button:disabled {
            background: #f9fafb;
            color: #9ca3af;
            cursor: not-allowed;
        }

        .alert {
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
        }

        .pagination button:hover:not(:disabled) {
            background: #f8f9fa;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination .current-page {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
            /* 群组选择样式 */
            .group-search-container {
                position: relative;
            }

            .group-search-results {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                border: 1px solid #d1d5db;
                border-top: none;
                max-height: 200px;
                overflow-y: auto;
                z-index: 1001;
                border-radius: 0 0 4px 4px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .group-search-item {
                padding: 10px 12px;
                cursor: pointer;
                border-bottom: 1px solid #f3f4f6;
                font-size: 14px;
                transition: background-color 0.2s;
            }

            .group-search-item:hover {
                background: #f9fafb;
            }

            .group-search-item:last-child {
                border-bottom: none;
            }

            .selected-groups {
                margin-top: 8px;
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
            }

            .selected-group-tag {
                background: #ff6700;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                display: flex;
                align-items: center;
                gap: 4px;
            }

            .selected-group-tag .remove-btn {
                cursor: pointer;
                font-weight: bold;
            }

            .selected-group-tag .remove-btn:hover {
                background: rgba(255, 255, 255, 0.2);
                border-radius: 50%;
            }
    </style>
</head>
<body>
    <div class="container">
                    <div class="header">
                <h1>定时任务管理</h1>
            <div class="header-actions">
                <button class="btn btn-success" onclick="openCreateModal()">
                    创建任务
                </button>
                <button class="btn btn-secondary" onclick="refreshTasksList()">
                    刷新
                </button>
            </div>
        </div>

        <div class="main-content">
            <!-- 筛选器 -->
            <div class="filters">
                <div class="filter-group">
                    <label for="user-email">用户邮箱:</label>
                    <input type="text" id="user-email" placeholder="输入用户邮箱" oninput="debounceSearch()">
                </div>
                <div class="filter-group">
                    <label for="task-search">任务搜索:</label>
                    <input type="text" id="task-search" placeholder="搜索任务名称或JQL内容" oninput="debounceSearch()">
                </div>
                <div class="filter-group">
                    <label for="task-status">状态:</label>
                    <select id="task-status" onchange="loadTasks()">
                        <option value="">全部</option>
                        <option value="active">活跃</option>
                        <option value="inactive">暂停</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="task-type">任务类型:</label>
                    <select id="task-type" onchange="loadTasks()">
                        <option value="">全部</option>
                        <option value="jira_query">JIRA查询</option>
                        <option value="reminder">提醒任务</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>
                        <input type="checkbox" id="admin-mode" onchange="loadTasks()">
                        管理员模式
                    </label>
                    <small style="color: #666; font-size: 11px; margin-left: 5px;">
                        (查看所有用户的任务)
                    </small>
                </div>
            </div>

            <!-- 任务列表 -->
            <div id="tasks-container">
                <div class="loading">正在加载任务列表...</div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑任务模态框 -->
    <div id="task-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">创建定时任务</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <div id="modal-alert"></div>
            
            <form id="task-form">
                {% csrf_token %}
                <input type="hidden" id="task-id">
                
                <div class="form-group">
                    <label for="task-name">任务名称 *</label>
                    <input type="text" id="task-name" required placeholder="请输入任务名称">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="task-task-type">任务类型 *</label>
                        <select id="task-task-type" required>
                            <option value="jira_query">JIRA查询</option>
                            <option value="reminder">提醒任务</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="task-user-email">创建人邮箱 *</label>
                        <input type="email" id="task-user-email" required placeholder="<EMAIL>">
                    </div>
                </div>

                <div class="form-group">
                    <label for="query-text">查询语句/提醒内容 *</label>
                    <textarea id="query-text" required placeholder="输入JQL查询语句或提醒内容"></textarea>
                </div>

                <div class="form-group">
                    <label for="reminder-message">消息附加文本（可选）</label>
                    <textarea id="reminder-message" placeholder="自定义通知消息内容"></textarea>
                </div>

                <div class="form-row-3">
                    <div class="form-group">
                        <label for="frequency">频率 *</label>
                        <select id="frequency" required onchange="updateScheduleOptions()">
                            <option value="weekly">定期执行</option>
                            <option value="interval">间隔执行</option>
                            <option value="monthly">每月</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="schedule-time">执行时间 *</label>
                        <input type="time" id="schedule-time" required>
                    </div>
                    <div class="form-group" style="display: flex; align-items: end;">
                        <div style="width: 100%;">
                            <label>&nbsp;</label>
                            <div style="font-size: 12px; color: #666; padding: 8px 0;">间隔执行时此项无效</div>
                        </div>
                    </div>
                </div>



                <div class="form-group" id="schedule-days-group">
                    <label>执行日期 (可选择多天):</label>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 8px; margin-top: 8px;">
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px; margin: 0;">
                            <input type="checkbox" id="day-1" value="1" style="width: auto; margin: 0;">
                            周一
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px; margin: 0;">
                            <input type="checkbox" id="day-2" value="2" style="width: auto; margin: 0;">
                            周二
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px; margin: 0;">
                            <input type="checkbox" id="day-3" value="3" style="width: auto; margin: 0;">
                            周三
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px; margin: 0;">
                            <input type="checkbox" id="day-4" value="4" style="width: auto; margin: 0;">
                            周四
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px; margin: 0;">
                            <input type="checkbox" id="day-5" value="5" style="width: auto; margin: 0;">
                            周五
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px; margin: 0;">
                            <input type="checkbox" id="day-6" value="6" style="width: auto; margin: 0;">
                            周六
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; font-size: 14px; margin: 0;">
                            <input type="checkbox" id="day-7" value="7" style="width: auto; margin: 0;">
                            周日
                        </label>
                    </div>
                    <small style="color: #6b7280; font-size: 12px; margin-top: 4px; display: block;">
                        不选择任何日期时，将按频率在所有日期执行
                    </small>
                </div>

                <div class="form-group" id="monthly-days-group" style="display: none;">
                    <label>每月执行日期 (可选择多个日期):</label>
                    <div style="display: grid; grid-template-columns: repeat(8, 1fr); gap: 6px; margin-top: 8px;">
                        <!-- 第一行：1-8号 -->
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-1" value="1" style="width: auto; margin: 0;">
                            1号
                        </label>
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-2" value="2" style="width: auto; margin: 0;">
                            2号
                        </label>
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-3" value="3" style="width: auto; margin: 0;">
                            3号
                        </label>
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-5" value="5" style="width: auto; margin: 0;">
                            5号
                        </label>
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-8" value="8" style="width: auto; margin: 0;">
                            8号
                        </label>
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-10" value="10" style="width: auto; margin: 0;">
                            10号
                        </label>
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-12" value="12" style="width: auto; margin: 0;">
                            12号
                        </label>
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-15" value="15" style="width: auto; margin: 0;">
                            15号
                        </label>
                        <!-- 第二行：18-28号 + 月末 -->
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-18" value="18" style="width: auto; margin: 0;">
                            18号
                        </label>
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-20" value="20" style="width: auto; margin: 0;">
                            20号
                        </label>
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-22" value="22" style="width: auto; margin: 0;">
                            22号
                        </label>
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-25" value="25" style="width: auto; margin: 0;">
                            25号
                        </label>
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-28" value="28" style="width: auto; margin: 0;">
                            28号
                        </label>
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-30" value="30" style="width: auto; margin: 0;">
                            30号
                        </label>
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-31" value="31" style="width: auto; margin: 0;">
                            31号
                        </label>
                        <label style="display: flex; align-items: center; gap: 3px; font-size: 12px; margin: 0;">
                            <input type="checkbox" id="monthly-day-last" value="-1" style="width: auto; margin: 0;">
                            月末
                        </label>
                    </div>
                    <small style="color: #6b7280; font-size: 12px; margin-top: 4px; display: block;">
                        不选择任何日期时，默认每月1号执行。月末表示每月最后一天。31号在没有31天的月份会自动跳过。
                    </small>
                </div>

                <div class="form-group" id="interval-config-group" style="display: none;">
                    <label style="font-size: 12px; color: #6b7280;">间隔执行配置:</label>
                    <div class="form-row-3" style="margin-top: 8px;">
                        <div class="form-group">
                            <label for="interval-start-time">开始时间</label>
                            <input type="time" id="interval-start-time" value="09:00">
                        </div>
                        <div class="form-group">
                            <label for="interval-end-time">结束时间</label>
                            <input type="time" id="interval-end-time" value="18:00">
                        </div>
                        <div class="form-group">
                            <label for="interval-minutes">间隔时间</label>
                            <select id="interval-minutes">
                                <option value="1">1分钟</option>
                                <option value="5">5分钟</option>
                                <option value="15">15分钟</option>
                                <option value="30" selected>30分钟</option>
                                <option value="60">1小时</option>
                                <option value="120">2小时</option>
                                <option value="180">3小时</option>
                                <option value="240">4小时</option>
                            </select>
                        </div>
                    </div>
                    <small style="color: #666; font-size: 11px;">
                        间隔执行将在指定时间段内按设定间隔重复执行任务
                    </small>
                </div>

                <div class="form-group">
                    <label>发送方式 (可多选)</label>
                    <div class="notification-types" style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                        <div class="notification-option" onclick="toggleCheckbox('notify-creator')">
                            <input type="checkbox" id="notify-creator" value="creator">
                            <label for="notify-creator">发送给任务创建人</label>
                        </div>
                        <div class="notification-option" onclick="toggleCheckbox('notify-assignee')">
                            <input type="checkbox" id="notify-assignee" value="assignee">
                            <label for="notify-assignee">发送给查询结果的assignee</label>
                        </div>
                        <div class="notification-option" onclick="toggleCheckbox('notify-group')">
                            <input type="checkbox" id="notify-group" value="group" onchange="updateGroupInput()">
                            <label for="notify-group">发送到指定群</label>
                        </div>
                        <div class="notification-option" onclick="toggleCheckbox('notify-auto-group')">
                            <input type="checkbox" id="notify-auto-group" value="auto_group">
                            <label for="notify-auto-group">发送到查询结果所在群</label>
                        </div>
                    </div>
                </div>

                <div class="form-group" id="target-group-group" style="display: none;">
                    <label for="group-search-input">选择群组</label>
                    <div class="group-search-container">
                        <input type="text" id="group-search-input" placeholder="搜索群名称或关键词..." oninput="searchGroups()">
                        <div id="group-search-results" class="group-search-results" style="display: none;"></div>
                        <div id="selected-groups" class="selected-groups"></div>
                    </div>
                    <small style="color: #666; font-size: 12px;">支持搜索群名称，点击选择群组，支持多选</small>
                    <!-- 隐藏字段存储原有格式数据 -->
                    <input type="hidden" id="target-group-keyword">
                </div>



                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存任务</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let currentTasks = [];
        let filteredTasks = [];
        let currentPage = 1;
        const pageSize = 50;
        let currentSort = { field: null, direction: 'asc' };

        // 全局函数声明 - 确保在HTML onclick事件中可用
        window.openCreateModal = function() {
            console.log('openCreateModal called');
            try {
                const modal = document.getElementById('task-modal');
                const modalTitle = document.getElementById('modal-title');
                const taskForm = document.getElementById('task-form');
                const taskId = document.getElementById('task-id');

                if (!modal) {
                    console.error('task-modal element not found');
                    return;
                }

                if (modalTitle) modalTitle.textContent = '创建定时任务';
                if (taskForm) taskForm.reset();
                if (taskId) taskId.value = '';

                // 默认选择发送给任务创建人
                setNotificationTypes(['creator']);

                // 初始化调度选项，但要确保在DOM元素存在后再调用
                setTimeout(() => {
                    try {
                        updateScheduleOptions();
                        updateGroupInput();
                    } catch (error) {
                        console.warn('初始化调度选项时出错:', error);
                    }
                }, 0);

                modal.style.display = 'block';
                console.log('Modal opened successfully');
            } catch (error) {
                console.error('Error opening modal:', error);
            }
        };

        window.refreshTasksList = function() {
            console.log('refreshTasksList called');
            currentPage = 1;
            loadTasks();
        };

        // 测试API连接
        async function testApiConnection() {
            try {
                const response = await fetch('/api/statistics/api/tasks/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                console.log('API connection test - Status:', response.status);
                return response.ok;
            } catch (error) {
                console.error('API connection test failed:', error);
                return false;
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded, initializing...');
            console.log('Current URL:', window.location.href);
            console.log('Base URL:', window.location.origin);

            // 先测试API连接
            testApiConnection().then(connected => {
                console.log('API connection status:', connected);
                // 延迟一点确保所有元素都已加载
                setTimeout(function() {
                    loadTasks();
                }, 100);
            });
        });

        // 加载任务列表
        async function loadTasks() {
            console.log('loadTasks called');
            const container = document.getElementById('tasks-container');
            if (!container) {
                console.error('tasks-container element not found');
                return;
            }

            container.innerHTML = '<div class="loading">正在加载任务列表...</div>';

            try {
                const userEmailEl = document.getElementById('user-email');
                const taskStatusEl = document.getElementById('task-status');
                const taskTypeEl = document.getElementById('task-type');
                const adminModeEl = document.getElementById('admin-mode');

                const userEmail = userEmailEl ? userEmailEl.value : '';
                const taskStatus = taskStatusEl ? taskStatusEl.value : '';
                const taskType = taskTypeEl ? taskTypeEl.value : '';
                const adminMode = adminModeEl ? adminModeEl.checked : false;

                const params = new URLSearchParams();
                if (userEmail) params.append('user_email', userEmail);
                if (adminMode) params.append('admin_mode', 'true');
                if (taskType) params.append('project_filter', taskType);

                // 添加排序参数
                if (currentSort.field) {
                    params.append('sort_field', currentSort.field);
                    params.append('sort_direction', currentSort.direction);
                }

                const url = `/api/statistics/api/tasks/?${params}`;
                console.log('Fetching URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    }
                });
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Response error text:', errorText);
                    throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
                }

                const result = await response.json();
                console.log('Response data:', result);

                if (result.success) {
                    currentTasks = result.data && result.data.tasks ? result.data.tasks : result.tasks || [];
                    console.log('Loaded tasks count:', currentTasks.length);
                    filterAndDisplayTasks();
                } else {
                    const errorMsg = result.error || '未知错误';
                    console.error('API returned error:', errorMsg);
                    container.innerHTML = `<div class="alert alert-error">加载失败: ${errorMsg}</div>`;
                }
            } catch (error) {
                console.error('Error loading tasks:', error);
                container.innerHTML = `<div class="alert alert-error">网络错误: ${error.message}<br><small>请检查网络连接和API服务状态</small></div>`;
            }
        }

        // 防抖搜索
        let debounceSearchTimeout;
        function debounceSearch() {
            clearTimeout(debounceSearchTimeout);
            debounceSearchTimeout = setTimeout(() => {
                filterAndDisplayTasks();
            }, 300);
        }

        // 筛选并显示任务
        function filterAndDisplayTasks() {
            const taskStatus = document.getElementById('task-status').value;
            const taskType = document.getElementById('task-type').value;
            const userEmail = document.getElementById('user-email').value.toLowerCase();
            const taskSearch = document.getElementById('task-search').value.toLowerCase();

            let filteredTasks = currentTasks;

            // 用户邮箱筛选（模糊匹配）
            if (userEmail) {
                filteredTasks = filteredTasks.filter(task => 
                    task.user_email && task.user_email.toLowerCase().includes(userEmail)
                );
            }

            // 任务搜索筛选（任务名称和JQL内容）
            if (taskSearch) {
                filteredTasks = filteredTasks.filter(task => {
                    const taskName = task.task_name ? task.task_name.toLowerCase() : '';
                    const queryText = task.query_text ? task.query_text.toLowerCase() : '';
                    const reminderMessage = task.reminder_message ? task.reminder_message.toLowerCase() : '';
                    return taskName.includes(taskSearch) || 
                           queryText.includes(taskSearch) || 
                           reminderMessage.includes(taskSearch);
                });
            }

            // 状态筛选
            if (taskStatus) {
                filteredTasks = filteredTasks.filter(task => {
                    if (taskStatus === 'active') return task.is_active;
                    if (taskStatus === 'inactive') return !task.is_active;
                    return true;
                });
            }

            // 任务类型筛选
            if (taskType) {
                filteredTasks = filteredTasks.filter(task => task.task_type === taskType);
            }

            currentPage = 1; // 重置到第一页
            displayTasks(filteredTasks);
        }

        // 显示任务列表
        function displayTasks(tasks) {
            const container = document.getElementById('tasks-container');

            if (tasks.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <h3>暂无任务</h3>
                        <p>点击"创建任务"按钮创建您的第一个定时任务</p>
                    </div>
                `;
                return;
            }

            // 存储当前筛选的任务
            filteredTasks = tasks;

            // 分页
            const totalPages = Math.ceil(tasks.length / pageSize);
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const paginatedTasks = tasks.slice(startIndex, endIndex);

            let html = `
                <table class="tasks-table">
                    <thead>
                        <tr>
                            <th class="sortable-header" onclick="sortTable('id')">ID<span class="sort-indicator" id="sort-id"></span></th>
                            <th class="sortable-header" onclick="sortTable('task_name')">任务名称和内容<span class="sort-indicator" id="sort-task_name"></span></th>
                            <th class="sortable-header" onclick="sortTable('user_email')">用户<span class="sort-indicator" id="sort-user_email"></span></th>
                            <th class="sortable-header" onclick="sortTable('task_type')">类型<span class="sort-indicator" id="sort-task_type"></span></th>
                            <th class="sortable-header" onclick="sortTable('frequency')">频率<span class="sort-indicator" id="sort-frequency"></span></th>
                            <th class="sortable-header" onclick="sortTable('schedule_time')">执行时间<span class="sort-indicator" id="sort-schedule_time"></span></th>
                            <th class="sortable-header" onclick="sortTable('is_active')">状态<span class="sort-indicator" id="sort-is_active"></span></th>
                            <th>通知方式</th>
                            <th class="sortable-header" onclick="sortTable('next_execution_time')">执行时间<span class="sort-indicator" id="sort-next_execution_time"></span></th>
                            <th style="width: 160px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            paginatedTasks.forEach(task => {
                const statusClass = task.is_active ? 'status-active' : 'status-inactive';
                const statusText = task.is_active ? '活跃' : '暂停';
                const notificationText = getNotificationTypeText(task);

                html += `
                    <tr>
                        <td class="task-id">${task.id}</td>
                        <td>
                            <div class="task-details">
                                <strong title="${escapeHtml(task.task_name)}">${escapeHtml(task.task_name)}</strong>
                                <div class="query-text" title="${escapeHtml(task.query_text || task.reminder_message || '')}">${escapeHtml(task.query_text || task.reminder_message || '').substring(0, 50)}${(task.query_text || task.reminder_message || '').length > 50 ? '...' : ''}</div>
                            </div>
                        </td>
                        <td title="${escapeHtml(task.user_email)}">${escapeHtml(task.user_email.split('@')[0])}</td>
                        <td><span class="frequency-badge">${task.task_type}</span></td>
                        <td><span class="frequency-badge">${task.frequency}</span></td>
                        <td>${task.schedule_time}</td>
                        <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                        <td class="notification-column" title="${escapeHtml(notificationText)}">${escapeHtml(notificationText)}</td>
                        <td>
                            <div style="font-size: 11px; line-height: 1.3;">
                                <div style="color: #666;">最后: ${formatDate(task.last_run_time)}</div>
                                <div style="color: #333; font-weight: 500;">下次: ${formatDate(task.next_execution_time)}</div>
                            </div>
                        </td>
                        <td>
                            <div class="task-actions">
                                <button class="btn btn-primary" onclick="editTask(${task.id})">编辑</button>
                                <button class="btn ${task.is_active ? 'btn-warning' : 'btn-success'}" onclick="toggleTask(${task.id})">
                                    ${task.is_active ? '暂停' : '启用'}
                                </button>
                                <button class="btn btn-danger" onclick="deleteTask(${task.id})">删除</button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table>';

            // 添加分页
            if (tasks.length > pageSize) {
                const totalPages = Math.ceil(tasks.length / pageSize);
                html += generatePagination(totalPages);
            }

            container.innerHTML = html;
        }

        // 生成分页HTML
        function generatePagination(totalPages) {
            let html = '<div class="pagination">';
            
            // 上一页
            html += `<button ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">上一页</button>`;
            
            // 页码
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage) {
                    html += `<button class="current-page">${i}</button>`;
                } else {
                    html += `<button onclick="changePage(${i})">${i}</button>`;
                }
            }
            
            // 下一页
            html += `<button ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">下一页</button>`;
            
            html += '</div>';
            return html;
        }

        // 换页
        function changePage(page) {
            currentPage = page;
            displayTasks(filteredTasks);
        }

        // 刷新任务列表
        function refreshTasks() {
            currentPage = 1;
            loadTasks();
        }

        // 编辑任务
        function editTask(taskId) {
            const task = currentTasks.find(t => t.id === taskId);
            if (!task) return;

            document.getElementById('modal-title').textContent = '编辑定时任务';
            document.getElementById('task-id').value = task.id;
            document.getElementById('task-name').value = task.task_name;
            document.getElementById('task-user-email').value = task.user_email;
            document.getElementById('task-task-type').value = task.task_type;
            document.getElementById('query-text').value = task.query_text || task.reminder_message || '';
            // 处理频率设置：将daily转换为weekly并选中所有天
            let displayFrequency = task.frequency;
            if (task.frequency === 'daily') {
                displayFrequency = 'weekly'; // 在前端显示为"定期执行"
            }
            document.getElementById('frequency').value = displayFrequency;
            document.getElementById('schedule-time').value = task.schedule_time;

            // 处理间隔执行配置
            if (task.frequency === 'interval' && task.interval_config) {
                try {
                    const intervalConfig = typeof task.interval_config === 'string'
                        ? JSON.parse(task.interval_config)
                        : task.interval_config;

                    if (intervalConfig.start_time) document.getElementById('interval-start-time').value = intervalConfig.start_time;
                    if (intervalConfig.end_time) document.getElementById('interval-end-time').value = intervalConfig.end_time;
                    if (intervalConfig.interval_minutes) document.getElementById('interval-minutes').value = intervalConfig.interval_minutes;
                } catch (e) {
                    console.error('解析间隔配置失败:', e);
                }
            }
            // 设置群关键词和群组选择
            selectedGroups = []; // 重置选中的群组
            let groupKeyword = '';
            if (task.target_group_id && task.target_group_id.startsWith('keyword:')) {
                groupKeyword = task.target_group_id.replace('keyword:', '');
            } else if (task.target_group_id && task.target_group_id.startsWith('group_id:')) {
                // 新格式：group_id:123456
                const groupId = task.target_group_id.replace('group_id:', '');
                groupKeyword = task.target_group_keyword || '';
                // 如果有群组信息，添加到选中列表
                if (groupKeyword) {
                    selectedGroups.push({ id: groupId, name: groupKeyword });
                    updateSelectedGroups();
                }
            }
            document.getElementById('target-group-keyword').value = groupKeyword;
            document.getElementById('reminder-message').value = task.reminder_message || '';
            
            // 设置通知方式（优先使用新的notification_methods字段）
            let notificationTypes = task.notification_methods || [];
            if (notificationTypes.length === 0) {
                // 兼容旧数据和新数据，从notification_type转换
                if (task.notification_type === 'multi') {
                    notificationTypes = ['creator']; // 多选类型无法确定，默认创建人
                } else if (['creator', 'assignee', 'group', 'auto_group'].includes(task.notification_type)) {
                    notificationTypes = [task.notification_type];
                } else {
                    // 处理遗留的老值
                    const legacyMapping = {
                        'private': ['creator'],
                        'smart': ['assignee'],
                        'both': ['creator', 'group']
                    };
                    notificationTypes = legacyMapping[task.notification_type] || ['creator'];
                }
            }
            setNotificationTypes(notificationTypes);

            // 设置调度日期
            let scheduleDays = [];
            if (task.frequency === 'daily') {
                // daily任务：选中所有天
                scheduleDays = [1, 2, 3, 4, 5, 6, 7];
            } else if (task.schedule_days) {
                if (typeof task.schedule_days === 'string') {
                    scheduleDays = task.schedule_days.split(',').map(d => parseInt(d)).filter(d => d > 0);
                } else if (Array.isArray(task.schedule_days)) {
                    scheduleDays = task.schedule_days.map(d => parseInt(d)).filter(d => d > 0);
                }
            }

            // 清空所有复选框（星期）
            for (let i = 1; i <= 7; i++) {
                const checkbox = document.getElementById(`day-${i}`);
                if (checkbox) {
                    checkbox.checked = scheduleDays.includes(i);
                }
            }

            // 清空所有复选框（每月日期）
            const monthlyDayIds = [
                'monthly-day-1', 'monthly-day-2', 'monthly-day-3', 'monthly-day-5', 'monthly-day-8',
                'monthly-day-10', 'monthly-day-12', 'monthly-day-15', 'monthly-day-18', 'monthly-day-20',
                'monthly-day-22', 'monthly-day-25', 'monthly-day-28', 'monthly-day-30', 'monthly-day-31',
                'monthly-day-last'
            ];
            monthlyDayIds.forEach(id => {
                const checkbox = document.getElementById(id);
                if (checkbox) {
                    const value = parseInt(checkbox.value);
                    checkbox.checked = scheduleDays.includes(value);
                }
            });

            try {
                updateScheduleOptions();
                updateGroupInput();
            } catch (error) {
                console.warn('更新调度选项时出错:', error);
            }
            document.getElementById('task-modal').style.display = 'block';
        }

        // 切换任务状态
        async function toggleTask(taskId) {
            try {
                const response = await fetch(`/api/statistics/api/tasks/${taskId}/toggle/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    }
                });

                const result = await response.json();
                if (result.success) {
                    showAlert('success', result.message);
                    loadTasks();
                } else {
                    showAlert('error', result.error);
                }
            } catch (error) {
                showAlert('error', `操作失败: ${error.message}`);
            }
        }

        // 删除任务
        async function deleteTask(taskId) {
            if (!confirm('确定要删除这个任务吗？此操作不可撤销。')) {
                return;
            }

            try {
                const response = await fetch(`/api/statistics/api/tasks/${taskId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    }
                });

                const result = await response.json();
                if (result.success) {
                    showAlert('success', result.message);
                    loadTasks();
                } else {
                    showAlert('error', result.error);
                }
            } catch (error) {
                showAlert('error', `删除失败: ${error.message}`);
            }
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('task-modal').style.display = 'none';
            document.getElementById('modal-alert').innerHTML = '';
        }

        // 更新调度选项显示
        function updateScheduleOptions() {
            const frequency = document.getElementById('frequency').value;
            const scheduleDaysGroup = document.getElementById('schedule-days-group');
            const monthlyDaysGroup = document.getElementById('monthly-days-group');
            const intervalConfigGroup = document.getElementById('interval-config-group');
            const scheduleTimeGroup = document.querySelector('label[for="schedule-time"]').parentElement;

            // 默认隐藏所有特殊配置组
            scheduleDaysGroup.style.display = 'none';
            monthlyDaysGroup.style.display = 'none';
            intervalConfigGroup.style.display = 'none';
            scheduleTimeGroup.style.display = 'block';

            if (frequency === 'weekly') {
                // 定期执行：显示星期日期选择
                scheduleDaysGroup.style.display = 'block';
                scheduleTimeGroup.style.display = 'block';

                // 检查是否有选中的天，如果没有则默认选择所有天
                try {
                    const selectedDays = getSelectedDays();
                    if (selectedDays.length === 0) {
                        // 默认选择所有天（每日执行）
                        for (let i = 1; i <= 7; i++) {
                            const checkbox = document.getElementById(`day-${i}`);
                            if (checkbox) checkbox.checked = true;
                        }
                    }
                } catch (error) {
                    console.warn('无法获取选中的天数，可能是复选框还未初始化:', error);
                }
            } else if (frequency === 'interval') {
                // 间隔执行显示间隔配置和星期选择
                scheduleDaysGroup.style.display = 'block';
                intervalConfigGroup.style.display = 'block';
                scheduleTimeGroup.style.display = 'none'; // 间隔执行不需要单独的执行时间
            } else if (frequency === 'hourly') {
                scheduleDaysGroup.style.display = 'block'; // 仍可选择执行日期
                scheduleTimeGroup.style.display = 'none'; // 每小时执行不需要指定时间
            } else if (frequency === 'monthly') {
                // 每月执行：显示每月日期选择，隐藏星期选择
                monthlyDaysGroup.style.display = 'block';
                scheduleTimeGroup.style.display = 'block';

                // 检查是否有选中的月日期，如果没有则默认选择1号
                try {
                    const selectedMonthlyDays = getSelectedMonthlyDays();
                    if (selectedMonthlyDays.length === 0) {
                        const checkbox = document.getElementById('monthly-day-1');
                        if (checkbox) checkbox.checked = true;
                    }
                } catch (error) {
                    console.warn('无法获取选中的月日期，可能是复选框还未初始化:', error);
                }
            }
        }

        // 保持向后兼容
        function updateScheduleDays() {
            updateScheduleOptions();
        }

        // 更新群组输入框显示
        function updateGroupInput() {
            const notifyGroup = document.getElementById('notify-group').checked;
            const targetGroupGroup = document.getElementById('target-group-group');
            
            if (notifyGroup) {
                targetGroupGroup.style.display = 'block';
            } else {
                targetGroupGroup.style.display = 'none';
            }
        }

        // 获取选中的通知方式
        function getSelectedNotificationTypes() {
            const types = [];
            if (document.getElementById('notify-creator').checked) types.push('creator');
            if (document.getElementById('notify-assignee').checked) types.push('assignee');
            if (document.getElementById('notify-group').checked) types.push('group');
            if (document.getElementById('notify-auto-group').checked) types.push('auto_group');
            return types;
        }

        // 获取选中的执行日期
        function getSelectedDays() {
            const days = [];
            for (let i = 1; i <= 7; i++) {
                const checkbox = document.getElementById(`day-${i}`);
                if (checkbox && checkbox.checked) {
                    days.push(i);
                }
            }
            return days;
        }

        // 获取选中的每月日期
        function getSelectedMonthlyDays() {
            const selectedDays = [];
            const monthlyDayIds = [
                'monthly-day-1', 'monthly-day-2', 'monthly-day-3', 'monthly-day-5', 'monthly-day-8',
                'monthly-day-10', 'monthly-day-12', 'monthly-day-15', 'monthly-day-18', 'monthly-day-20',
                'monthly-day-22', 'monthly-day-25', 'monthly-day-28', 'monthly-day-30', 'monthly-day-31',
                'monthly-day-last'
            ];

            monthlyDayIds.forEach(id => {
                const checkbox = document.getElementById(id);
                if (checkbox && checkbox.checked) {
                    selectedDays.push(parseInt(checkbox.value));
                }
            });
            return selectedDays;
        }

        // 表格排序功能
        window.sortTable = function(field) {
            // 切换排序方向
            if (currentSort.field === field) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.field = field;
                currentSort.direction = 'asc';
            }

            // 更新排序指示器
            updateSortIndicators();

            // 重新加载数据（带排序参数）
            loadTasks();
        };

        // 更新排序指示器
        function updateSortIndicators() {
            // 清除所有指示器
            document.querySelectorAll('.sort-indicator').forEach(indicator => {
                indicator.className = 'sort-indicator';
            });

            // 设置当前排序字段的指示器
            if (currentSort.field) {
                const indicator = document.getElementById(`sort-${currentSort.field}`);
                if (indicator) {
                    indicator.className = `sort-indicator ${currentSort.direction}`;
                }
            }
        }

        // 切换复选框状态
        window.toggleCheckbox = function(checkboxId) {
            const checkbox = document.getElementById(checkboxId);
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                // 触发change事件，以便执行相关的onchange处理
                checkbox.dispatchEvent(new Event('change'));
            }
        };

        // 设置通知方式选择
        function setNotificationTypes(types) {
            // 先清空所有选择
            document.getElementById('notify-creator').checked = false;
            document.getElementById('notify-assignee').checked = false;
            document.getElementById('notify-group').checked = false;
            document.getElementById('notify-auto-group').checked = false;
            
            // 设置选中项
            if (types.includes('creator')) document.getElementById('notify-creator').checked = true;
            if (types.includes('assignee')) document.getElementById('notify-assignee').checked = true;
            if (types.includes('group')) document.getElementById('notify-group').checked = true;
            if (types.includes('auto_group')) document.getElementById('notify-auto-group').checked = true;
            
            updateGroupInput();
        }

        // 群组搜索相关变量
        let selectedGroups = [];
        let searchTimeout = null;

        // 搜索群组
        function searchGroups() {
            const keyword = document.getElementById('group-search-input').value.trim();
            const resultsContainer = document.getElementById('group-search-results');

            if (!keyword) {
                resultsContainer.style.display = 'none';
                return;
            }

            // 防抖搜索
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                fetch(`/api/statistics/api/search-groups/?keyword=${encodeURIComponent(keyword)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displaySearchResults(data.groups);
                        } else {
                            console.error('搜索群组失败:', data.error);
                        }
                    })
                    .catch(error => {
                        console.error('搜索群组异常:', error);
                    });
            }, 300);
        }

        // 显示搜索结果
        function displaySearchResults(groups) {
            const resultsContainer = document.getElementById('group-search-results');
            
            if (groups.length === 0) {
                resultsContainer.innerHTML = '<div class="group-search-item">未找到匹配的群组</div>';
                resultsContainer.style.display = 'block';
                return;
            }

            resultsContainer.innerHTML = groups.map(group => 
                `<div class="group-search-item" onclick="selectGroup('${group.group_id}', '${escapeHtml(group.group_name)}')">
                    ${escapeHtml(group.display_name)}
                </div>`
            ).join('');
            
            resultsContainer.style.display = 'block';
        }

        // 选择群组
        function selectGroup(groupId, groupName) {
            // 避免重复选择
            if (selectedGroups.find(g => g.id === groupId)) {
                return;
            }

            selectedGroups.push({ id: groupId, name: groupName });
            updateSelectedGroups();
            updateTargetGroupKeyword();
            
            // 清空搜索
            document.getElementById('group-search-input').value = '';
            document.getElementById('group-search-results').style.display = 'none';
        }

        // 移除群组
        function removeGroup(groupId) {
            selectedGroups = selectedGroups.filter(g => g.id !== groupId);
            updateSelectedGroups();
            updateTargetGroupKeyword();
        }

        // 更新选中的群组显示
        function updateSelectedGroups() {
            const container = document.getElementById('selected-groups');
            
            if (selectedGroups.length === 0) {
                container.innerHTML = '';
                return;
            }

            container.innerHTML = selectedGroups.map(group =>
                `<div class="selected-group-tag">
                    ${escapeHtml(group.name)}
                    <span class="remove-btn" onclick="removeGroup('${group.id}')">&times;</span>
                </div>`
            ).join('');
        }

        // 更新隐藏字段（保持向后兼容）
        function updateTargetGroupKeyword() {
            const hiddenField = document.getElementById('target-group-keyword');
            if (selectedGroups.length > 0) {
                // 使用第一个群组的ID
                hiddenField.value = `group_id:${selectedGroups[0].id}`;
            } else {
                hiddenField.value = '';
            }
        }

        // 点击外部关闭搜索结果
        document.addEventListener('click', function(event) {
            const container = document.querySelector('.group-search-container');
            const resultsContainer = document.getElementById('group-search-results');
            
            if (container && !container.contains(event.target)) {
                resultsContainer.style.display = 'none';
            }
        });

        // 提交表单
        document.getElementById('task-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const taskId = document.getElementById('task-id').value;
            const isEdit = !!taskId;
            
            // 收集表单数据
            const selectedTypes = getSelectedNotificationTypes();
            if (selectedTypes.length === 0) {
                showModalAlert('error', '请至少选择一种发送方式');
                return;
            }
            
            const frequency = document.getElementById('frequency').value;
            let scheduleTime = document.getElementById('schedule-time').value;

            // 处理不同频率的时间设置
            if (frequency === 'hourly') {
                scheduleTime = '00:00'; // 每小时执行不需要具体时间
            } else if (frequency === 'interval') {
                // 间隔执行不使用schedule_time，保持原值或使用默认值
                scheduleTime = scheduleTime || '09:00';
            }

            const formData = {
                task_name: document.getElementById('task-name').value,
                user_email: document.getElementById('task-user-email').value,
                task_type: document.getElementById('task-task-type').value,
                query_text: document.getElementById('query-text').value,
                frequency: frequency,
                schedule_time: scheduleTime,
                notification_types: selectedTypes,
                target_group_keyword: document.getElementById('target-group-keyword').value,
                reminder_message: document.getElementById('reminder-message').value
            };

            // 添加间隔执行的配置
            if (frequency === 'interval') {
                formData.interval_start_time = document.getElementById('interval-start-time').value;
                formData.interval_end_time = document.getElementById('interval-end-time').value;
                formData.interval_minutes = parseInt(document.getElementById('interval-minutes').value);
            }

            // 收集调度日期
            let scheduleDays = [];

            if (frequency === 'monthly') {
                // 每月执行：收集每月日期
                const monthlyDayIds = [
                    'monthly-day-1', 'monthly-day-2', 'monthly-day-3', 'monthly-day-5', 'monthly-day-8',
                    'monthly-day-10', 'monthly-day-12', 'monthly-day-15', 'monthly-day-18', 'monthly-day-20',
                    'monthly-day-22', 'monthly-day-25', 'monthly-day-28', 'monthly-day-30', 'monthly-day-31',
                    'monthly-day-last'
                ];

                monthlyDayIds.forEach(id => {
                    const checkbox = document.getElementById(id);
                    if (checkbox && checkbox.checked) {
                        scheduleDays.push(parseInt(checkbox.value));
                    }
                });

                // 如果没有选择任何日期，默认选择1号
                if (scheduleDays.length === 0) {
                    scheduleDays.push(1);
                }
            } else {
                // 其他频率：收集星期日期
                for (let i = 1; i <= 7; i++) {
                    const checkbox = document.getElementById(`day-${i}`);
                    if (checkbox && checkbox.checked) {
                        scheduleDays.push(i);
                    }
                }
            }

            // 智能转换频率：如果选择了所有7天，转换为daily；否则保持weekly
            let finalFrequency = frequency;
            if (frequency === 'weekly' && scheduleDays.length === 7) {
                finalFrequency = 'daily';
                formData.schedule_days = []; // daily不需要指定具体天数
            } else {
                formData.schedule_days = scheduleDays;
            }
            formData.frequency = finalFrequency;

            try {
                const url = isEdit ? 
                    `/api/statistics/api/tasks/${taskId}/update/` : 
                    '/api/statistics/api/tasks/create/';
                
                const method = isEdit ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken()
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                
                if (result.success) {
                    showModalAlert('success', result.message || (isEdit ? '任务更新成功' : '任务创建成功'));
                    setTimeout(() => {
                        closeModal();
                        loadTasks();
                    }, 1500);
                } else {
                    showModalAlert('error', result.error);
                }
            } catch (error) {
                showModalAlert('error', `操作失败: ${error.message}`);
            }
        });

        // 显示全局提示
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            const container = document.querySelector('.main-content');
            container.insertBefore(alertDiv, container.firstChild);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 3000);
        }

        // 显示模态框内提示
        function showModalAlert(type, message) {
            const modalAlert = document.getElementById('modal-alert');
            modalAlert.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        }

        // 获取CSRF Token
        function getCsrfToken() {
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            return csrfToken ? csrfToken.value : '';
        }

        // HTML转义
        function escapeHtml(text) {
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text ? text.replace(/[&<>"']/g, m => map[m]) : '';
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            try {
                const date = new Date(dateStr);
                return date.toLocaleString('zh-CN');
            } catch {
                return '-';
            }
        }

        // 获取通知类型文本
        function getNotificationTypeText(task) {
            const types = [];
            const methods = task.notification_methods || [];
            
            if (methods.includes('creator')) types.push('创建人');
            if (methods.includes('assignee')) types.push('Assignee');
            if (methods.includes('group')) {
                // 指定群显示群信息
                if (task.target_group_id && task.target_group_id.startsWith('keyword:')) {
                    const keyword = task.target_group_id.replace('keyword:', '');
                    types.push(`指定群(${keyword})`);
                } else {
                    types.push('指定群');
                }
            }
            if (methods.includes('auto_group')) types.push('结果所在群');
            
            return types.join(' + ') || task.notification_type;
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('task-modal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
