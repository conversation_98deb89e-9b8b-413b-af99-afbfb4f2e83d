<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R&D效率指标说明</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #f0f0f0;
        }
        .page-title {
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
        }
        .page-title::before {
            content: "";
            width: 20px;
            height: 20px;
            background: #ff6b35;
            border-radius: 4px;
            margin-right: 8px;
            display: inline-block;
        }
        .subtitle {
            margin: 0;
            font-size: 14px;
            color: #666;
        }
        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            background: #ff6b35;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            margin-top: 16px;
            transition: all 0.2s ease;
        }
        .back-btn:hover {
            background: #e55a2b;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        .metric-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #f0f0f0;
        }
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: #ff6b35;
            border-radius: 8px 8px 0 0;
        }
        .metric-card {
            position: relative;
        }
        .metric-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        .metric-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
        }
        .metric-formula {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 8px 12px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            color: #495057;
            margin-bottom: 8px;
        }
        .metric-standard {
            font-size: 13px;
            color: #666;
        }
        .standard-item {
            margin: 4px 0;
        }
        .good { color: #52c41a; }
        .warning { color: #faad14; }
        .poor { color: #ff4d4f; }
        
        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="page-title">R&D效率指标说明</h1>
            <p class="subtitle">了解各项指标的计算方法和评判标准</p>
            <a href="/rd-metrics/" class="back-btn">← 返回仪表板</a>
        </div>

        <div class="metrics-grid">
            <!-- 团队速度 -->
            <div class="metric-card">
                <div class="metric-name">团队速度</div>
                <div class="metric-desc">团队在指定周期内完成的工时总数，反映团队整体交付能力</div>
                <div class="metric-formula">团队速度 = Σ(已完成任务的工时)</div>
                <div class="metric-standard">
                    <div class="standard-item good">• 优秀: ≥40工时/周期</div>
                    <div class="standard-item warning">• 一般: 20-40工时/周期</div>
                    <div class="standard-item poor">• 需改进: <20工时/周期</div>
                </div>
            </div>

            <!-- Bug修复率 -->
            <div class="metric-card">
                <div class="metric-name">Bug修复率</div>
                <div class="metric-desc">Bug修复的比例，反映团队问题解决能力</div>
                <div class="metric-formula">Bug修复率 = (已修复Bug数 ÷ 总Bug数) × 100%</div>
                <div class="metric-standard">
                    <div class="standard-item good">• 优秀: ≥95%</div>
                    <div class="standard-item warning">• 一般: 90-95%</div>
                    <div class="standard-item poor">• 需改进: <90%</div>
                </div>
            </div>

            <!-- 平均周期时间 -->
            <div class="metric-card">
                <div class="metric-name">平均周期时间</div>
                <div class="metric-desc">从开始开发到完成的平均时间，反映开发效率</div>
                <div class="metric-formula">周期时间 = 完成时间 - 开始开发时间</div>
                <div class="metric-standard">
                    <div class="standard-item good">• 优秀: ≤3天</div>
                    <div class="standard-item warning">• 一般: 3-7天</div>
                    <div class="standard-item poor">• 需改进: >7天</div>
                </div>
            </div>

            <!-- 代码质量评分 -->
            <div class="metric-card">
                <div class="metric-name">代码质量评分</div>
                <div class="metric-desc">基于缺陷密度、返工率等计算的综合质量评分</div>
                <div class="metric-formula">质量评分 = 10 - (缺陷密度×10) - (返工率÷10)</div>
                <div class="metric-standard">
                    <div class="standard-item good">• 优秀: ≥8分</div>
                    <div class="standard-item warning">• 一般: 6-8分</div>
                    <div class="standard-item poor">• 需改进: <6分</div>
                </div>
            </div>

            <!-- 人均生产力 -->
            <div class="metric-card">
                <div class="metric-name">人均生产力</div>
                <div class="metric-desc">人均完成的工时数，反映个人效率</div>
                <div class="metric-formula">人均生产力 = 总完成工时 ÷ 团队人数</div>
                <div class="metric-standard">
                    <div class="standard-item good">• 优秀: ≥15工时/人</div>
                    <div class="standard-item warning">• 一般: 10-15工时/人</div>
                    <div class="standard-item poor">• 需改进: <10工时/人</div>
                </div>
            </div>

            <!-- 综合评分 -->
            <div class="metric-card">
                <div class="metric-name">综合评分</div>
                <div class="metric-desc">基于速度、质量、周期时间、生产力的综合评分</div>
                <div class="metric-formula">综合评分 = 速度×30% + 质量×30% + 时间×20% + 生产力×20%</div>
                <div class="metric-standard">
                    <div class="standard-item good">• A级: ≥80分</div>
                    <div class="standard-item warning">• B级: 60-80分</div>
                    <div class="standard-item poor">• C级及以下: <60分</div>
                </div>
            </div>
        </div>

        <!-- 数据来源说明 -->
        <div class="metric-card" style="margin-top: 24px;">
            <div class="metric-name">数据来源说明</div>
            <div class="metric-desc">
                <p><strong>JIRA数据：</strong>从SPCB和SPCT项目中获取任务、Bug、工时等数据</p>
                <p><strong>Git数据：</strong>从关联的Git仓库中获取提交、合并请求等数据</p>
                <p><strong>更新频率：</strong>每日自动更新，支持手动刷新</p>
                <p><strong>数据范围：</strong>支持7天、30天、90天的时间范围查询</p>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="metric-card" style="margin-top: 20px;">
            <div class="metric-name">使用说明</div>
            <div class="metric-desc">
                <p><strong>1. 选择团队：</strong>从下拉列表中选择要查看的团队</p>
                <p><strong>2. 设置时间范围：</strong>选择要分析的时间周期</p>
                <p><strong>3. 查看指标：</strong>点击指标卡片可查看详细趋势</p>
                <p><strong>4. 团队对比：</strong>管理员可进行多团队对比分析</p>
                <p><strong>5. 数据导出：</strong>支持导出Excel格式的分析报告</p>
            </div>
        </div>
    </div>
</body>
</html>
