<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R&D团队管理 - Chatbot 任意门</title>
    <!-- 引入Element Plus样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #303133;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #409eff;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .btn-primary {
            background-color: #409eff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #66b1ff;
        }
        .btn-secondary {
            background-color: #909399;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #a6a9ad;
        }
        .btn-success {
            background-color: #67c23a;
            color: white;
        }
        .btn-success:hover {
            background-color: #85ce61;
        }
        .btn-warning {
            background-color: #e6a23c;
            color: white;
        }
        .btn-warning:hover {
            background-color: #ebb563;
        }
        .btn-danger {
            background-color: #f56c6c;
            color: white;
        }
        .btn-danger:hover {
            background-color: #f78989;
        }
        .card {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .team-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .team-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
        }
        .team-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        .team-title {
            font-size: 18px;
            font-weight: bold;
            color: #303133;
            margin: 0;
        }
        .team-id {
            font-size: 12px;
            color: #909399;
            background-color: #f4f4f5;
            padding: 2px 8px;
            border-radius: 12px;
        }
        .team-info {
            margin-bottom: 15px;
        }
        .team-info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        .team-info-label {
            color: #606266;
            font-weight: 500;
        }
        .team-info-value {
            color: #303133;
        }
        .team-projects {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 5px;
        }
        .project-tag {
            background-color: #e1f3ff;
            color: #409eff;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .team-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #ebeef5;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }
        .pagination a, .pagination span {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            text-decoration: none;
            color: #606266;
        }
        .pagination a:hover {
            background-color: #f5f7fa;
        }
        .pagination .current {
            background-color: #409eff;
            color: white;
            border-color: #409eff;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
        }
        .modal-title {
            font-size: 18px;
            font-weight: bold;
            color: #303133;
        }
        .close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #909399;
        }
        .close:hover {
            color: #606266;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #606266;
        }
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-control:focus {
            outline: none;
            border-color: #409eff;
        }
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ebeef5;
        }
        .alert {
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .alert-success {
            background-color: #f0f9ff;
            color: #67c23a;
            border: 1px solid #c2e7b0;
        }
        .alert-error {
            background-color: #fef0f0;
            color: #f56c6c;
            border: 1px solid #fbc4c4;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #909399;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>R&D团队管理</h1>
            <div>
                {% if can_configure %}
                <button class="btn btn-primary" onclick="openTeamModal()">
                    <span>➕</span> 新建团队
                </button>
                {% endif %}
                <button class="btn btn-secondary" onclick="refreshTeams()">
                    <span>🔄</span> 刷新
                </button>
            </div>
        </div>

        <!-- 团队列表 -->
        <div class="team-grid" id="teamGrid">
            {% for team in page_obj %}
            <div class="team-card" data-team-id="{{ team.team_id }}">
                <div class="team-header">
                    <h3 class="team-title">{{ team.team_name }}</h3>
                    <span class="team-id">{{ team.team_id }}</span>
                </div>
                
                <div class="team-info">
                    <div class="team-info-item">
                        <span class="team-info-label">部门:</span>
                        <span class="team-info-value">{{ team.department }}</span>
                    </div>
                    <div class="team-info-item">
                        <span class="team-info-label">负责人:</span>
                        <span class="team-info-value">{{ team.team_leader_email }}</span>
                    </div>
                    <div class="team-info-item">
                        <span class="team-info-label">成员数:</span>
                        <span class="team-info-value">{{ team.members.count }}</span>
                    </div>
                    <div class="team-info-item">
                        <span class="team-info-label">JIRA项目:</span>
                        <div class="team-projects">
                            {% for project in team.jira_projects %}
                            <span class="project-tag">{{ project }}</span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                
                <div class="team-actions">
                    <button class="btn btn-primary" onclick="viewTeamDetail('{{ team.team_id }}')">
                        查看详情
                    </button>
                    {% if can_configure %}
                    <button class="btn btn-warning" onclick="editTeam('{{ team.team_id }}')">
                        编辑
                    </button>
                    {% endif %}
                </div>
            </div>
            {% empty %}
            <div class="card">
                <p style="text-align: center; color: #909399;">暂无团队数据</p>
            </div>
            {% endfor %}
        </div>

        <!-- 分页 -->
        {% if page_obj.has_other_pages %}
        <div class="pagination">
            {% if page_obj.has_previous %}
                <a href="?page=1">&laquo; 首页</a>
                <a href="?page={{ page_obj.previous_page_number }}">上一页</a>
            {% endif %}
            
            <span class="current">
                第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页
            </span>
            
            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}">下一页</a>
                <a href="?page={{ page_obj.paginator.num_pages }}">末页 &raquo;</a>
            {% endif %}
        </div>
        {% endif %}
    </div>

    <!-- 团队编辑模态框 -->
    <div id="teamModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">新建团队</h3>
                <span class="close" onclick="closeTeamModal()">&times;</span>
            </div>
            
            <form id="teamForm">
                <div class="form-group">
                    <label class="form-label">团队ID *</label>
                    <input type="text" class="form-control" id="teamId" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">团队名称 *</label>
                    <input type="text" class="form-control" id="teamName" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">所属部门 *</label>
                    <input type="text" class="form-control" id="department" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">团队负责人邮箱 *</label>
                    <input type="email" class="form-control" id="teamLeaderEmail" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">父团队ID</label>
                    <input type="text" class="form-control" id="parentTeamId">
                </div>
                
                <div class="form-group">
                    <label class="form-label">JIRA项目 (逗号分隔)</label>
                    <input type="text" class="form-control" id="jiraProjects" placeholder="SPCB,SPCT">
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeTeamModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let currentTeamId = null;
        
        function openTeamModal(teamId = null) {
            currentTeamId = teamId;
            const modal = document.getElementById('teamModal');
            const title = document.getElementById('modalTitle');
            
            if (teamId) {
                title.textContent = '编辑团队';
                loadTeamData(teamId);
            } else {
                title.textContent = '新建团队';
                document.getElementById('teamForm').reset();
            }
            
            modal.style.display = 'block';
        }
        
        function closeTeamModal() {
            document.getElementById('teamModal').style.display = 'none';
            currentTeamId = null;
        }
        
        function editTeam(teamId) {
            openTeamModal(teamId);
        }
        
        function viewTeamDetail(teamId) {
            // 跳转到团队详情页面或打开详情模态框
            window.location.href = `/rd-metrics/teams/${teamId}/`;
        }
        
        async function loadTeamData(teamId) {
            try {
                const response = await fetch(`/rd-metrics/teams/${teamId}/`);
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    document.getElementById('teamId').value = data.team_id;
                    document.getElementById('teamName').value = data.team_name;
                    document.getElementById('department').value = data.department;
                    document.getElementById('teamLeaderEmail').value = data.team_leader_email;
                    document.getElementById('parentTeamId').value = data.parent_team_id || '';
                    document.getElementById('jiraProjects').value = data.jira_projects.join(',');
                } else {
                    alert('加载团队数据失败: ' + result.error);
                }
            } catch (error) {
                console.error('加载团队数据失败:', error);
                alert('加载团队数据失败');
            }
        }
        
        document.getElementById('teamForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                team_id: document.getElementById('teamId').value,
                team_name: document.getElementById('teamName').value,
                department: document.getElementById('department').value,
                team_leader_email: document.getElementById('teamLeaderEmail').value,
                parent_team_id: document.getElementById('parentTeamId').value || null,
                jira_projects: document.getElementById('jiraProjects').value.split(',').map(s => s.trim()).filter(s => s),
                git_repositories: []
            };
            
            try {
                const url = currentTeamId ? `/rd-metrics/teams/${currentTeamId}/` : '/rd-metrics/teams/new/';
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert(result.message);
                    closeTeamModal();
                    location.reload();
                } else {
                    alert('保存失败: ' + result.error);
                }
            } catch (error) {
                console.error('保存团队失败:', error);
                alert('保存团队失败');
            }
        });
        
        function refreshTeams() {
            location.reload();
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('teamModal');
            if (event.target === modal) {
                closeTeamModal();
            }
        }
    </script>
</body>
</html>
