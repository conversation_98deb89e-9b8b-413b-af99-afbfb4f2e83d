<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R&D效率指标说明 - Chatbot 任意门</title>
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #303133;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #409eff;
        }
        .header h1 {
            font-size: 32px;
            color: #409eff;
            margin: 0 0 10px 0;
        }
        .header p {
            font-size: 16px;
            color: #606266;
            margin: 0;
        }
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            color: #409eff;
            text-decoration: none;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .section {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        .section h2 {
            font-size: 24px;
            color: #303133;
            margin: 0 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
        }
        .section h3 {
            font-size: 18px;
            color: #409eff;
            margin: 25px 0 15px 0;
        }
        .metric-item {
            margin-bottom: 25px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #409eff;
        }
        .metric-name {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
        }
        .metric-description {
            color: #606266;
            margin-bottom: 10px;
        }
        .metric-formula {
            background-color: #e6f7ff;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #1890ff;
            margin-bottom: 10px;
        }
        .metric-interpretation {
            font-size: 14px;
            color: #67c23a;
        }
        .metric-interpretation strong {
            color: #303133;
        }
        .toc {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .toc h3 {
            margin: 0 0 15px 0;
            color: #303133;
        }
        .toc ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .toc li {
            margin-bottom: 8px;
        }
        .toc a {
            color: #409eff;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        .grade-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .grade-table th,
        .grade-table td {
            border: 1px solid #ebeef5;
            padding: 12px;
            text-align: center;
        }
        .grade-table th {
            background-color: #f5f7fa;
            font-weight: bold;
            color: #303133;
        }
        .grade-a { background-color: #f0f9ff; color: #67c23a; }
        .grade-b { background-color: #fdf6ec; color: #e6a23c; }
        .grade-c { background-color: #fef0f0; color: #f56c6c; }
    </style>
</head>
<body>
    <div class="container">
        <a href="/rd-metrics/" class="back-link">
            <span>←</span> 返回仪表板
        </a>
        
        <div class="header">
            <h1>📊 R&D效率指标说明</h1>
            <p>全面了解研发效率指标的定义、计算方法和解读标准</p>
        </div>

        <!-- 目录 -->
        <div class="toc">
            <h3>📋 目录</h3>
            <ul>
                <li><a href="#velocity-metrics"><span>🚀</span> 开发速度指标</a></li>
                <li><a href="#quality-metrics"><span>🎯</span> 质量指标</a></li>
                <li><a href="#cycle-time-metrics"><span>⏱️</span> 周期时间指标</a></li>
                <li><a href="#team-performance-metrics"><span>👥</span> 团队效能指标</a></li>
                <li><a href="#comprehensive-score"><span>📈</span> 综合评分体系</a></li>
                <li><a href="#data-sources"><span>📊</span> 数据来源说明</a></li>
            </ul>
        </div>

        <!-- 开发速度指标 -->
        <div class="section" id="velocity-metrics">
            <h2>🚀 开发速度指标</h2>
            <p>衡量团队交付能力和开发效率的核心指标</p>

            <div class="metric-item">
                <div class="metric-name">团队速度 (Team Velocity)</div>
                <div class="metric-description">团队在指定周期内完成的故事点总数，反映团队整体交付能力</div>
                <div class="metric-formula">团队速度 = Σ(已完成故事的故事点)</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>数值越高表示团队交付能力越强。建议与历史数据对比，关注趋势变化。
                </div>
            </div>

            <div class="metric-item">
                <div class="metric-name">平均速度 (Average Velocity)</div>
                <div class="metric-description">平均每个冲刺完成的故事点数，用于评估团队稳定性</div>
                <div class="metric-formula">平均速度 = 总故事点 ÷ 冲刺数量</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>稳定的平均速度表示团队工作节奏良好，波动较大可能存在计划或执行问题。
                </div>
            </div>

            <div class="metric-item">
                <div class="metric-name">吞吐量 (Throughput)</div>
                <div class="metric-description">平均每天解决的问题数量，反映团队处理效率</div>
                <div class="metric-formula">吞吐量 = 已解决问题数 ÷ 工作天数</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>高吞吐量表示团队处理问题效率高，但需要结合质量指标综合评估。
                </div>
            </div>

            <div class="metric-item">
                <div class="metric-name">交付效率 (Delivery Efficiency)</div>
                <div class="metric-description">承诺故事点的完成率，衡量团队承诺兑现能力</div>
                <div class="metric-formula">交付效率 = (完成故事点 ÷ 承诺故事点) × 100%</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>90%以上为优秀，80-90%为良好，低于80%需要改进计划准确性。
                </div>
            </div>
        </div>

        <!-- 质量指标 -->
        <div class="section" id="quality-metrics">
            <h2>🎯 质量指标</h2>
            <p>评估产品质量和团队质量管控能力的关键指标</p>

            <div class="metric-item">
                <div class="metric-name">Bug总数 (Total Bugs)</div>
                <div class="metric-description">周期内发现的Bug总数，反映产品质量状况</div>
                <div class="metric-formula">Bug总数 = 新发现Bug数量</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>需要结合功能复杂度和团队规模评估，关注趋势比绝对数值更重要。
                </div>
            </div>

            <div class="metric-item">
                <div class="metric-name">Bug修复率 (Bug Fix Rate)</div>
                <div class="metric-description">Bug修复的比例，衡量团队问题解决能力</div>
                <div class="metric-formula">Bug修复率 = (已修复Bug数 ÷ 总Bug数) × 100%</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>95%以上为优秀，90-95%为良好，低于90%需要关注修复效率。
                </div>
            </div>

            <div class="metric-item">
                <div class="metric-name">平均Bug修复时间 (Average Bug Fix Time)</div>
                <div class="metric-description">平均Bug修复时间，反映响应速度</div>
                <div class="metric-formula">平均修复时间 = Σ(Bug修复时间) ÷ 已修复Bug数</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>紧急Bug应在24小时内修复，一般Bug建议3-5天内修复。
                </div>
            </div>

            <div class="metric-item">
                <div class="metric-name">缺陷密度 (Defect Density)</div>
                <div class="metric-description">每个故事点的Bug数量，衡量代码质量</div>
                <div class="metric-formula">缺陷密度 = Bug总数 ÷ 完成故事点数</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>低于0.1为优秀，0.1-0.2为良好，高于0.2需要改进开发质量。
                </div>
            </div>

            <div class="metric-item">
                <div class="metric-name">返工率 (Rework Rate)</div>
                <div class="metric-description">需要返工的问题比例</div>
                <div class="metric-formula">返工率 = (重新打开问题数 ÷ 已解决问题数) × 100%</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>低于5%为优秀，5-10%为可接受，高于10%需要改进质量控制。
                </div>
            </div>
        </div>

        <!-- 周期时间指标 -->
        <div class="section" id="cycle-time-metrics">
            <h2>⏱️ 周期时间指标</h2>
            <p>衡量开发流程效率和响应能力的时间指标</p>

            <div class="metric-item">
                <div class="metric-name">平均周期时间 (Average Cycle Time)</div>
                <div class="metric-description">从开始开发到完成的平均时间，反映开发效率</div>
                <div class="metric-formula">周期时间 = 完成时间 - 开始开发时间</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>小功能1-3天，中等功能3-7天，大功能7-14天为合理范围。
                </div>
            </div>

            <div class="metric-item">
                <div class="metric-name">平均前置时间 (Average Lead Time)</div>
                <div class="metric-description">从需求提出到交付的平均时间，反映整体响应能力</div>
                <div class="metric-formula">前置时间 = 交付时间 - 需求提出时间</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>包含需求分析、设计、开发、测试全流程，通常是周期时间的1.5-2倍。
                </div>
            </div>
        </div>

        <!-- 团队效能指标 -->
        <div class="section" id="team-performance-metrics">
            <h2>👥 团队效能指标</h2>
            <p>评估团队协作效率和个人贡献度的指标</p>

            <div class="metric-item">
                <div class="metric-name">人均生产力 (Productivity per Person)</div>
                <div class="metric-description">人均完成的故事点数，衡量个人效率</div>
                <div class="metric-formula">人均生产力 = 总完成故事点 ÷ 团队人数</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>需要考虑团队成员经验和任务复杂度，关注团队整体提升。
                </div>
            </div>

            <div class="metric-item">
                <div class="metric-name">人均提交数 (Commits per Person)</div>
                <div class="metric-description">人均提交次数，反映代码贡献度</div>
                <div class="metric-formula">人均提交数 = 总提交次数 ÷ 团队人数</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>频繁小提交比大批量提交更好，建议每天2-5次提交。
                </div>
            </div>

            <div class="metric-item">
                <div class="metric-name">代码变动 (Code Churn)</div>
                <div class="metric-description">代码新增、删除和净变化情况</div>
                <div class="metric-formula">净代码行数 = 新增行数 - 删除行数</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>适度的代码变动表示持续改进，过度变动可能存在设计问题。
                </div>
            </div>

            <div class="metric-item">
                <div class="metric-name">MR合并率 (MR Merge Rate)</div>
                <div class="metric-description">合并请求的成功合并比例</div>
                <div class="metric-formula">MR合并率 = (已合并MR数 ÷ 创建MR数) × 100%</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>90%以上为优秀，表示代码质量高且团队协作良好。
                </div>
            </div>

            <div class="metric-item">
                <div class="metric-name">平均审查时间 (Average Review Time)</div>
                <div class="metric-description">代码审查的平均耗时</div>
                <div class="metric-formula">平均审查时间 = Σ(MR审查时间) ÷ MR数量</div>
                <div class="metric-interpretation">
                    <strong>解读：</strong>建议控制在4-8小时内，过长影响开发效率，过短可能审查不充分。
                </div>
            </div>
        </div>

        <!-- 综合评分体系 -->
        <div class="section" id="comprehensive-score">
            <h2>📈 综合评分体系</h2>
            <p>基于多维度指标的综合评估体系</p>

            <h3>评分权重</h3>
            <ul>
                <li><strong>开发速度 (30%)</strong>：基于交付效率计算</li>
                <li><strong>质量指标 (30%)</strong>：基于Bug修复率和缺陷密度计算</li>
                <li><strong>周期时间 (20%)</strong>：基于平均周期时间计算</li>
                <li><strong>团队效能 (20%)</strong>：基于人均生产力计算</li>
            </ul>

            <h3>等级标准</h3>
            <table class="grade-table">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>分数范围</th>
                        <th>评价</th>
                        <th>建议</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="grade-a">
                        <td>A+ / A</td>
                        <td>90-100 / 80-89</td>
                        <td>优秀</td>
                        <td>保持现有水平，分享最佳实践</td>
                    </tr>
                    <tr class="grade-b">
                        <td>B+ / B</td>
                        <td>70-79 / 60-69</td>
                        <td>良好</td>
                        <td>识别改进点，制定提升计划</td>
                    </tr>
                    <tr class="grade-c">
                        <td>C / D</td>
                        <td>50-59 / <50</td>
                        <td>需要改进</td>
                        <td>重点关注，制定详细改进措施</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 数据来源说明 -->
        <div class="section" id="data-sources">
            <h2>📊 数据来源说明</h2>
            
            <h3>JIRA数据</h3>
            <ul>
                <li><strong>项目范围</strong>：SPCB、SPCT项目</li>
                <li><strong>问题类型</strong>：Story、Task、Bug、Epic等</li>
                <li><strong>状态跟踪</strong>：创建、进行中、已解决、重新打开等</li>
                <li><strong>自定义字段</strong>：故事点、优先级、标签等</li>
            </ul>

            <h3>Git数据</h3>
            <ul>
                <li><strong>代码仓库</strong>：基于services_id.json配置的仓库</li>
                <li><strong>提交信息</strong>：提交次数、代码行数变化、作者信息</li>
                <li><strong>合并请求</strong>：MR创建、审查、合并时间</li>
                <li><strong>分支管理</strong>：主分支、功能分支的合并情况</li>
            </ul>

            <h3>数据更新频率</h3>
            <ul>
                <li><strong>实时数据</strong>：用户操作触发的查询</li>
                <li><strong>每日汇总</strong>：凌晨自动执行数据收集</li>
                <li><strong>历史数据</strong>：保留2年详细数据，5年汇总数据</li>
            </ul>

            <h3>数据质量保证</h3>
            <ul>
                <li><strong>数据验证</strong>：自动检测异常数据并标记</li>
                <li><strong>权限控制</strong>：基于邮箱的访问权限管理</li>
                <li><strong>审计日志</strong>：记录所有数据访问和修改操作</li>
            </ul>
        </div>
    </div>
</body>
</html>
