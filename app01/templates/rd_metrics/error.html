<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问受限 - Chatbot 任意门</title>
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #303133;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .error-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .error-icon {
            font-size: 64px;
            color: #f56c6c;
            margin-bottom: 20px;
        }
        .error-title {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 15px;
        }
        .error-message {
            font-size: 16px;
            color: #606266;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .error-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .btn-primary {
            background-color: #409eff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #66b1ff;
        }
        .btn-secondary {
            background-color: #909399;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #a6a9ad;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">🚫</div>
        <h1 class="error-title">访问受限</h1>
        <p class="error-message">
            {{ error_message|default:"您没有访问此页面的权限，请联系管理员获取相应权限。" }}
        </p>
        <div class="error-actions">
            <a href="javascript:history.back()" class="btn btn-secondary">
                <span>←</span> 返回上页
            </a>
            <a href="/" class="btn btn-primary">
                <span>🏠</span> 返回首页
            </a>
        </div>
    </div>
</body>
</html>
