<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R&D效率指标仪表板 - Chatbot 任意门</title>
    <!-- 引入Element Plus样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- 引入ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #303133;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            color: #409eff;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .header-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            text-decoration: none;
        }
        .btn-primary {
            background-color: #409eff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #66b1ff;
        }
        .btn-secondary {
            background-color: #909399;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #a6a9ad;
        }
        .btn-success {
            background-color: #67c23a;
            color: white;
        }
        .btn-success:hover {
            background-color: #85ce61;
        }
        
        /* 控制面板 */
        .control-panel {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .control-row {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .control-label {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
        }
        .control-input {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
            min-width: 150px;
        }
        .control-input:focus {
            outline: none;
            border-color: #409eff;
        }
        
        /* 指标卡片网格 */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
        }
        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .metric-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .metric-help {
            color: #909399;
            cursor: help;
            font-size: 16px;
        }
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 10px;
        }
        .metric-unit {
            font-size: 14px;
            color: #909399;
            margin-left: 5px;
        }
        .metric-description {
            font-size: 14px;
            color: #606266;
            line-height: 1.4;
        }
        .metric-trend {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-top: 10px;
            font-size: 14px;
        }
        .trend-up {
            color: #67c23a;
        }
        .trend-down {
            color: #f56c6c;
        }
        .trend-stable {
            color: #909399;
        }
        
        /* 图表容器 */
        .chart-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            color: #303133;
        }
        .chart-content {
            height: 400px;
        }
        
        /* 团队对比 */
        .comparison-section {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .comparison-controls {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .team-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .team-checkbox {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 5px 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .team-checkbox:hover {
            border-color: #409eff;
            background-color: #f0f9ff;
        }
        .team-checkbox.selected {
            border-color: #409eff;
            background-color: #409eff;
            color: white;
        }
        
        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 40px;
            color: #909399;
        }
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #409eff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 错误状态 */
        .error-message {
            background-color: #fef0f0;
            color: #f56c6c;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #fbc4c4;
            margin-bottom: 20px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            .header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }
            .header-actions {
                width: 100%;
                justify-content: flex-start;
            }
            .control-row {
                flex-direction: column;
                align-items: flex-start;
            }
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            .comparison-controls {
                flex-direction: column;
                align-items: flex-start;
            }
        }
        
        /* Tooltip样式 */
        .tooltip {
            position: relative;
            cursor: help;
        }
        .tooltip .tooltip-text {
            visibility: hidden;
            width: 300px;
            background-color: #333;
            color: #fff;
            text-align: left;
            border-radius: 6px;
            padding: 8px 12px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            line-height: 1.4;
            word-wrap: break-word;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        .tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>
                <span>📊</span> R&D效率指标仪表板
            </h1>
            <div class="header-actions">
                <a href="/rd-metrics/help/" class="btn btn-secondary">
                    <span>❓</span> 指标说明
                </a>
                {% if permission.can_configure_teams %}
                <a href="/rd-metrics/teams/" class="btn btn-primary">
                    <span>⚙️</span> 团队管理
                </a>
                {% endif %}
                {% if permission.can_export_data %}
                <button class="btn btn-success" onclick="exportData()">
                    <span>📥</span> 导出数据
                </button>
                {% endif %}
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="control-row">
                <div class="control-group">
                    <label class="control-label">选择团队</label>
                    <select class="control-input" id="teamSelector" onchange="loadTeamMetrics()">
                        <option value="">请选择团队</option>
                        {% for team in accessible_teams %}
                        <option value="{{ team.team_id }}">{{ team.team_name }} ({{ team.department }})</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="control-group">
                    <label class="control-label">时间范围</label>
                    <select class="control-input" id="timeRange" onchange="loadTeamMetrics()">
                        <option value="7">最近7天</option>
                        <option value="30" selected>最近30天</option>
                        <option value="90">最近90天</option>
                        <option value="180">最近180天</option>
                    </select>
                </div>
                <div class="control-group">
                    <label class="control-label">操作</label>
                    <button class="btn btn-primary" onclick="refreshData()">
                        <span>🔄</span> 刷新数据
                    </button>
                </div>
            </div>
        </div>

        <!-- 指标展示区域 -->
        <div id="metricsContainer">
            <div class="loading">
                <div class="loading-spinner"></div>
                请选择团队查看指标数据
            </div>
        </div>

        <!-- 团队对比区域 -->
        {% if can_view_comparison %}
        <div class="comparison-section" id="comparisonSection" style="display: none;">
            <div class="chart-header">
                <h3 class="chart-title">团队对比分析</h3>
                <button class="btn btn-primary" onclick="loadTeamComparison()">
                    <span>📈</span> 开始对比
                </button>
            </div>
            
            <div class="comparison-controls">
                <div class="control-group">
                    <label class="control-label">选择对比团队</label>
                    <div class="team-selector" id="comparisonTeamSelector">
                        {% for team in accessible_teams %}
                        <div class="team-checkbox" data-team-id="{{ team.team_id }}" onclick="toggleTeamSelection(this)">
                            <input type="checkbox" id="team_{{ team.team_id }}">
                            <label for="team_{{ team.team_id }}">{{ team.team_name }}</label>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <div id="comparisonChart" class="chart-content"></div>
        </div>
        {% endif %}
    </div>

    <script>
        // 全局变量
        let currentTeamId = null;
        let currentMetrics = null;
        let comparisonChart = null;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 如果只有一个团队，自动选择
            const teamSelector = document.getElementById('teamSelector');
            if (teamSelector.options.length === 2) {
                teamSelector.selectedIndex = 1;
                loadTeamMetrics();
            }

            // 显示对比区域
            const canViewComparison = {{ can_view_comparison|yesno:"true,false" }};
            if (canViewComparison) {
                document.getElementById('comparisonSection').style.display = 'block';
            }
        });
        
        // 加载团队指标数据
        async function loadTeamMetrics() {
            const teamSelector = document.getElementById('teamSelector');
            const timeRange = document.getElementById('timeRange');
            const container = document.getElementById('metricsContainer');
            
            const teamId = teamSelector.value;
            const days = timeRange.value;
            
            if (!teamId) {
                container.innerHTML = '<div class="loading"><div class="loading-spinner"></div>请选择团队查看指标数据</div>';
                return;
            }
            
            currentTeamId = teamId;
            
            // 显示加载状态
            container.innerHTML = '<div class="loading"><div class="loading-spinner"></div>正在加载指标数据...</div>';
            
            try {
                const response = await fetch(`/rd-metrics/api/teams/${teamId}/metrics/?days=${days}`);
                const result = await response.json();
                
                if (result.success) {
                    currentMetrics = result.data;
                    renderMetrics(result.data, result.team_info);
                } else {
                    container.innerHTML = `<div class="error-message">加载失败: ${result.error}</div>`;
                }
            } catch (error) {
                console.error('加载指标数据失败:', error);
                container.innerHTML = '<div class="error-message">网络错误，请稍后重试</div>';
            }
        }
        
        // 渲染指标数据
        function renderMetrics(metrics, teamInfo) {
            const container = document.getElementById('metricsContainer');
            
            if (!metrics || Object.keys(metrics).length === 0) {
                container.innerHTML = '<div class="error-message">暂无指标数据</div>';
                return;
            }
            
            let html = `
                <div class="chart-header">
                    <h3 class="chart-title">${teamInfo.team_name} - ${teamInfo.department}</h3>
                    <div style="font-size: 14px; color: #606266;">
                        团队成员: ${teamInfo.member_count}人 | 
                        数据周期: ${metrics.period ? metrics.period.start_date + ' 至 ' + metrics.period.end_date : '未知'}
                    </div>
                </div>
                
                <div class="metrics-grid">
            `;
            
            // 开发速度指标
            if (metrics.development_velocity) {
                const velocity = metrics.development_velocity;
                html += renderMetricCard('团队速度', velocity.team_velocity, '团队在指定周期内完成的故事点总数，反映团队整体交付能力');
                html += renderMetricCard('平均速度', velocity.average_velocity, '平均每个冲刺完成的故事点数，用于评估团队稳定性');
                html += renderMetricCard('吞吐量', velocity.throughput, '平均每天解决的问题数量，反映团队处理效率');
                html += renderMetricCard('交付效率', velocity.delivery_efficiency, '承诺故事点的完成率，衡量团队承诺兑现能力');
            }
            
            // 质量指标
            if (metrics.quality) {
                const quality = metrics.quality;
                html += renderMetricCard('Bug总数', quality.total_bugs, '周期内发现的Bug总数，反映产品质量状况');
                html += renderMetricCard('Bug修复率', quality.bug_fix_rate, 'Bug修复的比例，衡量团队问题解决能力');
                html += renderMetricCard('平均修复时间', quality.average_bug_fix_time, '平均Bug修复时间，反映响应速度');
                html += renderMetricCard('缺陷密度', quality.defect_density, '每个故事点的Bug数量，衡量代码质量');
            }
            
            // 周期时间指标
            if (metrics.cycle_time) {
                const cycleTime = metrics.cycle_time;
                html += renderMetricCard('平均周期时间', cycleTime.average_cycle_time, '从开始开发到完成的平均时间，反映开发效率');
                html += renderMetricCard('平均前置时间', cycleTime.average_lead_time, '从需求提出到交付的平均时间，反映整体响应能力');
            }
            
            // 团队效能指标
            if (metrics.team_performance) {
                const teamPerf = metrics.team_performance;
                html += renderMetricCard('人均生产力', teamPerf.team_productivity, '人均完成的故事点数，衡量个人效率');
                html += renderMetricCard('人均提交数', teamPerf.commits_per_person, '人均提交次数，反映代码贡献度');
            }
            
            html += '</div>';
            
            // 综合评分
            if (metrics.comprehensive_score) {
                const score = metrics.comprehensive_score;
                html += `
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3 class="chart-title">综合评分</h3>
                        </div>
                        <div style="text-align: center; padding: 20px;">
                            <div style="font-size: 48px; font-weight: bold; color: ${getScoreColor(score.total_score)}; margin-bottom: 10px;">
                                ${score.total_score}
                            </div>
                            <div style="font-size: 24px; color: #606266; margin-bottom: 20px;">
                                等级: ${score.grade}
                            </div>
                            <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 20px;">
                                ${Object.entries(score.individual_scores || {}).map(([key, value]) => `
                                    <div style="text-align: center;">
                                        <div style="font-size: 18px; font-weight: bold; color: ${getScoreColor(value)};">${value.toFixed(1)}</div>
                                        <div style="font-size: 12px; color: #909399;">${getScoreLabel(key)}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }
        
        // 渲染指标卡片
        function renderMetricCard(title, metric, description) {
            if (!metric || metric.value === null || metric.value === undefined) {
                return '';
            }
            
            const value = typeof metric.value === 'number' ? metric.value.toFixed(2) : metric.value;
            const unit = metric.unit || '';
            
            return `
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-title">
                            ${title}
                            <span class="tooltip metric-help">
                                ❓
                                <span class="tooltip-text">${description}</span>
                            </span>
                        </div>
                    </div>
                    <div class="metric-value">
                        ${value}
                        <span class="metric-unit">${unit}</span>
                    </div>
                    <div class="metric-description">${metric.description || description}</div>
                </div>
            `;
        }
        
        // 获取评分颜色
        function getScoreColor(score) {
            if (score >= 80) return '#67c23a';
            if (score >= 60) return '#e6a23c';
            return '#f56c6c';
        }
        
        // 获取评分标签
        function getScoreLabel(key) {
            const labels = {
                'velocity': '速度',
                'quality': '质量',
                'cycle_time': '周期时间',
                'team_performance': '团队效能'
            };
            return labels[key] || key;
        }
        
        // 刷新数据
        function refreshData() {
            if (currentTeamId) {
                loadTeamMetrics();
            }
        }
        
        // 导出数据
        async function exportData() {
            if (!currentTeamId) {
                alert('请先选择团队');
                return;
            }
            
            const timeRange = document.getElementById('timeRange').value;
            
            try {
                const response = await fetch(`/rd-metrics/api/metrics/export/?team_ids=${currentTeamId}&days=${timeRange}&format=json`);
                const blob = await response.blob();
                
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `rd_metrics_${currentTeamId}_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            } catch (error) {
                console.error('导出失败:', error);
                alert('导出失败，请稍后重试');
            }
        }
        
        // 团队选择切换
        function toggleTeamSelection(element) {
            element.classList.toggle('selected');
            const checkbox = element.querySelector('input[type="checkbox"]');
            checkbox.checked = !checkbox.checked;
        }
        
        // 加载团队对比
        async function loadTeamComparison() {
            const selectedTeams = Array.from(document.querySelectorAll('#comparisonTeamSelector .team-checkbox.selected'))
                .map(el => el.dataset.teamId);
            
            if (selectedTeams.length < 2) {
                alert('请至少选择2个团队进行对比');
                return;
            }
            
            const timeRange = document.getElementById('timeRange').value;
            
            try {
                const response = await fetch(`/rd-metrics/api/teams/comparison/?team_ids=${selectedTeams.join(',')}&days=${timeRange}`);
                const result = await response.json();
                
                if (result.success) {
                    renderComparisonChart(result.data);
                } else {
                    alert('对比失败: ' + result.error);
                }
            } catch (error) {
                console.error('团队对比失败:', error);
                alert('对比失败，请稍后重试');
            }
        }
        
        // 渲染对比图表
        function renderComparisonChart(data) {
            const chartContainer = document.getElementById('comparisonChart');
            
            if (comparisonChart) {
                comparisonChart.dispose();
            }
            
            comparisonChart = echarts.init(chartContainer);
            
            // 提取对比数据
            const teams = Object.keys(data.comparison_data);
            const scores = teams.map(teamId => {
                const teamData = data.comparison_data[teamId];
                return {
                    name: teamData.team_name,
                    value: teamData.metrics.comprehensive_score?.total_score || 0
                };
            });
            
            const option = {
                title: {
                    text: '团队综合评分对比',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                xAxis: {
                    type: 'category',
                    data: scores.map(item => item.name)
                },
                yAxis: {
                    type: 'value',
                    name: '综合评分',
                    min: 0,
                    max: 100
                },
                series: [{
                    name: '综合评分',
                    type: 'bar',
                    data: scores.map(item => ({
                        value: item.value,
                        itemStyle: {
                            color: getScoreColor(item.value)
                        }
                    })),
                    label: {
                        show: true,
                        position: 'top',
                        formatter: '{c}'
                    }
                }]
            };
            
            comparisonChart.setOption(option);
        }
    </script>
</body>
</html>
