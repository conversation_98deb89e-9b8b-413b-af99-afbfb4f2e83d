"""
AI功能配置文件
包含LLM平台配置和其他AI相关设置
"""

from .bot_config import BotConfig

# 模型配置池 - 支持灵活切换
MODEL_POOL = {
    # A 模型（主模型）
    'gpt-4.1': {
        'API_BASE_URL': 'https://compass.llm.shopee.io/compass-api/v1',
        'API_KEY': '936095a29b96bd31b5d107ce0326f008f91e07eaa8dd17e557f585ef626607f5',
        'MODEL_NAME': 'gpt-4.1',
        'MAX_TOKENS': 1500,
        'TOKEN_PARAM_KEY': 'max_tokens',
        'TEMPERATURE': 0.7,
        'TIMEOUT': 12,
        'MAX_RETRIES': 3,
        'RETRY_DELAY': 1,
        'DISPLAY_NAME': 'GPT-4.1',
        'PROVIDER': 'OpenAI'
    },
    # B 模型（对比模型）
    'gpt-5': {
        'API_BASE_URL': 'https://compass.llm.shopee.io/compass-api/v1',
        'API_KEY': '936095a29b96bd31b5d107ce0326f008f91e07eaa8dd17e557f585ef626607f5',
        'MODEL_NAME': 'gpt-5',
        'MAX_TOKENS': 1500,
        'TOKEN_PARAM_KEY': 'max_completion_tokens',
        'MAX_COMPLETION_TOKENS_LIMIT': 256,
        'TEMPERATURE': 1,
        'ALLOW_TEMPERATURE': False,
        'TIMEOUT': 25,
        'MAX_RETRIES': 3,
        'RETRY_DELAY': 1,
        'DISPLAY_NAME': 'GPT-5',
        'PROVIDER': 'OpenAI'
    }
}

# 兼容性配置（保持向后兼容）
LLM_CONFIG = MODEL_POOL['gpt-4.1']
SECONDARY_MODEL_CONFIG = MODEL_POOL['gpt-5']

# AB测试配置 - 使用模型别名，便于切换
AB_TEST_CONFIG = {
    'ENABLED': False,  # AB测试总开关 - 开启用于双模型对比
    'DUAL_MODEL_COMPARISON': False,  # 双模型对比功能 - 开启
    'SHOW_MODEL_SOURCE': True,  # 显示模型来源
    'PARALLEL_REQUESTS': True,  # 并行请求两个模型
    
    # 模型别名配置 - 通过修改这里来切换模型
    'MODEL_A': 'gpt-4.1',  # A模型（主模型）
    'MODEL_B': 'gpt-5',  # B模型（对比模型）
    
    # 默认和回退配置
    'DEFAULT_MODEL': 'A',  # 默认使用A模型 ('A' 或 'B')
    'FALLBACK_MODEL': 'B',  # A模型失败时使用B模型
    
    # 显示名称（自动从MODEL_POOL获取）
    'MODEL_A_DISPLAY_NAME': 'GPT-4.1',  # A模型显示名称
    'MODEL_B_DISPLAY_NAME': 'GPT-5',  # B模型显示名称
}

# Token加密密钥 (用于加密存储用户的JIRA Token)
# 生产环境请使用更安全的密钥管理方式
ENCRYPTION_KEY = b'ZmDfcTF7_60GrrY167zsiPd67pEvs0aGOv2oasOM1Pg='

# AI功能开关
AI_FEATURES = {
    'ENABLED': True,  # 总开关
    'SMART_JIRA_QUERY': True,  # 智能JIRA查询
    'SCHEDULED_TASKS': True,  # 定时任务
    'ADVANCED_FEATURES': True,  # 高级功能
    'CONVERSATION_MEMORY': True,  # 对话记忆
}

# 性能配置
PERFORMANCE_CONFIG = {
    'MAX_CONVERSATION_HISTORY': 10,  # 最大对话历史记录数
    'CACHE_TIMEOUT': 300,  # 缓存超时时间(秒)
    'MAX_CONCURRENT_REQUESTS': 5,  # 最大并发请求数
}

# 日志配置
LOGGING_CONFIG = {
    'LOG_LEVEL': 'INFO',
    'LOG_AI_REQUESTS': True,  # 记录AI请求日志
    'LOG_USER_QUERIES': True,  # 记录用户查询日志
    'LOG_PERFORMANCE': True,  # 记录性能日志
}

# 安全配置
SECURITY_CONFIG = {
    'RATE_LIMIT_PER_USER': 100,  # 每用户每小时请求限制
    'RATE_LIMIT_WINDOW': 3600,  # 限制窗口时间(秒)
    'BLACKLIST_KEYWORDS': [  # 敏感词过滤
        'password', 'token', 'secret', 'key'
    ],
} 