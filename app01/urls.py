"""
app01 URL Configuration
包含测试框架和其他功能的URL路由
"""
from django.urls import path
from . import views
from .testing_framework import mock_seatalk_api

urlpatterns = [
    # 测试框架API
    path('api/mock-seatalk/webhook/', mock_seatalk_api.mock_seatalk_webhook, name='mock_seatalk_webhook'),
    path('api/mock-seatalk/results/<str:test_session_id>/', mock_seatalk_api.get_test_result, name='get_test_result'),
    path('api/mock-seatalk/results/', mock_seatalk_api.list_test_results, name='list_test_results'),
    path('api/mock-seatalk/report/<str:test_session_id>/', mock_seatalk_api.generate_html_report, name='generate_html_report'),
    
    # Epic关键节点提醒手动触发接口
    path('api/epic-reminder/', views.epic_reminder_manual_trigger, name='epic_reminder_manual_trigger'),
    
    # SPCPM timeline reminder手动触发接口
    path('api/spcpm-timeline-reminder/', views.cronjob_spcpm_timeline_reminder, name='spcpm_timeline_reminder'),
    
    # SPCT/SPCB测试环境Bug提醒手动触发接口
    path('api/spct-test-bugs/', views.cronjob_spct_test_bugs, name='spct_test_bugs'),
    
    # SPCT Bug提醒调试模式控制接口
    path('api/spct-bug-debug/', views.spct_bug_reminder_debug_control, name='spct_bug_debug_control'),
    

    # 其他可能的app01路由可以在这里添加
] 