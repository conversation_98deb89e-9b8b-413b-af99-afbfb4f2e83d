"""
Django管理命令：设置R&D指标测试数据
使用方法：
python manage.py setup_rd_test_data --create-teams
python manage.py setup_rd_test_data --create-permissions
python manage.py setup_rd_test_data --create-sample-data
python manage.py setup_rd_test_data --cleanup-test-data
"""

import random
from datetime import datetime, timedelta, date
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone

from app01.models import (
    RDTeam, RDTeamMember, RDMetricsPermission,
    RDJiraMetrics, RDGitMetrics, RDMetricsSnapshot
)


class Command(BaseCommand):
    help = '设置R&D指标测试数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-teams',
            action='store_true',
            help='创建测试团队和成员'
        )
        
        parser.add_argument(
            '--create-permissions',
            action='store_true',
            help='创建测试权限'
        )
        
        parser.add_argument(
            '--create-sample-data',
            action='store_true',
            help='创建样本指标数据'
        )
        
        parser.add_argument(
            '--cleanup-test-data',
            action='store_true',
            help='清理所有测试数据'
        )
        
        parser.add_argument(
            '--all',
            action='store_true',
            help='执行所有创建操作'
        )

    def handle(self, *args, **options):
        """命令处理入口"""
        try:
            if options['cleanup_test_data']:
                self._cleanup_test_data()
                return
            
            if options['all']:
                self._create_test_teams()
                self._create_test_permissions()
                self._create_sample_data()
            else:
                if options['create_teams']:
                    self._create_test_teams()
                
                if options['create_permissions']:
                    self._create_test_permissions()
                
                if options['create_sample_data']:
                    self._create_sample_data()
            
            self.stdout.write(
                self.style.SUCCESS('✅ 测试数据设置完成')
            )
                
        except Exception as e:
            raise CommandError(f'执行失败: {str(e)}')

    def _create_test_teams(self):
        """创建测试团队和成员"""
        self.stdout.write('创建测试团队和成员...')
        
        # 测试团队数据
        test_teams = [
            {
                'team_id': 'test_chatbot_core',
                'team_name': '聊天机器人核心团队',
                'department': '产品技术部',
                'team_leader_email': '<EMAIL>',
                'jira_projects': ['SPCB', 'SPCT'],
                'git_repositories': ['shopee/chatbot/chatbot-context', 'shopee/chatbot/chatbot-botapi'],
                'members': [
                    {'email': '<EMAIL>', 'name': '张三', 'role': 'developer'},
                    {'email': '<EMAIL>', 'name': '李四', 'role': 'developer'},
                    {'email': '<EMAIL>', 'name': '王五', 'role': 'tester'},
                    {'email': '<EMAIL>', 'name': '赵六', 'role': 'pm'},
                ]
            },
            {
                'team_id': 'test_chatbot_web',
                'team_name': '聊天机器人前端团队',
                'department': '产品技术部',
                'team_leader_email': '<EMAIL>',
                'jira_projects': ['SPCB'],
                'git_repositories': ['shopee/chatbot/web-chatbot', 'shopee/chatbot/web-chatbot-admin'],
                'members': [
                    {'email': '<EMAIL>', 'name': '钱七', 'role': 'developer'},
                    {'email': '<EMAIL>', 'name': '孙八', 'role': 'developer'},
                    {'email': '<EMAIL>', 'name': '周九', 'role': 'pm'},
                ]
            },
            {
                'team_id': 'test_data_platform',
                'team_name': '数据平台团队',
                'department': '数据技术部',
                'team_leader_email': '<EMAIL>',
                'jira_projects': ['SPCT'],
                'git_repositories': ['shopee/chatbot/data-service', 'shopee/chatbot/dashboard-builder'],
                'members': [
                    {'email': '<EMAIL>', 'name': '吴十', 'role': 'developer'},
                    {'email': '<EMAIL>', 'name': '郑十一', 'role': 'developer'},
                    {'email': '<EMAIL>', 'name': '王十二', 'role': 'pm'},
                ]
            }
        ]
        
        for team_data in test_teams:
            # 创建团队
            team, created = RDTeam.objects.get_or_create(
                team_id=team_data['team_id'],
                defaults={
                    'team_name': team_data['team_name'],
                    'department': team_data['department'],
                    'team_leader_email': team_data['team_leader_email'],
                    'jira_projects': team_data['jira_projects'],
                    'git_repositories': team_data['git_repositories'],
                    'is_test_data': True,
                }
            )
            
            if created:
                self.stdout.write(f'  ✅ 创建团队: {team.team_name}')
            else:
                self.stdout.write(f'  ⚠️ 团队已存在: {team.team_name}')
            
            # 创建团队成员
            for member_data in team_data['members']:
                member, created = RDTeamMember.objects.get_or_create(
                    team=team,
                    member_email=member_data['email'],
                    defaults={
                        'member_name': member_data['name'],
                        'role': member_data['role'],
                        'join_date': date.today() - timedelta(days=random.randint(30, 365)),
                        'jira_account_id': f"jira_{member_data['email'].split('@')[0]}",
                        'git_username': member_data['email'].split('@')[0],
                        'is_test_data': True,
                    }
                )
                
                if created:
                    self.stdout.write(f'    ✅ 添加成员: {member.member_name}')

    def _create_test_permissions(self):
        """创建测试权限"""
        self.stdout.write('创建测试权限...')
        
        # 测试权限数据
        test_permissions = [
            {
                'user_email': '<EMAIL>',
                'permission_level': 'admin',
                'can_view_individual_metrics': True,
                'can_view_team_comparison': True,
                'can_export_data': True,
                'can_configure_teams': True,
                'granted_by': '<EMAIL>',
            },
            {
                'user_email': '<EMAIL>',
                'permission_level': 'manager',
                'can_view_individual_metrics': True,
                'can_view_team_comparison': True,
                'can_export_data': True,
                'can_configure_teams': False,
                'granted_by': '<EMAIL>',
            },
            {
                'user_email': '<EMAIL>',
                'permission_level': 'team_lead',
                'can_view_individual_metrics': True,
                'can_view_team_comparison': False,
                'can_export_data': False,
                'can_configure_teams': True,
                'granted_by': '<EMAIL>',
            },
            {
                'user_email': '<EMAIL>',
                'permission_level': 'viewer',
                'can_view_individual_metrics': True,
                'can_view_team_comparison': False,
                'can_export_data': False,
                'can_configure_teams': False,
                'granted_by': '<EMAIL>',
            },
            {
                'user_email': '<EMAIL>',
                'permission_level': 'viewer',
                'can_view_individual_metrics': True,
                'can_view_team_comparison': True,
                'can_export_data': True,
                'can_configure_teams': True,
                'granted_by': '<EMAIL>',
            }
        ]
        
        for perm_data in test_permissions:
            permission, created = RDMetricsPermission.objects.get_or_create(
                user_email=perm_data['user_email'],
                defaults={
                    **perm_data,
                    'is_test_data': True,
                }
            )
            
            if created:
                self.stdout.write(f'  ✅ 创建权限: {permission.user_email} ({permission.permission_level})')
                
                # 为team_lead和viewer设置可访问的团队
                if perm_data['permission_level'] in ['team_lead', 'viewer']:
                    test_teams = RDTeam.objects.filter(is_test_data=True)
                    if perm_data['user_email'] == '<EMAIL>':
                        # 团队负责人只能访问自己的团队
                        team = test_teams.filter(team_leader_email=perm_data['user_email']).first()
                        if team:
                            permission.accessible_teams.add(team)
                    elif perm_data['user_email'] == '<EMAIL>':
                        # 开发者只能访问所在团队
                        member = RDTeamMember.objects.filter(
                            member_email=perm_data['user_email'],
                            is_test_data=True
                        ).first()
                        if member:
                            permission.accessible_teams.add(member.team)
                    else:
                        # 测试用户可以访问所有测试团队
                        permission.accessible_teams.set(test_teams)
            else:
                self.stdout.write(f'  ⚠️ 权限已存在: {permission.user_email}')

    def _create_sample_data(self):
        """创建样本指标数据"""
        self.stdout.write('创建样本指标数据...')
        
        test_teams = RDTeam.objects.filter(is_test_data=True)
        
        # 生成最近30天的数据
        end_date = date.today()
        start_date = end_date - timedelta(days=30)
        
        for team in test_teams:
            self.stdout.write(f'  为团队 {team.team_name} 生成数据...')
            
            current_date = start_date
            while current_date <= end_date:
                # 生成JIRA指标数据
                for project_key in team.jira_projects:
                    jira_metrics, created = RDJiraMetrics.objects.get_or_create(
                        team=team,
                        project_key=project_key,
                        date=current_date,
                        defaults={
                            'total_issues': random.randint(5, 20),
                            'created_issues': random.randint(1, 8),
                            'resolved_issues': random.randint(1, 10),
                            'reopened_issues': random.randint(0, 2),
                            'total_bugs': random.randint(0, 5),
                            'new_bugs': random.randint(0, 3),
                            'fixed_bugs': random.randint(0, 4),
                            'bug_fix_time_avg': random.uniform(0.5, 5.0),
                            'story_points_committed': random.uniform(10, 50),
                            'story_points_completed': random.uniform(8, 45),
                            'velocity': random.uniform(8, 45),
                            'avg_cycle_time': random.uniform(1, 10),
                            'avg_lead_time': random.uniform(2, 15),
                            'defect_density': random.uniform(0.01, 0.3),
                            'rework_rate': random.uniform(0, 15),
                            'is_test_data': True,
                        }
                    )
                
                # 生成Git指标数据
                for repo_name in team.git_repositories[:2]:  # 限制仓库数量
                    git_metrics, created = RDGitMetrics.objects.get_or_create(
                        team=team,
                        repository_name=repo_name,
                        repository_id=random.randint(10000, 99999),
                        date=current_date,
                        defaults={
                            'total_commits': random.randint(1, 15),
                            'total_authors': random.randint(1, len(team.members.all())),
                            'lines_added': random.randint(10, 500),
                            'lines_deleted': random.randint(5, 200),
                            'merge_requests_created': random.randint(0, 5),
                            'merge_requests_merged': random.randint(0, 4),
                            'avg_mr_review_time': random.uniform(1, 24),
                            'code_churn_rate': random.uniform(0.1, 0.8),
                            'hotspot_files_count': random.randint(0, 10),
                            'is_test_data': True,
                        }
                    )
                
                current_date += timedelta(days=1)
            
            # 创建周度快照
            weekly_start = start_date
            while weekly_start <= end_date:
                weekly_end = min(weekly_start + timedelta(days=6), end_date)
                
                snapshot, created = RDMetricsSnapshot.objects.get_or_create(
                    team=team,
                    period_type='weekly',
                    period_start=weekly_start,
                    defaults={
                        'period_end': weekly_end,
                        'metrics_data': {
                            'jira_metrics': {
                                'total_issues': random.randint(30, 100),
                                'resolved_issues': random.randint(25, 90),
                                'velocity': random.uniform(50, 200),
                            },
                            'git_metrics': {
                                'total_commits': random.randint(20, 80),
                                'lines_added': random.randint(500, 2000),
                            }
                        },
                        'data_sources': {
                            'jira_records': 7,
                            'git_records': 7,
                        },
                        'is_test_data': True,
                    }
                )
                
                weekly_start += timedelta(days=7)
            
            self.stdout.write(f'    ✅ 完成团队 {team.team_name} 数据生成')

    def _cleanup_test_data(self):
        """清理测试数据"""
        self.stdout.write('清理测试数据...')
        
        # 确认操作
        confirm = input('确认要删除所有测试数据吗？(y/N): ')
        if confirm.lower() != 'y':
            self.stdout.write('操作已取消')
            return
        
        # 删除各类测试数据
        models_to_clean = [
            (RDMetricsSnapshot, 'R&D指标快照'),
            (RDJiraMetrics, 'JIRA指标'),
            (RDGitMetrics, 'Git指标'),
            (RDTeamMember, '团队成员'),
            (RDTeam, '团队'),
            (RDMetricsPermission, '权限'),
        ]
        
        for model, name in models_to_clean:
            deleted_count = model.objects.filter(is_test_data=True).delete()[0]
            self.stdout.write(f'  ✅ 删除{name}: {deleted_count}条记录')
        
        self.stdout.write(
            self.style.SUCCESS('✅ 测试数据清理完成')
        )
