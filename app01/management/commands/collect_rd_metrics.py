"""
Django管理命令：收集R&D指标数据
使用方法：
python manage.py collect_rd_metrics --team-id TEAM001 --type jira --days 30
python manage.py collect_rd_metrics --all-teams --type all --days 7
"""

import asyncio
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from app01.models import RDTeam
from app01.rd_metrics.tasks import (
    collect_team_metrics_task, 
    collect_all_teams_metrics_task,
    create_weekly_snapshots_task,
    create_monthly_snapshots_task,
    cleanup_old_data_task
)


class Command(BaseCommand):
    help = '收集R&D效率指标数据'

    def add_arguments(self, parser):
        # 团队选择参数
        parser.add_argument(
            '--team-id',
            type=str,
            help='指定团队ID'
        )
        
        parser.add_argument(
            '--all-teams',
            action='store_true',
            help='收集所有团队数据'
        )
        
        # 收集类型参数
        parser.add_argument(
            '--type',
            type=str,
            choices=['jira', 'git', 'all'],
            default='all',
            help='数据收集类型 (默认: all)'
        )
        
        # 时间范围参数
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='收集天数 (默认: 30)'
        )
        
        # 快照创建参数
        parser.add_argument(
            '--create-snapshots',
            type=str,
            choices=['weekly', 'monthly'],
            help='创建快照 (weekly/monthly)'
        )
        
        # 数据清理参数
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='清理旧数据'
        )
        
        parser.add_argument(
            '--keep-days',
            type=int,
            default=730,
            help='数据保留天数 (默认: 730天/2年)'
        )
        
        # 用户邮箱参数
        parser.add_argument(
            '--user-email',
            type=str,
            help='触发用户邮箱'
        )
        
        # 输出格式参数
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='详细输出'
        )

    def handle(self, *args, **options):
        """命令处理入口"""
        try:
            # 设置输出级别
            if options['verbose']:
                self.stdout.write(
                    self.style.SUCCESS('开始执行R&D指标数据收集任务...')
                )
            
            # 数据清理
            if options['cleanup']:
                self._handle_cleanup(options)
                return
            
            # 快照创建
            if options['create_snapshots']:
                self._handle_snapshots(options)
                return
            
            # 数据收集
            if options['team_id']:
                self._handle_single_team(options)
            elif options['all_teams']:
                self._handle_all_teams(options)
            else:
                raise CommandError('请指定 --team-id 或 --all-teams 参数')
                
        except Exception as e:
            raise CommandError(f'执行失败: {str(e)}')

    def _handle_single_team(self, options):
        """处理单个团队数据收集"""
        team_id = options['team_id']
        collection_type = options['type']
        days = options['days']
        user_email = options.get('user_email')
        
        self.stdout.write(f'开始收集团队 {team_id} 的数据...')
        
        # 验证团队存在
        try:
            team = RDTeam.objects.get(team_id=team_id, is_active=True, is_test_data=False)
        except RDTeam.DoesNotExist:
            raise CommandError(f'团队 {team_id} 不存在或未激活')
        
        # 执行收集任务
        result = asyncio.run(
            collect_team_metrics_task(team_id, collection_type, days, user_email)
        )
        
        # 输出结果
        if result['success']:
            self.stdout.write(
                self.style.SUCCESS(f'✅ 团队 {team_id} 数据收集完成')
            )
            
            if options['verbose']:
                self._print_detailed_result(result)
        else:
            self.stdout.write(
                self.style.ERROR(f'❌ 团队 {team_id} 数据收集失败: {result["error"]}')
            )

    def _handle_all_teams(self, options):
        """处理所有团队数据收集"""
        collection_type = options['type']
        days = options['days']
        
        self.stdout.write('开始收集所有团队的数据...')
        
        # 执行收集任务
        result = asyncio.run(
            collect_all_teams_metrics_task(collection_type, days)
        )
        
        # 输出结果
        if result['success']:
            self.stdout.write(
                self.style.SUCCESS(
                    f'✅ 所有团队数据收集完成，成功: {result["success_count"]}, 失败: {result["error_count"]}'
                )
            )
        else:
            self.stdout.write(
                self.style.WARNING(
                    f'⚠️ 部分团队数据收集失败，成功: {result["success_count"]}, 失败: {result["error_count"]}'
                )
            )
        
        if options['verbose']:
            self._print_detailed_result(result)

    def _handle_snapshots(self, options):
        """处理快照创建"""
        snapshot_type = options['create_snapshots']
        
        self.stdout.write(f'开始创建{snapshot_type}快照...')
        
        if snapshot_type == 'weekly':
            result = asyncio.run(create_weekly_snapshots_task())
        else:  # monthly
            result = asyncio.run(create_monthly_snapshots_task())
        
        # 输出结果
        if result['success']:
            self.stdout.write(
                self.style.SUCCESS(
                    f'✅ {snapshot_type}快照创建完成，成功: {result["success_count"]}, 失败: {result["error_count"]}'
                )
            )
        else:
            self.stdout.write(
                self.style.WARNING(
                    f'⚠️ 部分{snapshot_type}快照创建失败，成功: {result["success_count"]}, 失败: {result["error_count"]}'
                )
            )
        
        if options['verbose']:
            self._print_detailed_result(result)

    def _handle_cleanup(self, options):
        """处理数据清理"""
        keep_days = options['keep_days']
        
        self.stdout.write(f'开始清理 {keep_days} 天前的数据...')
        
        # 确认操作
        if not options.get('force', False):
            confirm = input(f'确认要删除 {keep_days} 天前的数据吗？(y/N): ')
            if confirm.lower() != 'y':
                self.stdout.write('操作已取消')
                return
        
        result = asyncio.run(cleanup_old_data_task(keep_days))
        
        # 输出结果
        if result['success']:
            self.stdout.write(
                self.style.SUCCESS(
                    f'✅ 数据清理完成，删除JIRA记录: {result["jira_records_deleted"]}, Git记录: {result["git_records_deleted"]}'
                )
            )
        else:
            self.stdout.write(
                self.style.ERROR(f'❌ 数据清理失败: {result["error"]}')
            )

    def _print_detailed_result(self, result):
        """打印详细结果"""
        self.stdout.write('\n📊 详细结果:')
        self.stdout.write(f'  开始时间: {result.get("started_at", "未知")}')
        self.stdout.write(f'  完成时间: {result.get("completed_at", "未知")}')
        
        if 'period' in result:
            self.stdout.write(f'  时间范围: {result["period"]}')
        
        if 'total_teams' in result:
            self.stdout.write(f'  团队总数: {result["total_teams"]}')
        
        if 'team_results' in result:
            self.stdout.write('\n📋 团队结果:')
            for team_result in result['team_results']:
                status = '✅' if team_result.get('success', False) else '❌'
                team_id = team_result.get('team_id', '未知')
                self.stdout.write(f'  {status} {team_id}')
                
                if not team_result.get('success', False) and 'error' in team_result:
                    self.stdout.write(f'    错误: {team_result["error"]}')
        
        if 'jira_result' in result and result['jira_result']:
            jira_status = '✅' if result['jira_result']['success'] else '❌'
            self.stdout.write(f'\n📈 JIRA数据: {jira_status}')
            
        if 'git_result' in result and result['git_result']:
            git_status = '✅' if result['git_result']['success'] else '❌'
            self.stdout.write(f'📊 Git数据: {git_status}')


# 使用示例：
# 
# # 收集单个团队的所有数据（最近30天）
# python manage.py collect_rd_metrics --team-id TEAM001 --type all --days 30
# 
# # 收集所有团队的JIRA数据（最近7天）
# python manage.py collect_rd_metrics --all-teams --type jira --days 7
# 
# # 创建周度快照
# python manage.py collect_rd_metrics --create-snapshots weekly
# 
# # 创建月度快照
# python manage.py collect_rd_metrics --create-snapshots monthly
# 
# # 清理2年前的数据
# python manage.py collect_rd_metrics --cleanup --keep-days 730
# 
# # 详细输出模式
# python manage.py collect_rd_metrics --team-id TEAM001 --verbose
