# -*- coding: utf-8 -*-
"""
清理Seatalk访问令牌缓存的管理命令
"""
from django.core.management.base import BaseCommand
from app01.config import _seatalk_token_cache
from icecream import ic

class Command(BaseCommand):
    help = '清理Seatalk访问令牌缓存'

    def handle(self, *args, **options):
        try:
            with _seatalk_token_cache['lock']:
                old_token = _seatalk_token_cache['token']
                _seatalk_token_cache['token'] = None
                _seatalk_token_cache['expires_at'] = 0
            
            if old_token:
                self.stdout.write(
                    self.style.SUCCESS('✅ Seatalk访问令牌缓存已清理')
                )
                ic("Seatalk访问令牌缓存已清理")
            else:
                self.stdout.write(
                    self.style.WARNING('⚠️ 缓存中没有访问令牌')
                )
                ic("缓存中没有访问令牌")
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ 清理缓存失败: {str(e)}')
            )
            ic(f"清理缓存失败: {str(e)}")
