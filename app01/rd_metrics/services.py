"""
R&D Metrics Data Integration Services
JIRA and Git data collection and processing
"""

import json
import logging
import asyncio
from datetime import datetime, timedelta, date
from typing import Dict, List, Optional, Tuple
from django.utils import timezone
from django.db.models import Q, Count, Avg, Sum
from jira import JIRA, JIRAError

from app01.models import (
    RDTeam, RDTeamMember, RDJiraMetrics, RDGitMetrics, 
    RDMetricsSnapshot, UserJiraToken
)
from app01.config import JIRA_TOKEN
from app01.ai_module.smart_jira_query import SmartJiraQuery

logger = logging.getLogger(__name__)


class RDJiraDataService:
    """R&D JIRA数据服务"""
    
    def __init__(self, user_email: str = None):
        self.user_email = user_email
        self.jira_token = None
        self.jira = None
        
    async def _get_jira_token(self) -> str:
        """获取JIRA token"""
        if self.user_email == "<EMAIL>":
            return JIRA_TOKEN
        elif self.user_email:
            try:
                user_token = UserJiraToken.objects.get(
                    user_email=self.user_email,
                    is_active=True
                )
                if not user_token.is_expired():
                    user_token.mark_used()
                    return user_token.jira_token
            except UserJiraToken.DoesNotExist:
                pass
        
        return JIRA_TOKEN
    
    def _get_jira_connection(self) -> JIRA:
        """获取JIRA连接"""
        if not self.jira:
            try:
                self.jira = JIRA(server='https://jira.shopee.io', token_auth=self.jira_token)
            except Exception as e:
                logger.error(f"JIRA连接失败: {str(e)}")
                raise
        return self.jira
    
    async def collect_team_jira_metrics(self, team: RDTeam, start_date: date, end_date: date) -> Dict:
        """收集团队JIRA指标数据"""
        try:
            logger.info(f"开始收集团队 {team.team_name} 的JIRA指标数据")
            
            # 获取JIRA token
            if not self.jira_token:
                self.jira_token = await self._get_jira_token()
            
            results = {}
            
            for project_key in team.jira_projects:
                logger.info(f"收集项目 {project_key} 的数据")
                project_metrics = await self._collect_project_metrics(
                    team, project_key, start_date, end_date
                )
                results[project_key] = project_metrics
            
            return {
                'success': True,
                'data': results,
                'team_id': team.team_id,
                'period': f"{start_date} to {end_date}"
            }
            
        except Exception as e:
            logger.error(f"收集团队JIRA指标失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'team_id': team.team_id
            }
    
    async def _collect_project_metrics(self, team: RDTeam, project_key: str, 
                                     start_date: date, end_date: date) -> Dict:
        """收集单个项目的指标数据"""
        try:
            # 构建JQL查询
            jql_queries = self._build_jql_queries(project_key, start_date, end_date)
            
            # 使用SmartJiraQuery执行查询
            smart_query = SmartJiraQuery(user_email=self.user_email)
            
            metrics = {
                'total_issues': 0,
                'created_issues': 0,
                'resolved_issues': 0,
                'reopened_issues': 0,
                'total_bugs': 0,
                'new_bugs': 0,
                'fixed_bugs': 0,
                'bug_fix_time_avg': None,
                'story_points_committed': 0,
                'story_points_completed': 0,
                'velocity': 0,
                'avg_cycle_time': None,
                'avg_lead_time': None,
                'defect_density': None,
                'rework_rate': None,
            }
            
            # 执行各种查询
            for query_name, jql in jql_queries.items():
                logger.info(f"执行查询: {query_name}")
                result = await smart_query.execute_jql(jql, max_results=1000)
                
                if result['success']:
                    issues = result['data']['issues']
                    metrics.update(self._process_query_result(query_name, issues))
                else:
                    logger.warning(f"查询 {query_name} 失败: {result.get('error')}")
            
            # 计算衍生指标
            metrics = self._calculate_derived_metrics(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"收集项目 {project_key} 指标失败: {str(e)}")
            return {}
    
    def _build_jql_queries(self, project_key: str, start_date: date, end_date: date) -> Dict[str, str]:
        """构建JQL查询语句"""
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        return {
            'total_issues': f'project = {project_key} AND created >= "{start_str}" AND created <= "{end_str}"',
            'resolved_issues': f'project = {project_key} AND resolved >= "{start_str}" AND resolved <= "{end_str}"',
            'bugs': f'project = {project_key} AND type = Bug AND created >= "{start_str}" AND created <= "{end_str}"',
            'fixed_bugs': f'project = {project_key} AND type = Bug AND resolved >= "{start_str}" AND resolved <= "{end_str}"',
            'stories': f'project = {project_key} AND type in (Story, Task) AND created >= "{start_str}" AND created <= "{end_str}"',
            'completed_stories': f'project = {project_key} AND type in (Story, Task) AND resolved >= "{start_str}" AND resolved <= "{end_str}"',
            'reopened_issues': f'project = {project_key} AND status changed to "Reopened" during ("{start_str}", "{end_str}")',
        }
    
    def _process_query_result(self, query_name: str, issues: List) -> Dict:
        """处理查询结果"""
        result = {}
        
        if query_name == 'total_issues':
            result['total_issues'] = len(issues)
            result['created_issues'] = len(issues)
            
        elif query_name == 'resolved_issues':
            result['resolved_issues'] = len(issues)
            
            # 计算平均解决时间
            resolution_times = []
            for issue in issues:
                created = issue.get('fields', {}).get('created')
                resolved = issue.get('fields', {}).get('resolutiondate')
                if created and resolved:
                    created_dt = datetime.fromisoformat(created.replace('Z', '+00:00'))
                    resolved_dt = datetime.fromisoformat(resolved.replace('Z', '+00:00'))
                    resolution_time = (resolved_dt - created_dt).days
                    resolution_times.append(resolution_time)
            
            if resolution_times:
                result['avg_cycle_time'] = sum(resolution_times) / len(resolution_times)
                
        elif query_name == 'bugs':
            result['total_bugs'] = len(issues)
            result['new_bugs'] = len(issues)
            
        elif query_name == 'fixed_bugs':
            result['fixed_bugs'] = len(issues)
            
            # 计算Bug修复时间
            bug_fix_times = []
            for issue in issues:
                created = issue.get('fields', {}).get('created')
                resolved = issue.get('fields', {}).get('resolutiondate')
                if created and resolved:
                    created_dt = datetime.fromisoformat(created.replace('Z', '+00:00'))
                    resolved_dt = datetime.fromisoformat(resolved.replace('Z', '+00:00'))
                    fix_time = (resolved_dt - created_dt).days
                    bug_fix_times.append(fix_time)
            
            if bug_fix_times:
                result['bug_fix_time_avg'] = sum(bug_fix_times) / len(bug_fix_times)
                
        elif query_name == 'stories':
            # 计算故事点
            total_story_points = 0
            for issue in issues:
                story_points = issue.get('fields', {}).get('customfield_10002')  # Story Points字段
                if story_points:
                    total_story_points += float(story_points)
            result['story_points_committed'] = total_story_points
            
        elif query_name == 'completed_stories':
            # 计算完成的故事点
            completed_story_points = 0
            for issue in issues:
                story_points = issue.get('fields', {}).get('customfield_10002')
                if story_points:
                    completed_story_points += float(story_points)
            result['story_points_completed'] = completed_story_points
            
        elif query_name == 'reopened_issues':
            result['reopened_issues'] = len(issues)
        
        return result
    
    def _calculate_derived_metrics(self, metrics: Dict) -> Dict:
        """计算衍生指标"""
        # 计算速度 (Velocity)
        metrics['velocity'] = metrics['story_points_completed']
        
        # 计算缺陷密度 (每个故事点的Bug数)
        if metrics['story_points_completed'] > 0:
            metrics['defect_density'] = metrics['total_bugs'] / metrics['story_points_completed']
        
        # 计算返工率 (重新打开的问题比例)
        if metrics['resolved_issues'] > 0:
            metrics['rework_rate'] = metrics['reopened_issues'] / metrics['resolved_issues'] * 100
        
        return metrics
    
    async def save_team_metrics(self, team: RDTeam, metrics_data: Dict, target_date: date) -> bool:
        """保存团队指标数据"""
        try:
            for project_key, project_metrics in metrics_data.items():
                # 更新或创建JIRA指标记录
                jira_metrics, created = RDJiraMetrics.objects.update_or_create(
                    team=team,
                    project_key=project_key,
                    date=target_date,
                    defaults={
                        'total_issues': project_metrics.get('total_issues', 0),
                        'created_issues': project_metrics.get('created_issues', 0),
                        'resolved_issues': project_metrics.get('resolved_issues', 0),
                        'reopened_issues': project_metrics.get('reopened_issues', 0),
                        'total_bugs': project_metrics.get('total_bugs', 0),
                        'new_bugs': project_metrics.get('new_bugs', 0),
                        'fixed_bugs': project_metrics.get('fixed_bugs', 0),
                        'bug_fix_time_avg': project_metrics.get('bug_fix_time_avg'),
                        'story_points_committed': project_metrics.get('story_points_committed', 0),
                        'story_points_completed': project_metrics.get('story_points_completed', 0),
                        'velocity': project_metrics.get('velocity', 0),
                        'avg_cycle_time': project_metrics.get('avg_cycle_time'),
                        'avg_lead_time': project_metrics.get('avg_lead_time'),
                        'defect_density': project_metrics.get('defect_density'),
                        'rework_rate': project_metrics.get('rework_rate'),
                    }
                )
                
                logger.info(f"保存团队 {team.team_name} 项目 {project_key} 的指标数据: {'创建' if created else '更新'}")
            
            return True
            
        except Exception as e:
            logger.error(f"保存团队指标数据失败: {str(e)}")
            return False


class RDGitDataService:
    """R&D Git数据服务"""
    
    def __init__(self):
        self.git_token = "pCkAzptSzLpiv4tNKRma"  # 从config中获取
        self.base_url = "https://git.garena.com/api/v4"
    
    async def collect_team_git_metrics(self, team: RDTeam, start_date: date, end_date: date) -> Dict:
        """收集团队Git指标数据"""
        try:
            logger.info(f"开始收集团队 {team.team_name} 的Git指标数据")
            
            results = {}
            
            # 加载services_id.json获取仓库映射
            services_mapping = self._load_services_mapping()
            
            for repo_name in team.git_repositories:
                if repo_name in services_mapping:
                    repo_id = services_mapping[repo_name]
                    logger.info(f"收集仓库 {repo_name} (ID: {repo_id}) 的数据")
                    
                    repo_metrics = await self._collect_repository_metrics(
                        team, repo_name, repo_id, start_date, end_date
                    )
                    results[repo_name] = repo_metrics
                else:
                    logger.warning(f"仓库 {repo_name} 未在services_id.json中找到")
            
            return {
                'success': True,
                'data': results,
                'team_id': team.team_id,
                'period': f"{start_date} to {end_date}"
            }
            
        except Exception as e:
            logger.error(f"收集团队Git指标失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'team_id': team.team_id
            }
    
    def _load_services_mapping(self) -> Dict:
        """加载services_id.json映射"""
        try:
            import os
            from djangoProject.settings import BASE_DIR
            
            services_path = os.path.join(BASE_DIR, 'app01', 'services_id.json')
            with open(services_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载services_id.json失败: {str(e)}")
            return {}
    
    async def _collect_repository_metrics(self, team: RDTeam, repo_name: str, repo_id: int,
                                        start_date: date, end_date: date) -> Dict:
        """收集单个仓库的指标数据"""
        # 这里需要实现Git API调用来获取提交、MR等数据
        # 由于篇幅限制，这里提供基本结构
        
        metrics = {
            'total_commits': 0,
            'total_authors': 0,
            'lines_added': 0,
            'lines_deleted': 0,
            'merge_requests_created': 0,
            'merge_requests_merged': 0,
            'avg_mr_review_time': None,
            'code_churn_rate': None,
            'hotspot_files_count': 0,
        }
        
        # TODO: 实现Git API调用
        # 1. 获取提交数据
        # 2. 获取MR数据
        # 3. 计算代码变动指标
        
        return metrics


class RDMetricsAggregationService:
    """R&D指标聚合服务"""
    
    @staticmethod
    async def create_team_snapshot(team: RDTeam, period_type: str, 
                                 period_start: date, period_end: date) -> bool:
        """创建团队指标快照"""
        try:
            # 聚合JIRA指标
            jira_metrics = RDJiraMetrics.objects.filter(
                team=team,
                date__gte=period_start,
                date__lte=period_end,
                is_test_data=False
            )
            
            # 聚合Git指标
            git_metrics = RDGitMetrics.objects.filter(
                team=team,
                date__gte=period_start,
                date__lte=period_end,
                is_test_data=False
            )
            
            # 计算聚合数据
            aggregated_data = {
                'jira_metrics': {
                    'total_issues': jira_metrics.aggregate(Sum('total_issues'))['total_issues__sum'] or 0,
                    'resolved_issues': jira_metrics.aggregate(Sum('resolved_issues'))['resolved_issues__sum'] or 0,
                    'total_bugs': jira_metrics.aggregate(Sum('total_bugs'))['total_bugs__sum'] or 0,
                    'avg_cycle_time': jira_metrics.aggregate(Avg('avg_cycle_time'))['avg_cycle_time__avg'],
                    'velocity': jira_metrics.aggregate(Sum('velocity'))['velocity__sum'] or 0,
                },
                'git_metrics': {
                    'total_commits': git_metrics.aggregate(Sum('total_commits'))['total_commits__sum'] or 0,
                    'lines_added': git_metrics.aggregate(Sum('lines_added'))['lines_added__sum'] or 0,
                    'merge_requests_merged': git_metrics.aggregate(Sum('merge_requests_merged'))['merge_requests_merged__sum'] or 0,
                },
                'period_info': {
                    'start_date': period_start.isoformat(),
                    'end_date': period_end.isoformat(),
                    'period_type': period_type,
                }
            }
            
            # 创建或更新快照
            snapshot, created = RDMetricsSnapshot.objects.update_or_create(
                team=team,
                period_type=period_type,
                period_start=period_start,
                defaults={
                    'period_end': period_end,
                    'metrics_data': aggregated_data,
                    'data_sources': {
                        'jira_records': jira_metrics.count(),
                        'git_records': git_metrics.count(),
                    }
                }
            )
            
            logger.info(f"团队 {team.team_name} 的 {period_type} 快照{'创建' if created else '更新'}成功")
            return True
            
        except Exception as e:
            logger.error(f"创建团队快照失败: {str(e)}")
            return False
