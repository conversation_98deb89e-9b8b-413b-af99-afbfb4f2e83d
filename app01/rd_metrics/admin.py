"""
R&D Metrics Django Admin Configuration
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
import json

from app01.models import (
    RDTeam, RDTeamMember, RDMetricsPermission,
    RDJiraMetrics, RDGitMetrics, RDMetricsSnapshot
)


@admin.register(RDTeam)
class RDTeamAdmin(admin.ModelAdmin):
    list_display = ['team_id', 'team_name', 'department', 'team_leader_email', 'member_count', 'is_active', 'is_test_data']
    list_filter = ['department', 'is_active', 'is_test_data', 'created_at']
    search_fields = ['team_id', 'team_name', 'department', 'team_leader_email']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('team_id', 'team_name', 'department', 'team_leader_email', 'parent_team_id', 'is_active')
        }),
        ('配置信息', {
            'fields': ('jira_projects', 'git_repositories')
        }),
        ('系统信息', {
            'fields': ('is_test_data', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def member_count(self, obj):
        count = obj.members.filter(is_active=True).count()
        if count > 0:
            url = reverse('admin:app01_rdteammember_changelist') + f'?team__id={obj.id}'
            return format_html('<a href="{}">{} 人</a>', url, count)
        return '0 人'
    member_count.short_description = '成员数量'
    
    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related('members')


@admin.register(RDTeamMember)
class RDTeamMemberAdmin(admin.ModelAdmin):
    list_display = ['member_name', 'member_email', 'team', 'role', 'join_date', 'is_active', 'is_test_data']
    list_filter = ['role', 'is_active', 'is_test_data', 'join_date', 'team__department']
    search_fields = ['member_name', 'member_email', 'team__team_name', 'jira_account_id', 'git_username']
    raw_id_fields = ['team']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('team', 'member_name', 'member_email', 'role', 'join_date', 'is_active')
        }),
        ('账户信息', {
            'fields': ('jira_account_id', 'git_username')
        }),
        ('系统信息', {
            'fields': ('is_test_data',),
            'classes': ('collapse',)
        }),
    )


@admin.register(RDMetricsPermission)
class RDMetricsPermissionAdmin(admin.ModelAdmin):
    list_display = ['user_email', 'permission_level', 'accessible_teams_count', 'is_active', 'granted_by', 'granted_at', 'is_test_data']
    list_filter = ['permission_level', 'is_active', 'is_test_data', 'granted_at']
    search_fields = ['user_email', 'granted_by']
    filter_horizontal = ['accessible_teams']
    
    fieldsets = (
        ('用户信息', {
            'fields': ('user_email', 'permission_level', 'is_active')
        }),
        ('权限设置', {
            'fields': ('can_view_individual_metrics', 'can_view_team_comparison', 'can_export_data', 'can_configure_teams')
        }),
        ('团队访问权限', {
            'fields': ('accessible_teams',)
        }),
        ('授权信息', {
            'fields': ('granted_by', 'granted_at', 'expires_at')
        }),
        ('系统信息', {
            'fields': ('is_test_data',),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['granted_at']
    
    def accessible_teams_count(self, obj):
        count = obj.accessible_teams.count()
        if count > 0:
            return f'{count} 个团队'
        return '无限制' if obj.permission_level in ['admin', 'manager'] else '0 个团队'
    accessible_teams_count.short_description = '可访问团队'


@admin.register(RDJiraMetrics)
class RDJiraMetricsAdmin(admin.ModelAdmin):
    list_display = ['team', 'project_key', 'date', 'total_issues', 'resolved_issues', 'velocity', 'bug_fix_rate', 'is_test_data']
    list_filter = ['project_key', 'is_test_data', 'date', 'team__department']
    search_fields = ['team__team_name', 'project_key']
    raw_id_fields = ['team']
    date_hierarchy = 'date'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('team', 'project_key', 'date')
        }),
        ('问题指标', {
            'fields': ('total_issues', 'created_issues', 'resolved_issues', 'reopened_issues')
        }),
        ('Bug指标', {
            'fields': ('total_bugs', 'new_bugs', 'fixed_bugs', 'bug_fix_time_avg')
        }),
        ('故事点指标', {
            'fields': ('story_points_committed', 'story_points_completed', 'velocity')
        }),
        ('时间指标', {
            'fields': ('avg_cycle_time', 'avg_lead_time')
        }),
        ('质量指标', {
            'fields': ('defect_density', 'rework_rate')
        }),
        ('系统信息', {
            'fields': ('is_test_data', 'created_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['created_at']
    
    def bug_fix_rate(self, obj):
        if obj.total_bugs > 0:
            rate = (obj.fixed_bugs / obj.total_bugs) * 100
            return f'{rate:.1f}%'
        return 'N/A'
    bug_fix_rate.short_description = 'Bug修复率'


@admin.register(RDGitMetrics)
class RDGitMetricsAdmin(admin.ModelAdmin):
    list_display = ['team', 'repository_name', 'date', 'total_commits', 'lines_added', 'lines_deleted', 'mr_merge_rate', 'is_test_data']
    list_filter = ['is_test_data', 'date', 'team__department']
    search_fields = ['team__team_name', 'repository_name']
    raw_id_fields = ['team']
    date_hierarchy = 'date'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('team', 'repository_name', 'repository_id', 'date')
        }),
        ('提交指标', {
            'fields': ('total_commits', 'total_authors', 'lines_added', 'lines_deleted')
        }),
        ('合并请求指标', {
            'fields': ('merge_requests_created', 'merge_requests_merged', 'avg_mr_review_time')
        }),
        ('代码质量指标', {
            'fields': ('code_churn_rate', 'hotspot_files_count')
        }),
        ('系统信息', {
            'fields': ('is_test_data', 'created_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['created_at']
    
    def mr_merge_rate(self, obj):
        if obj.merge_requests_created > 0:
            rate = (obj.merge_requests_merged / obj.merge_requests_created) * 100
            return f'{rate:.1f}%'
        return 'N/A'
    mr_merge_rate.short_description = 'MR合并率'


@admin.register(RDMetricsSnapshot)
class RDMetricsSnapshotAdmin(admin.ModelAdmin):
    list_display = ['team', 'period_type', 'period_start', 'period_end', 'calculated_at', 'data_summary', 'is_test_data']
    list_filter = ['period_type', 'is_test_data', 'calculated_at', 'team__department']
    search_fields = ['team__team_name']
    raw_id_fields = ['team']
    date_hierarchy = 'period_start'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('snapshot_id', 'team', 'period_type', 'period_start', 'period_end')
        }),
        ('指标数据', {
            'fields': ('metrics_data_display', 'data_sources_display')
        }),
        ('系统信息', {
            'fields': ('is_test_data', 'calculated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['snapshot_id', 'calculated_at', 'metrics_data_display', 'data_sources_display']
    
    def data_summary(self, obj):
        try:
            jira_data = obj.metrics_data.get('jira_metrics', {})
            git_data = obj.metrics_data.get('git_metrics', {})
            
            summary = []
            if jira_data.get('total_issues'):
                summary.append(f"问题: {jira_data['total_issues']}")
            if jira_data.get('velocity'):
                summary.append(f"速度: {jira_data['velocity']:.1f}")
            if git_data.get('total_commits'):
                summary.append(f"提交: {git_data['total_commits']}")
            
            return ' | '.join(summary) if summary else '无数据'
        except:
            return '数据解析错误'
    data_summary.short_description = '数据摘要'
    
    def metrics_data_display(self, obj):
        try:
            formatted_json = json.dumps(obj.metrics_data, indent=2, ensure_ascii=False)
            return format_html('<pre style="max-height: 300px; overflow-y: auto;">{}</pre>', formatted_json)
        except:
            return '数据格式错误'
    metrics_data_display.short_description = '指标数据'
    
    def data_sources_display(self, obj):
        try:
            formatted_json = json.dumps(obj.data_sources, indent=2, ensure_ascii=False)
            return format_html('<pre>{}</pre>', formatted_json)
        except:
            return '数据格式错误'
    data_sources_display.short_description = '数据源信息'


# 自定义Admin站点标题
admin.site.site_header = 'R&D效率指标管理'
admin.site.site_title = 'R&D指标'
admin.site.index_title = 'R&D效率指标管理系统'


# 添加自定义操作
@admin.action(description='标记为测试数据')
def mark_as_test_data(modeladmin, request, queryset):
    queryset.update(is_test_data=True)


@admin.action(description='标记为正式数据')
def mark_as_production_data(modeladmin, request, queryset):
    queryset.update(is_test_data=False)


# 为所有模型添加批量操作
for model_admin in [RDTeamAdmin, RDTeamMemberAdmin, RDMetricsPermissionAdmin, 
                   RDJiraMetricsAdmin, RDGitMetricsAdmin, RDMetricsSnapshotAdmin]:
    model_admin.actions = [mark_as_test_data, mark_as_production_data]
