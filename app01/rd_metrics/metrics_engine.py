"""
R&D Metrics Calculation Engine
Implements all metrics from specification with proper calculation logic
"""

import logging
from datetime import datetime, timedelta, date
from typing import Dict, List, Optional, Tuple, Any
from django.db.models import Q, Count, Avg, Sum, F, Case, When, FloatField
from django.utils import timezone

from app01.models import (
    RDTeam, RDTeamMember, RDJiraMetrics, RDGitMetrics, 
    RDMetricsSnapshot
)

logger = logging.getLogger(__name__)


class RDMetricsCalculator:
    """R&D指标计算器"""
    
    def __init__(self, team: RDTeam = None):
        self.team = team
    
    # ================================
    # 开发效率指标
    # ================================
    
    def calculate_development_velocity(self, start_date: date, end_date: date) -> Dict:
        """计算开发速度指标"""
        try:
            jira_metrics = self._get_jira_metrics(start_date, end_date)
            
            # 团队速度 (Story Points per Sprint)
            total_velocity = jira_metrics.aggregate(Sum('velocity'))['velocity__sum'] or 0
            avg_velocity = jira_metrics.aggregate(Avg('velocity'))['velocity__avg'] or 0
            
            # 吞吐量 (Issues per day)
            days = (end_date - start_date).days + 1
            total_resolved = jira_metrics.aggregate(Sum('resolved_issues'))['resolved_issues__sum'] or 0
            throughput = total_resolved / days if days > 0 else 0
            
            # 交付效率 (完成率)
            total_committed = jira_metrics.aggregate(Sum('story_points_committed'))['story_points_committed__sum'] or 0
            total_completed = jira_metrics.aggregate(Sum('story_points_completed'))['story_points_completed__sum'] or 0
            delivery_efficiency = (total_completed / total_committed * 100) if total_committed > 0 else 0
            
            return {
                'team_velocity': {
                    'value': total_velocity,
                    'unit': 'story_points',
                    'description': '团队在指定周期内完成的故事点总数'
                },
                'average_velocity': {
                    'value': round(avg_velocity, 2),
                    'unit': 'story_points_per_sprint',
                    'description': '平均每个冲刺完成的故事点数'
                },
                'throughput': {
                    'value': round(throughput, 2),
                    'unit': 'issues_per_day',
                    'description': '平均每天解决的问题数量'
                },
                'delivery_efficiency': {
                    'value': round(delivery_efficiency, 2),
                    'unit': 'percentage',
                    'description': '承诺故事点的完成率'
                }
            }
            
        except Exception as e:
            logger.error(f"计算开发速度指标失败: {str(e)}")
            return {}
    
    def calculate_cycle_time_metrics(self, start_date: date, end_date: date) -> Dict:
        """计算周期时间指标"""
        try:
            jira_metrics = self._get_jira_metrics(start_date, end_date)
            
            # 平均周期时间
            avg_cycle_time = jira_metrics.aggregate(Avg('avg_cycle_time'))['avg_cycle_time__avg']
            
            # 平均前置时间
            avg_lead_time = jira_metrics.aggregate(Avg('avg_lead_time'))['avg_lead_time__avg']
            
            # 周期时间趋势分析
            cycle_time_trend = self._calculate_trend(jira_metrics, 'avg_cycle_time', start_date, end_date)
            
            return {
                'average_cycle_time': {
                    'value': round(avg_cycle_time, 2) if avg_cycle_time else None,
                    'unit': 'days',
                    'description': '从开始开发到完成的平均时间'
                },
                'average_lead_time': {
                    'value': round(avg_lead_time, 2) if avg_lead_time else None,
                    'unit': 'days',
                    'description': '从需求提出到交付的平均时间'
                },
                'cycle_time_trend': cycle_time_trend
            }
            
        except Exception as e:
            logger.error(f"计算周期时间指标失败: {str(e)}")
            return {}
    
    # ================================
    # 质量指标
    # ================================
    
    def calculate_quality_metrics(self, start_date: date, end_date: date) -> Dict:
        """计算质量指标"""
        try:
            jira_metrics = self._get_jira_metrics(start_date, end_date)
            
            # Bug相关指标
            total_bugs = jira_metrics.aggregate(Sum('total_bugs'))['total_bugs__sum'] or 0
            fixed_bugs = jira_metrics.aggregate(Sum('fixed_bugs'))['fixed_bugs__sum'] or 0
            avg_bug_fix_time = jira_metrics.aggregate(Avg('bug_fix_time_avg'))['bug_fix_time_avg__avg']
            
            # 缺陷密度
            avg_defect_density = jira_metrics.aggregate(Avg('defect_density'))['defect_density__avg']
            
            # 返工率
            avg_rework_rate = jira_metrics.aggregate(Avg('rework_rate'))['rework_rate__avg']
            
            # Bug修复率
            bug_fix_rate = (fixed_bugs / total_bugs * 100) if total_bugs > 0 else 0
            
            # 质量趋势
            quality_trend = self._calculate_quality_trend(jira_metrics, start_date, end_date)
            
            return {
                'total_bugs': {
                    'value': total_bugs,
                    'unit': 'count',
                    'description': '周期内发现的Bug总数'
                },
                'bug_fix_rate': {
                    'value': round(bug_fix_rate, 2),
                    'unit': 'percentage',
                    'description': 'Bug修复率'
                },
                'average_bug_fix_time': {
                    'value': round(avg_bug_fix_time, 2) if avg_bug_fix_time else None,
                    'unit': 'days',
                    'description': '平均Bug修复时间'
                },
                'defect_density': {
                    'value': round(avg_defect_density, 4) if avg_defect_density else None,
                    'unit': 'bugs_per_story_point',
                    'description': '每个故事点的Bug数量'
                },
                'rework_rate': {
                    'value': round(avg_rework_rate, 2) if avg_rework_rate else None,
                    'unit': 'percentage',
                    'description': '需要返工的问题比例'
                },
                'quality_trend': quality_trend
            }
            
        except Exception as e:
            logger.error(f"计算质量指标失败: {str(e)}")
            return {}
    
    # ================================
    # 团队效能指标
    # ================================
    
    def calculate_team_performance_metrics(self, start_date: date, end_date: date) -> Dict:
        """计算团队效能指标"""
        try:
            jira_metrics = self._get_jira_metrics(start_date, end_date)
            git_metrics = self._get_git_metrics(start_date, end_date)
            
            # 团队生产力指标
            total_story_points = jira_metrics.aggregate(Sum('story_points_completed'))['story_points_completed__sum'] or 0
            team_size = self.team.members.filter(is_active=True).count() if self.team else 1
            productivity_per_person = total_story_points / team_size if team_size > 0 else 0
            
            # 代码贡献指标
            total_commits = git_metrics.aggregate(Sum('total_commits'))['total_commits__sum'] or 0
            total_lines_added = git_metrics.aggregate(Sum('lines_added'))['lines_added__sum'] or 0
            total_lines_deleted = git_metrics.aggregate(Sum('lines_deleted'))['lines_deleted__sum'] or 0
            
            commits_per_person = total_commits / team_size if team_size > 0 else 0
            
            # 协作指标
            total_mrs_created = git_metrics.aggregate(Sum('merge_requests_created'))['merge_requests_created__sum'] or 0
            total_mrs_merged = git_metrics.aggregate(Sum('merge_requests_merged'))['merge_requests_merged__sum'] or 0
            mr_merge_rate = (total_mrs_merged / total_mrs_created * 100) if total_mrs_created > 0 else 0
            
            avg_mr_review_time = git_metrics.aggregate(Avg('avg_mr_review_time'))['avg_mr_review_time__avg']
            
            return {
                'team_productivity': {
                    'value': round(productivity_per_person, 2),
                    'unit': 'story_points_per_person',
                    'description': '人均完成的故事点数'
                },
                'commits_per_person': {
                    'value': round(commits_per_person, 2),
                    'unit': 'commits_per_person',
                    'description': '人均提交次数'
                },
                'code_churn': {
                    'lines_added': total_lines_added,
                    'lines_deleted': total_lines_deleted,
                    'net_lines': total_lines_added - total_lines_deleted,
                    'description': '代码变动情况'
                },
                'collaboration_metrics': {
                    'mr_merge_rate': round(mr_merge_rate, 2),
                    'avg_review_time': round(avg_mr_review_time, 2) if avg_mr_review_time else None,
                    'description': '团队协作效率指标'
                }
            }
            
        except Exception as e:
            logger.error(f"计算团队效能指标失败: {str(e)}")
            return {}
    
    # ================================
    # 综合指标计算
    # ================================
    
    def calculate_comprehensive_metrics(self, start_date: date, end_date: date) -> Dict:
        """计算综合指标"""
        try:
            # 获取各类指标
            velocity_metrics = self.calculate_development_velocity(start_date, end_date)
            cycle_time_metrics = self.calculate_cycle_time_metrics(start_date, end_date)
            quality_metrics = self.calculate_quality_metrics(start_date, end_date)
            team_metrics = self.calculate_team_performance_metrics(start_date, end_date)
            
            # 计算综合评分
            comprehensive_score = self._calculate_comprehensive_score({
                'velocity': velocity_metrics,
                'cycle_time': cycle_time_metrics,
                'quality': quality_metrics,
                'team_performance': team_metrics
            })
            
            return {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': (end_date - start_date).days + 1
                },
                'development_velocity': velocity_metrics,
                'cycle_time': cycle_time_metrics,
                'quality': quality_metrics,
                'team_performance': team_metrics,
                'comprehensive_score': comprehensive_score,
                'calculated_at': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"计算综合指标失败: {str(e)}")
            return {}
    
    # ================================
    # 辅助方法
    # ================================
    
    def _get_jira_metrics(self, start_date: date, end_date: date):
        """获取JIRA指标数据"""
        queryset = RDJiraMetrics.objects.filter(
            date__gte=start_date,
            date__lte=end_date,
            is_test_data=False
        )
        
        if self.team:
            queryset = queryset.filter(team=self.team)
        
        return queryset
    
    def _get_git_metrics(self, start_date: date, end_date: date):
        """获取Git指标数据"""
        queryset = RDGitMetrics.objects.filter(
            date__gte=start_date,
            date__lte=end_date,
            is_test_data=False
        )
        
        if self.team:
            queryset = queryset.filter(team=self.team)
        
        return queryset
    
    def _calculate_trend(self, queryset, field_name: str, start_date: date, end_date: date) -> Dict:
        """计算趋势"""
        try:
            # 按周分组计算趋势
            weekly_data = []
            current_date = start_date
            
            while current_date <= end_date:
                week_end = min(current_date + timedelta(days=6), end_date)
                week_metrics = queryset.filter(
                    date__gte=current_date,
                    date__lte=week_end
                )
                
                avg_value = week_metrics.aggregate(Avg(field_name))[f'{field_name}__avg']
                
                weekly_data.append({
                    'week_start': current_date.isoformat(),
                    'week_end': week_end.isoformat(),
                    'value': round(avg_value, 2) if avg_value else None
                })
                
                current_date = week_end + timedelta(days=1)
            
            # 计算趋势方向
            valid_values = [item['value'] for item in weekly_data if item['value'] is not None]
            trend_direction = 'stable'
            
            if len(valid_values) >= 2:
                if valid_values[-1] > valid_values[0]:
                    trend_direction = 'increasing'
                elif valid_values[-1] < valid_values[0]:
                    trend_direction = 'decreasing'
            
            return {
                'weekly_data': weekly_data,
                'trend_direction': trend_direction,
                'data_points': len(valid_values)
            }
            
        except Exception as e:
            logger.error(f"计算趋势失败: {str(e)}")
            return {'weekly_data': [], 'trend_direction': 'unknown', 'data_points': 0}
    
    def _calculate_quality_trend(self, queryset, start_date: date, end_date: date) -> Dict:
        """计算质量趋势"""
        try:
            # 计算质量指标的趋势
            bug_trend = self._calculate_trend(queryset, 'total_bugs', start_date, end_date)
            defect_density_trend = self._calculate_trend(queryset, 'defect_density', start_date, end_date)
            
            return {
                'bug_count_trend': bug_trend,
                'defect_density_trend': defect_density_trend
            }
            
        except Exception as e:
            logger.error(f"计算质量趋势失败: {str(e)}")
            return {}
    
    def _calculate_comprehensive_score(self, metrics: Dict) -> Dict:
        """计算综合评分"""
        try:
            scores = {}
            weights = {
                'velocity': 0.3,
                'quality': 0.3,
                'cycle_time': 0.2,
                'team_performance': 0.2
            }
            
            # 速度评分 (基于交付效率)
            delivery_efficiency = metrics.get('velocity', {}).get('delivery_efficiency', {}).get('value', 0)
            scores['velocity'] = min(delivery_efficiency, 100)  # 最高100分
            
            # 质量评分 (基于Bug修复率和缺陷密度)
            bug_fix_rate = metrics.get('quality', {}).get('bug_fix_rate', {}).get('value', 0)
            defect_density = metrics.get('quality', {}).get('defect_density', {}).get('value', 0)
            
            quality_score = bug_fix_rate
            if defect_density is not None and defect_density > 0:
                # 缺陷密度越低，质量分越高
                quality_score = quality_score * (1 / (1 + defect_density))
            
            scores['quality'] = min(quality_score, 100)
            
            # 周期时间评分 (基于周期时间，越短越好)
            avg_cycle_time = metrics.get('cycle_time', {}).get('average_cycle_time', {}).get('value')
            if avg_cycle_time:
                # 假设理想周期时间为5天，超过则扣分
                cycle_time_score = max(0, 100 - (avg_cycle_time - 5) * 10)
                scores['cycle_time'] = min(cycle_time_score, 100)
            else:
                scores['cycle_time'] = 50  # 默认分数
            
            # 团队效能评分 (基于生产力)
            productivity = metrics.get('team_performance', {}).get('team_productivity', {}).get('value', 0)
            scores['team_performance'] = min(productivity * 10, 100)  # 假设10个故事点/人为满分
            
            # 计算加权总分
            total_score = sum(scores[key] * weights[key] for key in scores)
            
            return {
                'individual_scores': scores,
                'weights': weights,
                'total_score': round(total_score, 2),
                'grade': self._get_grade(total_score)
            }
            
        except Exception as e:
            logger.error(f"计算综合评分失败: {str(e)}")
            return {'total_score': 0, 'grade': 'Unknown'}
    
    def _get_grade(self, score: float) -> str:
        """根据分数获取等级"""
        if score >= 90:
            return 'A+'
        elif score >= 80:
            return 'A'
        elif score >= 70:
            return 'B+'
        elif score >= 60:
            return 'B'
        elif score >= 50:
            return 'C'
        else:
            return 'D'


class RDMetricsComparator:
    """R&D指标对比器"""
    
    @staticmethod
    def compare_teams(teams: List[RDTeam], start_date: date, end_date: date) -> Dict:
        """对比多个团队的指标"""
        try:
            comparison_data = {}
            
            for team in teams:
                calculator = RDMetricsCalculator(team)
                team_metrics = calculator.calculate_comprehensive_metrics(start_date, end_date)
                comparison_data[team.team_id] = {
                    'team_name': team.team_name,
                    'department': team.department,
                    'metrics': team_metrics
                }
            
            # 生成对比分析
            analysis = RDMetricsComparator._generate_comparison_analysis(comparison_data)
            
            return {
                'comparison_data': comparison_data,
                'analysis': analysis,
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"团队对比失败: {str(e)}")
            return {}
    
    @staticmethod
    def _generate_comparison_analysis(comparison_data: Dict) -> Dict:
        """生成对比分析"""
        try:
            # 提取各团队的综合评分
            scores = {}
            for team_id, data in comparison_data.items():
                score = data.get('metrics', {}).get('comprehensive_score', {}).get('total_score', 0)
                scores[team_id] = score
            
            # 排名
            sorted_teams = sorted(scores.items(), key=lambda x: x[1], reverse=True)
            
            # 分析结果
            analysis = {
                'ranking': [
                    {
                        'rank': i + 1,
                        'team_id': team_id,
                        'team_name': comparison_data[team_id]['team_name'],
                        'score': score
                    }
                    for i, (team_id, score) in enumerate(sorted_teams)
                ],
                'top_performer': sorted_teams[0] if sorted_teams else None,
                'average_score': sum(scores.values()) / len(scores) if scores else 0,
                'score_distribution': {
                    'max': max(scores.values()) if scores else 0,
                    'min': min(scores.values()) if scores else 0,
                    'std_dev': RDMetricsComparator._calculate_std_dev(list(scores.values()))
                }
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"生成对比分析失败: {str(e)}")
            return {}
    
    @staticmethod
    def _calculate_std_dev(values: List[float]) -> float:
        """计算标准差"""
        if not values:
            return 0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance ** 0.5
