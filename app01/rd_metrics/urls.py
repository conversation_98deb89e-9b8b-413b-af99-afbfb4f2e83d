"""
R&D Metrics URL Configuration
"""

from django.urls import path
from . import views

app_name = 'rd_metrics'

urlpatterns = [
    # Dashboard
    path('', views.rd_metrics_dashboard, name='dashboard'),
    path('help/', views.metrics_help, name='metrics_help'),

    # Team Management
    path('teams/', views.team_management, name='team_management'),
    path('teams/new/', views.team_detail, name='team_create'),
    path('teams/<str:team_id>/', views.team_detail, name='team_detail'),
    path('teams/<str:team_id>/members/', views.team_member_manage, name='team_member_manage'),

    # Permission Management
    path('permissions/', views.permission_management, name='permission_management'),
    path('permissions/grant/', views.grant_permission, name='grant_permission'),

    # API Endpoints
    path('api/teams/', views.api_teams_list, name='api_teams_list'),
    path('api/teams/<str:team_id>/metrics/', views.api_team_metrics, name='api_team_metrics'),
    path('api/teams/comparison/', views.api_teams_comparison, name='api_teams_comparison'),
    path('api/metrics/export/', views.api_metrics_export, name='api_metrics_export'),
    path('api/data/collect/', views.trigger_data_collection, name='trigger_data_collection'),
]
