# R&D效率指标计算方法详细说明

## 📊 指标体系概览

R&D效率指标系统包含四大类指标，每个指标都有明确的计算公式和业务含义。

## 🚀 开发速度指标

### 1. 团队速度 (Team Velocity)
**定义**: 团队在指定周期内完成的工时总数
**计算公式**: 
```
团队速度 = Σ(已完成任务的工时)
```
**数据来源**: JIRA中已解决(Resolved)状态的Story/Task的工时字段
**计算逻辑**:
1. 查询指定时间范围内状态变更为"已解决"的所有Story和Task
2. 提取每个任务的工时字段(customfield_10002)
3. 对所有工时进行求和
**业务意义**: 反映团队整体交付能力，数值越高表示团队产出越大

### 2. 平均速度 (Average Velocity)
**定义**: 平均每个冲刺/周期完成的工时数
**计算公式**: 
```
平均速度 = 总完成工时 ÷ 统计周期数
```
**计算逻辑**:
1. 将统计时间范围按周分割
2. 计算每周的完成工时
3. 求平均值
**业务意义**: 衡量团队工作节奏的稳定性，波动小表示计划准确性高

### 3. 吞吐量 (Throughput)
**定义**: 平均每天解决的问题数量
**计算公式**: 
```
吞吐量 = 已解决问题总数 ÷ 工作天数
```
**计算逻辑**:
1. 统计时间范围内所有已解决的问题数量
2. 计算工作天数（排除周末）
3. 求平均值
**业务意义**: 反映团队处理问题的效率，数值越高表示处理速度越快

### 4. 交付效率 (Delivery Efficiency)
**定义**: 承诺工时的完成率
**计算公式**: 
```
交付效率 = (完成工时 ÷ 承诺工时) × 100%
```
**计算逻辑**:
1. 统计周期开始时处于"进行中"状态的任务工时总和（承诺工时）
2. 统计周期内实际完成的任务工时总和（完成工时）
3. 计算完成率
**业务意义**: 衡量团队承诺兑现能力，90%以上为优秀

## 🎯 质量指标

### 1. Bug总数 (Total Bugs)
**定义**: 周期内发现的Bug总数
**计算公式**: 
```
Bug总数 = COUNT(type = 'Bug' AND created >= start_date AND created <= end_date)
```
**数据来源**: JIRA中问题类型为Bug的记录
**业务意义**: 反映产品质量状况，需结合功能复杂度评估

### 2. Bug修复率 (Bug Fix Rate)
**定义**: Bug修复的比例
**计算公式**: 
```
Bug修复率 = (已修复Bug数 ÷ 总Bug数) × 100%
```
**计算逻辑**:
1. 统计周期内新发现的Bug总数
2. 统计其中已修复（状态为Resolved/Closed）的Bug数量
3. 计算修复比例
**业务意义**: 95%以上为优秀，90-95%为良好

### 3. 平均Bug修复时间 (Average Bug Fix Time)
**定义**: 从Bug创建到修复的平均时间
**计算公式**: 
```
平均修复时间 = Σ(Bug修复时间) ÷ 已修复Bug数
Bug修复时间 = 解决时间 - 创建时间
```
**计算逻辑**:
1. 对每个已修复的Bug计算修复时间（天数）
2. 求所有修复时间的平均值
**业务意义**: 紧急Bug应24小时内修复，一般Bug建议3-5天

### 4. 缺陷密度 (Defect Density)
**定义**: 每个工时的Bug数量
**计算公式**: 
```
缺陷密度 = Bug总数 ÷ 完成工时数
```
**计算逻辑**:
1. 统计周期内发现的Bug总数
2. 统计周期内完成的工时总数
3. 计算比值
**业务意义**: 低于0.1为优秀，0.1-0.2为良好，高于0.2需改进

### 5. 返工率 (Rework Rate)
**定义**: 需要返工的问题比例
**计算公式**: 
```
返工率 = (重新打开问题数 ÷ 已解决问题数) × 100%
```
**计算逻辑**:
1. 统计周期内状态变更为"重新打开"的问题数
2. 统计周期内已解决的问题总数
3. 计算返工比例
**业务意义**: 低于5%为优秀，5-10%可接受，高于10%需改进

## ⏱️ 周期时间指标

### 1. 平均周期时间 (Average Cycle Time)
**定义**: 从开始开发到完成的平均时间
**计算公式**: 
```
周期时间 = 完成时间 - 开始开发时间
平均周期时间 = Σ(周期时间) ÷ 完成任务数
```
**计算逻辑**:
1. 对每个已完成任务，计算从"进行中"状态到"已解决"状态的时间
2. 求所有任务周期时间的平均值
**业务意义**: 小功能1-3天，中等功能3-7天，大功能7-14天为合理

### 2. 平均前置时间 (Average Lead Time)
**定义**: 从需求提出到交付的平均时间
**计算公式**: 
```
前置时间 = 交付时间 - 需求创建时间
平均前置时间 = Σ(前置时间) ÷ 完成任务数
```
**计算逻辑**:
1. 对每个已完成任务，计算从创建到解决的总时间
2. 求所有任务前置时间的平均值
**业务意义**: 包含需求分析、设计、开发、测试全流程

## 👥 团队效能指标

### 1. 人均生产力 (Productivity per Person)
**定义**: 人均完成的工时数
**计算公式**: 
```
人均生产力 = 总完成工时 ÷ 团队人数
```
**计算逻辑**:
1. 统计团队在周期内完成的总工时
2. 获取团队活跃成员数量
3. 计算人均产出
**业务意义**: 衡量个人效率，需考虑成员经验和任务复杂度

### 2. 人均提交数 (Commits per Person)
**定义**: 人均代码提交次数
**计算公式**: 
```
人均提交数 = 总提交次数 ÷ 团队人数
```
**数据来源**: Git仓库提交记录
**业务意义**: 反映代码贡献度，建议每天2-5次提交

### 3. 代码变动率 (Code Churn)
**定义**: 代码新增、删除和净变化情况
**计算公式**: 
```
代码变动率 = (新增行数 + 删除行数) ÷ 总代码行数
净代码行数 = 新增行数 - 删除行数
```
**业务意义**: 适度变动表示持续改进，过度变动可能存在设计问题

### 4. MR合并率 (MR Merge Rate)
**定义**: 合并请求的成功合并比例
**计算公式**: 
```
MR合并率 = (已合并MR数 ÷ 创建MR数) × 100%
```
**业务意义**: 90%以上为优秀，表示代码质量高且团队协作良好

### 5. 平均审查时间 (Average Review Time)
**定义**: 代码审查的平均耗时
**计算公式**: 
```
审查时间 = MR合并时间 - MR创建时间
平均审查时间 = Σ(审查时间) ÷ MR数量
```
**业务意义**: 建议控制在4-8小时内

## 📈 综合评分体系

### 评分权重分配
- **开发速度 (30%)**: 基于交付效率计算
- **质量指标 (30%)**: 基于Bug修复率和缺陷密度计算  
- **周期时间 (20%)**: 基于平均周期时间计算
- **团队效能 (20%)**: 基于人均生产力计算

### 各维度评分计算

#### 1. 速度评分
```
速度评分 = min(交付效率, 100)
```

#### 2. 质量评分
```
质量评分 = Bug修复率 × (1 / (1 + 缺陷密度))
质量评分 = min(质量评分, 100)
```

#### 3. 周期时间评分
```
理想周期时间 = 5天
周期时间评分 = max(0, 100 - (实际周期时间 - 理想周期时间) × 10)
```

#### 4. 团队效能评分
```
团队效能评分 = min(人均生产力 × 10, 100)
```

### 综合评分计算
```
综合评分 = 速度评分 × 0.3 + 质量评分 × 0.3 + 周期时间评分 × 0.2 + 团队效能评分 × 0.2
```

### 等级划分
- **A+ (90-100分)**: 优秀，保持现有水平
- **A (80-89分)**: 良好，继续优化
- **B+ (70-79分)**: 中等，识别改进点
- **B (60-69分)**: 一般，制定提升计划
- **C (50-59分)**: 较差，重点关注
- **D (<50分)**: 差，需要详细改进措施

## 🔄 数据更新频率

- **实时数据**: 用户操作触发的查询
- **每日汇总**: 凌晨自动执行数据收集
- **历史数据**: 保留2年详细数据，5年汇总数据

## 📝 注意事项

1. **数据准确性**: 所有计算基于JIRA和Git的实际数据
2. **时区处理**: 统一使用服务器时区进行时间计算
3. **异常处理**: 对于异常数据（如负数、超大值）进行过滤
4. **权限控制**: 不同权限级别用户看到的数据范围不同
5. **测试数据**: 所有测试数据都有明确标记，不影响正式统计
