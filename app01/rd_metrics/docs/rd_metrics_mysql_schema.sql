-- R&D效率指标系统 MySQL数据库表结构
-- 使用方法：mysql -u username -p database_name < rd_metrics_mysql_schema.sql

-- 1. R&D团队表
CREATE TABLE IF NOT EXISTS `rd_team` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `team_id` varchar(50) NOT NULL UNIQUE,
    `team_name` varchar(100) NOT NULL,
    `department` varchar(100) NOT NULL,
    `team_leader_email` varchar(254) NOT NULL,
    `parent_team_id` varchar(50) DEFAULT NULL,
    `is_active` tinyint(1) NOT NULL DEFAULT 1,
    `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `jira_projects` text NOT NULL DEFAULT '[]',
    `git_repositories` text NOT NULL DEFAULT '[]',
    `is_test_data` tinyint(1) NOT NULL DEFAULT 0,
    PRIMARY KEY (`id`),
    KEY `rd_team_team_id_is_active_idx` (`team_id`, `is_active`),
    KEY `rd_team_department_idx` (`department`),
    KEY `rd_team_is_test_data_idx` (`is_test_data`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. R&D团队成员表
CREATE TABLE IF NOT EXISTS `rd_team_member` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `team_id` bigint NOT NULL,
    `member_email` varchar(254) NOT NULL,
    `member_name` varchar(100) NOT NULL,
    `role` varchar(50) NOT NULL,
    `join_date` date NOT NULL,
    `is_active` tinyint(1) NOT NULL DEFAULT 1,
    `jira_account_id` varchar(100) DEFAULT NULL,
    `git_username` varchar(100) DEFAULT NULL,
    `is_test_data` tinyint(1) NOT NULL DEFAULT 0,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_team_member_email` (`team_id`, `member_email`),
    KEY `rd_team_member_email_active_idx` (`member_email`, `is_active`),
    KEY `rd_team_member_team_role_idx` (`team_id`, `role`),
    KEY `rd_team_member_test_data_idx` (`is_test_data`),
    CONSTRAINT `rd_team_member_team_id_fk` FOREIGN KEY (`team_id`) REFERENCES `rd_team` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. R&D指标快照表
CREATE TABLE IF NOT EXISTS `rd_metrics_snapshot` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `snapshot_id` char(36) NOT NULL UNIQUE,
    `team_id` bigint NOT NULL,
    `period_type` varchar(20) NOT NULL,
    `period_start` date NOT NULL,
    `period_end` date NOT NULL,
    `metrics_data` longtext NOT NULL DEFAULT '{}',
    `calculated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `data_sources` text NOT NULL DEFAULT '{}',
    `is_test_data` tinyint(1) NOT NULL DEFAULT 0,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_team_period_snapshot` (`team_id`, `period_type`, `period_start`),
    KEY `rd_metrics_snapshot_team_period_idx` (`team_id`, `period_type`, `period_start`),
    KEY `rd_metrics_snapshot_calc_at_idx` (`calculated_at`),
    KEY `rd_metrics_snapshot_test_data_idx` (`is_test_data`),
    CONSTRAINT `rd_metrics_snapshot_team_id_fk` FOREIGN KEY (`team_id`) REFERENCES `rd_team` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. R&D JIRA指标表
CREATE TABLE IF NOT EXISTS `rd_jira_metrics` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `team_id` bigint NOT NULL,
    `project_key` varchar(20) NOT NULL,
    `date` date NOT NULL,
    `total_issues` int NOT NULL DEFAULT 0,
    `created_issues` int NOT NULL DEFAULT 0,
    `resolved_issues` int NOT NULL DEFAULT 0,
    `reopened_issues` int NOT NULL DEFAULT 0,
    `total_bugs` int NOT NULL DEFAULT 0,
    `new_bugs` int NOT NULL DEFAULT 0,
    `fixed_bugs` int NOT NULL DEFAULT 0,
    `bug_fix_time_avg` double DEFAULT NULL,
    `story_points_committed` double NOT NULL DEFAULT 0,
    `story_points_completed` double NOT NULL DEFAULT 0,
    `velocity` double NOT NULL DEFAULT 0,
    `avg_cycle_time` double DEFAULT NULL,
    `avg_lead_time` double DEFAULT NULL,
    `defect_density` double DEFAULT NULL,
    `rework_rate` double DEFAULT NULL,
    `is_test_data` tinyint(1) NOT NULL DEFAULT 0,
    `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_team_project_date_jira` (`team_id`, `project_key`, `date`),
    KEY `rd_jira_metrics_team_date_idx` (`team_id`, `date`),
    KEY `rd_jira_metrics_project_date_idx` (`project_key`, `date`),
    KEY `rd_jira_metrics_test_data_idx` (`is_test_data`),
    CONSTRAINT `rd_jira_metrics_team_id_fk` FOREIGN KEY (`team_id`) REFERENCES `rd_team` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. R&D Git指标表
CREATE TABLE IF NOT EXISTS `rd_git_metrics` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `team_id` bigint NOT NULL,
    `repository_name` varchar(200) NOT NULL,
    `repository_id` int NOT NULL,
    `date` date NOT NULL,
    `total_commits` int NOT NULL DEFAULT 0,
    `total_authors` int NOT NULL DEFAULT 0,
    `lines_added` int NOT NULL DEFAULT 0,
    `lines_deleted` int NOT NULL DEFAULT 0,
    `merge_requests_created` int NOT NULL DEFAULT 0,
    `merge_requests_merged` int NOT NULL DEFAULT 0,
    `avg_mr_review_time` double DEFAULT NULL,
    `code_churn_rate` double DEFAULT NULL,
    `hotspot_files_count` int NOT NULL DEFAULT 0,
    `is_test_data` tinyint(1) NOT NULL DEFAULT 0,
    `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_team_repo_date_git` (`team_id`, `repository_id`, `date`),
    KEY `rd_git_metrics_team_date_idx` (`team_id`, `date`),
    KEY `rd_git_metrics_repo_date_idx` (`repository_id`, `date`),
    KEY `rd_git_metrics_test_data_idx` (`is_test_data`),
    CONSTRAINT `rd_git_metrics_team_id_fk` FOREIGN KEY (`team_id`) REFERENCES `rd_team` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. R&D指标权限表
CREATE TABLE IF NOT EXISTS `rd_metrics_permission` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `user_email` varchar(254) NOT NULL UNIQUE,
    `permission_level` varchar(20) NOT NULL,
    `can_view_individual_metrics` tinyint(1) NOT NULL DEFAULT 1,
    `can_view_team_comparison` tinyint(1) NOT NULL DEFAULT 0,
    `can_export_data` tinyint(1) NOT NULL DEFAULT 0,
    `can_configure_teams` tinyint(1) NOT NULL DEFAULT 0,
    `is_active` tinyint(1) NOT NULL DEFAULT 1,
    `granted_by` varchar(254) NOT NULL,
    `granted_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `expires_at` datetime(6) DEFAULT NULL,
    `is_test_data` tinyint(1) NOT NULL DEFAULT 0,
    PRIMARY KEY (`id`),
    KEY `rd_metrics_perm_email_active_idx` (`user_email`, `is_active`),
    KEY `rd_metrics_perm_level_idx` (`permission_level`),
    KEY `rd_metrics_perm_test_data_idx` (`is_test_data`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 7. R&D权限团队关联表（多对多关系）
CREATE TABLE IF NOT EXISTS `rd_metrics_permission_accessible_teams` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `rdmetricspermission_id` bigint NOT NULL,
    `rdteam_id` bigint NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_permission_team` (`rdmetricspermission_id`, `rdteam_id`),
    KEY `rd_perm_teams_perm_idx` (`rdmetricspermission_id`),
    KEY `rd_perm_teams_team_idx` (`rdteam_id`),
    CONSTRAINT `rd_perm_teams_perm_fk` FOREIGN KEY (`rdmetricspermission_id`) REFERENCES `rd_metrics_permission` (`id`) ON DELETE CASCADE,
    CONSTRAINT `rd_perm_teams_team_fk` FOREIGN KEY (`rdteam_id`) REFERENCES `rd_team` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入初始权限数据
INSERT IGNORE INTO `rd_metrics_permission` (
    `user_email`, 
    `permission_level`, 
    `can_view_individual_metrics`, 
    `can_view_team_comparison`, 
    `can_export_data`, 
    `can_configure_teams`, 
    `granted_by`, 
    `is_test_data`
) VALUES 
('<EMAIL>', 'admin', 1, 1, 1, 1, '<EMAIL>', 0),
('<EMAIL>', 'admin', 1, 1, 1, 1, '<EMAIL>', 1);

-- 创建视图用于统计查询
CREATE OR REPLACE VIEW `rd_team_summary` AS
SELECT 
    t.id,
    t.team_id,
    t.team_name,
    t.department,
    t.team_leader_email,
    COUNT(m.id) as member_count,
    t.is_active,
    t.created_at
FROM rd_team t
LEFT JOIN rd_team_member m ON t.id = m.team_id AND m.is_active = 1
WHERE t.is_active = 1
GROUP BY t.id, t.team_id, t.team_name, t.department, t.team_leader_email, t.is_active, t.created_at;
