# R&D指标系统本地部署指南

## 🎯 部署目标

在本地环境部署R&D指标系统，连接远程MySQL数据库，避免与线上服务冲突。

## 📋 前置准备

### 1. 确认远程数据库连接信息
根据您之前提到的远程服务器访问方式，需要以下信息：
- 远程MySQL服务器地址
- 数据库端口（通常是3306）
- 数据库名称
- 用户名和密码
- SSH隧道信息（如果需要）

### 2. 本地环境要求
- Python 3.8+
- MySQL 8.0+ (与远程服务器版本一致)
- Git

## 🔧 本地MySQL安装和配置

### 安装MySQL 8.0
```bash
# macOS (使用Homebrew)
brew install mysql@8.0
brew services start mysql@8.0

# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server-8.0

# CentOS/RHEL
sudo yum install mysql-server
sudo systemctl start mysqld
```

### 创建本地数据库
```bash
# 登录MySQL
mysql -u root -p

# 创建数据库和用户
CREATE DATABASE chatbot_rd_metrics CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'rd_metrics_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON chatbot_rd_metrics.* TO 'rd_metrics_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

## 📊 数据同步方案

### 方案1: 直接连接远程数据库（推荐用于开发测试）

修改本地 `djangoProject/settings.py`:

```python
# 本地开发配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': '远程数据库名',
        'USER': '远程用户名',
        'PASSWORD': '远程密码',
        'HOST': '远程服务器IP',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}

# 如果需要SSH隧道
# 先建立SSH隧道：ssh -L 3307:localhost:3306 user@remote_server
# 然后使用：
# 'HOST': '127.0.0.1',
# 'PORT': '3307',
```

### 方案2: 数据导出导入（推荐用于完全本地测试）

#### 从远程服务器导出数据
```bash
# 在远程服务器上执行
mysqldump -u username -p --single-transaction --routines --triggers database_name > chatbot_backup.sql

# 或者只导出特定表
mysqldump -u username -p database_name \
  rd_team rd_team_member rd_jira_metrics rd_git_metrics \
  rd_metrics_snapshot rd_metrics_permission \
  rd_metrics_permission_accessible_teams > rd_metrics_backup.sql
```

#### 导入到本地数据库
```bash
# 下载备份文件到本地
scp user@remote_server:/path/to/chatbot_backup.sql ./

# 导入到本地数据库
mysql -u rd_metrics_user -p chatbot_rd_metrics < chatbot_backup.sql

# 或者先创建表结构，再导入数据
mysql -u rd_metrics_user -p chatbot_rd_metrics < docs/rd_metrics_mysql_schema.sql
```

## 🚀 本地项目配置

### 1. 创建本地配置文件

创建 `djangoProject/local_settings.py`:

```python
# 本地开发配置
import os
from .settings import *

# 数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'chatbot_rd_metrics',
        'USER': 'rd_metrics_user',
        'PASSWORD': 'your_password',
        'HOST': '127.0.0.1',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}

# 调试模式
DEBUG = True

# 允许的主机
ALLOWED_HOSTS = ['localhost', '127.0.0.1']

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/rd_metrics_local.log',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'app01.rd_metrics': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}

# 禁用某些中间件（如果有冲突）
# MIDDLEWARE = [m for m in MIDDLEWARE if 'some_middleware' not in m]

# 静态文件配置
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# 媒体文件配置
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
```

### 2. 修改启动脚本

创建 `run_local.py`:

```python
#!/usr/bin/env python
import os
import sys

if __name__ == '__main__':
    # 使用本地配置
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.local_settings')
    
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)
```

## 🗄️ 数据库表创建

### 使用SQL文件直接创建
```bash
# 使用我们提供的SQL文件创建表结构
mysql -u rd_metrics_user -p chatbot_rd_metrics < docs/rd_metrics_mysql_schema.sql
```

### 验证表创建
```sql
-- 检查表是否创建成功
USE chatbot_rd_metrics;
SHOW TABLES LIKE 'rd_%';

-- 检查表结构
DESCRIBE rd_team;
DESCRIBE rd_jira_metrics;
```

## 🧪 创建测试数据

```bash
# 使用本地配置启动
python run_local.py setup_rd_test_data --all

# 或者分步创建
python run_local.py setup_rd_test_data --create-teams
python run_local.py setup_rd_test_data --create-permissions  
python run_local.py setup_rd_test_data --create-sample-data
```

## 🚦 启动本地服务

```bash
# 创建日志目录
mkdir -p logs

# 启动开发服务器
python run_local.py runserver 8000

# 或者指定端口避免冲突
python run_local.py runserver 8001
```

## 🔍 验证部署

### 1. 检查数据库连接
```bash
python run_local.py shell

# 在Django shell中测试
from django.db import connection
cursor = connection.cursor()
cursor.execute("SELECT COUNT(*) FROM rd_team")
print(cursor.fetchone())
```

### 2. 访问页面验证
- 仪表板: `http://localhost:8000/rd-metrics/`
- 团队管理: `http://localhost:8000/rd-metrics/teams/`
- 管理后台: `http://localhost:8000/admin/`

### 3. API接口测试
```bash
# 测试团队列表API
curl "http://localhost:8000/rd-metrics/api/teams/?user_email=<EMAIL>"

# 测试团队指标API
curl "http://localhost:8000/rd-metrics/api/teams/test_chatbot_core/metrics/?days=30&user_email=<EMAIL>"
```

## ⚠️ 注意事项

### 1. 避免计划任务冲突
本地环境**不要**启动以下计划任务：
- 数据收集任务
- 邮件发送任务
- 外部API调用任务

### 2. 数据安全
- 本地数据库密码要设置强密码
- 不要将本地配置文件提交到版本控制
- 定期清理本地测试数据

### 3. 性能考虑
- 本地数据库可以使用较小的配置
- 可以只同步最近的数据（如最近3个月）
- 使用SSD存储提高性能

## 🔧 常见问题解决

### 1. 数据库连接失败
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查端口占用
netstat -tlnp | grep 3306

# 测试连接
mysql -u rd_metrics_user -p -h 127.0.0.1 -P 3306
```

### 2. 表不存在错误
```bash
# 重新创建表
python run_local.py migrate --fake-initial

# 或者手动创建
mysql -u rd_metrics_user -p chatbot_rd_metrics < docs/rd_metrics_mysql_schema.sql
```

### 3. 权限问题
```sql
-- 重新授权
GRANT ALL PRIVILEGES ON chatbot_rd_metrics.* TO 'rd_metrics_user'@'localhost';
FLUSH PRIVILEGES;
```

## 📝 本地开发工作流

1. **启动服务**: `python run_local.py runserver 8001`
2. **代码修改**: 修改代码后自动重载
3. **数据测试**: 使用测试数据验证功能
4. **API测试**: 使用curl或Postman测试接口
5. **数据清理**: 定期清理测试数据

这样配置后，您就可以在本地安全地测试R&D指标系统，而不会影响线上服务。
