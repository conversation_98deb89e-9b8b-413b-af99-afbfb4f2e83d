"""
R&D指标模块化计算系统
每个指标作为独立模块，便于维护和扩展
"""

import logging
from abc import ABC, abstractmethod
from datetime import date, timedelta
from typing import Dict, List, Optional, Any
from django.db.models import Q, Count, Avg, Sum, F, Case, When, FloatField
from django.utils import timezone

from app01.models import RDTeam, RDJiraMetrics, RDGitMetrics

logger = logging.getLogger(__name__)


class BaseMetric(ABC):
    """指标基类"""
    
    def __init__(self, team: RDTeam):
        self.team = team
    
    @property
    @abstractmethod
    def name(self) -> str:
        """指标名称"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """指标描述"""
        pass
    
    @abstractmethod
    def calculate(self, start_date: date, end_date: date) -> Dict[str, Any]:
        """计算指标值"""
        pass
    
    def _get_jira_metrics(self, start_date: date, end_date: date):
        """获取JIRA指标数据"""
        return RDJiraMetrics.objects.filter(
            team=self.team,
            date__range=[start_date, end_date]
        )
    
    def _get_git_metrics(self, start_date: date, end_date: date):
        """获取Git指标数据"""
        return RDGitMetrics.objects.filter(
            team=self.team,
            date__range=[start_date, end_date]
        )


class VelocityMetric(BaseMetric):
    """团队速度指标"""
    
    @property
    def name(self) -> str:
        return "团队速度"
    
    @property
    def description(self) -> str:
        return "团队在指定周期内完成的工时总数，反映团队整体交付能力"
    
    def calculate(self, start_date: date, end_date: date) -> Dict[str, Any]:
        jira_metrics = self._get_jira_metrics(start_date, end_date)
        
        total_velocity = jira_metrics.aggregate(Sum('velocity'))['velocity__sum'] or 0
        avg_velocity = jira_metrics.aggregate(Avg('velocity'))['velocity__avg'] or 0
        
        # 计算趋势
        days = (end_date - start_date).days + 1
        mid_date = start_date + timedelta(days=days//2)
        
        first_half = jira_metrics.filter(date__lt=mid_date).aggregate(Avg('velocity'))['velocity__avg'] or 0
        second_half = jira_metrics.filter(date__gte=mid_date).aggregate(Avg('velocity'))['velocity__avg'] or 0
        
        trend = ((second_half - first_half) / first_half * 100) if first_half > 0 else 0
        
        return {
            'value': round(total_velocity, 1),
            'average': round(avg_velocity, 1),
            'trend': round(trend, 1),
            'unit': '工时',
            'status': 'good' if avg_velocity >= 40 else 'warning' if avg_velocity >= 20 else 'poor'
        }


class BugFixRateMetric(BaseMetric):
    """Bug修复率指标"""
    
    @property
    def name(self) -> str:
        return "Bug修复率"
    
    @property
    def description(self) -> str:
        return "Bug修复的比例，反映团队问题解决能力"
    
    def calculate(self, start_date: date, end_date: date) -> Dict[str, Any]:
        jira_metrics = self._get_jira_metrics(start_date, end_date)
        
        total_bugs = jira_metrics.aggregate(Sum('total_bugs'))['total_bugs__sum'] or 0
        fixed_bugs = jira_metrics.aggregate(Sum('fixed_bugs'))['fixed_bugs__sum'] or 0
        
        fix_rate = (fixed_bugs / total_bugs * 100) if total_bugs > 0 else 0
        
        # 计算平均修复时间
        avg_fix_time = jira_metrics.aggregate(Avg('bug_fix_time_avg'))['bug_fix_time_avg__avg'] or 0
        
        return {
            'value': round(fix_rate, 1),
            'average_fix_time': round(avg_fix_time, 1),
            'total_bugs': total_bugs,
            'fixed_bugs': fixed_bugs,
            'unit': '%',
            'status': 'good' if fix_rate >= 95 else 'warning' if fix_rate >= 90 else 'poor'
        }


class CycleTimeMetric(BaseMetric):
    """周期时间指标"""
    
    @property
    def name(self) -> str:
        return "平均周期时间"
    
    @property
    def description(self) -> str:
        return "从开始开发到完成的平均时间，反映开发效率"
    
    def calculate(self, start_date: date, end_date: date) -> Dict[str, Any]:
        jira_metrics = self._get_jira_metrics(start_date, end_date)
        
        avg_cycle_time = jira_metrics.aggregate(Avg('avg_cycle_time'))['avg_cycle_time__avg'] or 0
        avg_lead_time = jira_metrics.aggregate(Avg('avg_lead_time'))['avg_lead_time__avg'] or 0
        
        return {
            'value': round(avg_cycle_time, 1),
            'lead_time': round(avg_lead_time, 1),
            'unit': '天',
            'status': 'good' if avg_cycle_time <= 3 else 'warning' if avg_cycle_time <= 7 else 'poor'
        }


class CodeQualityMetric(BaseMetric):
    """代码质量指标"""
    
    @property
    def name(self) -> str:
        return "代码质量评分"
    
    @property
    def description(self) -> str:
        return "基于缺陷密度、返工率等计算的综合质量评分"
    
    def calculate(self, start_date: date, end_date: date) -> Dict[str, Any]:
        jira_metrics = self._get_jira_metrics(start_date, end_date)
        
        # 缺陷密度
        total_bugs = jira_metrics.aggregate(Sum('total_bugs'))['total_bugs__sum'] or 0
        total_completed = jira_metrics.aggregate(Sum('story_points_completed'))['story_points_completed__sum'] or 0
        defect_density = (total_bugs / total_completed) if total_completed > 0 else 0
        
        # 返工率
        reopened_issues = jira_metrics.aggregate(Sum('reopened_issues'))['reopened_issues__sum'] or 0
        resolved_issues = jira_metrics.aggregate(Sum('resolved_issues'))['resolved_issues__sum'] or 0
        rework_rate = (reopened_issues / resolved_issues * 100) if resolved_issues > 0 else 0
        
        # 综合评分 (10分制)
        quality_score = max(0, 10 - (defect_density * 10) - (rework_rate / 10))
        
        return {
            'value': round(quality_score, 1),
            'defect_density': round(defect_density, 3),
            'rework_rate': round(rework_rate, 1),
            'unit': '分',
            'status': 'good' if quality_score >= 8 else 'warning' if quality_score >= 6 else 'poor'
        }


class ProductivityMetric(BaseMetric):
    """人均生产力指标"""
    
    @property
    def name(self) -> str:
        return "人均生产力"
    
    @property
    def description(self) -> str:
        return "人均完成的工时数，反映个人效率"
    
    def calculate(self, start_date: date, end_date: date) -> Dict[str, Any]:
        jira_metrics = self._get_jira_metrics(start_date, end_date)
        git_metrics = self._get_git_metrics(start_date, end_date)
        
        # 获取团队成员数量
        team_size = self.team.members.filter(is_active=True).count()
        
        # 计算人均工时
        total_completed = jira_metrics.aggregate(Sum('story_points_completed'))['story_points_completed__sum'] or 0
        productivity = (total_completed / team_size) if team_size > 0 else 0
        
        # 计算人均提交数
        total_commits = git_metrics.aggregate(Sum('total_commits'))['total_commits__sum'] or 0
        commits_per_person = (total_commits / team_size) if team_size > 0 else 0
        
        return {
            'value': round(productivity, 1),
            'commits_per_person': round(commits_per_person, 1),
            'team_size': team_size,
            'unit': '工时/人',
            'status': 'good' if productivity >= 15 else 'warning' if productivity >= 10 else 'poor'
        }


class OverallScoreMetric(BaseMetric):
    """综合评分指标"""
    
    @property
    def name(self) -> str:
        return "综合评分"
    
    @property
    def description(self) -> str:
        return "基于速度、质量、周期时间、生产力的综合评分"
    
    def calculate(self, start_date: date, end_date: date) -> Dict[str, Any]:
        # 获取各项指标
        velocity = VelocityMetric(self.team).calculate(start_date, end_date)
        bug_fix_rate = BugFixRateMetric(self.team).calculate(start_date, end_date)
        cycle_time = CycleTimeMetric(self.team).calculate(start_date, end_date)
        productivity = ProductivityMetric(self.team).calculate(start_date, end_date)
        
        # 计算各维度得分 (0-100)
        velocity_score = min(100, velocity['average'] * 2)  # 50工时 = 100分
        quality_score = bug_fix_rate['value']  # 直接使用百分比
        time_score = max(0, 100 - (cycle_time['value'] - 3) * 20)  # 3天为满分，每增加1天扣20分
        productivity_score = min(100, productivity['value'] * 5)  # 20工时/人 = 100分
        
        # 加权平均 (速度30%, 质量30%, 时间20%, 生产力20%)
        overall_score = (
            velocity_score * 0.3 + 
            quality_score * 0.3 + 
            time_score * 0.2 + 
            productivity_score * 0.2
        )
        
        # 等级评定
        if overall_score >= 90:
            grade = 'A+'
        elif overall_score >= 85:
            grade = 'A'
        elif overall_score >= 80:
            grade = 'A-'
        elif overall_score >= 75:
            grade = 'B+'
        elif overall_score >= 70:
            grade = 'B'
        elif overall_score >= 65:
            grade = 'B-'
        elif overall_score >= 60:
            grade = 'C+'
        elif overall_score >= 55:
            grade = 'C'
        elif overall_score >= 50:
            grade = 'C-'
        else:
            grade = 'D'
        
        return {
            'value': grade,
            'score': round(overall_score, 1),
            'breakdown': {
                'velocity': round(velocity_score, 1),
                'quality': round(quality_score, 1),
                'time': round(time_score, 1),
                'productivity': round(productivity_score, 1)
            },
            'unit': '等级',
            'status': 'good' if overall_score >= 80 else 'warning' if overall_score >= 60 else 'poor'
        }


class MetricsRegistry:
    """指标注册表"""
    
    _metrics = {
        'velocity': VelocityMetric,
        'bug_fix_rate': BugFixRateMetric,
        'cycle_time': CycleTimeMetric,
        'code_quality': CodeQualityMetric,
        'productivity': ProductivityMetric,
        'overall_score': OverallScoreMetric,
    }
    
    @classmethod
    def get_metric(cls, metric_type: str, team: RDTeam) -> BaseMetric:
        """获取指标实例"""
        if metric_type not in cls._metrics:
            raise ValueError(f"Unknown metric type: {metric_type}")
        return cls._metrics[metric_type](team)
    
    @classmethod
    def get_all_metrics(cls, team: RDTeam) -> Dict[str, BaseMetric]:
        """获取所有指标实例"""
        return {name: metric_class(team) for name, metric_class in cls._metrics.items()}
    
    @classmethod
    def calculate_all(cls, team: RDTeam, start_date: date, end_date: date) -> Dict[str, Any]:
        """计算所有指标"""
        results = {}
        for name, metric_class in cls._metrics.items():
            try:
                metric = metric_class(team)
                results[name] = metric.calculate(start_date, end_date)
                logger.info(f"Calculated {metric.name} for team {team.team_id}")
            except Exception as e:
                logger.error(f"Failed to calculate {name} for team {team.team_id}: {e}")
                results[name] = {
                    'value': 0,
                    'error': str(e),
                    'status': 'error'
                }
        return results
