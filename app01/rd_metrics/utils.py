"""
R&D指标系统公共工具类
提供通用的数据处理、验证和格式化功能
"""

import json
import logging
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.http import JsonResponse

logger = logging.getLogger(__name__)


class DataValidator:
    """数据验证工具"""
    
    @staticmethod
    def validate_date_range(start_date: Union[str, date], end_date: Union[str, date]) -> tuple:
        """验证日期范围"""
        try:
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            if start_date > end_date:
                raise ValidationError("开始日期不能晚于结束日期")
            
            if (end_date - start_date).days > 365:
                raise ValidationError("日期范围不能超过365天")
            
            return start_date, end_date
            
        except ValueError as e:
            raise ValidationError(f"日期格式错误: {e}")
    
    @staticmethod
    def validate_team_id(team_id: str) -> str:
        """验证团队ID"""
        if not team_id or not isinstance(team_id, str):
            raise ValidationError("团队ID不能为空")
        
        if len(team_id) > 50:
            raise ValidationError("团队ID长度不能超过50个字符")
        
        return team_id.strip()
    
    @staticmethod
    def validate_email(email: str) -> str:
        """验证邮箱格式"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, email):
            raise ValidationError("邮箱格式不正确")
        return email.lower().strip()


class DateHelper:
    """日期处理工具"""
    
    @staticmethod
    def get_date_range_by_days(days: int) -> tuple:
        """根据天数获取日期范围"""
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days-1)
        return start_date, end_date
    
    @staticmethod
    def get_week_range(date_obj: date = None) -> tuple:
        """获取指定日期所在周的范围"""
        if date_obj is None:
            date_obj = timezone.now().date()
        
        # 获取周一
        start_date = date_obj - timedelta(days=date_obj.weekday())
        end_date = start_date + timedelta(days=6)
        
        return start_date, end_date
    
    @staticmethod
    def get_month_range(date_obj: date = None) -> tuple:
        """获取指定日期所在月的范围"""
        if date_obj is None:
            date_obj = timezone.now().date()
        
        start_date = date_obj.replace(day=1)
        
        # 获取下个月第一天，然后减一天
        if date_obj.month == 12:
            next_month = date_obj.replace(year=date_obj.year + 1, month=1, day=1)
        else:
            next_month = date_obj.replace(month=date_obj.month + 1, day=1)
        
        end_date = next_month - timedelta(days=1)
        
        return start_date, end_date
    
    @staticmethod
    def format_date_range(start_date: date, end_date: date) -> str:
        """格式化日期范围显示"""
        if start_date == end_date:
            return start_date.strftime('%Y-%m-%d')
        return f"{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}"


class JSONHelper:
    """JSON处理工具"""
    
    @staticmethod
    def safe_json_loads(json_str: str, default: Any = None) -> Any:
        """安全的JSON解析"""
        try:
            if not json_str or json_str.strip() == '':
                return default or {}
            return json.loads(json_str)
        except (json.JSONDecodeError, TypeError) as e:
            logger.warning(f"JSON解析失败: {e}, 原始数据: {json_str}")
            return default or {}
    
    @staticmethod
    def safe_json_dumps(data: Any, default: str = '{}') -> str:
        """安全的JSON序列化"""
        try:
            return json.dumps(data, ensure_ascii=False, default=str)
        except (TypeError, ValueError) as e:
            logger.warning(f"JSON序列化失败: {e}, 原始数据: {data}")
            return default


class ResponseHelper:
    """响应处理工具"""
    
    @staticmethod
    def success_response(data: Any = None, message: str = "操作成功") -> JsonResponse:
        """成功响应"""
        return JsonResponse({
            'success': True,
            'message': message,
            'data': data
        })
    
    @staticmethod
    def error_response(message: str, code: int = 400, data: Any = None) -> JsonResponse:
        """错误响应"""
        return JsonResponse({
            'success': False,
            'message': message,
            'data': data
        }, status=code)
    
    @staticmethod
    def validation_error_response(errors: Dict[str, List[str]]) -> JsonResponse:
        """验证错误响应"""
        return JsonResponse({
            'success': False,
            'message': "数据验证失败",
            'errors': errors
        }, status=400)


class MetricsFormatter:
    """指标格式化工具"""
    
    @staticmethod
    def format_percentage(value: float, decimal_places: int = 1) -> str:
        """格式化百分比"""
        return f"{round(value, decimal_places)}%"
    
    @staticmethod
    def format_number(value: float, decimal_places: int = 1) -> str:
        """格式化数字"""
        return f"{round(value, decimal_places)}"
    
    @staticmethod
    def format_duration(days: float) -> str:
        """格式化时长"""
        if days < 1:
            hours = days * 24
            return f"{round(hours, 1)}小时"
        return f"{round(days, 1)}天"
    
    @staticmethod
    def format_trend(current: float, previous: float) -> Dict[str, Any]:
        """格式化趋势"""
        if previous == 0:
            return {
                'change': 0,
                'direction': 'neutral',
                'text': '无变化'
            }
        
        change = ((current - previous) / previous) * 100
        
        if abs(change) < 0.1:
            direction = 'neutral'
            text = '持平'
        elif change > 0:
            direction = 'up'
            text = f'+{round(change, 1)}%'
        else:
            direction = 'down'
            text = f'{round(change, 1)}%'
        
        return {
            'change': round(change, 1),
            'direction': direction,
            'text': text
        }
    
    @staticmethod
    def get_status_color(status: str) -> str:
        """获取状态颜色"""
        colors = {
            'good': '#52c41a',
            'warning': '#faad14',
            'poor': '#ff4d4f',
            'error': '#ff4d4f',
            'neutral': '#d9d9d9'
        }
        return colors.get(status, '#d9d9d9')


class CacheHelper:
    """缓存工具"""
    
    @staticmethod
    def get_cache_key(prefix: str, *args) -> str:
        """生成缓存键"""
        key_parts = [prefix] + [str(arg) for arg in args]
        return ':'.join(key_parts)
    
    @staticmethod
    def get_metrics_cache_key(team_id: str, start_date: date, end_date: date) -> str:
        """生成指标缓存键"""
        return CacheHelper.get_cache_key(
            'rd_metrics', 
            team_id, 
            start_date.strftime('%Y%m%d'), 
            end_date.strftime('%Y%m%d')
        )


class PermissionHelper:
    """权限检查工具"""
    
    @staticmethod
    def check_team_access(user_email: str, team_id: str) -> bool:
        """检查用户是否有团队访问权限"""
        from app01.models import RDMetricsPermission, RDTeam
        
        try:
            permission = RDMetricsPermission.objects.get(
                user_email=user_email,
                is_active=True
            )
            
            # 管理员有所有权限
            if permission.permission_level == 'admin':
                return True
            
            # 检查是否有特定团队权限
            team = RDTeam.objects.get(team_id=team_id)
            return permission.accessible_teams.filter(id=team.id).exists()
            
        except (RDMetricsPermission.DoesNotExist, RDTeam.DoesNotExist):
            return False
    
    @staticmethod
    def get_accessible_teams(user_email: str) -> List[str]:
        """获取用户可访问的团队列表"""
        from app01.models import RDMetricsPermission
        
        try:
            permission = RDMetricsPermission.objects.get(
                user_email=user_email,
                is_active=True
            )
            
            if permission.permission_level == 'admin':
                from app01.models import RDTeam
                return list(RDTeam.objects.filter(is_active=True).values_list('team_id', flat=True))
            
            return list(permission.accessible_teams.filter(is_active=True).values_list('team_id', flat=True))
            
        except RDMetricsPermission.DoesNotExist:
            return []


class DataExporter:
    """数据导出工具"""
    
    @staticmethod
    def export_to_csv(data: List[Dict], filename: str) -> str:
        """导出为CSV格式"""
        import csv
        import io
        
        if not data:
            return ""
        
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)
        
        return output.getvalue()
    
    @staticmethod
    def export_metrics_summary(metrics_data: Dict, team_name: str, date_range: str) -> Dict:
        """导出指标摘要"""
        summary = {
            'team_name': team_name,
            'date_range': date_range,
            'export_time': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
            'metrics': {}
        }
        
        for metric_name, metric_data in metrics_data.items():
            if isinstance(metric_data, dict) and 'value' in metric_data:
                summary['metrics'][metric_name] = {
                    'value': metric_data['value'],
                    'status': metric_data.get('status', 'unknown'),
                    'unit': metric_data.get('unit', '')
                }
        
        return summary


# 常用的装饰器
def handle_exceptions(default_return=None):
    """异常处理装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Function {func.__name__} failed: {e}")
                return default_return
        return wrapper
    return decorator


def log_execution_time(func):
    """执行时间记录装饰器"""
    def wrapper(*args, **kwargs):
        import time
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logger.info(f"Function {func.__name__} executed in {end_time - start_time:.2f} seconds")
        return result
    return wrapper
