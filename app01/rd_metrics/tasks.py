"""
R&D Metrics Background Tasks
Data collection and processing tasks
"""

import logging
from datetime import datetime, timedelta, date
from typing import List, Dict

from app01.models import RDTeam, RDTeamMember
from .services import RDJiraDataService, RDGitDataService, RDMetricsAggregationService

logger = logging.getLogger(__name__)


def collect_team_metrics_task(team_id: str, collection_type: str = 'all', 
                             days: int = 30, user_email: str = None) -> Dict:
    """
    收集团队指标数据任务
    
    Args:
        team_id: 团队ID
        collection_type: 收集类型 ('jira', 'git', 'all')
        days: 收集天数
        user_email: 触发用户邮箱
    
    Returns:
        任务执行结果
    """
    try:
        logger.info(f"开始收集团队 {team_id} 的指标数据，类型: {collection_type}")
        
        # 获取团队信息
        team = RDTeam.objects.get(team_id=team_id, is_active=True, is_test_data=False)
        
        # 计算时间范围
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        results = {
            'team_id': team_id,
            'collection_type': collection_type,
            'period': f"{start_date} to {end_date}",
            'started_at': datetime.now().isoformat(),
            'jira_result': None,
            'git_result': None,
            'success': False,
            'error': None
        }
        
        # 收集JIRA数据
        if collection_type in ['jira', 'all']:
            logger.info(f"收集团队 {team_id} 的JIRA数据")
            jira_service = RDJiraDataService(user_email=user_email)
            jira_result = await jira_service.collect_team_jira_metrics(team, start_date, end_date)
            results['jira_result'] = jira_result
            
            if jira_result['success']:
                # 保存JIRA指标数据
                await jira_service.save_team_metrics(team, jira_result['data'], end_date)
                logger.info(f"团队 {team_id} JIRA数据收集完成")
            else:
                logger.error(f"团队 {team_id} JIRA数据收集失败: {jira_result['error']}")
        
        # 收集Git数据
        if collection_type in ['git', 'all']:
            logger.info(f"收集团队 {team_id} 的Git数据")
            git_service = RDGitDataService()
            git_result = await git_service.collect_team_git_metrics(team, start_date, end_date)
            results['git_result'] = git_result
            
            if git_result['success']:
                logger.info(f"团队 {team_id} Git数据收集完成")
            else:
                logger.error(f"团队 {team_id} Git数据收集失败: {git_result['error']}")
        
        # 创建快照
        if collection_type == 'all':
            logger.info(f"创建团队 {team_id} 的指标快照")
            snapshot_success = await RDMetricsAggregationService.create_team_snapshot(
                team, 'daily', start_date, end_date
            )
            results['snapshot_created'] = snapshot_success
        
        results['success'] = True
        results['completed_at'] = datetime.now().isoformat()
        
        logger.info(f"团队 {team_id} 指标数据收集任务完成")
        return results
        
    except Exception as e:
        error_msg = f"收集团队 {team_id} 指标数据失败: {str(e)}"
        logger.error(error_msg)
        results['success'] = False
        results['error'] = error_msg
        results['completed_at'] = datetime.now().isoformat()
        return results


def collect_all_teams_metrics_task(collection_type: str = 'all', days: int = 30) -> Dict:
    """
    收集所有团队的指标数据任务
    
    Args:
        collection_type: 收集类型 ('jira', 'git', 'all')
        days: 收集天数
    
    Returns:
        任务执行结果
    """
    try:
        logger.info(f"开始收集所有团队的指标数据，类型: {collection_type}")
        
        # 获取所有活跃团队
        teams = RDTeam.objects.filter(is_active=True, is_test_data=False)
        
        results = {
            'collection_type': collection_type,
            'total_teams': teams.count(),
            'started_at': datetime.now().isoformat(),
            'team_results': [],
            'success_count': 0,
            'error_count': 0,
            'success': False,
            'error': None
        }
        
        # 逐个收集团队数据
        for team in teams:
            try:
                team_result = await collect_team_metrics_task(
                    team.team_id, collection_type, days
                )
                results['team_results'].append(team_result)
                
                if team_result['success']:
                    results['success_count'] += 1
                else:
                    results['error_count'] += 1
                    
            except Exception as e:
                logger.error(f"收集团队 {team.team_id} 数据时发生异常: {str(e)}")
                results['error_count'] += 1
                results['team_results'].append({
                    'team_id': team.team_id,
                    'success': False,
                    'error': str(e)
                })
        
        results['success'] = results['error_count'] == 0
        results['completed_at'] = datetime.now().isoformat()
        
        logger.info(f"所有团队指标数据收集完成，成功: {results['success_count']}, 失败: {results['error_count']}")
        return results
        
    except Exception as e:
        error_msg = f"收集所有团队指标数据失败: {str(e)}"
        logger.error(error_msg)
        return {
            'success': False,
            'error': error_msg,
            'completed_at': datetime.now().isoformat()
        }


def create_weekly_snapshots_task() -> Dict:
    """
    创建周度快照任务
    """
    try:
        logger.info("开始创建周度快照")
        
        # 计算上周的时间范围
        today = datetime.now().date()
        last_monday = today - timedelta(days=today.weekday() + 7)
        last_sunday = last_monday + timedelta(days=6)
        
        teams = RDTeam.objects.filter(is_active=True, is_test_data=False)
        
        results = {
            'period': f"{last_monday} to {last_sunday}",
            'total_teams': teams.count(),
            'started_at': datetime.now().isoformat(),
            'success_count': 0,
            'error_count': 0,
            'team_results': []
        }
        
        for team in teams:
            try:
                success = await RDMetricsAggregationService.create_team_snapshot(
                    team, 'weekly', last_monday, last_sunday
                )
                
                results['team_results'].append({
                    'team_id': team.team_id,
                    'success': success
                })
                
                if success:
                    results['success_count'] += 1
                else:
                    results['error_count'] += 1
                    
            except Exception as e:
                logger.error(f"创建团队 {team.team_id} 周度快照失败: {str(e)}")
                results['error_count'] += 1
                results['team_results'].append({
                    'team_id': team.team_id,
                    'success': False,
                    'error': str(e)
                })
        
        results['success'] = results['error_count'] == 0
        results['completed_at'] = datetime.now().isoformat()
        
        logger.info(f"周度快照创建完成，成功: {results['success_count']}, 失败: {results['error_count']}")
        return results
        
    except Exception as e:
        error_msg = f"创建周度快照失败: {str(e)}"
        logger.error(error_msg)
        return {
            'success': False,
            'error': error_msg,
            'completed_at': datetime.now().isoformat()
        }


def create_monthly_snapshots_task() -> Dict:
    """
    创建月度快照任务
    """
    try:
        logger.info("开始创建月度快照")
        
        # 计算上个月的时间范围
        today = datetime.now().date()
        first_day_this_month = today.replace(day=1)
        last_day_last_month = first_day_this_month - timedelta(days=1)
        first_day_last_month = last_day_last_month.replace(day=1)
        
        teams = RDTeam.objects.filter(is_active=True, is_test_data=False)
        
        results = {
            'period': f"{first_day_last_month} to {last_day_last_month}",
            'total_teams': teams.count(),
            'started_at': datetime.now().isoformat(),
            'success_count': 0,
            'error_count': 0,
            'team_results': []
        }
        
        for team in teams:
            try:
                success = await RDMetricsAggregationService.create_team_snapshot(
                    team, 'monthly', first_day_last_month, last_day_last_month
                )
                
                results['team_results'].append({
                    'team_id': team.team_id,
                    'success': success
                })
                
                if success:
                    results['success_count'] += 1
                else:
                    results['error_count'] += 1
                    
            except Exception as e:
                logger.error(f"创建团队 {team.team_id} 月度快照失败: {str(e)}")
                results['error_count'] += 1
                results['team_results'].append({
                    'team_id': team.team_id,
                    'success': False,
                    'error': str(e)
                })
        
        results['success'] = results['error_count'] == 0
        results['completed_at'] = datetime.now().isoformat()
        
        logger.info(f"月度快照创建完成，成功: {results['success_count']}, 失败: {results['error_count']}")
        return results
        
    except Exception as e:
        error_msg = f"创建月度快照失败: {str(e)}"
        logger.error(error_msg)
        return {
            'success': False,
            'error': error_msg,
            'completed_at': datetime.now().isoformat()
        }


def cleanup_old_data_task(days_to_keep: int = 730) -> Dict:
    """
    清理旧数据任务
    
    Args:
        days_to_keep: 保留天数，默认2年
    """
    try:
        logger.info(f"开始清理 {days_to_keep} 天前的数据")
        
        cutoff_date = datetime.now().date() - timedelta(days=days_to_keep)
        
        # 清理详细指标数据
        from app01.models import RDJiraMetrics, RDGitMetrics
        
        jira_deleted = RDJiraMetrics.objects.filter(date__lt=cutoff_date).delete()
        git_deleted = RDGitMetrics.objects.filter(date__lt=cutoff_date).delete()
        
        results = {
            'cutoff_date': cutoff_date.isoformat(),
            'jira_records_deleted': jira_deleted[0] if jira_deleted[0] else 0,
            'git_records_deleted': git_deleted[0] if git_deleted[0] else 0,
            'completed_at': datetime.now().isoformat(),
            'success': True
        }
        
        logger.info(f"数据清理完成，删除JIRA记录: {results['jira_records_deleted']}, Git记录: {results['git_records_deleted']}")
        return results
        
    except Exception as e:
        error_msg = f"清理旧数据失败: {str(e)}"
        logger.error(error_msg)
        return {
            'success': False,
            'error': error_msg,
            'completed_at': datetime.now().isoformat()
        }


# 如果使用Celery，可以添加以下装饰器
# from celery import shared_task
# 
# @shared_task
# def collect_team_metrics_celery_task(team_id: str, collection_type: str = 'all', days: int = 30):
#     return collect_team_metrics_task(team_id, collection_type, days)
# 
# @shared_task
# def collect_all_teams_metrics_celery_task(collection_type: str = 'all', days: int = 30):
#     return collect_all_teams_metrics_task(collection_type, days)
# 
# @shared_task
# def create_weekly_snapshots_celery_task():
#     return create_weekly_snapshots_task()
# 
# @shared_task
# def create_monthly_snapshots_celery_task():
#     return create_monthly_snapshots_task()
# 
# @shared_task
# def cleanup_old_data_celery_task(days_to_keep: int = 730):
#     return cleanup_old_data_task(days_to_keep)
