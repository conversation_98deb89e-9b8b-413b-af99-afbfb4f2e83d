"""
R&D Metrics Views
Team and Personnel Management Views
"""

import json
import logging
from datetime import datetime, timedelta
from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone

from app01.models import (
    RDTeam, RDTeamMember, RDMetricsPermission,
    RDMetricsSnapshot, RDJiraMetrics, RDGitMetrics
)
from app01.rd_metrics.metrics_modules import MetricsRegistry
from app01.rd_metrics.utils import (
    DataValidator, DateHelper, ResponseHelper,
    PermissionHelper, MetricsFormatter
)

logger = logging.getLogger(__name__)


def get_user_email_from_request(request):
    """从请求中获取用户邮箱"""
    # 这里可以根据实际的认证系统来获取用户邮箱
    # 暂时从请求参数或session中获取
    return request.GET.get('user_email') or request.session.get('user_email', '<EMAIL>')


def check_rd_metrics_permission(user_email, required_level='viewer'):
    """检查R&D指标权限"""
    try:
        permission = RDMetricsPermission.objects.get(
            user_email=user_email,
            is_active=True
        )
        
        # 检查是否过期
        if permission.expires_at and timezone.now() > permission.expires_at:
            return False, "权限已过期"
        
        # 权限级别检查
        level_hierarchy = {
            'viewer': 1,
            'team_lead': 2,
            'manager': 3,
            'admin': 4
        }
        
        user_level = level_hierarchy.get(permission.permission_level, 0)
        required_level_num = level_hierarchy.get(required_level, 1)
        
        if user_level >= required_level_num:
            return True, permission
        else:
            return False, f"需要{required_level}级别权限"
            
    except RDMetricsPermission.DoesNotExist:
        return False, "无访问权限"


# ================================
# Team Management Views
# ================================

def team_management(request):
    """团队管理主页"""
    user_email = get_user_email_from_request(request)
    has_permission, permission_or_error = check_rd_metrics_permission(user_email, 'team_lead')
    
    if not has_permission:
        return render(request, 'rd_metrics/error.html', {
            'error_message': permission_or_error
        })
    
    # 获取团队列表
    teams = RDTeam.objects.filter(is_active=True, is_test_data=False).order_by('department', 'team_name')
    
    # 分页
    paginator = Paginator(teams, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'user_email': user_email,
        'permission': permission_or_error,
        'can_configure': permission_or_error.can_configure_teams,
    }
    
    return render(request, 'rd_metrics/team_management.html', context)


@csrf_exempt
@require_http_methods(["GET", "POST"])
def team_detail(request, team_id=None):
    """团队详情和编辑"""
    user_email = get_user_email_from_request(request)
    has_permission, permission_or_error = check_rd_metrics_permission(user_email, 'team_lead')
    
    if not has_permission:
        return JsonResponse({'success': False, 'error': permission_or_error})
    
    if request.method == 'GET':
        if team_id:
            team = get_object_or_404(RDTeam, team_id=team_id, is_test_data=False)
            members = team.members.filter(is_active=True, is_test_data=False)
            
            team_data = {
                'team_id': team.team_id,
                'team_name': team.team_name,
                'department': team.department,
                'team_leader_email': team.team_leader_email,
                'parent_team_id': team.parent_team_id,
                'jira_projects': team.jira_projects,
                'git_repositories': team.git_repositories,
                'members': [
                    {
                        'id': member.id,
                        'member_email': member.member_email,
                        'member_name': member.member_name,
                        'role': member.role,
                        'join_date': member.join_date.isoformat(),
                        'jira_account_id': member.jira_account_id,
                        'git_username': member.git_username,
                    }
                    for member in members
                ]
            }
            
            return JsonResponse({'success': True, 'data': team_data})
        else:
            # 返回新建团队的表单数据
            return JsonResponse({
                'success': True,
                'data': {
                    'jira_projects': ['SPCB', 'SPCT'],  # 默认项目
                    'available_repositories': []  # 可以从services_id.json加载
                }
            })
    
    elif request.method == 'POST':
        if not permission_or_error.can_configure_teams:
            return JsonResponse({'success': False, 'error': '无配置权限'})
        
        try:
            data = json.loads(request.body)
            
            if team_id:
                # 更新现有团队
                team = get_object_or_404(RDTeam, team_id=team_id, is_test_data=False)
            else:
                # 创建新团队
                team = RDTeam()
                team.team_id = data.get('team_id')
            
            team.team_name = data.get('team_name')
            team.department = data.get('department')
            team.team_leader_email = data.get('team_leader_email')
            team.parent_team_id = data.get('parent_team_id')
            team.jira_projects = data.get('jira_projects', [])
            team.git_repositories = data.get('git_repositories', [])
            
            team.save()
            
            return JsonResponse({
                'success': True,
                'message': '团队保存成功',
                'team_id': team.team_id
            })
            
        except Exception as e:
            logger.error(f"保存团队失败: {str(e)}")
            return JsonResponse({'success': False, 'error': f'保存失败: {str(e)}'})


@csrf_exempt
@require_http_methods(["POST"])
def team_member_manage(request, team_id):
    """团队成员管理"""
    user_email = get_user_email_from_request(request)
    has_permission, permission_or_error = check_rd_metrics_permission(user_email, 'team_lead')
    
    if not has_permission or not permission_or_error.can_configure_teams:
        return JsonResponse({'success': False, 'error': '无权限'})
    
    try:
        team = get_object_or_404(RDTeam, team_id=team_id, is_test_data=False)
        data = json.loads(request.body)
        action = data.get('action')
        
        if action == 'add':
            member = RDTeamMember(
                team=team,
                member_email=data.get('member_email'),
                member_name=data.get('member_name'),
                role=data.get('role'),
                join_date=datetime.strptime(data.get('join_date'), '%Y-%m-%d').date(),
                jira_account_id=data.get('jira_account_id'),
                git_username=data.get('git_username'),
            )
            member.save()
            message = '成员添加成功'
            
        elif action == 'update':
            member = get_object_or_404(RDTeamMember, id=data.get('member_id'), team=team)
            member.member_name = data.get('member_name')
            member.role = data.get('role')
            member.jira_account_id = data.get('jira_account_id')
            member.git_username = data.get('git_username')
            member.save()
            message = '成员更新成功'
            
        elif action == 'remove':
            member = get_object_or_404(RDTeamMember, id=data.get('member_id'), team=team)
            member.is_active = False
            member.save()
            message = '成员移除成功'
            
        else:
            return JsonResponse({'success': False, 'error': '无效操作'})
        
        return JsonResponse({'success': True, 'message': message})
        
    except Exception as e:
        logger.error(f"团队成员管理失败: {str(e)}")
        return JsonResponse({'success': False, 'error': f'操作失败: {str(e)}'})


# ================================
# Permission Management Views
# ================================

def permission_management(request):
    """权限管理页面"""
    user_email = get_user_email_from_request(request)
    has_permission, permission_or_error = check_rd_metrics_permission(user_email, 'admin')
    
    if not has_permission:
        return render(request, 'rd_metrics/error.html', {
            'error_message': permission_or_error
        })
    
    # 获取权限列表
    permissions = RDMetricsPermission.objects.filter(
        is_active=True, 
        is_test_data=False
    ).order_by('permission_level', 'user_email')
    
    # 分页
    paginator = Paginator(permissions, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'user_email': user_email,
        'permission_levels': RDMetricsPermission.PERMISSION_LEVELS,
    }
    
    return render(request, 'rd_metrics/permission_management.html', context)


@csrf_exempt
@require_http_methods(["POST"])
def grant_permission(request):
    """授予权限"""
    user_email = get_user_email_from_request(request)
    has_permission, permission_or_error = check_rd_metrics_permission(user_email, 'admin')
    
    if not has_permission:
        return JsonResponse({'success': False, 'error': permission_or_error})
    
    try:
        data = json.loads(request.body)
        
        permission, created = RDMetricsPermission.objects.get_or_create(
            user_email=data.get('user_email'),
            defaults={
                'permission_level': data.get('permission_level'),
                'granted_by': user_email,
                'can_view_individual_metrics': data.get('can_view_individual_metrics', True),
                'can_view_team_comparison': data.get('can_view_team_comparison', False),
                'can_export_data': data.get('can_export_data', False),
                'can_configure_teams': data.get('can_configure_teams', False),
            }
        )
        
        if not created:
            # 更新现有权限
            permission.permission_level = data.get('permission_level')
            permission.can_view_individual_metrics = data.get('can_view_individual_metrics', True)
            permission.can_view_team_comparison = data.get('can_view_team_comparison', False)
            permission.can_export_data = data.get('can_export_data', False)
            permission.can_configure_teams = data.get('can_configure_teams', False)
            permission.granted_by = user_email
            permission.is_active = True
            permission.save()
        
        # 设置团队访问权限
        if data.get('accessible_team_ids'):
            teams = RDTeam.objects.filter(id__in=data.get('accessible_team_ids'))
            permission.accessible_teams.set(teams)
        
        return JsonResponse({
            'success': True,
            'message': '权限设置成功',
            'created': created
        })
        
    except Exception as e:
        logger.error(f"权限设置失败: {str(e)}")
        return JsonResponse({'success': False, 'error': f'设置失败: {str(e)}'})


# ================================
# API Views for Data
# ================================

def api_teams_list(request):
    """获取团队列表API"""
    user_email = get_user_email_from_request(request)
    has_permission, permission_or_error = check_rd_metrics_permission(user_email, 'viewer')

    if not has_permission:
        return JsonResponse({'success': False, 'error': permission_or_error})

    teams = RDTeam.objects.filter(is_active=True, is_test_data=False)

    # 如果不是管理员，只返回有权限的团队
    if permission_or_error.permission_level not in ['admin', 'manager']:
        accessible_teams = permission_or_error.accessible_teams.all()
        teams = teams.filter(id__in=[t.id for t in accessible_teams])

    teams_data = [
        {
            'id': team.id,
            'team_id': team.team_id,
            'team_name': team.team_name,
            'department': team.department,
            'team_leader_email': team.team_leader_email,
            'member_count': team.members.filter(is_active=True).count(),
            'jira_projects': team.jira_projects,
        }
        for team in teams
    ]

    return JsonResponse({'success': True, 'data': teams_data})


# ================================
# Dashboard Views
# ================================

def rd_metrics_dashboard(request):
    """R&D效率指标仪表板主页"""
    user_email = get_user_email_from_request(request)
    has_permission, permission_or_error = check_rd_metrics_permission(user_email, 'viewer')

    if not has_permission:
        return render(request, 'rd_metrics/error.html', {
            'error_message': permission_or_error
        })

    # 获取用户可访问的团队
    accessible_teams = []
    if permission_or_error.permission_level in ['admin', 'manager']:
        accessible_teams = RDTeam.objects.filter(is_active=True, is_test_data=False)
    else:
        accessible_teams = permission_or_error.accessible_teams.filter(is_active=True, is_test_data=False)

    context = {
        'user_email': user_email,
        'permission': permission_or_error,
        'accessible_teams': accessible_teams,
        'can_view_comparison': permission_or_error.can_view_team_comparison,
        'can_export_data': permission_or_error.can_export_data,
    }

    return render(request, 'rd_metrics/dashboard.html', context)


@csrf_exempt
@require_http_methods(["GET"])
def api_team_metrics(request, team_id):
    """获取团队指标数据API"""
    user_email = get_user_email_from_request(request)
    has_permission, permission_or_error = check_rd_metrics_permission(user_email, 'viewer')

    if not has_permission:
        return JsonResponse({'success': False, 'error': permission_or_error})

    try:
        team = get_object_or_404(RDTeam, team_id=team_id, is_test_data=False)

        # 检查团队访问权限
        if not permission_or_error.has_team_access(team):
            return JsonResponse({'success': False, 'error': '无权限访问此团队数据'})

        # 获取时间范围参数
        days = int(request.GET.get('days', 30))
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)

        # 使用指标计算引擎
        from .metrics_engine import RDMetricsCalculator
        calculator = RDMetricsCalculator(team)
        metrics = calculator.calculate_comprehensive_metrics(start_date, end_date)

        return JsonResponse({
            'success': True,
            'data': metrics,
            'team_info': {
                'team_id': team.team_id,
                'team_name': team.team_name,
                'department': team.department,
                'member_count': team.members.filter(is_active=True).count(),
            }
        })

    except Exception as e:
        logger.error(f"获取团队指标失败: {str(e)}")
        return JsonResponse({'success': False, 'error': f'获取数据失败: {str(e)}'})


@csrf_exempt
@require_http_methods(["GET"])
def api_teams_comparison(request):
    """团队对比API"""
    user_email = get_user_email_from_request(request)
    has_permission, permission_or_error = check_rd_metrics_permission(user_email, 'viewer')

    if not has_permission or not permission_or_error.can_view_team_comparison:
        return JsonResponse({'success': False, 'error': '无权限查看团队对比'})

    try:
        # 获取要对比的团队ID列表
        team_ids = request.GET.get('team_ids', '').split(',')
        team_ids = [tid.strip() for tid in team_ids if tid.strip()]

        if not team_ids:
            return JsonResponse({'success': False, 'error': '请选择要对比的团队'})

        # 获取团队对象
        teams = RDTeam.objects.filter(team_id__in=team_ids, is_active=True, is_test_data=False)

        # 检查权限
        accessible_teams = []
        for team in teams:
            if permission_or_error.has_team_access(team):
                accessible_teams.append(team)

        if not accessible_teams:
            return JsonResponse({'success': False, 'error': '无权限访问选中的团队'})

        # 获取时间范围
        days = int(request.GET.get('days', 30))
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)

        # 使用对比器
        from .metrics_engine import RDMetricsComparator
        comparison_result = RDMetricsComparator.compare_teams(accessible_teams, start_date, end_date)

        return JsonResponse({
            'success': True,
            'data': comparison_result
        })

    except Exception as e:
        logger.error(f"团队对比失败: {str(e)}")
        return JsonResponse({'success': False, 'error': f'对比失败: {str(e)}'})


@csrf_exempt
@require_http_methods(["GET"])
def api_metrics_export(request):
    """指标数据导出API"""
    user_email = get_user_email_from_request(request)
    has_permission, permission_or_error = check_rd_metrics_permission(user_email, 'viewer')

    if not has_permission or not permission_or_error.can_export_data:
        return JsonResponse({'success': False, 'error': '无权限导出数据'})

    try:
        # 获取导出参数
        export_format = request.GET.get('format', 'json')  # json, csv, excel
        team_ids = request.GET.get('team_ids', '').split(',')
        team_ids = [tid.strip() for tid in team_ids if tid.strip()]

        days = int(request.GET.get('days', 30))
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)

        # 获取数据
        export_data = []
        teams = RDTeam.objects.filter(team_id__in=team_ids, is_active=True, is_test_data=False)

        for team in teams:
            if permission_or_error.has_team_access(team):
                from .metrics_engine import RDMetricsCalculator
                calculator = RDMetricsCalculator(team)
                metrics = calculator.calculate_comprehensive_metrics(start_date, end_date)

                export_data.append({
                    'team_id': team.team_id,
                    'team_name': team.team_name,
                    'department': team.department,
                    'metrics': metrics
                })

        if export_format == 'json':
            response = JsonResponse({
                'success': True,
                'data': export_data,
                'export_info': {
                    'format': export_format,
                    'period': f"{start_date} to {end_date}",
                    'exported_at': timezone.now().isoformat()
                }
            })
            response['Content-Disposition'] = f'attachment; filename="rd_metrics_{start_date}_{end_date}.json"'
            return response

        # TODO: 实现CSV和Excel导出
        return JsonResponse({'success': False, 'error': f'暂不支持{export_format}格式导出'})

    except Exception as e:
        logger.error(f"数据导出失败: {str(e)}")
        return JsonResponse({'success': False, 'error': f'导出失败: {str(e)}'})


# ================================
# Data Collection Trigger Views
# ================================

@csrf_exempt
@require_http_methods(["POST"])
def trigger_data_collection(request):
    """触发数据收集"""
    user_email = get_user_email_from_request(request)
    has_permission, permission_or_error = check_rd_metrics_permission(user_email, 'admin')

    if not has_permission:
        return JsonResponse({'success': False, 'error': permission_or_error})

    try:
        data = json.loads(request.body)
        team_ids = data.get('team_ids', [])
        collection_type = data.get('type', 'jira')  # jira, git, all

        # 异步触发数据收集任务
        from .tasks import collect_team_metrics_task

        results = []
        for team_id in team_ids:
            try:
                team = RDTeam.objects.get(team_id=team_id, is_active=True, is_test_data=False)

                # 这里应该使用Celery等异步任务队列
                # 暂时同步执行
                if collection_type in ['jira', 'all']:
                    # 触发JIRA数据收集
                    pass

                if collection_type in ['git', 'all']:
                    # 触发Git数据收集
                    pass

                results.append({
                    'team_id': team_id,
                    'status': 'triggered',
                    'message': f'{collection_type}数据收集已触发'
                })

            except RDTeam.DoesNotExist:
                results.append({
                    'team_id': team_id,
                    'status': 'error',
                    'message': '团队不存在'
                })

        return JsonResponse({
            'success': True,
            'results': results,
            'message': '数据收集任务已触发'
        })

    except Exception as e:
        logger.error(f"触发数据收集失败: {str(e)}")
        return JsonResponse({'success': False, 'error': f'触发失败: {str(e)}'})


def metrics_help(request):
    """指标说明页面"""
    return render(request, 'rd_metrics/metrics_help.html')
