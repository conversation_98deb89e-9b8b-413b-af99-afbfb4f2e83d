import time
import datetime
from collections import defaultdict
import sqlite3
import pymysql
import requests
import json
from icecream import ic
import os
from pathlib import Path

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent

LIST_BY_SERVICES = "http://space.shopee.io/apis/pipeline/openapi/pipeline/list_by_service"

pingGroupA = [
    'shopee-chatbot-admin',
    'shopee-chatbot-adminasynctask',
    'shopee-chatbot-botapi',
    'shopee-chatbot-dm',
    'shopee-chatbotcommon-rulebaseservice',
    'shopee-chatbot-context',
    'shopee-chatbot-featurecenter',
    'shopee-chatbot-intentclarification',
    'shopee-chatbot-liveagentconnection',
    'shopee-chatbot-nlu',
    'shopee-chatbot-ordercard',
    'shopee-chatbot-pilotapi',
    'shopee-chatbot-auditlog',
    'shopee-chatbot-asynctask',
    'shopee-chatbot-agentcontrol',
    'shopee-chatbot-adminservice',
    'shopee-chatbot-adminconfigservice',
    'shopee-chatbotcommon-adminasynctask',
    'shopee-chatbotcommon-adminconfigservice',
    'shopee-chatbotcommon-adminservice',
    'shopee-chatbotcommon-agentcontrol',
    'shopee-chatbotcommon-asynctask',
    'shopee-chatbotcommon-botapi',
    'shopee-chatbotcommon-context',
    'shopee-chatbotcommon-dm',
    'shopee-chatbotcommon-featurecenter',
    'shopee-chatbotcommon-nlu',
    'shopee-chatbotcommon-productrecommend',
    'shopee-chatbotcommon-rulebaseservice',
    'shopee-chatbotcommon-shopconsole',
    'shopee-chatbot-messageverification',
    'shopee-chatbot-messageasynctask',
    'shopee-chatbot-messageservice',
    'shopee-chatbotcommon-logic',
    'shopee-chatbotcommon-msgdetection'
]
pingGroupB = [
  'shopee-knowledgebase-admin',
  'shopee-knowledgebase-asynctask',
  'shopee-knowledgebase-labelclarification',
  'shopee-chatbotcommon-component',
  'shopee-chatbotcommon-modelgw',
  'shopee-chatbotcommon-kblabelclarification',
  'shopee-chatbotcommon-kbasynctask',
  'shopee-chatbot-intent',
  'shopee-chatbotcommon-intent',
  'shopee-chatbotcommon-kbapi',
  'shopee-chatbotcommon-kbadmin',
  'shopee-chatbotcommon-featureapiproxy',
  'shopee-chatbotcommon-experimentmanagement',
  'shopee-chatbotcommon-apasynctask',
  'shopee-chatbotcommon-apadmin',
  'shopee-knowledgebase-admin',
  'shopee-chatbotcommon-kbadmin',
  'shopee-knowledgebase-api',
  'shopee-chatbotcommon-kbapi',
  'shopee-knowledgebase-labelclarification',
  'shopee-chatbotcommon-kblabelclarification',
  'shopee-chatbot-featureapiproxy',
  'shopee-chatbot-modelgw',
  'shopee-chatbot-realtime',
  'shopee-chatbot-recallmanager',
  'shopee-chatbot-recallservice',
  'shopee-chatbot-recommendation',
  'shopee-chatbot-experimentmanagement'
]
featureGroup = [
  'shopee-chatbotcommon-tfvariateserving',
  'shopee-chatbotcommon-tfserving',
  'shopee-chatbotcommon-tfeditor',
  'shopee-chatbotcommon-tfapiproxy',
  'shopee-taskflow-taskflowsop',
  'shopee-chatbotcommon-tfserving',
  'shopee-chatbotcommon-tfapiproxy',
  'shopee-chatbot-api',
  'shopee-taskflow-editor',
  'shopee-taskflow-apiproxy',
  'shopee-taskflow-taskflowserving',
  'shopee-chatbot-autotraining',
  'shopee-taskflow-variateserving'
]


def add_services_info():
    detail_url = "http://luban.cs.test.shopee.io/chatbot-config-tool/space/get_chatbot_services"
    rep = requests.get(url=detail_url)
    rep_text = json.loads(rep.text)
    result = gettoken()
    tokens = "Bearer " + result["token"]
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    pipeline_list = []
    pipeline_list_a = {}
    pipeline_list_b = []
    pipeline_list_c = []
    pipeline_list_d = []
    for i in rep_text['data']:
        parameters = {
            "service_id": i["service_id"]
        }
        r = requests.post(url=LIST_BY_SERVICES, json=parameters, headers=headers)
        time.sleep(1)
        result_json = json.loads(r.text)
        # if result_json['data']['list']:
        #     if "chatbotxx" in result_json['data']['list'][0]['pipeline_name']:
        #         continue
        #     else:
        #         pipeline_list.append({
        #                 "pipeline_name": result_json['data']['list'][0]['pipeline_name'],
        #                 "service_id": i["service_id"],
        #                 "services_name": i["service_name"],
        #                 "services_link": f'https://space.shopee.io/console/cmdb/deployment/detail/{i["service_name"]}'
        #             })
        if result_json['data']['list']:
            if "chatbotxx" in result_json['data']['list'][0]['pipeline_name']:
                continue
            else:

                pipeline_list.append({
                    "pipeline_name": result_json['data']['list'][0]['pipeline_name'].replace("-live", ""),
                    "services_link": f'https://space.shopee.io/console/cmdb/deployment/detail/{i["service_name"]}'
                })
    unique_arr = list(set(featureGroup))
    for i in pipeline_list:
        if "static" in i["pipeline_name"]:
            pipeline_list_a[i["pipeline_name"]] = i["services_link"]

            #             continue
    # for i in unique_arr:
    #     for y in pipeline_list:
    #         if i == y["pipeline_name"]:
    #             pipeline_list_a[i] = y["services_link"]
    #             continue
    ic(pipeline_list_a)
    # conn = sqlite3.connect('/Users/<USER>/Documents/autodeploy/app01/service.db')
    # cursor = conn.cursor()
    # for pipeline in pipeline_list:
    #     pipeline_name = pipeline['pipeline_name']
    #     service_id = pipeline['service_id']
    #     services_name = pipeline['services_name']
    #     services_link = pipeline['services_link']
    #
    #     # Check if the pipeline already exists in the database
    #     cursor.execute("SELECT * FROM services_info WHERE pipeline_name=?", (pipeline_name,))
    #     existing_pipeline = cursor.fetchone()
    #
    #     if existing_pipeline:
    #         # If the pipeline already exists, update the corresponding data
    #         cursor.execute("UPDATE services_info SET service_id=?, services_name=?, services_link=? WHERE pipeline_name=?",
    #                        (service_id, services_name, services_link, pipeline_name))
    #     else:
    #         # If the pipeline doesn't exist, insert a new row
    #         cursor.execute(
    #             "INSERT INTO services_info (pipeline_name, service_id, services_name, services_link) VALUES (?, ?, ?, ?)",
    #             (pipeline_name, service_id, services_name, services_link))
    #
    # conn.commit()
    # conn.close()


def get_pod_info(service_name):
    # 连接 SQLite 数据库
    # ic(service_name)
    conn = sqlite3.connect('/Users/<USER>/Documents/autodeploy/app01/service.db')
    # 定义要查询的服务名称
    # service_name = 'service_1'
    pod_count = 0
    k8s_link = ""
    # 查询数据库表，获取对应服务名称的pod_count和k8s_link
    with conn:
        cursor = conn.execute('SELECT pod_count, k8s_link FROM service_info WHERE service_name=?', (service_name,))
        row = cursor.fetchone()
        if row is not None:
            pod_count = row[0]
            k8s_link = row[1]
            ic(f"Pod count for {service_name}: {pod_count}")
            ic(f"Kubernetes link for {service_name}: {k8s_link}")
        else:
            ic("Service not found in the database")

    # 关闭数据库连接
    conn.close()
    return pod_count, k8s_link


def get_pipeline_name():
    detail_url = "http://luban.cs.test.shopee.io/chatbot-config-tool/space/get_chatbot_services"
    try:
        rep = requests.get(url=detail_url, timeout=10)
        rep_text = json.loads(rep.text)
        
        # 检查返回的数据结构
        if not rep_text or 'data' not in rep_text or not rep_text['data']:
            print(f"API response is empty or invalid: {rep_text}")
            return {}
            
    except Exception as e:
        print(f"Failed to get services from luban API: {e}")
        return {}
    
    result = gettoken()
    if not result:
        print("Failed to get token")
        return {}
        
    tokens = "Bearer " + result["token"]
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    
    pipeline_list = {}
    skipped_services = []
    failed_services = []
    
    print(f"Found {len(rep_text['data'])} services from luban API")
    
    for i in rep_text['data']:
        parameters = {
            "service_id": i["service_id"]
        }
        ic(parameters)
        
        try:
            r = requests.post(url=LIST_BY_SERVICES, json=parameters, headers=headers, timeout=10)
            time.sleep(1)
            
            if r.status_code != 200:
                failed_services.append(f"service_id: {i['service_id']}, status: {r.status_code}")
                continue
                
            result_json = json.loads(r.text)
            
            if result_json.get('data', {}).get('list'):
                pipeline_name = result_json['data']['list'][0]['pipeline_name']
                
                # 注释掉原来的过滤逻辑，记录被跳过的服务
                # if "chatbotxx" in pipeline_name:
                #     skipped_services.append(f"pipeline: {pipeline_name} (contains chatbotxx)")
                #     continue
                
                pipeline_list[pipeline_name] = i["service_id"]
            else:
                failed_services.append(f"service_id: {i['service_id']}, no pipeline data")
                
        except Exception as e:
            failed_services.append(f"service_id: {i['service_id']}, error: {str(e)}")
            continue
    
    # 打印调试信息
    print(f"Successfully processed {len(pipeline_list)} services")
    print(f"Failed services: {len(failed_services)}")
    if failed_services:
        print("Failed services details:", failed_services[:5])  # 只打印前5个
    if skipped_services:
        print("Skipped services:", skipped_services[:5])  # 只打印前5个
        
    return pipeline_list


def write_dict_to_json_file(data, filename):
    with open(filename, 'w') as outfile:
        json.dump(data, outfile)


def insert_data(result):
    # 连接数据库
    service_name = result["pipeline_name"]
    last_deploy_date = result["end_time"]
    deploy_person = result["executor"]
    deploy_tag = result["TAG"]
    jump_link = result["space_link"]
    deploy_status = result["build_result"]
    conn = pymysql.connect(
        host='localhost',
        port=3306,
        user='root',
        password='shopee',
        db='chatbotcicd',
        cursorclass=pymysql.cursors.DictCursor  # 使用字典游标返回查询结果
    )

    # 插入或更新数据
    with conn.cursor() as cursor:
        sql = 'INSERT INTO `deployments` (`service_name`, `last_deploy_date`, `deploy_person`, `deploy_tag`, `jump_link`, `deploy_status`) VALUES (%s, %s, %s, %s, %s, %s) ON DUPLICATE KEY UPDATE `last_deploy_date`=VALUES(`last_deploy_date`), `deploy_person`=VALUES(`deploy_person`), `deploy_tag`=VALUES(`deploy_tag`), `jump_link`=VALUES(`jump_link`), `deploy_status`=VALUES(`deploy_status`)'
        values = (service_name, last_deploy_date, deploy_person, deploy_tag, jump_link, deploy_status)
        cursor.execute(sql, values)
        conn.commit()

    # 关闭连接
    conn.close()


def get_service(project_id):
    detail_url = "http://luban.cs.test.shopee.io/chatbot-config-tool/space/get_chatbot_services"
    rep = requests.get(url=detail_url)
    rep_text = json.loads(rep.text)
    data = rep_text['data']
    #解决NoneType error
    if data is not None:
        rep_dict = {i['service_id']: i for i in data}
    service_name = rep_dict.get(project_id, {}).get('service_name')
    return service_name


def unixtime(ms_timestamp):
    # 将毫秒级 Unix 时间戳转换为 datetime 对象
    dt_object = datetime.datetime.fromtimestamp(ms_timestamp / 1000.0)

    # 将 datetime 对象转换为北京时间字符串
    beijing_time_str = dt_object.astimezone(datetime.timezone(datetime.timedelta(hours=8))).strftime(
        '%Y-%m-%d %H:%M:%S')

    # 输出结果
    return beijing_time_str


def get_project_id(url):
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    url = url.replace("https://git.garena.com/", "")
    url = url.replace(".git", "")
    url = url.replace("/", "%2f")
    real_url = BASE_URL + '{url}/merge_requests?private_token=kBV8bRxCbEqk2G8eyFyz'. \
        format(url=url)
    get_feedback_isue = requests.get(real_url)
    text_all = json.loads(get_feedback_isue.text)
    if text_all:
        if type(text_all) == dict and text_all['message'] == '404 Project Not Found':
            return None
        else:
            return text_all[0]['project_id']


def gettoken():
    URL = "https://space.shopee.io/apis/uic/v2/auth/basic_login"
    headers = {
        'content-type': "application/json",
        'Authorization': 'Basic ********************************************************'
    }

    try:
        r = requests.post(url=URL, headers=headers, timeout=5)
        results = json.loads(r.text)
        # print(results)
        return results
    except requests.exceptions.RequestException as e:
        print(e)


def ms_to_min_sec(milliseconds):
    seconds = milliseconds // 1000
    minutes = seconds // 60
    seconds %= 60
    return f"{minutes}分钟{seconds}秒"


def get_k8s_pod(services):
    token = gettoken()
    tokens = "Bearer " + token["token"]
    headers = {
        "content-type": "application/json",
        "Authorization": tokens,
        "X-App-Name": "CHATBOT-AR"
    }
    url = f"https://kube-open-api-live.devops.i.sz.shopee.io/ecpapi/v2/services/{services}/bulk_get_sdus?filterBy=env==live"
    r = requests.get(url=url, headers=headers)
    if r.text:
        result_rep = json.loads(r.text)
        count_single = 0
        print(result_rep)
        for i in result_rep["items"]:
            if i["env"] == "live":
                if i["summary"]:
                    count_single += i["summary"]["releaseInstances"]
        return count_single
    else:
        return False


def cluster_by_date(data):
    # 提取所有日期并转化为日期对象
    all_dates = [datetime.strptime(d['start_time'], '%Y-%m-%d %H:%M:%S').date() for d in data]

    # 利用日期对象进行聚类
    clustered_data = {}
    for d, date in zip(data, all_dates):
        if date not in clustered_data:
            clustered_data[date] = []
        clustered_data[date].append(d)

        # 对每一组相同日期的数据赋予一个相同的索引，并在不同日期的数据组之间递增索引值
    result = []
    index = 0
    for date, group in clustered_data.items():
        for d in group:
            d['index'] = str(index)

        result.extend(group)
        index += 1

    return result

def app_access_token():
    """获取Seatalk访问令牌，使用统一的缓存和重试机制"""
    from .config import get_seatalk_access_token
    token = get_seatalk_access_token()
    if not token:
        raise Exception("无法获取Seatalk访问令牌")
    return token
def test_for_seatalk_bot(text, mentionals, group_id):
    if not text:
        return
    url = "https://openapi.seatalk.io/messaging/v2/group_chat"
    result = app_access_token()
    tokens = "Bearer " + result
    mentional_text = ""
    if not mentionals:
        param = {
            "group_id": group_id,
            "message": {
                "tag": "text",
                "text": {
                    "content": text,
                },
            }
        }

    else:
        for mentional in mentionals:
            if mentional == "ALL":
                mentional_text = "<mention-tag target=\"seatalk://user?id=0\"/>"
                break
            if "@shopee.com" not in mentional:
                mentional_text = f"<mention-tag target=\"seatalk://user?id={mentional}\"/>" + mentional_text
            else:
                mentional_text = f"<mention-tag target=\"seatalk://user?email={mentional}\"/>" + mentional_text
        text_all = f"{mentional_text}\n{text}"
        param = {
            "group_id": group_id,
            "message": {
                "tag": "text",
                "text": {
                    "content": text_all,
                },
            }
        }
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    r = requests.post(url=url, json=param, headers=headers)
    ic(r.text)

def get_pipeinle_info(pipeline_name, service_id):
    history_url = "http://space.shopee.io/apis/pipeline/openapi/pipeline/history/list"
    result = gettoken()
    if not result:
        print(f"Failed to get token for pipeline: {pipeline_name}")
        return False
        
    tokens = "Bearer " + result["token"]
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    parameters = {
        "pipeline_name": pipeline_name,
        "page": 1,
        "page_size": 3
    }
    
    try:
        r = requests.post(url=history_url, json=parameters, headers=headers, timeout=10)
        if r.status_code != 200:
            print(f"API request failed for {pipeline_name}: status {r.status_code}")
            return False
            
        if not r.text:
            print(f"Empty response for pipeline: {pipeline_name}")
            return False
            
        result_rep = json.loads(r.text)
        
        # 检查响应结构
        if not result_rep or 'data' not in result_rep or not result_rep['data'] or 'list' not in result_rep['data']:
            print(f"Invalid response structure for {pipeline_name}: {result_rep}")
            return False
            
    except Exception as e:
        print(f"Exception getting pipeline info for {pipeline_name}: {e}")
        return False

    build_info = {}
    build_info_list = []
    list_result = result_rep['data']['list']
    
    if list_result:
        for i in list_result:
            try:
                build_result = i.get('build_status')
                end_time = i.get('end_time')
                parameter = i.get('parameter', '{}')
                err_msg = ""
                
                # 安全解析 JSON 参数
                try:
                    param_dict = json.loads(parameter)
                    TAG = param_dict.get('FROM_BRANCH', 'unknown')
                    CID = param_dict.get('DEPLOY_CIDS', 'unknown')
                    build_only = param_dict.get('BUILD_ONLY', 'false')
                    
                    # 检查是否有 CANARY 字段
                    if "CANARY" in param_dict:
                        canary = param_dict["CANARY"]
                    else:
                        canary = "false"
                except json.JSONDecodeError:
                    print(f"Failed to parse parameter JSON for {pipeline_name}")
                    TAG = 'unknown'
                    CID = 'unknown'
                    canary = 'false'
                    build_only = 'false'
                
                executor = i.get('executor', 'unknown').replace("@shopee.com", "")
                pipeline_name_item = i.get('pipeline_name', pipeline_name)
                callback_id = i.get('callback_id', '')
                
                if build_result != "SUCCESS":
                    try:
                        err_msg = get_error_msg(callback_id)
                        if err_msg == "default":
                            err_msg = "未定位到具体错误"
                    except:
                        err_msg = "获取错误信息失败"
                
                duration = ms_to_min_sec(i.get('duration', 0))
                
                # 跳过预编译任务
                if build_only == "true":
                    continue

                build_info["build_result"] = build_result
                build_info["TAG"] = TAG
                build_info["executor"] = executor
                build_info["pipeline_name"] = pipeline_name_item
                build_info["end_time"] = unixtime(end_time)
                
                pause_duration = i.get('pause_duration', 0)
                start_time = unixtime(end_time - pause_duration)
                build_info["start_time"] = start_time
                build_info["CID"] = CID
                build_info["duration"] = duration
                build_info["build_type"] = "灰度发布" if canary == "true" else "全量发布"
                
                service_name = get_service(service_id)
                space_link = f"https://space.shopee.io/console/cmdb/deployment/pipeline/{service_name}/{callback_id}"
                build_info["space_link"] = space_link
                build_info["err_msg"] = err_msg
                
                if i.get('duration', 0) > 3600000 and build_result == 'PAUSED':
                    text = f"下面的服务已经灰度超过1小时，请关注并及时全量或者回滚：\n{pipeline_name}\nSPACE地址：{space_link}"
                    test_for_seatalk_bot(text, [i.get('executor')], "NDY0MzgxNDI4OTEw")
                
                build_info_list.append(build_info.copy())
                
            except Exception as e:
                print(f"Error processing build info for {pipeline_name}: {e}")
                continue

    return build_info_list if build_info_list else False


def sort_dict_list(input_list):
    temp_dict = defaultdict(list)
    for element in input_list:
        for key, value_list in element.items():
            sorted_list = sorted(value_list, key=lambda x: x.get('end_time', ''))
            latest_time = sorted_list[-1].get('end_time', '')
            temp_dict[latest_time].append({key: sorted_list})
    result_list = []
    for time, value_dict in sorted(temp_dict.items(), reverse=True):
        result_list.extend(value_dict)
    return result_list


class Params():
    def __init__(self, json_path):
        with open(json_path) as f:
            params = json.load(f)  # 将json格式数据转换为字典
            self.__dict__.update(params)

    def save(self, json_path):
        with open(json_path, 'w') as f:
            json.dump(self.__dict__, f, indent=4)

    def update(self, json_path):
        with open(json_path) as f:
            params = json.load(f)
            self.__dict__.update(params)

    @property
    def dict(self):
        return self.__dict__


def get_error_msg(callback_id):
    history_url = "http://space.shopee.io/apis/pipeline/openapi/pipeline/error_info"
    result = gettoken()
    tokens = "Bearer " + result["token"]
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    parameters = {
        "callback_id": callback_id,
    }
    r = requests.post(url=history_url, json=parameters, headers=headers)
    if r.text:
        result_rep = json.loads(r.text)
        return result_rep["data"]["detail"][0]["err_msg"]
    else:
        return False


if __name__ == '__main__':
    #add_services_info()
    pipeline_name = get_pipeline_name()
    
    # 如果动态获取失败，使用固定配置作为备用
    if not pipeline_name:
        print("Dynamic API failed, using Config.srv2id as fallback")
        from config import Config
        pipeline_name = Config.srv2id
        print(f"Using {len(pipeline_name)} services from config")
    
    service = []
    successful_count = 0
    failed_count = 0
    
    for pipeline, service_id in pipeline_name.items():
        try:
            if "shopee-annotation-dataproxy-live" == pipeline:
                pipeline_name_one = pipeline.replace("live", "staging")
                result = get_pipeinle_info(pipeline_name_one, service_id)
                if result:
                    for i in result:
                        service.append(i)
                    successful_count += 1
                else:
                    failed_count += 1
            else:
                result = get_pipeinle_info(pipeline, service_id)
                if result:
                    for i in result:
                        service.append(i)
                    successful_count += 1
                else:
                    failed_count += 1
                    
        except Exception as e:
            print(f"Error processing {pipeline}: {e}")
            failed_count += 1
            continue

    print(f"\nSummary:")
    print(f"  Successfully processed services: {successful_count}")
    print(f"  Failed services: {failed_count}")
    print(f"  Total build records collected: {len(service)}")

    if service:
        from datetime import datetime
        service.sort(key=lambda x: x["end_time"], reverse=True)
        service = cluster_by_date(service)
        filename_new = os.path.join(BASE_DIR, 'app01', 'new_deploy_data.json')
        write_dict_to_json_file({"data": service}, filename_new)
        print(f"  Data written to: {filename_new}")
    else:
        print("  ⚠️  No data collected - check API permissions and authentication")
        
        # 生成一个带有错误信息的 JSON 文件，让前端知道有问题
        from datetime import datetime
        error_data = {
            "data": [],
            "error": "API authentication failed or no data available",
            "timestamp": datetime.now().isoformat(),
            "message": "Please check Space API credentials and permissions"
        }
        filename_new = os.path.join(BASE_DIR, 'app01', 'new_deploy_data.json')
        write_dict_to_json_file(error_data, filename_new)
        print(f"  Error info written to: {filename_new}")
