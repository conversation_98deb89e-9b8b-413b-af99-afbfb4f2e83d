# Generated manually for R&D Metrics Models

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('app01', '0024_add_statistics_models'),
    ]

    operations = [
        migrations.CreateModel(
            name='RDTeam',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('team_id', models.Char<PERSON>ield(help_text='团队唯一标识', max_length=50, unique=True)),
                ('team_name', models.CharField(help_text='团队名称', max_length=100)),
                ('department', models.CharField(help_text='所属部门', max_length=100)),
                ('team_leader_email', models.EmailField(help_text='团队负责人邮箱', max_length=254)),
                ('parent_team_id', models.CharField(blank=True, help_text='父团队ID（用于层级结构）', max_length=50, null=True)),
                ('is_active', models.BooleanField(default=True, help_text='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('jira_projects', models.JSONField(default=list, help_text="关联的JIRA项目列表，如['SPCB', 'SPCT']")),
                ('git_repositories', models.JSONField(default=list, help_text='关联的Git仓库列表，来自services_id.json')),
                ('is_test_data', models.BooleanField(default=False, help_text='是否为测试数据')),
            ],
            options={
                'verbose_name': 'R&D团队',
                'verbose_name_plural': 'R&D团队',
                'db_table': 'rd_team',
            },
        ),
        migrations.CreateModel(
            name='RDTeamMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('member_email', models.EmailField(help_text='成员邮箱（唯一标识）', max_length=254)),
                ('member_name', models.CharField(help_text='成员姓名', max_length=100)),
                ('role', models.CharField(choices=[('developer', 'Developer'), ('tester', 'Tester'), ('pm', 'Product Manager'), ('lead', 'Tech Lead'), ('manager', 'Manager')], help_text='角色', max_length=50)),
                ('join_date', models.DateField(help_text='加入日期')),
                ('is_active', models.BooleanField(default=True, help_text='是否激活')),
                ('jira_account_id', models.CharField(blank=True, help_text='JIRA账户ID', max_length=100, null=True)),
                ('git_username', models.CharField(blank=True, help_text='Git用户名', max_length=100, null=True)),
                ('is_test_data', models.BooleanField(default=False, help_text='是否为测试数据')),
                ('team', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='members', to='app01.rdteam')),
            ],
            options={
                'verbose_name': 'R&D团队成员',
                'verbose_name_plural': 'R&D团队成员',
                'db_table': 'rd_team_member',
            },
        ),
        migrations.CreateModel(
            name='RDMetricsSnapshot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('snapshot_id', models.UUIDField(default=uuid.uuid4, help_text='快照唯一ID', unique=True)),
                ('period_type', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('quarterly', 'Quarterly')], help_text='统计周期', max_length=20)),
                ('period_start', models.DateField(help_text='周期开始日期')),
                ('period_end', models.DateField(help_text='周期结束日期')),
                ('metrics_data', models.JSONField(default=dict, help_text='指标数据（JSON格式）')),
                ('calculated_at', models.DateTimeField(auto_now_add=True, help_text='计算时间')),
                ('data_sources', models.JSONField(default=dict, help_text='数据源信息')),
                ('is_test_data', models.BooleanField(default=False, help_text='是否为测试数据')),
                ('team', models.ForeignKey(help_text='关联团队', on_delete=django.db.models.deletion.CASCADE, to='app01.rdteam')),
            ],
            options={
                'verbose_name': 'R&D指标快照',
                'verbose_name_plural': 'R&D指标快照',
                'db_table': 'rd_metrics_snapshot',
            },
        ),
        migrations.CreateModel(
            name='RDJiraMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('project_key', models.CharField(help_text='JIRA项目键值，如SPCB、SPCT', max_length=20)),
                ('date', models.DateField(help_text='统计日期')),
                ('total_issues', models.IntegerField(default=0, help_text='总问题数')),
                ('created_issues', models.IntegerField(default=0, help_text='新建问题数')),
                ('resolved_issues', models.IntegerField(default=0, help_text='已解决问题数')),
                ('reopened_issues', models.IntegerField(default=0, help_text='重新打开问题数')),
                ('total_bugs', models.IntegerField(default=0, help_text='总Bug数')),
                ('new_bugs', models.IntegerField(default=0, help_text='新Bug数')),
                ('fixed_bugs', models.IntegerField(default=0, help_text='已修复Bug数')),
                ('bug_fix_time_avg', models.FloatField(help_text='平均Bug修复时间（天）', null=True)),
                ('story_points_committed', models.FloatField(default=0, help_text='承诺故事点')),
                ('story_points_completed', models.FloatField(default=0, help_text='完成故事点')),
                ('velocity', models.FloatField(default=0, help_text='团队速度')),
                ('avg_cycle_time', models.FloatField(help_text='平均周期时间（天）', null=True)),
                ('avg_lead_time', models.FloatField(help_text='平均前置时间（天）', null=True)),
                ('defect_density', models.FloatField(help_text='缺陷密度', null=True)),
                ('rework_rate', models.FloatField(help_text='返工率', null=True)),
                ('is_test_data', models.BooleanField(default=False, help_text='是否为测试数据')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('team', models.ForeignKey(help_text='关联团队', on_delete=django.db.models.deletion.CASCADE, to='app01.rdteam')),
            ],
            options={
                'verbose_name': 'R&D JIRA指标',
                'verbose_name_plural': 'R&D JIRA指标',
                'db_table': 'rd_jira_metrics',
            },
        ),
        migrations.CreateModel(
            name='RDGitMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('repository_name', models.CharField(help_text='仓库名称', max_length=200)),
                ('repository_id', models.IntegerField(help_text='仓库ID（来自services_id.json）')),
                ('date', models.DateField(help_text='统计日期')),
                ('total_commits', models.IntegerField(default=0, help_text='总提交数')),
                ('total_authors', models.IntegerField(default=0, help_text='总作者数')),
                ('lines_added', models.IntegerField(default=0, help_text='新增代码行数')),
                ('lines_deleted', models.IntegerField(default=0, help_text='删除代码行数')),
                ('merge_requests_created', models.IntegerField(default=0, help_text='创建的合并请求数')),
                ('merge_requests_merged', models.IntegerField(default=0, help_text='已合并的合并请求数')),
                ('avg_mr_review_time', models.FloatField(help_text='平均MR审查时间（小时）', null=True)),
                ('code_churn_rate', models.FloatField(help_text='代码变动率', null=True)),
                ('hotspot_files_count', models.IntegerField(default=0, help_text='热点文件数量')),
                ('is_test_data', models.BooleanField(default=False, help_text='是否为测试数据')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('team', models.ForeignKey(help_text='关联团队', on_delete=django.db.models.deletion.CASCADE, to='app01.rdteam')),
            ],
            options={
                'verbose_name': 'R&D Git指标',
                'verbose_name_plural': 'R&D Git指标',
                'db_table': 'rd_git_metrics',
            },
        ),
        migrations.CreateModel(
            name='RDMetricsPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_email', models.EmailField(help_text='用户邮箱', max_length=254, unique=True)),
                ('permission_level', models.CharField(choices=[('viewer', 'Viewer'), ('team_lead', 'Team Lead'), ('manager', 'Manager'), ('admin', 'Admin')], help_text='权限级别', max_length=20)),
                ('can_view_individual_metrics', models.BooleanField(default=True, help_text='可查看个人指标')),
                ('can_view_team_comparison', models.BooleanField(default=False, help_text='可查看团队对比')),
                ('can_export_data', models.BooleanField(default=False, help_text='可导出数据')),
                ('can_configure_teams', models.BooleanField(default=False, help_text='可配置团队')),
                ('is_active', models.BooleanField(default=True, help_text='是否激活')),
                ('granted_by', models.EmailField(help_text='授权人邮箱', max_length=254)),
                ('granted_at', models.DateTimeField(auto_now_add=True, help_text='授权时间')),
                ('expires_at', models.DateTimeField(blank=True, help_text='过期时间', null=True)),
                ('is_test_data', models.BooleanField(default=False, help_text='是否为测试数据')),
                ('accessible_teams', models.ManyToManyField(blank=True, help_text='可访问的团队', to='app01.rdteam')),
            ],
            options={
                'verbose_name': 'R&D指标权限',
                'verbose_name_plural': 'R&D指标权限',
                'db_table': 'rd_metrics_permission',
            },
        ),
        # Add indexes
        migrations.AddIndex(
            model_name='rdteam',
            index=models.Index(fields=['team_id', 'is_active'], name='rd_team_team_id_is_active_idx'),
        ),
        migrations.AddIndex(
            model_name='rdteam',
            index=models.Index(fields=['department'], name='rd_team_department_idx'),
        ),
        migrations.AddIndex(
            model_name='rdteam',
            index=models.Index(fields=['is_test_data'], name='rd_team_is_test_data_idx'),
        ),
        migrations.AddIndex(
            model_name='rdteammember',
            index=models.Index(fields=['member_email', 'is_active'], name='rd_team_member_email_active_idx'),
        ),
        migrations.AddIndex(
            model_name='rdteammember',
            index=models.Index(fields=['team', 'role'], name='rd_team_member_team_role_idx'),
        ),
        migrations.AddIndex(
            model_name='rdteammember',
            index=models.Index(fields=['is_test_data'], name='rd_team_member_test_data_idx'),
        ),
        migrations.AddIndex(
            model_name='rdmetricssnapshot',
            index=models.Index(fields=['team', 'period_type', 'period_start'], name='rd_metrics_snapshot_team_period_idx'),
        ),
        migrations.AddIndex(
            model_name='rdmetricssnapshot',
            index=models.Index(fields=['calculated_at'], name='rd_metrics_snapshot_calc_at_idx'),
        ),
        migrations.AddIndex(
            model_name='rdmetricssnapshot',
            index=models.Index(fields=['is_test_data'], name='rd_metrics_snapshot_test_data_idx'),
        ),
        migrations.AddIndex(
            model_name='rdjirametrics',
            index=models.Index(fields=['team', 'date'], name='rd_jira_metrics_team_date_idx'),
        ),
        migrations.AddIndex(
            model_name='rdjirametrics',
            index=models.Index(fields=['project_key', 'date'], name='rd_jira_metrics_project_date_idx'),
        ),
        migrations.AddIndex(
            model_name='rdjirametrics',
            index=models.Index(fields=['is_test_data'], name='rd_jira_metrics_test_data_idx'),
        ),
        migrations.AddIndex(
            model_name='rdgitmetrics',
            index=models.Index(fields=['team', 'date'], name='rd_git_metrics_team_date_idx'),
        ),
        migrations.AddIndex(
            model_name='rdgitmetrics',
            index=models.Index(fields=['repository_id', 'date'], name='rd_git_metrics_repo_date_idx'),
        ),
        migrations.AddIndex(
            model_name='rdgitmetrics',
            index=models.Index(fields=['is_test_data'], name='rd_git_metrics_test_data_idx'),
        ),
        migrations.AddIndex(
            model_name='rdmetricspermission',
            index=models.Index(fields=['user_email', 'is_active'], name='rd_metrics_perm_email_active_idx'),
        ),
        migrations.AddIndex(
            model_name='rdmetricspermission',
            index=models.Index(fields=['permission_level'], name='rd_metrics_perm_level_idx'),
        ),
        migrations.AddIndex(
            model_name='rdmetricspermission',
            index=models.Index(fields=['is_test_data'], name='rd_metrics_perm_test_data_idx'),
        ),
        # Add unique constraints
        migrations.AddConstraint(
            model_name='rdteammember',
            constraint=models.UniqueConstraint(fields=['team', 'member_email'], name='unique_team_member_email'),
        ),
        migrations.AddConstraint(
            model_name='rdmetricssnapshot',
            constraint=models.UniqueConstraint(fields=['team', 'period_type', 'period_start'], name='unique_team_period_snapshot'),
        ),
        migrations.AddConstraint(
            model_name='rdjirametrics',
            constraint=models.UniqueConstraint(fields=['team', 'project_key', 'date'], name='unique_team_project_date_jira'),
        ),
        migrations.AddConstraint(
            model_name='rdgitmetrics',
            constraint=models.UniqueConstraint(fields=['team', 'repository_id', 'date'], name='unique_team_repo_date_git'),
        ),
    ]
