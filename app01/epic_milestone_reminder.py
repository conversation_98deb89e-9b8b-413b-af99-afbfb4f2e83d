# -*- coding: utf-8 -*-
"""
Epic关键节点风险预警提醒功能
<EMAIL>
"""

from datetime import datetime, timedelta
from jira import JIRA
from icecream import ic
from django.http import JsonResponse
import json
from typing import List, Dict, Set
import os
import sys
import locale

from app01.statistics.decorators import track_cronjob_execution

# 导入views.py中的函数和配置
from .views import get_jira_connection, test_for_seatalk_bot, JIRA_TOKEN
from .seatalk_group_manager import simplify_jira_error
# 导入群组同步管理模块
from .group_sync_manager import get_epic_project_groups_with_fallback, daily_sync_groups

# Workee PJ 相关日志群配置
WORKEE_PJ_LOG_GROUP_ID = "ODY1NTY4NTMwMjEw"

# Epic关键节点风险预警提醒配置
# 项目配置：可以单独为每种提醒类型配置适用的项目
EPIC_REMINDER_CONFIG = {
    # 联调开始提醒配置
    "integration_start": {
        "enabled_projects": ["SPCB", "SPCT"],  # 可配置多个项目key
        "enabled": True,
        "reminder_days": 1,  # 提前1个工作日提醒
        "target_audience": "dev",  # 提醒对象：dev, pm, all
        "message_template": "🔔 **联调开始提醒** 🔔\n\n项目：【{epic_key}】{epic_title}\n🗓️ 联调开始时间：{date}\n⏰ 距离联调开始还有 1 个工作日\n\n请各位开发同学做好准备工作：\n• 确保开发工作已完成\n• 检查代码质量\n• 准备联调环境\n\n🔗 Epic链接：https://jira.shopee.io/browse/{epic_key}"
    },

    # 业务需求标签检查提醒配置
    "business_requirement_label_check": {
        "enabled_projects": ["SPCT", "Shopee ChatBot", "Shopee Live Streaming"],  # 支持的项目
        "enabled": True,
        "reminder_days": [0, 3],  # 每周一(0)和周四(3)提醒
        "target_audience": "assignee",  # 提醒Epic的assignee
        "message_template": "📋 **业务需求KP标签检查提醒** 📋\n\n项目：【{epic_key}】{epic_title}\n📅 创建时间：{created_date}\n👤 经办人：{assignee}\n\n⚠️ **重要提醒**：\n• 请判断该需求是否属于KP，如果属于KP，请增加KP 标签，如果不属于请添加None标签。\n• 当前状态：{status}\n\n🔗 Epic链接：https://jira.shopee.io/browse/{epic_key}"
    },
    
    # 提测时间提醒配置  
    "qa_start": {
        "enabled_projects": ["SPCB", "SPCT"],
        "enabled": True,
        "reminder_days": 1,
        "target_audience": "dev",
        "message_template": "🧪 **提测时间提醒** 🧪\n\n项目：【{epic_key}】{epic_title}\n🗓️ 提测开始时间：{date}\n⏰ 距离提测还有 1 个工作日\n\n请各位开发同学今日准备：\n•完成测试环境部署\n• 完成主流程自测\n• 提测前联系Designer完成Demo Review\n• 填写提测单并添加到 Epic 单中\n\n🔗 Epic链接：https://jira.shopee.io/browse/{epic_key}"
    },
    
    # UAT开始提醒配置
    "uat_start": {
        "enabled_projects": ["SPCB", "SPCT"],
        "enabled": True,
        "reminder_days": 1,
        "target_audience": "pm",
        "message_template": "📋 **UAT开始提醒** 📋\n\n项目：【{epic_key}】{epic_title}\n🗓️ UAT开始时间：{date}\n⏰ 距离UAT开始还有 1 个工作日\n\n请PM准备：\n• 确认UAT测试用例\n• 协调测试资源\n\n🔗 Epic链接：https://jira.shopee.io/browse/{epic_key}"
    },
    
    # UAT sign-off提醒配置
    "uat_signoff": {
        "enabled_projects": ["SPCB", "SPCT"],
        "enabled": True,
        "reminder_days": 1,
        "target_audience": "pm",
        "message_template": "✅ **UAT Sign-off提醒** ✅\n\n项目：【{epic_key}】{epic_title}\n🗓️ UAT Sign-off截止时间：{date}\n⏰ 距离UAT Sign-off还有 1 个工作日\n\n请PM确认：\n• UAT测试是否完成\n• 所有bug是否已修复\n• 相关方是否确认sign-off\n• 准备进入发布流程\n\n🔗 Epic链接：https://jira.shopee.io/browse/{epic_key}"
    },
    
    # 发布信息提醒配置（基于Planned Release Date）
    "release_reminder": {
        "enabled_projects": ["SPCB"],
        "enabled": True,
        "reminder_days": 0,  # 当天提醒
        "target_audience": "dev",
        "message_template": "🚀 **发布准备提醒** 🚀\n\n项目：【{epic_key}】{epic_title}\n🗓️ 计划发布时间：{date}\n📅 今天是发布日\n\n请各位开发同学确认：\n• 发布准备工作是否就绪？\n• 发布流程是否已经启动？\n• 相关方是否已通知？\n\n🔗 Epic链接：https://jira.shopee.io/browse/{epic_key}"
    }
}

def get_workday_before(target_date, days_before):
    """
    获取指定日期前N个工作日的日期
    
    Args:
        target_date: 目标日期 (datetime.date)
        days_before: 提前几个工作日
    
    Returns:
        datetime.date: 计算出的工作日日期
    """
    current_date = target_date
    workdays_count = 0
    
    while workdays_count < days_before:
        current_date -= timedelta(days=1)
        # 0-4代表周一到周五
        if current_date.weekday() < 5:
            workdays_count += 1
            
    return current_date

def generate_smart_epic_jql(today):
    """
    根据当前日期和提醒配置生成智能JQL查询
    
    Args:
        today: 当前日期 (datetime.date)
        
    Returns:
        str: 优化后的JQL查询语句
    """
    # 计算查询的日期范围
    # 考虑各种提醒类型的时间要求和工作日计算
    
    # 最小日期：今天（发布提醒是当天）
    start_date = today
    
    # 最大日期：考虑工作日计算的复杂性，给足够的缓冲时间
    # 提前1个工作日的规则，在最坏情况下（周五提醒下周二的事件）需要4天缓冲
    # 再加上一些额外缓冲以防遗漏，设置为未来14天
    max_advance_days = max([
        EPIC_REMINDER_CONFIG["integration_start"]["reminder_days"],  # 1天
        EPIC_REMINDER_CONFIG["qa_start"]["reminder_days"],          # 1天  
        EPIC_REMINDER_CONFIG["uat_start"]["reminder_days"],         # 1天
        EPIC_REMINDER_CONFIG["uat_signoff"]["reminder_days"],       # 1天
        EPIC_REMINDER_CONFIG["release_reminder"]["reminder_days"],   # 0天
    ])
    
    # 考虑工作日计算和周末跨度，设置14天缓冲期
    buffer_days = 14
    end_date = today + timedelta(days=max_advance_days + buffer_days)
    
    # 格式化为JIRA日期格式
    start_str = start_date.strftime('%Y-%m-%d')
    end_str = end_date.strftime('%Y-%m-%d')
    
    # 构建智能JQL查询
    # 只查询在指定日期范围内有关键节点的Epic，大幅减少查询结果
    base_query = f'project in (SPCB, SPCT) AND type = Epic AND status not in (Done, Closed, Icebox)'
    
    date_conditions = [
        f'"Planned Integration Start Date" >= "{start_str}" AND "Planned Integration Start Date" <= "{end_str}"',
        f'"Planned QA Start Date" >= "{start_str}" AND "Planned QA Start Date" <= "{end_str}"',
        f'"Planned UAT Start Date" >= "{start_str}" AND "Planned UAT Start Date" <= "{end_str}"',
        f'"Planned UAT Due Date" >= "{start_str}" AND "Planned UAT Due Date" <= "{end_str}"',
        f'"Planned Release Date" >= "{start_str}" AND "Planned Release Date" <= "{end_str}"'
    ]
    
    # 用OR连接所有日期条件
    date_query = '(' + ' OR '.join(date_conditions) + ')'
    
    # 组合完整的JQL
    jql = f'{base_query} AND {date_query}'
    
    ic(f"智能JQL日期范围: {start_str} 到 {end_str} (共{(end_date - start_date).days}天)")
    
    return jql

def parse_epic_date_field(date_value):
    """
    解析Epic中的日期字段
    
    Args:
        date_value: 日期字段值，可能是字符串或None
    
    Returns:
        datetime.date or None: 解析后的日期，解析失败返回None
    """
    if not date_value:
        return None
        
    try:
        if isinstance(date_value, str):
            if 'T' in date_value:  # 处理带时间的ISO格式
                date_obj = datetime.fromisoformat(date_value.replace('Z', '+00:00'))
                return date_obj.date()
            else:  # 处理仅日期的格式
                date_obj = datetime.strptime(date_value, '%Y-%m-%d')
                return date_obj.date()
        return None
    except:
        return None

def get_epic_team_members(epic_info, audience_type):
    """
    从Epic信息中获取对应的团队成员
    
    Args:
        epic_info: Epic信息字典
        audience_type: 目标受众类型 ("dev", "pm", "all")
        
    Returns:
        list: 邮箱地址列表
    """
    members = []
    people = epic_info.get("people", {})
    ic(people)
    
    if audience_type == "dev" or audience_type == "all":
        # 添加开发人员
        dev_fields = ["Developer", "FE List", "BE List"]
        for field in dev_fields:
            if field in people and people[field]:
                if isinstance(people[field], list):
                    members.extend(people[field])
                else:
                    members.append(people[field])
    ic("dev or all members:",members)
    
    if audience_type == "pm" or audience_type == "all":
        # 添加PM
        pm_fields = ["Product Manager", "Project Manager"]
        for field in pm_fields:
            if field in people and people[field]:
                if isinstance(people[field], list):
                    members.extend(people[field])
                else:
                    members.append(people[field])
    ic("pm or all members:",members)
    # 过滤有效邮箱
    valid_members = []
    for member in members:
        if member and isinstance(member, str) and "@" in member:
            valid_members.append(member)
    ic("有效邮箱",valid_members)

    return list(set(valid_members))  # 去重

def send_epic_reminder_to_seatalk(epic_key, epic_title, date_str, message_template, target_members):
    """
    发送Epic提醒消息到Seatalk群组（支持多个群组）
    
    Args:
        epic_key: Epic key
        epic_title: Epic标题
        date_str: 日期字符串
        message_template: 消息模板
        target_members: 目标成员邮箱列表
    """
    try:
        # 格式化消息
        message = message_template.format(
            epic_key=epic_key,
            epic_title=epic_title,
            date=date_str
        )
        
        # 添加@提醒
        if target_members:
            message += "\n\n📧 **相关人员：**\n"
            for member in target_members:
                message += f"<mention-tag target=\"seatalk://user?email={member}\"/>\n"
        
        # 从数据库获取所有相关群组
        try:
            groups = get_epic_project_groups_with_fallback(epic_key)
        except Exception as e:
            error_msg = f"获取Epic群组失败: {str(e)}"
            ic(error_msg)
            send_error_notification(error_msg, epic_key)
            return
        
        # 向所有找到的群发送消息
        success_count = 0
        failed_groups = []
        
        for group in groups:
            try:
                ic(f"{epic_key} - 正在发送提醒到群组: {group.group_name}")
                # 发送到Seatalk
                test_for_seatalk_bot(message, False, group.group_id)
                success_count += 1
                ic(f"{epic_key} - 成功发送提醒到群组: {group.group_name}")
            except Exception as e:
                ic(f"{epic_key} - 发送提醒失败: {group.group_name} - {str(e)}")
                failed_groups.append({
                    "group_id": group.group_id,
                    "group_name": group.group_name,
                    "error": str(e)
                })
        
        # 记录发送结果
        if success_count > 0:
            ic(f"Epic提醒发送完成: {epic_key} - 成功 {success_count}/{len(groups)} 个群组")
        
        # 如果有发送失败的情况，记录错误信息
        if failed_groups:
            error_message = f"⚠️ {epic_key} Epic提醒部分发送失败\n\n"
            error_message += f"• 成功发送: {success_count}/{len(groups)} 个群组\n"
            error_message += "• 失败详情:\n"
            for group in failed_groups:
                error_message += f"  - 群名: {group['group_name']}\n"
                error_message += f"    群ID: {group['group_id']}\n"
                if 'error' in group:
                    error_message += f"    错误: {group['error']}\n"
            
            # 发送错误通知到调试群
            send_error_notification(error_message, epic_key)
        
    except Exception as e:
        error_msg = f"发送Epic提醒失败: {str(e)}"
        ic(error_msg)
        send_error_notification(error_msg, epic_key)
@track_cronjob_execution('check_epic_milestone_reminders')
def check_epic_milestone_reminders():
    """
    检查Epic关键节点提醒
    这个函数应该通过crontab每天早上10点执行
    """
    try:
        ic("开始检查Epic关键节点提醒...")
        
        today = datetime.now().date()
        current_weekday = today.weekday()  # 0=周一, 6=周日
        
        # 检查今天是否为工作日（周一到周五）
        if current_weekday >= 5:  # 5=周六, 6=周日
            ic(f"今天是{'周六' if current_weekday == 5 else '周日'}，跳过Epic提醒检查")
            return
        
        ic(f"今天是工作日（周{'一二三四五'[current_weekday]}），开始检查Epic提醒...")
        
        # 初始化统计计数器
        reminder_counts = {
            "integration_start": 0,
            "qa_start": 0,
            "uat_start": 0,
            "uat_signoff": 0,
            "release_reminder": 0
        }
        
        # 连接JIRA
        jira = get_jira_connection()
        
        # 生成智能JQL查询，只获取在提醒时间范围内的Epic
        jql = generate_smart_epic_jql(today)
        ic(f"生成的智能JQL: {jql}")
        
        # 设置maxResults=0获取所有结果，不受默认50条限制
        epics = jira.search_issues(jql, maxResults=0)
        
        ic(f"找到 {len(epics)} 个未完成的Epic")
        
        for epic in epics:
            try:
                epic_key = epic.key
                epic_title = epic.fields.summary
                ic(f"🔍 开始处理Epic: {epic_key} - {epic_title}")
                
                # 获取Epic的详细信息
                epic_issue = jira.issue(epic_key)
                
                # 获取日期字段
                dates = {}
                date_custom_fields = {
                    "Planned Integration Start Date": "customfield_12634",
                    "Planned QA Start Date": "customfield_11521",
                    "Planned UAT Start Date": "customfield_11522",
                    "Planned UAT Due Date": "customfield_11511",
                    "Planned Release Date": "customfield_11513",  # 添加发布日期字段
                }
                
                # 获取所有可用字段
                all_fields = jira.fields()
                field_map = {field['name']: field['id'] for field in all_fields}
                
                # 动态获取日期字段值
                for date_name, field_id in date_custom_fields.items():
                    try:
                        actual_field_id = field_map.get(date_name, field_id)
                        value = getattr(epic_issue.fields, actual_field_id, None)
                        dates[date_name] = value
                    except:
                        dates[date_name] = None
                
                # 获取团队成员信息
                people = {}
                people_custom_fields = {
                    "Product Manager": "customfield_10306",
                    "FE List": "customfield_37801",
                    "BE List": "customfield_37800",
                    "QA List": "customfield_12202",
                    "Developer": "customfield_10307",
                    "Project Manager": "customfield_10600",
                }
                
                for person_name, field_id in people_custom_fields.items():
                    try:
                        actual_field_id = field_map.get(person_name, field_id)
                        value = getattr(epic_issue.fields, actual_field_id, None)
                        if value:
                            if isinstance(value, list):
                                people[person_name] = [item.emailAddress if hasattr(item, 'emailAddress') else str(item) for item in value]
                            else:
                                people[person_name] = value.emailAddress if hasattr(value, 'emailAddress') else str(value)
                    except:
                        people[person_name] = None
                
                epic_info = {
                    "epic_key": epic_key,
                    "epic_title": epic_title,
                    "people": people
                }
                
                # 打印Epic的日期信息用于调试
                ic(f"📅 Epic {epic_key} 的日期信息: 联调开始={dates.get('Planned Integration Start Date')}, "
                   f"提测开始={dates.get('Planned QA Start Date')}, UAT开始={dates.get('Planned UAT Start Date')}, "
                   f"UAT截止={dates.get('Planned UAT Due Date')}, 发布日期={dates.get('Planned Release Date')}")
                
                # 检查各种提醒类型
                
                # 1. 联调开始提醒
                if EPIC_REMINDER_CONFIG["integration_start"]["enabled"]:
                    # 检查项目是否在启用列表中
                    epic_project = epic_key.split('-')[0]  # 从 epic_key 获取项目名
                    enabled_projects = EPIC_REMINDER_CONFIG["integration_start"]["enabled_projects"]
                    ic(f"🔍 Epic {epic_key} 项目: {epic_project}, 联调开始提醒启用项目: {enabled_projects}")
                    
                    if epic_project in enabled_projects:
                        integration_date = parse_epic_date_field(dates.get("Planned Integration Start Date"))
                        if integration_date:
                            reminder_date = get_workday_before(integration_date, EPIC_REMINDER_CONFIG["integration_start"]["reminder_days"])
                            ic(f"📅 Epic {epic_key} 联调开始: {integration_date}, 提醒日期: {reminder_date}, 今天: {today}")
                            if today == reminder_date:
                                ic(f"📋 联调开始提醒 - 符合条件的Epic: {epic_key} (联调开始时间: {integration_date.strftime('%Y-%m-%d')})")
                                reminder_counts["integration_start"] += 1
                                config = EPIC_REMINDER_CONFIG["integration_start"]
                                target_members = get_epic_team_members(epic_info, config["target_audience"])
                                send_epic_reminder_to_seatalk(
                                    epic_key, epic_title, integration_date.strftime('%Y-%m-%d'),
                                    config["message_template"], target_members
                                )
                
                # 2. 提测时间提醒
                if EPIC_REMINDER_CONFIG["qa_start"]["enabled"]:
                    # 检查项目是否在启用列表中
                    epic_project = epic_key.split('-')[0]  # 从 epic_key 获取项目名
                    enabled_projects = EPIC_REMINDER_CONFIG["qa_start"]["enabled_projects"]
                    ic(f"🔍 Epic {epic_key} 项目: {epic_project}, 提测时间提醒启用项目: {enabled_projects}")
                    
                    if epic_project in enabled_projects:
                        qa_date = parse_epic_date_field(dates.get("Planned QA Start Date"))
                        if qa_date:
                            reminder_date = get_workday_before(qa_date, EPIC_REMINDER_CONFIG["qa_start"]["reminder_days"])
                            ic(f"📅 Epic {epic_key} 提测开始: {qa_date}, 提醒日期: {reminder_date}, 今天: {today}")
                            if today == reminder_date:
                                ic(f"🧪 提测时间提醒 - 符合条件的Epic: {epic_key} (提测开始时间: {qa_date.strftime('%Y-%m-%d')})")
                                reminder_counts["qa_start"] += 1
                                config = EPIC_REMINDER_CONFIG["qa_start"]
                                target_members = get_epic_team_members(epic_info, config["target_audience"])
                                send_epic_reminder_to_seatalk(
                                    epic_key, epic_title, qa_date.strftime('%Y-%m-%d'),
                                    config["message_template"], target_members
                                )
                
                # 3. UAT开始提醒
                if EPIC_REMINDER_CONFIG["uat_start"]["enabled"]:
                    # 检查项目是否在启用列表中
                    epic_project = epic_key.split('-')[0]  # 从 epic_key 获取项目名
                    enabled_projects = EPIC_REMINDER_CONFIG["uat_start"]["enabled_projects"]
                    ic(f"🔍 Epic {epic_key} 项目: {epic_project}, UAT开始提醒启用项目: {enabled_projects}")
                    
                    if epic_project in enabled_projects:
                        uat_start_date = parse_epic_date_field(dates.get("Planned UAT Start Date"))
                        if uat_start_date:
                            reminder_date = get_workday_before(uat_start_date, EPIC_REMINDER_CONFIG["uat_start"]["reminder_days"])
                            ic(f"📅 Epic {epic_key} UAT开始: {uat_start_date}, 提醒日期: {reminder_date}, 今天: {today}")
                            if today == reminder_date:
                                ic(f"📋 UAT开始提醒 - 符合条件的Epic: {epic_key} (UAT开始时间: {uat_start_date.strftime('%Y-%m-%d')})")
                                reminder_counts["uat_start"] += 1
                                config = EPIC_REMINDER_CONFIG["uat_start"]
                                target_members = get_epic_team_members(epic_info, config["target_audience"])
                                send_epic_reminder_to_seatalk(
                                    epic_key, epic_title, uat_start_date.strftime('%Y-%m-%d'),
                                    config["message_template"], target_members
                                )
                
                # 4. UAT sign-off提醒
                if EPIC_REMINDER_CONFIG["uat_signoff"]["enabled"]:
                    # 检查项目是否在启用列表中
                    epic_project = epic_key.split('-')[0]  # 从 epic_key 获取项目名
                    enabled_projects = EPIC_REMINDER_CONFIG["uat_signoff"]["enabled_projects"]
                    ic(f"🔍 Epic {epic_key} 项目: {epic_project}, UAT Sign-off提醒启用项目: {enabled_projects}")
                    
                    if epic_project in enabled_projects:
                        uat_due_date = parse_epic_date_field(dates.get("Planned UAT Due Date"))
                        if uat_due_date:
                            reminder_date = get_workday_before(uat_due_date, EPIC_REMINDER_CONFIG["uat_signoff"]["reminder_days"])
                            ic(f"📅 Epic {epic_key} UAT截止: {uat_due_date}, 提醒日期: {reminder_date}, 今天: {today}")
                            if today == reminder_date:
                                ic(f"✅ UAT Sign-off提醒 - 符合条件的Epic: {epic_key} (UAT截止时间: {uat_due_date.strftime('%Y-%m-%d')})")
                                reminder_counts["uat_signoff"] += 1
                                config = EPIC_REMINDER_CONFIG["uat_signoff"]
                                target_members = get_epic_team_members(epic_info, config["target_audience"])
                                send_epic_reminder_to_seatalk(
                                    epic_key, epic_title, uat_due_date.strftime('%Y-%m-%d'),
                                    config["message_template"], target_members
                                )
                
                # 5. 发布信息提醒（基于Planned Release Date）
                if EPIC_REMINDER_CONFIG["release_reminder"]["enabled"]:
                    # 检查项目是否在启用列表中
                    epic_project = epic_key.split('-')[0]  # 从 epic_key 获取项目名
                    enabled_projects = EPIC_REMINDER_CONFIG["release_reminder"]["enabled_projects"]
                    ic(f"🔍 Epic {epic_key} 项目: {epic_project}, 发布准备提醒启用项目: {enabled_projects}")
                    
                    if epic_project in enabled_projects:
                        release_date = parse_epic_date_field(dates.get("Planned Release Date"))
                        if release_date:
                            reminder_date = get_workday_before(release_date, EPIC_REMINDER_CONFIG["release_reminder"]["reminder_days"])
                            ic(f"📅 Epic {epic_key} 发布日期: {release_date}, 提醒日期: {reminder_date}, 今天: {today}")
                            if today == reminder_date:
                                ic(f"🚀 发布准备提醒 - 符合条件的Epic: {epic_key} (计划发布时间: {release_date.strftime('%Y-%m-%d')})")
                                reminder_counts["release_reminder"] += 1
                                config = EPIC_REMINDER_CONFIG["release_reminder"]
                                target_members = get_epic_team_members(epic_info, config["target_audience"])
                                send_epic_reminder_to_seatalk(
                                    epic_key, epic_title, release_date.strftime('%Y-%m-%d'),
                                    config["message_template"], target_members
                                )
                
                ic(f"✅ Epic {epic_key} 处理完成")
                        
            except Exception as e:
                # 使用简化的错误信息
                simplified_error = simplify_jira_error(str(e))
                error_msg = f"处理Epic时出错: {simplified_error}"
                ic(error_msg)
                send_error_notification(error_msg, epic.key)
                continue
        
        # 打印总结统计
        total_reminders = sum(reminder_counts.values())
        ic(f"📊 Epic关键节点提醒检查完成 - 总计发送 {total_reminders} 个提醒")
        if total_reminders > 0:
            ic(f"📋 联调开始提醒: {reminder_counts['integration_start']} 个")
            ic(f"🧪 提测时间提醒: {reminder_counts['qa_start']} 个")
            ic(f"📋 UAT开始提醒: {reminder_counts['uat_start']} 个")
            ic(f"✅ UAT Sign-off提醒: {reminder_counts['uat_signoff']} 个")
            ic(f"🚀 发布准备提醒: {reminder_counts['release_reminder']} 个")
        else:
            ic("✅ 今天没有需要发送的Epic提醒")
        
    except Exception as e:
        # 使用简化的错误信息
        simplified_error = simplify_jira_error(str(e))
        error_msg = f"Epic关键节点提醒检查失败: {simplified_error}"
        ic(error_msg)
        send_error_notification(error_msg)

def has_kp_label(labels):
    """
    检查labels列表中是否包含KP相关的标签

    Args:
        labels: 标签列表

    Returns:
        bool: 如果包含KP标签返回True，否则返回False
    """
    if not labels:
        return False

    # 检查是否包含KP相关标签（不区分大小写）
    for label in labels:
        if label and 'kp' in str(label).lower():
            return True
    return False

def should_exclude_epic_by_labels(labels):
    """
    根据labels判断是否应该排除该Epic
    注意：JQL已经过滤了None和cpm-test标签，这里只需要过滤KP标签

    Args:
        labels: Epic的标签列表

    Returns:
        bool: 如果应该排除返回True，否则返回False
    """
    if not labels:
        # 如果没有labels，不排除（JQL已经过滤了None标签的Epic）
        return False

    # 检查是否包含KP标签（JQL已经过滤了None和cpm-test）
    for label in labels:
        if label and 'kp' in str(label).lower():
            ic(f"Epic包含KP标签: {label}")
            return True

    return False

def generate_business_requirement_jql():
    """
    生成业务需求Epic的JQL查询语句

    Returns:
        str: 优化后的JQL查询语句
    """
    # 基础JQL - 按照用户提供的完整条件
    base_jql = '''project in (SPCT, "Shopee ChatBot", "Shopee Live Streaming")
                    AND issuetype = Epic
                    AND "Project Type" = "Feature Project"
                    AND createdDate >= 2025-01-01
                    AND status NOT IN (Done, Closed, Icebox)
                    AND "[POP] Key Project ID" IS EMPTY
                    AND (labels NOT IN (None, cpm-test) OR labels IS EMPTY)'''

    # 清理多余的空格和换行
    jql = ' '.join(base_jql.split())

    ic(f"生成的业务需求JQL: {jql}")
    return jql
@track_cronjob_execution('check_business_requirement_epic_reminders')
def check_business_requirement_epic_reminders():
    """
    检查业务需求Epic标签提醒
    每周一和周四执行
    """
    try:
        ic("开始检查业务需求Epic标签提醒...")

        today = datetime.now().date()
        current_weekday = today.weekday()  # 0=周一, 6=周日

        # 检查今天是否为周一(0)或周四(3)
        reminder_days = EPIC_REMINDER_CONFIG["business_requirement_label_check"]["reminder_days"]
        if current_weekday not in reminder_days:
            ic(f"今天是周{'一二三四五六日'[current_weekday]}，不是提醒日（周一/周四），跳过检查")
            return

        ic(f"今天是周{'一二三四五六日'[current_weekday]}，开始检查业务需求Epic标签...")

        # 连接JIRA
        jira = get_jira_connection()

        # 生成JQL查询
        jql = generate_business_requirement_jql()

        # 执行查询，获取所有符合条件的Epic（包含labels字段）
        epics = jira.search_issues(jql, fields=['key', 'summary', 'assignee', 'status', 'created', 'labels'], maxResults=0)

        ic(f"找到 {len(epics)} 个符合基础条件的Epic")

        # 统计计数器
        total_checked = 0
        excluded_by_labels = 0
        reminder_sent = 0
        no_assignee_count = 0

        for epic in epics:
            try:
                epic_key = epic.key
                epic_title = epic.fields.summary
                epic_labels = getattr(epic.fields, 'labels', [])
                epic_assignee = epic.fields.assignee
                epic_status = epic.fields.status.name if epic.fields.status else 'Unknown'
                epic_created = epic.fields.created

                total_checked += 1

                ic(f"🔍 检查Epic: {epic_key} - {epic_title}")
                ic(f"📋 Labels: {epic_labels}")

                # 检查是否应该排除该Epic
                if should_exclude_epic_by_labels(epic_labels):
                    excluded_by_labels += 1
                    ic(f"⏭️ Epic {epic_key} 被排除（包含排除标签）")
                    continue

                # 检查是否有assignee
                if not epic_assignee:
                    no_assignee_count += 1
                    ic(f"⚠️ Epic {epic_key} 没有assignee，跳过提醒")
                    continue

                # 发送提醒
                assignee_email = epic_assignee.emailAddress if hasattr(epic_assignee, 'emailAddress') else str(epic_assignee)
                created_date = epic_created[:10] if epic_created else 'Unknown'  # 只取日期部分

                ic(f"📧 准备发送私聊提醒给: {assignee_email}")

                # 格式化消息
                config = EPIC_REMINDER_CONFIG["business_requirement_label_check"]
                message = config["message_template"].format(
                    epic_key=epic_key,
                    epic_title=epic_title,
                    created_date=created_date,
                    assignee=assignee_email,
                    status=epic_status
                )

                # 发送私聊提醒给assignee
                send_business_requirement_reminder_to_assignee(epic_key, message, assignee_email)
                reminder_sent += 1

                ic(f"✅ Epic {epic_key} 提醒发送完成")

            except Exception as e:
                # 使用简化的错误信息
                simplified_error = simplify_jira_error(str(e))
                error_msg = f"处理业务需求Epic时出错: {simplified_error}"
                ic(error_msg)
                send_error_notification(error_msg, epic.key)
                continue

        # 打印总结统计
        ic(f"📊 业务需求Epic标签检查完成")
        ic(f"📋 总计检查: {total_checked} 个Epic")
        ic(f"⏭️ 标签排除: {excluded_by_labels} 个Epic")
        ic(f"⚠️ 无assignee: {no_assignee_count} 个Epic")
        ic(f"📧 发送提醒: {reminder_sent} 个Epic")

        # 生成日志消息并发送到Workee PJ群
        log_message = f"📋 **业务需求KP标签检查提醒执行报告** 📋\n\n"
        log_message += f"🔍 **监控范围**: SPCT、Shopee ChatBot、Shopee Live Streaming项目\n"
        log_message += f"📊 **执行统计**:\n"
        log_message += f"• 总计检查: {total_checked} 个Epic\n"
        log_message += f"• 标签排除: {excluded_by_labels} 个Epic（已有KP相关标签）\n"
        log_message += f"• 无assignee: {no_assignee_count} 个Epic\n"
        log_message += f"• 发送提醒: {reminder_sent} 个Epic\n\n"
        
        if reminder_sent > 0:
            log_message += f"✅ **提醒详情**: 成功发送 {reminder_sent} 个业务需求Epic标签提醒给对应的assignee"
            ic(f"✅ 成功发送 {reminder_sent} 个业务需求Epic标签提醒")
        else:
            log_message += f"✅ **结果**: 今天没有需要发送的业务需求Epic标签提醒"
            ic("✅ 今天没有需要发送的业务需求Epic标签提醒")
        
        # 发送日志到Workee PJ群
        send_log_to_workee_pj_group(log_message)

    except Exception as e:
        # 使用简化的错误信息
        simplified_error = simplify_jira_error(str(e))
        error_msg = f"业务需求Epic标签检查失败: {simplified_error}"
        ic(error_msg)
        send_error_notification(error_msg)

def send_business_requirement_reminder_to_assignee(epic_key, message, assignee_email):
    """
    发送业务需求Epic提醒消息到assignee的私聊

    Args:
        epic_key: Epic key
        message: 格式化后的消息内容
        assignee_email: assignee的邮箱地址
    """
    try:
        # 导入私聊发送相关的模块
        from .seatalk_group_manager import get_employee_codes, send_message_to_user

        # 获取assignee的employee_code
        email_to_code = get_employee_codes([assignee_email])

        if assignee_email not in email_to_code:
            error_msg = f"无法获取用户 {assignee_email} 的employee_code，可能用户不存在或无权限访问"
            ic(error_msg)
            send_error_notification(f"{epic_key}: {error_msg} - User Email: {assignee_email}", epic_key)
            return

        employee_code = email_to_code[assignee_email]
        ic(f"{epic_key} - 正在发送业务需求提醒私聊给: {assignee_email} (employee_code: {employee_code})")

        # 发送私聊消息
        result = send_message_to_user(
            employee_code=employee_code,
            message_content=message,
            message_type="text",
            format=1  # 支持markdown格式
        )

        if result.get("code") == 0:
            ic(f"{epic_key} - 成功发送业务需求提醒私聊给: {assignee_email}")
        else:
            error_msg = f"发送私聊消息失败: {result}"
            ic(f"{epic_key} - {error_msg}")
            # 在错误信息中包含 user email 和 employee code
            send_error_notification(f"{epic_key}: {error_msg} - User Email: {assignee_email} - Employee Code: {employee_code}", epic_key)

    except Exception as e:
        error_msg = f"发送业务需求Epic私聊提醒失败: {str(e)}"
        ic(f"{epic_key} - {error_msg}")
        # 在错误信息中包含 user email
        send_error_notification(f"{error_msg} - User Email: {assignee_email}", epic_key)

def send_log_to_workee_pj_group(log_message):
    """
    发送日志消息到Workee PJ相关日志群

    Args:
        log_message: 日志消息内容
    """
    try:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        formatted_message = f"📋 **Workee PJ 相关日志** 📋\n\n⏰ 时间：{timestamp}\n\n{log_message}"
        
        # 发送到Workee PJ日志群
        test_for_seatalk_bot(formatted_message, False, WORKEE_PJ_LOG_GROUP_ID)
        ic(f"日志已发送到Workee PJ群: {log_message}")

    except Exception as e:
        ic(f"发送日志到Workee PJ群失败: {str(e)}")

def send_error_notification(error_message, epic_key=None):
    """
    发送错误通知到调试群 "NzQzMzAxODcyMjAy"

    Args:
        error_message: 错误信息
        epic_key: 相关的Epic key（可选）
    """
    try:
        # 格式化错误消息
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        if epic_key:
            message = f"🚨 **Epic提醒功能错误报告** 🚨\n\n"
            message += f"⏰ 时间：{timestamp}\n"
            message += f"📋 Epic：{epic_key}\n"
            message += f"❌ 错误：{error_message}\n"
        else:
            message = f"🚨 **Epic提醒功能系统错误** 🚨\n\n"
            message += f"⏰ 时间：{timestamp}\n"
            message += f"❌ 错误：{error_message}\n"

        # 发送到调试群
        test_for_seatalk_bot(message, False, "NzQzMzAxODcyMjAy")
        ic(f"错误通知已发送: {error_message}")

    except Exception as e:
        ic(f"发送错误通知失败: {str(e)}")