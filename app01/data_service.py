import time
import datetime
import pymysql
import requests
import json
from config import Config

def write_dict_to_json_file(data, filename):
    with open(filename, 'w') as outfile:
        json.dump(data, outfile)


def insert_data(result):
    # 连接数据库
    print(result)
    service_name = result["pipeline_name"]
    last_deploy_date = result["end_time"]
    deploy_person = result["executor"]
    deploy_tag = result["TAG"]
    jump_link = result["space_link"]
    deploy_status = result["build_result"]
    conn = pymysql.connect(
        host='localhost',
        port=3306,
        user='root',
        password='shopee',
        db='chatbotcicd',
        cursorclass=pymysql.cursors.DictCursor  # 使用字典游标返回查询结果
    )

    # 插入或更新数据
    with conn.cursor() as cursor:
        sql = 'INSERT INTO `deployments` (`service_name`, `last_deploy_date`, `deploy_person`, `deploy_tag`, `jump_link`, `deploy_status`) VALUES (%s, %s, %s, %s, %s, %s) ON DUPLICATE KEY UPDATE `last_deploy_date`=VALUES(`last_deploy_date`), `deploy_person`=VALUES(`deploy_person`), `deploy_tag`=VALUES(`deploy_tag`), `jump_link`=VALUES(`jump_link`), `deploy_status`=VALUES(`deploy_status`)'
        values = (service_name, last_deploy_date, deploy_person, deploy_tag, jump_link, deploy_status)
        cursor.execute(sql, values)
        conn.commit()

    # 关闭连接
    conn.close()


def get_service(project_id):
    detail_url = "http://luban.cs.test.shopee.io/chatbot-config-tool/space/get_chatbot_services"
    rep = requests.get(url=detail_url)
    rep_text = json.loads(rep.text)
    for i in rep_text['data']:
        if project_id == i['service_id']:
            return i['service_name']


def unixtime(ms_timestamp):
    # 将毫秒级 Unix 时间戳转换为 datetime 对象
    dt_object = datetime.datetime.fromtimestamp(ms_timestamp / 1000.0)

    # 将 datetime 对象转换为北京时间字符串
    beijing_time_str = dt_object.astimezone(datetime.timezone(datetime.timedelta(hours=8))).strftime(
        '%Y-%m-%d %H:%M:%S')

    # 输出结果
    return beijing_time_str


def get_project_id(url):
    BASE_URL = "https://git.garena.com/api/v4/projects/"
    url = url.replace("https://git.garena.com/", "")
    url = url.replace(".git", "")
    url = url.replace("/", "%2f")
    real_url = BASE_URL + '{url}/merge_requests?private_token=kBV8bRxCbEqk2G8eyFyz'. \
        format(url=url)
    get_feedback_isue = requests.get(real_url)
    text_all = json.loads(get_feedback_isue.text)
    print(text_all)
    if text_all:
        if type(text_all) == dict and text_all['message'] == '404 Project Not Found':
            return None
        else:
            return text_all[0]['project_id']

def gettoken():
    URL = "https://space.shopee.io/apis/uic/v2/auth/basic_login"
    headers = {
        'content-type': "application/json",
        'Authorization': 'Basic ********************************************************'
    }
    try:
        r = requests.post(url=URL, headers=headers, timeout=5)
        results = json.loads(r.text)
        # print(results)
        return results
    except requests.exceptions.RequestException as e:
        print(e)


def get_pipeinle_info(pipeline_name):
    history_url = "http://space.shopee.io/apis/pipeline/openapi/pipeline/history/list"
    result = gettoken()
    tokens = "Bearer " + result["token"]
    headers = {
        'content-type': "application/json",
        'Authorization': tokens
    }
    parameters = {
        "pipeline_name": pipeline_name,
        "page": 1,
        "page_size": 1
    }
    r = requests.post(url=history_url, json=parameters, headers=headers)
    result_rep = eval(r.text)
    build_info = {}
    if result_rep['data']['list']:
        build_result = result_rep['data']['list'][0]['build_status']
        end_time = result_rep['data']['list'][0]['end_time']
        TAG = json.loads(result_rep['data']['list'][0]['parameter'])["FROM_BRANCH"]
        executor = result_rep['data']['list'][0]['executor']
        pipeline_name = result_rep['data']['list'][0]['pipeline_name']
        project_id = result_rep['data']['list'][0]['id']
        callback_id = result_rep['data']['list'][0]['callback_id']
        build_info["build_result"] = build_result
        build_info["TAG"] = TAG
        build_info["executor"] = executor
        build_info["pipeline_name"] = pipeline_name
        build_info["end_time"] = unixtime(end_time)
        service_name = get_service(project_id)
        space_link = f"https://space.shopee.io/console/cmdb/deployment/pipeline/{service_name}/{callback_id}"
        build_info["space_link"] = space_link
        return build_info


class Params():
    def __init__(self, json_path):
        with open(json_path) as f:
            params = json.load(f)  # 将json格式数据转换为字典
            self.__dict__.update(params)

    def save(self, json_path):
        with open(json_path, 'w') as f:
            json.dump(self.__dict__, f, indent=4)

    def update(self, json_path):
        with open(json_path) as f:
            params = json.load(f)
            self.__dict__.update(params)

    @property
    def dict(self):
        return self.__dict__


if __name__ == '__main__':
    service_list = Config.srv2id
    service = []
    for pipeline in service_list.keys():
        print(f"{pipeline}-live")
        if "shopee-annotation-dataproxy-live" == f"{pipeline}-live":
            result = get_pipeinle_info(f"{pipeline}-staging")
            if result:
                service.append(result)
            #insert_data(result)
        else:
            result = get_pipeinle_info(f"{pipeline}-live")
            if result:
                service.append(result)
            #insert_data(result)
    if service:
        sorted_data = sorted(service, key=lambda x: x['end_time'], reverse=True)

        filename = 'data.json'
        write_dict_to_json_file({"data":sorted_data},filename)
    # project_dict = {}
    # project_id_dict = {}
    # get_service(project_dict=project_dict, project_id_dict=project_id_dict)


