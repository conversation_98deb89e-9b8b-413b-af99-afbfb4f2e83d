#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试统计修复的脚本
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
django.setup()

from django.test import RequestFactory
from app01.statistics.views import get_command_statistics, get_user_statistics, get_performance_statistics
import json

def test_command_statistics():
    """测试指令统计API"""
    print("🧪 测试指令统计API...")
    
    factory = RequestFactory()
    request = factory.get('/api/statistics/data/command-statistics/?days=7')
    
    try:
        response = get_command_statistics(request)
        data = json.loads(response.content)
        
        print(f"✅ 指令统计API响应: {response.status_code}")
        print(f"📊 数据内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        if data.get('success'):
            cmd_types = data.get('data', {}).get('command_type_distribution', [])
            success_trend = data.get('data', {}).get('success_rate_trend', [])
            print(f"📈 指令类型数量: {len(cmd_types)}")
            print(f"📈 成功率趋势数据点: {len(success_trend)}")
        
    except Exception as e:
        print(f"❌ 指令统计API测试失败: {e}")

def test_user_statistics():
    """测试用户统计API"""
    print("\n🧪 测试用户统计API...")
    
    factory = RequestFactory()
    request = factory.get('/api/statistics/data/user-statistics/?days=7')
    
    try:
        response = get_user_statistics(request)
        data = json.loads(response.content)
        
        print(f"✅ 用户统计API响应: {response.status_code}")
        print(f"📊 数据内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        if data.get('success'):
            user_data = data.get('data', {})
            print(f"👥 总用户数: {user_data.get('total_users', 0)}")
            print(f"🔥 活跃用户数: {user_data.get('active_users', 0)}")
            print(f"🏢 服务群数量: {user_data.get('total_groups', 0)}")
        
    except Exception as e:
        print(f"❌ 用户统计API测试失败: {e}")

def check_duplicate_records():
    """检查重复记录情况"""
    print("\n🔍 检查重复记录情况...")
    
    from app01.models import CommandExecutionRecord
    from django.db.models import Count
    
    try:
        # 统计各类型指令数量
        type_stats = CommandExecutionRecord.objects.values('command_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        print("📊 指令类型统计:")
        for stat in type_stats:
            print(f"  {stat['command_type']}: {stat['count']} 条记录")
        
        # 检查最近的重复记录
        recent_records = CommandExecutionRecord.objects.filter(
            user_id='1373330284'
        ).order_by('-created_at')[:10]
        
        print("\n📝 最近10条记录:")
        for record in recent_records:
            print(f"  {record.created_at} | {record.command_type} | {record.raw_input[:50]}")
            
    except Exception as e:
        print(f"❌ 检查重复记录失败: {e}")

if __name__ == '__main__':
    print("🚀 开始测试统计修复...")
    
    check_duplicate_records()
    test_command_statistics()
    test_user_statistics()
    
    print("\n✅ 测试完成!")
