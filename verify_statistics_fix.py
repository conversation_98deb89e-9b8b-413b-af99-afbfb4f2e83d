#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证统计系统修复效果的脚本
"""

import requests
import json
from datetime import datetime

def test_api_endpoint(url, description):
    """测试API端点"""
    print(f"\n🔍 测试 {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    print("✅ API响应成功")
                    return True, data
                else:
                    print(f"❌ API返回错误: {data.get('error', '未知错误')}")
                    return False, data
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                return False, None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False, None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False, None

def verify_statistics_apis(base_url="http://localhost:8000"):
    """验证统计API"""
    print("🚀 开始验证统计系统修复效果...")
    print(f"基础URL: {base_url}")
    
    # 测试API端点
    apis = [
        ("/api/statistics/data/command-statistics/?days=7", "指令统计API"),
        ("/api/statistics/data/user-statistics/?days=7", "用户统计API"),
        ("/api/statistics/data/performance-statistics/?days=7", "性能统计API"),
        ("/api/statistics/dashboard/", "统计面板页面"),
    ]
    
    results = {}
    
    for endpoint, description in apis:
        url = base_url + endpoint
        success, data = test_api_endpoint(url, description)
        results[endpoint] = {
            'success': success,
            'data': data,
            'description': description
        }
    
    # 生成验证报告
    print("\n" + "="*60)
    print("📋 验证结果汇总")
    print("="*60)
    
    success_count = 0
    total_count = len(apis)
    
    for endpoint, result in results.items():
        status = "✅ 通过" if result['success'] else "❌ 失败"
        print(f"{status} {result['description']}")
        if result['success']:
            success_count += 1
            
            # 显示关键数据
            if result['data'] and result['data'].get('data'):
                data = result['data']['data']
                if 'command_type_distribution' in data:
                    cmd_types = len(data['command_type_distribution'])
                    print(f"    📊 指令类型数量: {cmd_types}")
                if 'total_users' in data:
                    print(f"    👥 总用户数: {data['total_users']}")
                if 'cpu_usage_rate' in data:
                    print(f"    🖥️ CPU使用率: {data['cpu_usage_rate']}%")
    
    print(f"\n📈 总体成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count:
        print("🎉 所有API测试通过！统计系统修复成功！")
    else:
        print("⚠️ 部分API测试失败，请检查服务器状态和配置")
    
    return results

def check_database_duplicates():
    """检查数据库重复记录（需要数据库连接）"""
    print("\n🔍 检查数据库重复记录...")
    
    try:
        import os
        import django
        
        # 设置Django环境
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangoProject.settings')
        django.setup()
        
        from app01.models import CommandExecutionRecord
        from django.db.models import Count
        
        # 统计各类型指令数量
        type_stats = CommandExecutionRecord.objects.values('command_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        print("📊 当前指令类型统计:")
        total_records = 0
        for stat in type_stats:
            print(f"  {stat['command_type']}: {stat['count']} 条记录")
            total_records += stat['count']
        
        print(f"📝 总记录数: {total_records}")
        
        # 检查是否还有重复记录
        unknown_count = CommandExecutionRecord.objects.filter(command_type='unknown').count()
        ai_query_count = CommandExecutionRecord.objects.filter(command_type='ai_query').count()
        
        if unknown_count > 0 and ai_query_count > 0:
            print(f"⚠️ 仍存在可能的重复记录: unknown({unknown_count}) + ai_query({ai_query_count})")
            print("💡 建议清理历史重复数据")
        else:
            print("✅ 未发现明显的重复记录问题")
            
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        print("💡 请确保Django环境配置正确")

if __name__ == '__main__':
    # 验证API
    verify_statistics_apis()
    
    # 检查数据库（可选）
    try:
        check_database_duplicates()
    except:
        print("\n💡 跳过数据库检查（需要Django环境）")
    
    print(f"\n⏰ 验证完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("📖 详细修复信息请查看: statistics_fix_summary.md")
