<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R&D效率指标仪表板 - 预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #f0f0f0;
        }

        .header h1 {
            color: #333;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }

        .header h1::before {
            content: "";
            width: 24px;
            height: 24px;
            background: #ff6b35;
            border-radius: 4px;
            margin-right: 12px;
            display: inline-block;
        }

        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        select, button {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s ease;
            background: #fff;
        }

        select:focus, button:focus {
            outline: none;
            border-color: #ff6b35;
            box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.1);
        }

        button {
            background: #ff6b35;
            color: white;
            border: 1px solid #ff6b35;
            cursor: pointer;
            font-weight: 500;
        }

        button:hover {
            background: #e55a2b;
            border-color: #e55a2b;
        }

        button:active {
            transform: translateY(1px);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #f0f0f0;
            transition: all 0.2s ease;
            position: relative;
            cursor: pointer;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: #ff6b35;
            border-radius: 8px 8px 0 0;
        }

        .metric-card:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border-color: #ff6b35;
        }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .metric-title {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .metric-icon {
            width: 20px;
            height: 20px;
            background: #ff6b35;
            border-radius: 4px;
            position: relative;
        }

        /* 不同类型的图标 */
        .velocity-icon::after {
            content: '';
            position: absolute;
            top: 6px;
            left: 6px;
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 8px solid #fff;
        }

        .bug-icon::after {
            content: '';
            position: absolute;
            top: 6px;
            left: 6px;
            width: 8px;
            height: 8px;
            background: #fff;
            border-radius: 50%;
        }

        .time-icon::after {
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            width: 12px;
            height: 12px;
            border: 2px solid #fff;
            border-radius: 50%;
            border-top-color: transparent;
        }

        .quality-icon::after {
            content: '';
            position: absolute;
            top: 5px;
            left: 7px;
            width: 0;
            height: 0;
            border-left: 3px solid transparent;
            border-right: 3px solid transparent;
            border-bottom: 6px solid #fff;
        }

        .productivity-icon::after {
            content: '';
            position: absolute;
            top: 6px;
            left: 4px;
            width: 12px;
            height: 2px;
            background: #fff;
            border-radius: 1px;
        }

        .productivity-icon::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 4px;
            width: 8px;
            height: 2px;
            background: #fff;
            border-radius: 1px;
        }

        .score-icon::after {
            content: '';
            position: absolute;
            top: 6px;
            left: 6px;
            width: 8px;
            height: 8px;
            background: #fff;
            border-radius: 2px;
        }

        .metric-value {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin: 12px 0 8px 0;
        }

        .metric-change {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .metric-change.positive {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .metric-change.negative {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }

        .metric-change.neutral {
            background: #fafafa;
            color: #666;
            border: 1px solid #d9d9d9;
        }

        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #f0f0f0;
        }

        .chart-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 16px;
            font-weight: 600;
        }

        .chart-placeholder {
            height: 280px;
            background: #fafafa;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
            border: 1px dashed #d9d9d9;
        }

        .team-comparison {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .comparison-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .comparison-title {
            font-size: 20px;
            color: #2c3e50;
            font-weight: 600;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .comparison-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .comparison-table tr:hover {
            background: #f8f9fa;
        }

        .grade {
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 12px;
        }

        .grade-a { background: #d4edda; color: #155724; }
        .grade-b { background: #cce5ff; color: #004085; }
        .grade-c { background: #fff3cd; color: #856404; }
        .grade-d { background: #f8d7da; color: #721c24; }

        @media (max-width: 768px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .metric-value {
                font-size: 24px;
            }
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 趋势图模态框 */
        .trend-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .trend-modal.show {
            display: flex;
        }

        .trend-modal-content {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .trend-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .trend-modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .trend-modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #999;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .trend-modal-close:hover {
            color: #ff6b35;
            background: #f5f5f5;
            border-radius: 4px;
        }

        .trend-chart {
            height: 400px;
            background: #fafafa;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            border: 1px dashed #d9d9d9;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>R&D效率指标仪表板</h1>
            <div class="controls">
                <div class="control-group">
                    <label>选择团队</label>
                    <select id="teamSelector">
                        <option value="">请选择团队...</option>
                        <option value="chatbot_core">ChatBot核心团队</option>
                        <option value="chatbot_web">ChatBot前端团队</option>
                        <option value="chatbot_ai">ChatBot AI团队</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>时间范围</label>
                    <select id="timeRange">
                        <option value="7">最近7天</option>
                        <option value="30" selected>最近30天</option>
                        <option value="90">最近90天</option>
                    </select>
                </div>
                <button onclick="refreshData()">刷新数据</button>
                <button onclick="exportData()">导出数据</button>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loadingIndicator">
            <div class="spinner"></div>
            <div>正在加载数据...</div>
        </div>

        <!-- 指标卡片 -->
        <div class="metrics-grid">
            <div class="metric-card" onclick="showTrendModal('团队速度', 'velocity')">
                <div class="metric-header">
                    <span class="metric-title">团队速度</span>
                    <div class="metric-icon velocity-icon"></div>
                </div>
                <div class="metric-value">45.5</div>
                <div class="metric-change positive">+12.3% vs 上周</div>
            </div>

            <div class="metric-card" onclick="showTrendModal('Bug修复率', 'bug_fix_rate')">
                <div class="metric-header">
                    <span class="metric-title">Bug修复率</span>
                    <div class="metric-icon bug-icon"></div>
                </div>
                <div class="metric-value">94.2%</div>
                <div class="metric-change positive">+2.1% vs 上周</div>
            </div>

            <div class="metric-card" onclick="showTrendModal('平均周期时间', 'cycle_time')">
                <div class="metric-header">
                    <span class="metric-title">平均周期时间</span>
                    <div class="metric-icon time-icon"></div>
                </div>
                <div class="metric-value">3.2天</div>
                <div class="metric-change negative">+0.5天 vs 上周</div>
            </div>

            <div class="metric-card" onclick="showTrendModal('代码质量评分', 'code_quality')">
                <div class="metric-header">
                    <span class="metric-title">代码质量评分</span>
                    <div class="metric-icon quality-icon"></div>
                </div>
                <div class="metric-value">8.7</div>
                <div class="metric-change neutral">持平 vs 上周</div>
            </div>

            <div class="metric-card" onclick="showTrendModal('人均生产力', 'productivity')">
                <div class="metric-header">
                    <span class="metric-title">人均生产力</span>
                    <div class="metric-icon productivity-icon"></div>
                </div>
                <div class="metric-value">12.3</div>
                <div class="metric-change positive">+8.7% vs 上周</div>
            </div>

            <div class="metric-card" onclick="showTrendModal('综合评分', 'overall_score')">
                <div class="metric-header">
                    <span class="metric-title">综合评分</span>
                    <div class="metric-icon score-icon"></div>
                </div>
                <div class="metric-value">A-</div>
                <div class="metric-change positive">从B+提升</div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-section">
            <div class="chart-card">
                <div class="chart-title">团队速度趋势</div>
                <div class="chart-placeholder">
                    ECharts图表将在这里显示<br>
                    (团队速度 vs 时间)
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-title">质量指标分布</div>
                <div class="chart-placeholder">
                    ECharts图表将在这里显示<br>
                    (Bug修复率、缺陷密度等)
                </div>
            </div>
        </div>

        <!-- 团队对比 -->
        <div class="team-comparison">
            <div class="comparison-header">
                <div class="comparison-title">团队效能对比</div>
                <button onclick="toggleComparison()">切换对比模式</button>
            </div>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>团队</th>
                        <th>速度</th>
                        <th>质量</th>
                        <th>周期时间</th>
                        <th>综合评分</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>ChatBot核心团队</strong></td>
                        <td>45.5</td>
                        <td>94.2%</td>
                        <td>3.2天</td>
                        <td><span class="grade grade-a">A-</span></td>
                    </tr>
                    <tr>
                        <td><strong>ChatBot前端团队</strong></td>
                        <td>38.7</td>
                        <td>91.8%</td>
                        <td>2.8天</td>
                        <td><span class="grade grade-b">B+</span></td>
                    </tr>
                    <tr>
                        <td><strong>ChatBot AI团队</strong></td>
                        <td>52.1</td>
                        <td>96.5%</td>
                        <td>4.1天</td>
                        <td><span class="grade grade-a">A</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 趋势图模态框 -->
        <div class="trend-modal" id="trendModal">
            <div class="trend-modal-content">
                <div class="trend-modal-header">
                    <div class="trend-modal-title" id="trendModalTitle">指标趋势</div>
                    <button class="trend-modal-close" onclick="closeTrendModal()">&times;</button>
                </div>
                <div class="trend-chart" id="trendChart">
                    趋势图表将在这里显示
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据刷新
        function refreshData() {
            const loading = document.getElementById('loadingIndicator');
            loading.classList.add('show');
            
            setTimeout(() => {
                loading.classList.remove('show');
                alert('数据已刷新！');
            }, 2000);
        }

        // 模拟数据导出
        function exportData() {
            alert('数据导出功能（CSV/Excel格式）');
        }

        // 切换对比模式
        function toggleComparison() {
            alert('团队对比模式切换');
        }

        // 团队选择变化
        document.getElementById('teamSelector').addEventListener('change', function() {
            if (this.value) {
                refreshData();
            }
        });

        // 时间范围变化
        document.getElementById('timeRange').addEventListener('change', function() {
            refreshData();
        });

        // 显示趋势图模态框
        function showTrendModal(metricName, metricType) {
            const modal = document.getElementById('trendModal');
            const title = document.getElementById('trendModalTitle');
            const chart = document.getElementById('trendChart');

            title.textContent = metricName + ' - 30天趋势';

            // 模拟不同指标的趋势数据
            const trendData = {
                'velocity': '团队速度呈上升趋势，从35.2提升至45.5',
                'bug_fix_rate': 'Bug修复率保持稳定，平均94%以上',
                'cycle_time': '周期时间略有增加，需要关注',
                'code_quality': '代码质量评分稳定在8.5-8.8之间',
                'productivity': '人均生产力持续提升，团队效率优化明显',
                'overall_score': '综合评分从B+提升至A-，整体表现良好'
            };

            chart.innerHTML = `
                <div style="text-align: center; padding: 40px;">
                    <div style="font-size: 16px; color: #333; margin-bottom: 20px;">${metricName}趋势分析</div>
                    <div style="font-size: 14px; color: #666; line-height: 1.6;">
                        ${trendData[metricType] || '暂无趋势数据'}
                    </div>
                    <div style="margin-top: 30px; padding: 20px; background: #fff7e6; border: 1px solid #ffd591; border-radius: 6px;">
                        <div style="color: #ff6b35; font-weight: 500; margin-bottom: 8px;">📊 实际应用中将显示：</div>
                        <div style="color: #666; font-size: 13px;">
                            • ECharts折线图显示30天数据变化<br>
                            • 支持数据点悬停查看详情<br>
                            • 可切换不同时间范围（7天/30天/90天）
                        </div>
                    </div>
                </div>
            `;

            modal.classList.add('show');
        }

        // 关闭趋势图模态框
        function closeTrendModal() {
            const modal = document.getElementById('trendModal');
            modal.classList.remove('show');
        }

        // 点击模态框背景关闭
        document.getElementById('trendModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeTrendModal();
            }
        });

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('R&D效率指标仪表板预览页面已加载');

            // 添加键盘事件监听
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeTrendModal();
                }
            });
        });
    </script>
</body>
</html>
