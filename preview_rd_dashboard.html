<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R&D效率指标仪表板 - 预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .header h1::before {
            content: "📊";
            margin-right: 10px;
            font-size: 32px;
        }

        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        select, button {
            padding: 8px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        select:focus, button:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 500;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .metric-title {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .metric-icon {
            font-size: 24px;
            opacity: 0.7;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .metric-change {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .metric-change.positive {
            background: #d4edda;
            color: #155724;
        }

        .metric-change.negative {
            background: #f8d7da;
            color: #721c24;
        }

        .metric-change.neutral {
            background: #e2e3e5;
            color: #383d41;
        }

        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chart-title {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 16px;
            border: 2px dashed #dee2e6;
        }

        .team-comparison {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .comparison-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .comparison-title {
            font-size: 20px;
            color: #2c3e50;
            font-weight: 600;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .comparison-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .comparison-table tr:hover {
            background: #f8f9fa;
        }

        .grade {
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 12px;
        }

        .grade-a { background: #d4edda; color: #155724; }
        .grade-b { background: #cce5ff; color: #004085; }
        .grade-c { background: #fff3cd; color: #856404; }
        .grade-d { background: #f8d7da; color: #721c24; }

        @media (max-width: 768px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .metric-value {
                font-size: 24px;
            }
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>R&D效率指标仪表板</h1>
            <div class="controls">
                <div class="control-group">
                    <label>选择团队</label>
                    <select id="teamSelector">
                        <option value="">请选择团队...</option>
                        <option value="chatbot_core">ChatBot核心团队</option>
                        <option value="chatbot_web">ChatBot前端团队</option>
                        <option value="chatbot_ai">ChatBot AI团队</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>时间范围</label>
                    <select id="timeRange">
                        <option value="7">最近7天</option>
                        <option value="30" selected>最近30天</option>
                        <option value="90">最近90天</option>
                    </select>
                </div>
                <button onclick="refreshData()">🔄 刷新数据</button>
                <button onclick="exportData()">📊 导出数据</button>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loadingIndicator">
            <div class="spinner"></div>
            <div>正在加载数据...</div>
        </div>

        <!-- 指标卡片 -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">团队速度</span>
                    <span class="metric-icon">🚀</span>
                </div>
                <div class="metric-value">45.5</div>
                <div class="metric-change positive">↗ +12.3% vs 上周</div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">Bug修复率</span>
                    <span class="metric-icon">🐛</span>
                </div>
                <div class="metric-value">94.2%</div>
                <div class="metric-change positive">↗ +2.1% vs 上周</div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">平均周期时间</span>
                    <span class="metric-icon">⏱️</span>
                </div>
                <div class="metric-value">3.2天</div>
                <div class="metric-change negative">↘ +0.5天 vs 上周</div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">代码质量评分</span>
                    <span class="metric-icon">⭐</span>
                </div>
                <div class="metric-value">8.7</div>
                <div class="metric-change neutral">→ 持平 vs 上周</div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">人均生产力</span>
                    <span class="metric-icon">👥</span>
                </div>
                <div class="metric-value">12.3</div>
                <div class="metric-change positive">↗ +8.7% vs 上周</div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">综合评分</span>
                    <span class="metric-icon">🏆</span>
                </div>
                <div class="metric-value">A-</div>
                <div class="metric-change positive">↗ 从B+提升</div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-section">
            <div class="chart-card">
                <div class="chart-title">📈 团队速度趋势</div>
                <div class="chart-placeholder">
                    ECharts图表将在这里显示<br>
                    (团队速度 vs 时间)
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-title">🎯 质量指标分布</div>
                <div class="chart-placeholder">
                    ECharts图表将在这里显示<br>
                    (Bug修复率、缺陷密度等)
                </div>
            </div>
        </div>

        <!-- 团队对比 -->
        <div class="team-comparison">
            <div class="comparison-header">
                <div class="comparison-title">🏅 团队效能对比</div>
                <button onclick="toggleComparison()">切换对比模式</button>
            </div>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>团队</th>
                        <th>速度</th>
                        <th>质量</th>
                        <th>周期时间</th>
                        <th>综合评分</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>ChatBot核心团队</strong></td>
                        <td>45.5</td>
                        <td>94.2%</td>
                        <td>3.2天</td>
                        <td><span class="grade grade-a">A-</span></td>
                    </tr>
                    <tr>
                        <td><strong>ChatBot前端团队</strong></td>
                        <td>38.7</td>
                        <td>91.8%</td>
                        <td>2.8天</td>
                        <td><span class="grade grade-b">B+</span></td>
                    </tr>
                    <tr>
                        <td><strong>ChatBot AI团队</strong></td>
                        <td>52.1</td>
                        <td>96.5%</td>
                        <td>4.1天</td>
                        <td><span class="grade grade-a">A</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 模拟数据刷新
        function refreshData() {
            const loading = document.getElementById('loadingIndicator');
            loading.classList.add('show');
            
            setTimeout(() => {
                loading.classList.remove('show');
                alert('数据已刷新！');
            }, 2000);
        }

        // 模拟数据导出
        function exportData() {
            alert('数据导出功能（CSV/Excel格式）');
        }

        // 切换对比模式
        function toggleComparison() {
            alert('团队对比模式切换');
        }

        // 团队选择变化
        document.getElementById('teamSelector').addEventListener('change', function() {
            if (this.value) {
                refreshData();
            }
        });

        // 时间范围变化
        document.getElementById('timeRange').addEventListener('change', function() {
            refreshData();
        });

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('R&D效率指标仪表板预览页面已加载');
        });
    </script>
</body>
</html>
