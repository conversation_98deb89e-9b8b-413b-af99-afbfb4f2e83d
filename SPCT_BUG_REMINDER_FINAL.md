# SPCT/SPCB Bug提醒功能 - 最终部署版本

## ✅ 功能完成

SPCT/SPCB测试环境Bug提醒功能已完成开发和调试，现已可以正式部署使用。

### 核心功能

1. **智能Epic-Bug关联**: 通过blocks关系查找与Epic相关的Bug
2. **分级提醒机制**: 
   - P0/highest/high优先级：1天未解决 → @assignee + TL
   - P1/medium优先级：3天未解决 → @assignee + TL
3. **消息整合**: 同一群组的多个Epic的Bug会整合到一条消息中
4. **人员映射**: 自动查找assignee对应的Team Leader
5. **调试模式**: 支持调试模式，消息发送到调试群进行测试

### 查询逻辑

- **Epic筛选**: 查询SPCB/SPCT项目中符合条件的Epic，且在数据库中有对应群组
- **Bug查询**: 使用JQL `issue in linkedIssues("EPIC_KEY", "is blocked by")` 查找blocks这些Epic的Bug
- **优先级过滤**: 只处理P0/P1/highest/high/medium优先级的Bug
- **状态过滤**: 排除Done/Closed状态的Bug

## 🚀 部署配置

### 定时任务

已配置cron任务，在工作日15点执行：
```python
('0 15 * * 1-5', 'app01.views.cronjob_spct_test_bugs', f'>> {os.path.join(LOGS_DIR, "cronjob_spct_test_bugs.log")} 2>&1')
```

### API接口

- `/api/spct-test-bugs/` - 手动触发Bug提醒
- `/api/spct-bug-debug/` - 调试模式控制

### 调试模式管理

```bash
# 查看调试状态
python3 manage_spct_bug_debug.py status

# 开启调试模式（测试阶段）
python3 manage_spct_bug_debug.py enable

# 关闭调试模式（正式运行）
python3 manage_spct_bug_debug.py disable
```

## 🎯 使用建议

### 测试阶段（当前）
1. 保持调试模式开启
2. 监控调试群中的消息
3. 验证Bug筛选和人员@是否正确
4. 确认消息格式和内容

### 正式运行
1. 关闭调试模式：`python3 manage_spct_bug_debug.py disable`
2. 消息将发送到对应的Epic群组
3. 监控定时任务执行日志

## 🔧 问题修复记录

### v1.1 - 调试模式持久化修复 (2025-07-31)

**问题**: 调试模式无法持久化，每次设置后重新加载模块会重置为代码中的默认值

**解决方案**: 
1. 创建配置文件 `app01/spct_bug_debug_config.json` 存储调试模式状态
2. 添加 `load_spct_bug_debug_config()` 函数从配置文件加载设置
3. 修改 `set_spct_bug_reminder_debug_mode()` 函数将设置持久化到配置文件
4. 更新 `get_spct_bug_reminder_debug_status()` 函数实时从配置文件读取状态

**测试验证**: ✅ 调试模式可以正确开启/关闭并持久化保存

## 🔧 配置文件

### 调试配置
```python
# app01/seatalk_group_manager.py
SPCT_BUG_REMINDER_DEBUG = True  # 调试模式开关
SPCT_BUG_REMINDER_DEBUG_GROUP = "NzQzMzAxODcyMjAy"  # 调试群ID
```

### 团队配置
```python
# app01/seatalk_group_manager.py
TEAM_DATA = {
    "ChatSS": {
        "leaders": ["<EMAIL>"],
        "members": [...],
        "project": "SPCT"
    },
    # 更多团队配置...
}
```

## 📊 监控

### 日志文件
- **定时任务日志**: `logs/cronjob_spct_test_bugs.log`
- **详细调试**: 代码中使用`ic()`输出的详细调试信息

### 关键指标
- 处理的Epic数量
- 找到的Bug数量
- 发送的提醒消息数量
- 人员TL映射成功率

## 🧪 测试验证

测试证实功能正常工作：
- ✅ Epic SPCT-8239有10个blocks关系的Bug
- ✅ Bug查询JQL语句正确
- ✅ 调试模式正常工作
- ✅ 消息整合功能正常

## 📝 后续优化

1. **测试完成时间检查**: 实现基于Epic测试完成时间的超期提醒
2. **统计报告**: 添加Bug处理趋势分析
3. **配置界面**: 提供Web界面管理团队映射和提醒规则

---

**部署状态**: ✅ 已完成，调试模式运行中  
**下一步**: 在live环境调试模式下测试，确认无误后关闭调试模式正式启用